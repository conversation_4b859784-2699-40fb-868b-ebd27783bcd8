"use client";
import { MapInteractionCSS } from "react-map-interaction";
import type { fileDataType } from "@/types";
import Image from "next/image";

export const ImageViewer: React.FC<fileDataType> = (
  props,
): React.JSX.Element => {
  const myLoader = (): string => {
    return `${props.url}`;
  };
  return (
    <MapInteractionCSS showControls={"true"} btnClass={"plusminusBtnClass"}>
      <Image
        loader={myLoader}
        src={props.url}
        alt="image__preview"
        width={800}
        height={800}
        layout="responsive"
      />
    </MapInteractionCSS>
  );
};
