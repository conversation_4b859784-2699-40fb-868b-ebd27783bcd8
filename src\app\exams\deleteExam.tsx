import React, { useState } from "react";
import type {
  ExamDataType,
  ErrorCatch,
  ToastType,
  DeleteExamRequest,
} from "@/types";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import useExams from "@/hooks/useExams";
import { useTranslation } from "react-i18next";

interface DeleteExamProps {
  data: ExamDataType;
  onSave: (value: boolean) => void;
  onCancel: () => void;
  isModal?: boolean;
}

export default function DeleteExam({
  data,
  onSave,
  onCancel,
}: DeleteExamProps): React.JSX.Element {
  const { t } = useTranslation();
  const { toast } = useToast() as ToastType;
  const { deleteExam } = useExams();
  const [isLoading, setIsLoading] = useState(false);

  const handleDeleteClick = (): void => {
    void handleDeleteExam();
  };

  const handleDeleteExam = async (): Promise<void> => {
    try {
      setIsLoading(true);
      const orgId = localStorage.getItem("orgId");

      const deleteRequest: DeleteExamRequest = {
        exam_id: data.id,
        org_id: orgId ?? "",
      };

      const result = await deleteExam(deleteRequest);

      if (result.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.toast_success_title"),
          description: t("exams.examDeletedSuccessfully"),
        });
        onSave(true);
        onCancel();
      } else {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: t("exams.failedToDeleteExam"),
        });
      }
    } catch (error) {
      const err = error as ErrorCatch;
      console.log(err);
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("exams.failedToDeleteExam"),
      });
      console.error("An unexpected error occurred:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <div className="">
        <p className="mb-2">{t("exams.deleteExamMessage")}</p>
      </div>
      <div className="flex justify-end space-x-2 ">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isLoading}
        >
          {t("buttons.cancel")}
        </Button>
        <Button
          type="button"
          onClick={handleDeleteClick}
          disabled={isLoading}
          variant="default"
        >
          {isLoading ? t("buttons.deleting") : t("buttons.delete")}
        </Button>
      </div>
    </>
  );
}
