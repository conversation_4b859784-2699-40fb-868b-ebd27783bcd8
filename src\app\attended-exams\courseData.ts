const courseData = [
  {
    label: " Earth sciences",
    value: "7a6d1ee9-a570-4791-a1f1-b9893c8cf7f2",
  },
  {
    label: " Post Quantum Encryption",
    value: "********-aa85-4049-98b8-4f9a872ac51b",
  },
  {
    label: "Angular Programming",
    value: "c13dfa44-1d31-486f-a3d9-25be4861f482",
  },
  {
    label: "Appium Tool",
    value: "a579d0c4-142a-4c12-8902-d417cad6528c",
  },
  {
    label: "Bank new course",
    value: "33ad360a-d610-426d-a8f2-01a2aa0f615f",
  },
  {
    label: "Bank new course-Batch 2",
    value: "77545d33-e8b1-4c36-9b08-0b6765011bdd",
  },
  {
    label: "Bank new course copy",
    value: "a2027f3a-9c25-4a67-9b33-57aedb3b763a",
  },
  {
    label: "Bank new course dt",
    value: "0d841392-e9f3-4c20-80e1-e68d7c887f4b",
  },
  {
    label: "Basic Hindi",
    value: "a22ee02c-1a24-4e62-ab75-fc48e45233b4",
  },
  {
    label: "Introduction to Integration",
    value: "7af6b995-7533-4807-83a8-fb0a0f3fbcc0",
  },
  {
    label: "Malayalam Basic",
    value: "d5213dfb-fb38-48c0-9997-a1db52801e0c",
  },
  {
    label: "Bio-Chemistry",
    value: "f2974840-5f19-493e-84d3-e9392d01f79a",
  },
  {
    label: "Chemical Bonding",
    value: "364a5705-0b89-4189-8593-e087289fe183",
  },
  {
    label: "Networking Specialist",
    value: "76b8347b-abf4-4849-89ca-b317676fd3a7",
  },
  {
    label: "Types Of Cryptography",
    value: "e30a368d-9bce-4db6-bfd1-80d584982fcd",
  },
  {
    label: "Data Structures and Algorthms",
    value: "5f45c882-32d1-43d3-91ed-f47b3dafedd4",
  },
  {
    label: "Sample Course Demo",
    value: "f17c0813-6e74-430d-ac24-b5d3e4784d9f",
  },
  {
    label: "Electro magnetic field theory",
    value: "52c594b1-940d-42bf-a30c-b65acf2ee5c6",
  },
  {
    label: "Electro magnetic induction",
    value: "88607823-cb54-4648-b574-0b7ecb1373fe",
  },
  {
    label: "English Tutorial",
    value: "3e87b65d-b6c7-40da-be72-acc85d0100d4",
  },
  {
    label: "Features",
    value: "79b134f0-a63c-446f-a957-8fbf6ef4adb0",
  },
  {
    label: "Flask (web framework)",
    value: "f145c800-ba50-4ee8-ba23-0080c4827d81",
  },
  {
    label: "HTML",
    value: "4b6ecc65-abcc-41d2-b9c3-f476c35f51a7",
  },
  {
    label: "HaloAlkenes and haloarenes",
    value: "da97c2da-d8df-437d-8741-0d6673835224",
  },
  {
    label: "Ionic Framework Program",
    value: "daf331c1-e93b-4761-900c-6e56b0fd56e9",
  },
];
export default courseData;
