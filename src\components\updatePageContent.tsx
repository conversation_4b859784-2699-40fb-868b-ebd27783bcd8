"use client";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { useForm } from "react-hook-form";
import React, { useEffect, useState } from "react";

import { Editor } from "primereact/editor";
import type {
  ToastType,
  ErrorCatch,
  ResourceLibraryData,
  PageResourceForm,
  richTextType,
  UpdateResourceUrlRequest,
} from "@/types";
import { pageUrl } from "@/lib/constants";
import { zodResolver } from "@hookform/resolvers/zod";
import { AddPageResourceSchema } from "@/schema/schema";
import Link from "next/link";
import { useToast } from "@/components/ui/use-toast";
import useResourceLibrary from "@/hooks/useResourceLibrary";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import { useTranslation } from "react-i18next";

export default function UpdatePgaeContent({
  onCancel,
  resourceData,
  onSave,
}: {
  onCancel: () => void;
  onSave: () => void;
  resourceData: ResourceLibraryData;
}): React.JSX.Element {
  const { t } = useTranslation();
  
  const form = useForm<PageResourceForm>({
    resolver: zodResolver(AddPageResourceSchema),
  });
  const { toast } = useToast() as ToastType;
  const { editResourcesUrl } = useResourceLibrary();
  const [isButtonDisable, setButtonDisable] = useState<boolean>(false);
  const [richTextValues, setRichTextValues] = useState<
    richTextType | undefined
  >(undefined);

  useEffect(() => {
    form.setValue("pageTitle", resourceData.name);
    form.setValue("pageContent", resourceData.content as string);
    form.setValue("extension", resourceData.extension as string);
  }, []);

  const setRichTextValue = (richTextValue: richTextType | undefined): void => {
    if (richTextValue && richTextValue.htmlValue == null) {
      richTextValue = undefined;
    }
    form.setValue("pageContent", richTextValue?.htmlValue ?? "");
    setRichTextValues(richTextValue);
  };

  async function onSubmit(data: PageResourceForm): Promise<void> {
    if (richTextValues?.htmlValue !== undefined) {
      data.pageContent = richTextValues.htmlValue;
    }
    const org_id = localStorage.getItem("orgId");
    const params: UpdateResourceUrlRequest = {
      org_id: org_id ?? "",
      module_type: "page",
      resource_url: null,
      video_length: "00:00:00",
      thumbnail_url: null,
      extension: null,
      page_count: 0,
      resource_id: resourceData.id,
      page_content: data.pageContent,
    };

    try {
      const result = await editResourcesUrl(params);
      if (result.status === SUCCESS_MESSAGES.api_status_success) {
        setButtonDisable(true);
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_success,
          title: t("successMessages.toast_success_title"),
          description: t("successMessages.resource_update_success"),
        });
        onSave();
      } else {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: result.status,
        });
      }
    } catch (error) {
      setButtonDisable(false);
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
    }
  }

  const handleCancel = (): void => {
    onCancel();
  };

  return (
    <div className="border rounded-md p-4">
      <Form {...form}>
        <form
          onSubmit={(event) => void form.handleSubmit(onSubmit)(event)}
          className="space-y-8"
        >
          <div className="sm:col-span-4">
            <label>
            {t("resourceLibrary.pageContent")}<span className="text-red-700">*</span>
            </label>
            <div className="mt-2">
              <FormField
                control={form.control}
                name="pageContent"
                render={() => (
                  <FormItem>
                    <FormLabel></FormLabel>
                    <FormControl>
                      <Editor
                        value={resourceData.content}
                        onTextChange={(event) => {
                          const htmlValue = event.htmlValue;
                          const richTextValue = {
                            htmlValue: htmlValue,
                          };
                          setRichTextValue(richTextValue as richTextType);
                        }}
                        style={{ height: "320px" }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>
          {/* <div className="w-full md:w-1/3">
            <FormItem>
              <FormControl>
                <>
                  <Checkbox id="premium" />
                  <FormLabel className="px-4">Premium content</FormLabel>
                </>
              </FormControl>
              <FormMessage />
            </FormItem>
          </div> */}
          <div className="flex justify-end mt-4 gap-3">
            <Link href={pageUrl.ResourceLibraryLink}>
              <Button
                className="w-full sm:w-auto bg-[#33363F]"
                onClick={handleCancel}
              >
                {t("buttons.close")}
              </Button>
            </Link>
            <Button disabled={isButtonDisable} className="bg-[#9FC089]">
            {t("buttons.submit")}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
