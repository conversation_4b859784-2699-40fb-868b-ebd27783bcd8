"use client";
import React, { useState, useEffect } from "react";
import MainLayout from "../layout/mainlayout";
import { Label } from "@/components/ui/label";
import { Combobox } from "@/components/ui/combobox";
import useUsers from "@/hooks/useUsers";
import usePrivileges from "@/hooks/usePrivileges";
import { useToast } from "@/components/ui/use-toast";
// import { Button } from "@/components/ui/button";
import type {
  // AccessPrivilege,
  ErrorCatch,
  ToastType,
  // NewAccessPrivilege,
  GetPrivilegeListResponse,
  InnerItem,
  LogUserActivityRequest,
} from "@/types";
import { getColumns } from "./columns";
import { DataTable } from "../../components/ui/data-table/data-table";
import { Modal } from "@/components/ui/modal";
import ComfirmSubmit from "@/components/ui/confirmationmodal";
import { Spinner } from "@/components/ui/progressiveLoader";
import { privilegeData } from "@/lib/constants";
import getPrivilegeList from "@/hooks/useCheckPrivilege";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumb";
import { Button } from "@/components/ui/button";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";

export default function AccessPrivileges(): React.JSX.Element {
  const { t } = useTranslation();
  const columns = getColumns(t);
  const { getRoles } = useUsers();
  const { getListOfPrivilege, updatePrivileges } = usePrivileges();
  const { toast } = useToast() as ToastType;
  const [roleData, setRoles] = useState<{ value: string; label: string }[]>([]);
  const [privilegeDatas, setPrivilageData] = useState<
    GetPrivilegeListResponse[]
  >([]);
  const [selectedRole, setSelectedRole] = React.useState<string | undefined>(
    "",
  );
  const [defaultRoleLabel, setDefaultRoleLabel] = useState<string>("");
  const [emptyChoice, setEmptyChoice] = useState<boolean>(false);
  const { getPrivilegesList } = usePrivileges();
  const [isLoading, setIsLoading] = React.useState<boolean>(true);
  const [disableBtn, setDisableBtn] = useState<boolean>(false);
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);
  const { updateUserActivity } = useLogUserActivity();
  useEffect(() => {
    setBreadcrumbItems(
      getBreadCrumbItems(t, t("breadcrumb.accessPrivileges"), { "": "" }),
    );
    const fetchRoles = async (): Promise<void> => {
      try {
        const org_id = localStorage.getItem("orgId") as string;
        const roles = await getRoles(org_id ?? "");
        if (roles !== null && roles.length > 0) {
          const filteredRoles = roles.map((role) => ({
            value: role.id,
            label: role.display_name ?? role.name,
          }));
          setRoles(filteredRoles);
          if (filteredRoles?.length > 0) {
            const firstRole = filteredRoles[0].value ?? "";
            setSelectedRole(firstRole);
            fetchPrivilegeList(firstRole as string).catch((error) =>
              console.log(error),
            );
            setDefaultRoleLabel(filteredRoles[0].label ?? "");
          }
        } else {
          setRoles([]);
        }
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
      }
    };
    fetchRoles().catch((error) => console.log(error));
    setDisableBtn(getPrivilegeList("Role", privilegeData.Role.assignPrivilege));
  }, [t]);

  const fetchPrivilegeList = async (selectedRoleId: string): Promise<void> => {
    setIsLoading(true);
    try {
      const orgId = localStorage.getItem("orgId") as string;
      const roleId = selectedRoleId;

      const params = {
        org_id: orgId ?? "",
        role_id: roleId ?? "",
      };
      const privilegeList = await getListOfPrivilege(params);
      if (privilegeList !== null && privilegeList !== undefined) {
        const privilegeListData = privilegeList.privileges_data;
        setPrivilageData(privilegeListData as GetPrivilegeListResponse[]);
      }
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
    }
  };

  const handleRoleChange = (selectedOption: string): void => {
    setSelectedRole(selectedOption);
    fetchPrivilegeList(selectedOption as string).catch((error) =>
      console.log(error),
    );
  };

  const getAddedItem = (dataIndex: string, isSelect: boolean): void => {
    const index = parseInt(dataIndex);
    if (isSelect === true) {
      privilegeDatas[index].is_part_of_role = true;
    } else {
      privilegeDatas[index].is_part_of_role = false;
    }
  };

  const updatePrivilegeData = (): void => {
    const isPartOfRole = privilegeDatas?.filter(
      (item) => item.is_part_of_role === true,
    );
    const roleIds = isPartOfRole.map((item) => item.id);
    if (roleIds?.length !== 0) {
      setEmptyChoice(false);
      updateRolePrivileges(roleIds as string[]);
    } else {
      setEmptyChoice(true);
    }
  };

  const onSubmit = (): void => {
    updateRolePrivileges([]);
    setEmptyChoice(false);
  };

  const closeConfirmation = (): void => {
    setEmptyChoice(false);
  };

  const updateRolePrivileges = (idList: string[]): void => {
    const updateRole = async (): Promise<void> => {
      const orgId = localStorage.getItem("orgId") as string;
      const params = {
        org_id: orgId,
        user_role_id: selectedRole,
        role_privileges: idList.map((id) => ({ privilege_id: id })),
      };
      try {
        const result = await updatePrivileges(params);
        if (result.status === "success") {
          toast({
            variant: SUCCESS_MESSAGES.toast_variant_default,
            title: t("successMessages.toast_success_title"),
            description: t("successMessages.privilegeAdded"),
          });
          localStorage.removeItem("role_privileges");
          void getAllPrivileges();
          const params = {
            activity_type: "Access_Privilege",
            screen_name: "Access Privileges",
            action_details: "Privilege updated ",
            target_id: selectedRole as string,
            log_result: "SUCCESS",
          };
          void updateLogUserActivity(params).catch((error) => {
            console.error(error);
          });
        } else if (result.status === "error") {
          toast({
            variant: ERROR_MESSAGES.toast_variant_destructive,
            title: t("errorMessages.toast_error_title"),
            description: result.status,
          });
          const params = {
            activity_type: "Access_Privilege",
            screen_name: "Access Privileges",
            action_details: "Failed to update privilege ",
            target_id: selectedRole as string,
            log_result: "ERROR",
          };
          void updateLogUserActivity(params).catch((error) => {
            console.error(error);
          });
        }
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
        const params = {
          activity_type: "Access_Privilege",
          screen_name: "Access Privileges",
          action_details: "Failed to update privilege ",
          target_id: selectedRole as string,
          log_result: "ERROR",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      }
    };
    updateRole().catch((error) => console.log(error));
  };

  const getAllPrivileges = async (): Promise<void> => {
    const privileges = await getPrivilegesList();
    const rolePrivilegesString = JSON.stringify(privileges.role_privileges);
    localStorage.setItem("role_privileges", rolePrivilegesString);
  };

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };
  return (
    <MainLayout>
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      <div className="w-full">
        <>
          <h1 className="text-2xl font-semibold tracking-tight">
            {t("accessPrivileges.title")}
          </h1>
        </>
        <div className="border rounded-md p-4 mt-4 bg-[#fff]">
          <div className="w-full flex flex-col lg:flex-row lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
            <div className="w-full lg:w-1/4">
              <Label>{t("accessPrivileges.selectRole")}</Label>
              <div>
                <Combobox
                  data={roleData}
                  onSelectChange={handleRoleChange}
                  defaultLabel={defaultRoleLabel}
                />
              </div>
            </div>
          </div>
          {isLoading ? (
            <Spinner />
          ) : (
            <div className="w-full">
              {privilegeDatas?.length > 0 && (
                <DataTable
                  columns={columns}
                  data={privilegeDatas as GetPrivilegeListResponse[]}
                  FilterLabel={t("accessPrivileges.filterByPrivilegeName")}
                  FilterBy={"privilege_key"}
                  actions={[]}
                  isSelectedColumn={"is_part_of_role"}
                  onSetCheckedStatus={(
                    index: string,
                    checkedStatus: boolean,
                  ) => {
                    getAddedItem(index as string, checkedStatus as boolean);
                  }}
                />
              )}
            </div>
          )}
          {disableBtn && (
            <div className="flex justify-end mt-2">
              <div className="sm:flex">
                <div className="mb-4 sm:mb-0">
                  <Button
                    className="w-full sm:w-auto"
                    type="submit"
                    onClick={updatePrivilegeData}
                  >
                    {t("buttons.submit")}
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
      {emptyChoice && (
        <Modal
          title={""}
          header=""
          openDialog={emptyChoice}
          closeDialog={closeConfirmation}
        >
          <ComfirmSubmit
            onSave={onSubmit}
            onCancel={closeConfirmation}
            isModal={true}
            data={"privilege"}
          />
        </Modal>
      )}
    </MainLayout>
  );
}
