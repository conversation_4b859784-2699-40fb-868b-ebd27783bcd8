"use client";
import { type Metadata } from "next";
import React, {
  // type PropsWithChildren,
  createContext,
  useContext,
  useMemo,
  useState,
  useEffect,
  Suspense,
} from "react";
import { useTranslation } from "react-i18next";

import { Menu } from "../../components/ui/menu";
import { Sidebar } from "../../components/ui/sidebar";
import { Footer } from "@/components/ui/footer";
import { supabase } from "../../lib/client";
import useAuthorization from "@/hooks/useAuth";
import { useRouter } from "next/navigation";
import { Skeleton } from "@/components/ui/skeleton";
import type { SupabaseClient } from "@supabase/supabase-js";
import usePrivileges from "@/hooks/usePrivileges";
import type { RolePrivileges } from "@/types";
import { cn } from "@/lib/utils";
import { Spinner } from "@/components/ui/progressiveLoader";
export const metadata: Metadata = {
  title: "Music App",
  description: "Example music app using the components.",
};
export const AuthContext = createContext({ session: null, user: null });

export const AuthProvider = ({
  ...props
}: {
  supabase: SupabaseClient;
  children?: React.JSX.Element;
}): React.JSX.Element => {
  const [session] = useState(null);
  const [user] = useState(null);

  const value = useMemo(() => {
    return {
      session,
      user,
    };
  }, [session, user]);

  return <AuthContext.Provider value={value} {...props} />;
};

export const useAuth = (): unknown => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

interface MainLayoutProps {
  children: React.ReactNode;
}
export default function MainLayout({
  children,
}: MainLayoutProps): React.JSX.Element {
  const { i18n } = useTranslation();
  const isRTL = i18n.language === "ar";
  // const [showMenu, setShowMenu] = useState<boolean>(true);
  const [user, setUser] = useState<string>("");
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const [showSpinner, setShowSpinner] = useState(true);
  const { getUserInfo } = useAuthorization();
  const { getPrivilegesList } = usePrivileges();
  const [sidebarData, setSidebarData] = useState<RolePrivileges[]>();
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  useEffect(() => {
    const fetchUserInfo = async (): Promise<void> => {
      const userInfo = await getUserInfo();

      setUser(userInfo);
      setIsLoading(false);
    };
    fetchUserInfo().catch((error) => console.log(error));
    const rolePrivileges = localStorage.getItem("role_privileges");
    if (
      rolePrivileges !== null &&
      rolePrivileges !== undefined &&
      rolePrivileges !== ""
    ) {
      setShowSpinner(false);
      const privilege = localStorage.getItem("role_privileges") as string;
      const privilageList = JSON.parse(privilege) as RolePrivileges[];
      setSidebarData(privilageList);
    } else {
      const getAllPrivileges = async (): Promise<void> => {
        const privileges = await getPrivilegesList();
        const rolePrivilegesString = JSON.stringify(privileges.role_privileges);
        console.log("role_privileges", privileges.role_privileges);

        localStorage.setItem("role_privileges", rolePrivilegesString);
        setShowSpinner(false);
        const privilege = localStorage.getItem("role_privileges") as string;
        const privilageList = JSON.parse(privilege) as RolePrivileges[];
        setSidebarData(privilageList);
      };
      getAllPrivileges().catch((error) => console.log(error));
    }
  }, []);
  useEffect(() => {
    if (
      localStorage.getItem("access_token") === undefined ||
      localStorage.getItem("access_token") === null
    ) {
      router.push("/login");
    } else {
      if (user.length === 0 && !isLoading) {
        router.push("/login");
      }
      if (
        localStorage.getItem("orgId") === undefined ||
        localStorage.getItem("orgId") === null
      ) {
        router.push("/organization");
      }
    }
  }, [user, isLoading, router]);

  const handleSidebarToggle = (isOpen: boolean): void => {
    // Add this function
    setIsSidebarOpen(isOpen);
  };
  // const handleMenuToggle = (): void => {
  //   setIsSidebarOpen(!isSidebarOpen);
  // };

  return (
    <AuthProvider supabase={supabase}>
      <>
        {/* <Menu onTap={(val: boolean) => setShowMenu(val)} /> */}
        {!showSpinner ? (
          <div>
            <Menu isOpen={isSidebarOpen} />
            <div className="flex min-h-screen bg-[#f5f5f5]">
              {/* <div
            className={`fixed pt-10 md:pt-0 left-0 h-full transition-all duration-500 transform bg-white  z-50
            ${
              showMenu
                ? "-translate-x-0  peer-checked:translate-x-full w-full md:w-auto"
                : "-translate-x-full peer-checked:translate-x-0"
            }
          `}
          > */}
              <div
                className={cn(
                  "fixed pt-10 md:pt-0 h-full transition-all duration-500 transform bg-white z-50",
                  isSidebarOpen ? "w-56" : "w-16",
                  isRTL ? "right-0" : "left-0"
                )}
              >
                <Sidebar
                  sidebarData={sidebarData as RolePrivileges[]}
                  onSidebarToggle={handleSidebarToggle}
                />
              </div>
              {/* <div
            className={`w-full relative transition-all duration-500 transform -translate-x-0 mt-20 ${
              showMenu
                ? "ml-56  peer-checked:translate-x-full "
                : "peer-checked:translate-x-0"
            }`}
          > */}
              <div
                className={cn(
                  "w-full relative transition-all duration-500 transform mt-20",
                  isRTL 
                    ? (isSidebarOpen ? "mr-56" : "mr-16") 
                    : (isSidebarOpen ? "ml-56" : "ml-16")
                )}
              >
                <div className="h-full px-4 py-6 lg:px-8 pt-10 sm:pt-12 md:pt-0">
                  <Suspense
                    fallback={
                      <div className="flex items-center justify-center h-screen ">
                        <Skeleton className="w-[100px] h-[20px] rounded-full" />
                      </div>
                    }
                  >
                    {children}
                  </Suspense>
                </div>
                <Footer />
              </div>
            </div>
          </div>
        ) : (
          <Spinner></Spinner>
        )}
      </>
    </AuthProvider>
  );
}
