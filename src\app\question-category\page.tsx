"use client";

import React, { useState, useEffect } from "react";
import MainLayout from "../layout/mainlayout";
import { DataTable } from "../../components/ui/data-table/data-table";
import { getColumns } from "./columns";
import useQuestionBank from "@/hooks/useQuestionBank";
import type {
  CategoryList,
  ErrorCatch,
  ToastType,
  TopicDataType,
  QuestionCategoryForm,
  InnerItem,
} from "@/types";
import { useToast } from "@/components/ui/use-toast";
import { Spinner } from "@/components/ui/progressiveLoader";
import { Archive, Edit, PencilLineIcon, PlusIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import getPrivilegeList from "@/hooks/useCheckPrivilege";
import { privilegeData } from "@/lib/constants";
import { Modal } from "@/components/ui/modal";
import { AddNewQuesionCategoryForm } from "../questionbank/addNewQuestionCategory";
import DeleteQuestionCategory from "./deleteCategory";
import PublishQuestionCategory from "./publishQuestion-Category";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumb";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
} from "@/components/ui/tooltip";
import { TooltipTrigger } from "@radix-ui/react-tooltip";
import { useTranslation } from "react-i18next";
import { ERROR_MESSAGES } from "@/lib/messages";

function QuestionCategory(): React.JSX.Element {
  const { t } = useTranslation();
  const catColumns = getColumns(t);
  const { getQuestionCategory } = useQuestionBank();
  const { toast } = useToast() as ToastType;
  const [categories, setCategories] = useState<TopicDataType[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [categoryAddStatus, setCategoryAddStatus] = useState<boolean>(false);
  // const [formData, setFormData] = useState<QuestionCategoryForm[]>([]);
  const [passData, setpassData] = useState<QuestionCategoryForm>();
  const [title, setTitle] = useState("");
  const [deleteCategory, setDelete] = useState<boolean>(false);
  const [publishCategory, setPublish] = useState<boolean>(false);
  const [publishStatus, setPublishStatus] = useState<string>("");
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);
  const disableBtn: boolean = getPrivilegeList(
    "Question_Category",
    privilegeData.Question_Category.addQuestionCategory,
  );

  useEffect(() => {
    setBreadcrumbItems(
      getBreadCrumbItems(t, t("breadcrumb.questionCategory"), { "": "" }),
    );
    categoryList(true);
  }, [t]);

  const categoryList = (value: boolean): void => {
    console.log("value", value);
    const fetchData = async (): Promise<void> => {
      setIsLoading(true);
      try {
        const category: TopicDataType[] = await getQuestionCategory();
        setIsLoading(false);
        if (category.length > 0) {
          setCategories(category);
        } else {
          setCategories([]);
        }
        category.map((item) => {
          if (item.publish_status !== "Published") {
            item.hideIcon = false;
          } else {
            item.hideIcon = true;
          }
        });
        category.map((item) => {
          if (item.publish_status !== "Published") {
            item.hideEditDelete = false;
          } else {
            item.hideEditDelete = true;
          }
        });
        category.map((item) => {
          if (item.publish_status !== "Published") {
            item.hideEdit = false;
          } else {
            item.hideEdit = true;
          }
        });
      } catch (error) {
        setIsLoading(false);
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
        console.error("Error fetching data:");
      }
    };
    fetchData().catch((error) => console.log(error));
  };

  const handleAddCategory = (): void => {
    setCategoryAddStatus(!categoryAddStatus);
    setpassData({ value: "", label: "", description: "" });
    setTitle(t("questionCategory.addQuestionCategory"));
  };

  const openEditDialog = (val: CategoryList): void => {
    setTitle(t("questionCategory.editQuestionCategory"));
    setpassData(val);
    setCategoryAddStatus(true);
  };

  const openDeleteDialog = (val: CategoryList): void => {
    setTitle("");
    setpassData(val);
    setDelete(true);
  };

  const openPublishDialog = (val: CategoryList): void => {
    setTitle("");
    setPublishStatus(val.publish_status as string);
    setpassData(val);
    setPublish(true);
  };

  const closeDelete = (): void => {
    setDelete(false);
  };
  const closePublish = (): void => {
    setPublish(false);
  };

  return (
    <MainLayout>
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      <span>
        <h1 className="text-2xl font-semibold ">
          {t("questionCategory.title")}
          {disableBtn && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    onClick={handleAddCategory}
                    className="bg-ring bg-[#fb8500] hover:bg-[#fb5c00] py-2 md:py-3 px-4 md:px-6 whitespace-nowrap ml-2"
                  >
                    <PlusIcon className="h-4 w-4 md:h-5 md:w-5" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{t("questionCategory.addNewQuestionCategory")}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </h1>
      </span>
      <div className="border rounded-md p-4 mt-4 bg-[#fff]">
        <div className="rounded-md bg-white">
          {isLoading ? (
            <Spinner />
          ) : (
            <DataTable
              columns={catColumns}
              data={categories}
              // disablePublish={"hideIcon"}
              disableIcon={"hideIcon"}
              hideEdit={"hideEdit"}
              disableEditDelete={"hideEditDelete"}
              FilterLabel={t("questionCategory.filterByCategoryName")}
              FilterBy={"label"}
              actions={[
                {
                  title: `${t("questionCategory.edit")}`,
                  icon: Edit,
                  varient: "icon",
                  color: "#f29b26",
                  isEnable: getPrivilegeList(
                    "Question_Category",
                    privilegeData.Question_Category.updateQuestionCategory,
                  ),
                  handleClick: (val: unknown) =>
                    openEditDialog(val as CategoryList),
                },
                {
                  title: `${t("questionCategory.delete")}`,
                  icon: Archive,
                  varient: "icon",
                  color: "#bf3831",
                  isEnable: getPrivilegeList(
                    "Question_Category",
                    privilegeData.Question_Category.deleteQuestionCategory,
                  ),
                  handleClick: (val: unknown) =>
                    openDeleteDialog(val as CategoryList),
                },
                {
                  title: `${t("questionCategory.status")}`,
                  icon: PencilLineIcon,
                  color: "#fb8500",
                  varient: "icon",
                  isEnable: getPrivilegeList(
                    "Question_Bank",
                    privilegeData.Question_Bank.publishQuestionCategory,
                  ),
                  handleClick: (val: unknown) =>
                    openPublishDialog(val as CategoryList),
                  disabled: (val: unknown) =>
                    openPublishDialog(val as CategoryList),
                },
              ]}
              onSelectedDataChange={() => {}}
            />
          )}
        </div>
        <div>
          {categoryAddStatus && (
            <Modal
              title={title}
              header=""
              openDialog={categoryAddStatus}
              closeDialog={handleAddCategory}
            >
              <AddNewQuesionCategoryForm
                closeDialog={() => handleAddCategory()}
                isDialogOpen={() => categoryAddStatus}
                categoryList={(value: boolean) => categoryList(value)}
                data={passData as QuestionCategoryForm}
              />
            </Modal>
          )}
        </div>
        <div>
          {deleteCategory && (
            <Modal
              title={title}
              header=""
              openDialog={deleteCategory}
              closeDialog={closeDelete}
            >
              <DeleteQuestionCategory
                onSave={(value: boolean) => categoryList(value)}
                onCancel={closeDelete}
                isModal={true}
                data={passData as QuestionCategoryForm}
              />
            </Modal>
          )}
        </div>
        <div>
          {publishCategory && (
            <Modal
              title={
                publishStatus === "Published"
                  ? `${t("questionCategory.draftCategory")}`
                  : `${t("questionCategory.publishCategory")}`
              }
              header=""
              openDialog={publishCategory}
              closeDialog={closePublish}
            >
              <PublishQuestionCategory
                onSave={(value: boolean) => categoryList(value)}
                onCancel={closePublish}
                isModal={true}
                data={passData as QuestionCategoryForm}
              />
            </Modal>
          )}
        </div>
      </div>
    </MainLayout>
  );
}

export default QuestionCategory;
