import * as React from "react";
import { type FieldValues, useForm } from "react-hook-form";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { ModalButton } from "@/components/ui/modalButton";
import { useToast } from "@/components/ui/use-toast";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import { API_RESPONSE_SUCCESS, ORG_KEY } from "@/lib/constants";
import type { ErrorCatch, LogUserActivityRequest, ToastType } from "@/types";
import useComments from "@/hooks/useComments";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";

interface ApproveRejectType {
  closeDialog: (value: boolean) => void;
  commentId: string;
  commentStatus: string;
  commentString: string;
  onSave: () => void;
}

const ApproveRejectComments: React.FC<ApproveRejectType> = (
  props,
): React.JSX.Element => {
  const { t } = useTranslation();
  const form = useForm({
    defaultValues: {
      name: props.commentStatus === "Pending" ? "" : props.commentStatus,
    },
  });
  const { toast } = useToast() as ToastType;
  const { commentStatusUpdate } = useComments();
  const { updateUserActivity } = useLogUserActivity();
  async function onSubmit(data: FieldValues): Promise<void> {
    const org_id = localStorage.getItem(ORG_KEY);

    if (data.name === undefined) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.comment_status_not_selected"),
      });
    } else {
      let newStatus = "";

      if (props.commentStatus === "Pending") {
        newStatus = data.name === "approve" ? "Approved" : "Rejected";
      } else if (props.commentStatus === "Rejected") {
        newStatus = "Approved";
      } else if (props.commentStatus === "Approved") {
        newStatus = "Rejected";
      }

      const params = {
        org_id: org_id ?? "",
        comment_id: props.commentId,
        status: newStatus,
      };
      try {
        const response = await commentStatusUpdate(params);
        props.onSave();

        if (response.status === API_RESPONSE_SUCCESS) {
          if (newStatus === "Approved") {
            toast({
              variant: SUCCESS_MESSAGES.toast_variant_default,
              title: t("successMessages.comment_approve"),
              description: t("successMessages.comment_approve_msg"),
            });
            const params = {
              activity_type: "Comment",
              screen_name: "Approve Comment",
              action_details: "Comment Approved  ",
              target_id: props.commentId,
              log_result: "SUCCESS",
            };
            void updateLogUserActivity(params).catch((error) => {
              console.error(error);
            });
          } else {
            toast({
              variant: SUCCESS_MESSAGES.toast_variant_default,
              title: t("successMessages.comment_reject"),
              description: t("successMessages.comment_reject_msg"),
            });
            const params = {
              activity_type: "Comment",
              screen_name: "Reject Comment ",
              action_details: "Comment Rejected  ",
              target_id: props.commentId,
              log_result: "ERROR",
            };
            void updateLogUserActivity(params).catch((error) => {
              console.error(error);
            });
          }
          props.closeDialog(true);
        } else {
          toast({
            variant: ERROR_MESSAGES.toast_variant_destructive,
            title: t("errorMessages.toast_error_title"),
            description: t("errorMessages.something_went_wrong"),
          });
          const params = {
            activity_type: "Comment",
            screen_name: "Approve Comment ",
            action_details: "Failed to updated comment status  ",
            target_id: props.commentId,
            log_result: "ERROR",
          };
          void updateLogUserActivity(params).catch((error) => {
            console.error(error);
          });
        }
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err.message,
        });
        console.error("An unexpected error occurred:", error);
        const params = {
          activity_type: "Comment",
          screen_name: "Approve Comment ",
          action_details: "Failed to updated comment status  ",
          target_id: props.commentId,
          log_result: "ERROR",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      }
    }
  }
  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };
  return (
    <Form {...form}>
      <form
        onSubmit={(event) => void form.handleSubmit(onSubmit)(event)}
        className="space-y-8"
      >
        {props.commentStatus === "Pending" && (
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <>
                {props.commentStatus === "Pending" && (
                  <>
                    <FormItem>
                      <div>
                        <FormLabel>{t("comments.comment")}:</FormLabel>
                      </div>
                      <div>
                        <FormLabel>
                          <strong>&ldquo;{props.commentString}&rdquo;</strong>
                        </FormLabel>
                      </div>
                    </FormItem>
                    <FormItem>
                      <div>
                        <FormLabel>{t("comments.action")}:</FormLabel>
                      </div>
                      <FormControl>
                        <RadioGroup
                          onValueChange={field.onChange}
                          defaultValue={field.value as string}
                          className="flex flex-col space-y-1"
                        >
                          <FormItem className="flex items-center space-x-3 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="approve" />
                            </FormControl>
                            <FormLabel className="font-normal">
                              {t("comments.approve")}
                            </FormLabel>
                          </FormItem>
                          <FormItem className="flex items-center space-x-3 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="reject" />
                            </FormControl>
                            <FormLabel className="font-normal">
                              {t("comments.reject")}
                            </FormLabel>
                          </FormItem>
                        </RadioGroup>
                      </FormControl>
                      <FormDescription></FormDescription>
                      <FormMessage />
                    </FormItem>
                  </>
                )}
              </>
            )}
          />
        )}

        {props.commentStatus === "Rejected" && (
          <>
            <FormItem>
              <div>
                <FormLabel>{t("comments.comment")}:</FormLabel>
              </div>
              <div>
                <FormLabel>
                  <strong>&ldquo;{props.commentString}&rdquo;</strong>
                </FormLabel>
              </div>
            </FormItem>
            <FormItem>
              <FormLabel>{t("comments.approvePrompt")}</FormLabel>
            </FormItem>
          </>
        )}

        {props.commentStatus === "Approved" && (
          <>
            <FormItem>
              <div>
                <FormLabel>{t("comments.comment")}:</FormLabel>
              </div>
              <div>
                <FormLabel>
                  <strong>&ldquo;{props.commentString}&rdquo;</strong>
                </FormLabel>
              </div>
            </FormItem>
            <FormItem>
              <FormLabel>{t("comments.rejectPrompt")}</FormLabel>
            </FormItem>
          </>
        )}

        {props.commentStatus !== "Pending" && (
          <input
            type="hidden"
            {...form.register("name")}
            value={props.commentStatus === "Rejected" ? "Approved" : "Rejected"}
          />
        )}

        <ModalButton
          closeDialog={() => props.closeDialog(false)}
          closeLabel={t("buttons.cancel")}
          submitLabel={t("buttons.submit")}
        />
      </form>
    </Form>
  );
};

export default ApproveRejectComments;
