"use client";

import React, { useState, useEffect } from "react";
import type { Question } from "./columns";
import { getColumns } from "./columns";
import { DataTable } from "../../components/ui/data-table/data-table";
//import questionBankList from "./questionbankData";
import MainLayout from "../layout/mainlayout";
import * as XLSX from "xlsx"; // Import XLSX
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import {
  Download,
  Edit,
  Eye,
  FileDown,
  PencilLineIcon,
  PlusIcon,
  WalletIcon,
} from "lucide-react";
import type {
  QuestionBankData,
  ComboData,
  TopicDataType,
  ErrorCatch,
  ToastType,
  BatchQuestionData,
  InnerItem,
  LogUserActivityRequest,
} from "../../types";
import { XCircle } from "lucide-react";
import { Combobox } from "@/components/ui/combobox";
import useQuestionBank from "@/hooks/useQuestionBank";
import { Modal } from "@/components/ui/modal";
import { AddNewQuesionCategoryForm } from "./addNewQuestionCategory";
import { useToast } from "@/components/ui/use-toast";
import { Spinner } from "@/components/ui/progressiveLoader";
import getPrivilegeList from "@/hooks/useCheckPrivilege";
import { pageUrl, privilegeData } from "@/lib/constants";
import { useRouter } from "next/navigation";
import PublishQuestion from "./publishQuestion";
import PublishBatchQuestions from "./pulishBatchQuestions";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumb";
import { Document, Page, Text, View } from "@react-pdf/renderer";
import { pdfStyle } from "@/components/pdfStyle";
import dynamic from "next/dynamic";
import QuestionsFromExcel from "@/components/importQuestionsFromExcel/questionFromExcel";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";
import { ERROR_MESSAGES } from "@/lib/messages";

function QuestionBankList(): React.JSX.Element {
  const { t } = useTranslation();
  const PDFDownloadLink = dynamic(
    () => import("@react-pdf/renderer").then((mod) => mod.PDFDownloadLink),
    { ssr: false },
  );
  const columns = getColumns(t);
  const [selectedRow, setSelectedRow] = useState<QuestionBankData>({});
  const [showQuestions, setShowQuestions] = useState(false);
  const [publishQuestions, setPublish] = useState(false);
  const [batchPublish, setBatchPublish] = useState(false);
  const [categoryId, setCategoryId] = React.useState("");
  const [quetionId, setQuestionId] = React.useState<string | undefined>("");
  const [comboData, setComboData] = useState<ComboData[]>([]);
  const [categoryAddStatus, setCategoryAddStatus] = useState<boolean>(false);
  const { toast } = useToast() as ToastType;
  const [isLoading, setIsLoading] = React.useState<boolean>(true);
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);
  // const [disableBtn, setDisableBtn] = React.useState<boolean>(getPrivilegeList("Question_Bank", "ADD_QUESTION"));
  const [questionBankList, setQuestionBankList] = React.useState<
    QuestionBankData[] | undefined
  >([]);
  const [questionBatchBankData, setBatchQuestionBankList] = React.useState<
    BatchQuestionData[] | undefined
  >([]);
  const [publishStatus, setPublishStatus] = useState<string>("");
  const [isImportQuestions, setIsImportQuestions] =
    React.useState<boolean>(false);
  const [defaultLabel, setDefaultLabel] = useState<string>("");
  const [defaultId, setDefaultId] = useState<string>("");

  const disableBtn: boolean = getPrivilegeList(
    "Question_Bank",
    privilegeData.Question_Bank.addQuestion,
  );
  // const disableCategory: boolean = getPrivilegeList(
  //   "Question_Bank",
  //   privilegeData.Question_Bank.addQuestionCategory,
  // );
  const { getQuestionBankList, getPublishedQuestionCategory } =
    useQuestionBank();
  const { updateUserActivity } = useLogUserActivity();

  const handleDetailView = (data: QuestionBankData): void => {
    setSelectedRow(data);
    setShowQuestions(true);
  };
  const handlePublish = (data: QuestionBankData): void => {
    setPublishStatus(data.question_publish_status as string);
    setQuestionId(data?.question_id);
    setSelectedRow(data);
    setPublish(true);
  };
  const editQuestionBank = (data: QuestionBankData): void => {
    router.push(`${pageUrl.addQuestion}?questionId=${data.question_id}`);
  };
  const closeQuestionSection = (): void => {
    setShowQuestions(false);
    setSelectedRow({});
  };
  const publishClose = (): void => {
    setPublish(false);
  };
  const onBatchPublish = (): void => {
    setBatchPublish(true);
  };
  const batchPublishClose = (): void => {
    setBatchPublish(false);
  };

  const customColumnWidths: Record<string, { width: number; align: string }> = {
    question_text: { width: 700, align: "justify" },
    question_category_name: { width: 500, align: "center" },
    default_mark: { width: 150, align: "center" },
    penalty: { width: 150, align: "center" },
    status: { width: 150, align: "center" },
  };

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  const questionCategoryList = (): void => {
    const fetchData = async (): Promise<void> => {
      try {
        const category: TopicDataType[] = await getPublishedQuestionCategory();
        const comboData: ComboData[] = category?.map((cat) => ({
          value: cat.value,
          label: cat.label,
        }));

        setComboData(comboData);
        setDefaultId(comboData[0]?.value as string);
        setDefaultLabel(comboData[0]?.label as string);
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
        console.error("Error fetching data:");
      }
    };
    fetchData().catch((error) => console.log(error));
  };

  const router = useRouter();

  useEffect(() => {
    setBreadcrumbItems(
      getBreadCrumbItems(t, t("breadcrumb.questionBank"), { "": "" }),
    );
  }, [t]);
  useEffect(() => {
    questionCategoryList();
  }, [categoryAddStatus]);
  useEffect(() => {
    getQuestionsList(true);
  }, [categoryId]);

  const getQuestionsList = (value: boolean): void => {
    const fetchDataQuestion = async (): Promise<void> => {
      setIsLoading(true);
      console.log(value);
      try {
        const qcatId = categoryId;
        const data = await getQuestionBankList(
          qcatId !== "" ? qcatId : undefined,
        );
        setIsLoading(false);
        if (data?.length > 0) {
          data.sort((a, b) =>
            (a.question_category_name ?? "").localeCompare(
              b.question_category_name ?? "",
            ),
          );
          data.map((item) => {
            if (item.question_text != null) {
              item.question_text = item.question_text.replace(
                /<pre\b[^>]*>(.*?)<\/pre>/s,
                "<p>$1</p>",
              );
            }
          });

          setQuestionBankList(data);
          data.map((item) => {
            if (item.question_publish_status !== "Published") {
              item.hideIcon = false;
            } else {
              item.hideIcon = true;
            }
          });
          data.map((item) => {
            if (item.question_publish_status !== "Published") {
              item.hideEdit = false;
            } else {
              item.hideEdit = true;
            }
          });

          const batchData: BatchQuestionData[] = data
            ?.filter((item) => item.question_publish_status !== "Published") // Filter the items based on the condition
            .map((item) => ({
              question_id: item.question_id,
              question_text: item.question_text,
              question_category: item.question_category_id,
            }));
          setBatchQuestionBankList(batchData);
        } else {
          setQuestionBankList([]);
          setBatchQuestionBankList([]);
        }
      } catch (error) {
        setIsLoading(false);
        console.error("Error fetching data:");
      }
    };

    fetchDataQuestion().catch((error) => console.log(error));
  };
  const handleComboValueChange = (selectedValue: string): void => {
    setCategoryId(selectedValue);
  };

  const stripHtmlTags = (html: string): string => {
    const doc = new DOMParser().parseFromString(html, "text/html");
    const textContent = doc.body.textContent;
    return textContent !== null ? textContent.trim() : "";
  };

  const exportToExcel = (): void => {
    console.log(defaultId);

    try {
      if (!questionBankList || questionBankList.length === 0) {
        alert("No data to export");
        const params = {
          activity_type: "Question_Bank",
          screen_name: "Question_Bank",
          action_details: "No questions for export",
          target_id: categoryId !== "" ? categoryId : defaultId,
          log_result: "ERROR",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
        return;
      }

      const processQuestionData = (item: QuestionBankData): unknown => {
        return {
          "Question Name": stripHtmlTags(item.question_text ?? ""),
          "Question Category": item.question_category_name,
          "Default Mark": item.default_mark,
          "Penalty Score": item.penalty,
          // "Status": item.status,
        };
      };

      const data = questionBankList.map(processQuestionData);
      const worksheet: XLSX.WorkSheet = XLSX.utils.json_to_sheet(data);
      const workbook: XLSX.WorkBook = XLSX.utils.book_new();

      worksheet["!cols"] = [
        { wch: 100 }, // Question Name
        { wch: 50 }, // Question Name
        { wch: 20 }, // Default Mark
        { wch: 15 }, // Penalty Score
        { wch: 15 }, // Status
      ];

      worksheet["!rows"] = [
        { hpt: 20 }, // Row 1 height in points
        { hpt: 20 }, // Row 2 height in points
        { hpt: 20 }, // Row 1 height in points
        { hpt: 20 }, // Row 2 height in points
        { hpt: 20 }, // Row 2 height in points
      ];

      XLSX.utils.book_append_sheet(workbook, worksheet, "Questions");
      XLSX.writeFile(workbook, "Questions.xlsx");
      const params = {
        activity_type: "Question_Bank",
        screen_name: "Question_Bank",
        action_details: "Successfully export questions to Excel",
        target_id: categoryId !== "" ? categoryId : defaultId,
        log_result: "SUCCESS",
      };
      void updateLogUserActivity(params).catch((error) => {
        console.error(error);
      });
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
      console.error("Error exporting to Excel:", error);
      const params = {
        activity_type: "Question_Bank",
        screen_name: "Question_Bank",
        action_details: "Failed to export questions to Excel",
        target_id: categoryId !== "" ? categoryId : defaultId,
        log_result: "ERROR",
      };
      void updateLogUserActivity(params).catch((error) => {
        console.error(error);
      });
    }
  };

  const removeHTMLTags = (html: string | undefined): React.JSX.Element => {
    if (html != null) {
      const tempDiv = document.createElement("div");
      tempDiv.innerHTML = html;
      return <div dangerouslySetInnerHTML={{ __html: tempDiv.innerHTML }} />;
    } else {
      return <div></div>;
    }
  };

  const handleImportQuestions = (): void => {
    setIsImportQuestions(true);
  };

  const handleAddCategory = (): void => {
    setCategoryAddStatus(!categoryAddStatus);
  };

  const pdfDownload = (): void => {
    const params = {
      activity_type: "Question_Bank",
      screen_name: "Question_Bank",
      action_details: "Successfully download PDF of questions",
      target_id: categoryId !== "" ? categoryId : defaultId,
      log_result: "SUCCESS",
    };
    void updateLogUserActivity(params).catch((error) => {
      console.error(error);
    });
  };

  const ExportToPDF = ({
    questionBankList,
  }: {
    questionBankList: QuestionBankData[];
  }): React.JSX.Element => {
    return (
      <Document>
        <Page size="A4" style={pdfStyle.page}>
          <View style={pdfStyle.section}>
            <Text style={pdfStyle.header}>{t("questionBank.title")}</Text>
            <View style={pdfStyle.tableContainer}>
              {/* Table Header */}
              <View style={pdfStyle.tableRow}>
                <Text style={[pdfStyle.tableHeader, pdfStyle.siNo]}>
                  {t("questionBank.slno")}
                </Text>
                <Text style={[pdfStyle.tableHeader, pdfStyle.question]}>
                  {t("questionBank.question")}
                </Text>
                <Text style={[pdfStyle.tableHeader, pdfStyle.category]}>
                  {t("questionBank.category")}
                </Text>
                <Text style={[pdfStyle.tableHeader, pdfStyle.mark]}>
                  {t("questionBank.defaultMark")}
                </Text>
                <Text style={[pdfStyle.tableHeader, pdfStyle.penalty]}>
                  {t("questionBank.penaltyScore")}
                </Text>
              </View>

              {/* Table Data */}
              {questionBankList.map((item, index) => (
                <View key={index} style={pdfStyle.tableRow}>
                  <Text style={[pdfStyle.tableCell, pdfStyle.siNo]}>
                    {index + 1}
                  </Text>
                  <Text style={[pdfStyle.tableCell, pdfStyle.question]}>
                    {stripHtmlTags(item.question_text as string)}
                  </Text>
                  <Text style={[pdfStyle.tableCell, pdfStyle.category]}>
                    {item.question_category_name}
                  </Text>
                  <Text style={[pdfStyle.tableCell, pdfStyle.mark]}>
                    {item.default_mark}
                  </Text>
                  <Text style={[pdfStyle.tableCell, pdfStyle.penalty]}>
                    {item.penalty}
                  </Text>
                </View>
              ))}
            </View>
          </View>
        </Page>
      </Document>
    );
  };

  const closeImportQuestions = (): void => {
    setIsImportQuestions(false);
  };

  return (
    <MainLayout>
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      <div className="flex justify-between">
        <div className={`w-full ${showQuestions ? "md:w-3/4" : ""}`}>
          <span>
            <h1 className="text-2xl font-semibold tracking-tight">
              {t("questionBank.title")}
            </h1>
            {disableBtn && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Link href="./add-question">
                      <Button className="bg-ring bg-[#fb8500] hover:bg-[#fb5c00] py-2 md:py-3 px-4 md:px-6 whitespace-nowrap ml-2">
                        <PlusIcon className="h-4 w-4 md:h-5 md:w-5" />
                      </Button>
                    </Link>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{t("questionBank.createQuestion")}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
            {disableBtn && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      onClick={onBatchPublish}
                      className="bg-ring bg-[#fb8500] hover:bg-[#fb5c00] py-2 md:py-3 px-4 md:px-6 whitespace-nowrap ml-2"
                    >
                      <WalletIcon className="h-4 w-4 md:h-5 md:w-5" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{t("questionBank.publishQuestionInBatch")}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </span>

          <div className="border rounded-md p-4 mt-4 bg-[#fff]">
            {/* {disableCategory && ( */}
            <div className="flex gap-x-4 mt-4">
              <div className="sm:w-1/2 md:w-1/4 gap-x-4">
                <Label>{t("questionBank.selectQuestionCategory")}</Label>{" "}
                <Combobox
                  data={comboData}
                  onSelectChange={handleComboValueChange}
                  defaultLabel={defaultLabel}
                />
              </div>
              <div className="flex flex-grow justify-end gap-x-4 pt-5">
                <Button
                  className="w-full md:w-auto px-2  bg-[#fb8500] hover:bg-[#fb5c00]"
                  onClick={handleImportQuestions}
                >
                  {t("questionBank.importQuestions")}
                </Button>
                {/* {disableBtn && (
                  <Link href="./add-question">
                    <Button className=" w-full md:w-auto px-2  bg-[#fb8500] hover:bg-[#fb5c00]">
                      <PlusIcon className="h-5 w-5" />
                      Add New Question
                    </Button>
                  </Link>
                )} */}
                {/* {disableBtn && (
                  <Button
                    className="w-full md:w-auto px-2  bg-[#fb8500] hover:bg-[#fb5c00]"
                    onClick={onBatchPublish}
                  >
                    Batch Publish
                  </Button>
                )} */}
                <div title={t("questionBank.editResource")}>
                  <FileDown
                    onClick={exportToExcel}
                    className="text-green-600 cursor-pointer hover:text-green-700 mr-2 mt-2"
                    style={{ fontSize: "24px" }}
                  />
                </div>
                <div>
                  <PDFDownloadLink
                    document={
                      <ExportToPDF
                        questionBankList={
                          questionBankList as QuestionBankData[]
                        }
                      />
                    }
                    fileName="Questions.pdf"
                  >
                    <div title={t("questionBank.exportToPDF")} className="mt-2">
                      <Download onClick={pdfDownload} />
                    </div>
                  </PDFDownloadLink>
                </div>
              </div>
            </div>

            {isLoading ? (
              <Spinner />
            ) : (
              <div>
                <DataTable
                  columns={columns}
                  data={questionBankList as Question[]}
                  disableIcon={"hideIcon"}
                  hideEdit={"hideEdit"}
                  FilterLabel={t("questionBank.filterByQuestion")}
                  FilterBy={"question_text"}
                  actions={[
                    {
                      title: t("questionBank.edit"),
                      icon: Edit,
                      color: "#fb8500",
                      isEnable: getPrivilegeList(
                        "Question_Bank",
                        privilegeData.Question_Bank.getQuestionBankList,
                      ),
                      varient: "icon",
                      handleClick: (val: unknown) =>
                        editQuestionBank(val as QuestionBankData),
                    },
                    {
                      title: t("questionBank.view"),
                      icon: Eye,
                      color: "#9bbb5c",
                      isEnable: getPrivilegeList(
                        "Question_Bank",
                        privilegeData.Question_Bank.getQuestionBankList,
                      ),
                      varient: "icon",
                      handleClick: (val: unknown) =>
                        handleDetailView(val as QuestionBankData),
                    },
                    {
                      title: t("questionBank.publishedStatus"),
                      icon: PencilLineIcon,
                      isEnable: getPrivilegeList(
                        "Question_Bank",
                        privilegeData.Question_Bank.approveQuestion,
                      ),
                      varient: "icon",
                      color: "#fb8500",
                      handleClick: (val: unknown) =>
                        handlePublish(val as QuestionBankData),
                      // disabled: (val: unknown) =>
                      //   handlePublish(val as QuestionBankData),
                    },
                  ]}
                  customColumnWidths={customColumnWidths}
                />
              </div>
            )}
          </div>
        </div>
        {showQuestions && (
          <div className={`w-full md:w-1/4 p-4 mt-4`}>
            <div className="mt-4 p-4 border rounded-md relative">
              <Button
                className="absolute top-0 right-0 p-2"
                onClick={closeQuestionSection}
              >
                <XCircle />
              </Button>

              {selectedRow?.question_type === "HTML" ? (
                <div className="text-gray-600 mt-2">
                  {selectedRow?.question_text != null &&
                  selectedRow?.question_text !== "" ? (
                    <div
                      dangerouslySetInnerHTML={{
                        __html: selectedRow.question_text,
                      }}
                    />
                  ) : (
                    <div></div>
                  )}
                </div>
              ) : (
                <p className="text-gray-600 mt-2">
                  {removeHTMLTags(selectedRow.question_text)}
                </p>
              )}

              <h2 className="text-xl font-semibold mt-4">Answers</h2>
              <ul className="list-disc list-inside mt-2">
                {/* {selectedRow.answer_type === "HTML" */}
                {selectedRow?.answers?.map((answer) =>
                  answer.answer_type === "HTML" ? (
                    <li
                      key={answer.id}
                      dangerouslySetInnerHTML={{
                        __html: answer.answer,
                      }}
                    />
                  ) : (
                    <li key={answer.id}>{answer.answer}</li>
                  ),
                )}
              </ul>
            </div>
          </div>
        )}
      </div>
      {categoryAddStatus && (
        <Modal
          title={t("questionBank.addQuestionCategory")}
          header=""
          openDialog={categoryAddStatus}
          closeDialog={handleAddCategory}
        >
          <AddNewQuesionCategoryForm
            closeDialog={() => handleAddCategory()}
            isDialogOpen={() => categoryAddStatus}
            categoryList={() => {}}
            data={{ value: "", label: "", description: "" }}
          />
        </Modal>
      )}
      {publishQuestions && (
        <Modal
          title={
            publishStatus === "Published"
              ? `${t("questionBank.draftQuestion")}`
              : `${t("questionBank.publishQuestion")}`
          }
          header=""
          openDialog={publishQuestions}
          closeDialog={publishClose}
        >
          <PublishQuestion
            onSave={(value: boolean) => getQuestionsList(value)}
            onCancel={publishClose}
            questionId={quetionId}
            publishStatus={publishStatus}
          />
        </Modal>
      )}
      {batchPublish && (
        <Modal
          title={t("questionBank.publishQuestions")}
          header=""
          openDialog={batchPublish}
          closeDialog={batchPublishClose}
          type="max-w-6xl"
        >
          <PublishBatchQuestions
            onSave={(value: boolean) => getQuestionsList(value)}
            onCancel={batchPublishClose}
            unPublishedQuestions={questionBatchBankData as BatchQuestionData[]}
          />
        </Modal>
      )}
      {isImportQuestions && (
        <Modal
          title="Import Questions from Excel"
          header=""
          openDialog={isImportQuestions}
          closeDialog={closeImportQuestions}
          type="max-w-8xl"
        >
          <QuestionsFromExcel
            onSave={(value: boolean) => getQuestionsList(value)}
            onCancel={closeImportQuestions}
          />
        </Modal>
      )}
    </MainLayout>
  );
}

export default QuestionBankList;
