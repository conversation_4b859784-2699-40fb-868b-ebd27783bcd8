import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader } from "../card";

import Chart, { ArcElement, Legend, Tooltip } from "chart.js/auto";
import type { ActiveElement, ChartEvent } from "chart.js/auto";

import { Bar } from "react-chartjs-2";
import type {
  CheckpointsCourseStatsReturnType,
  CourseAttendancesGraphType,
} from "@/types";
//import { useRouter } from "next/navigation";

Chart.register(ArcElement, Tooltip, Legend);

interface AttendanceGraphProps {
  children: [];
  labels: string[];
  datasets: CheckpointsCourseStatsReturnType[];
}

export function DBCourseWiseStats({
  labels,
  datasets,
}: AttendanceGraphProps): React.JSX.Element {
  const [attendanceData, setAttendanceData] =
    useState<CourseAttendancesGraphType>();

  useEffect(() => {
    console.log(datasets);
    const state: CourseAttendancesGraphType = {
      labels: labels, //["course1", "course2", "course3", "course4", "course5"],
      datasets: [
        {
          data: [66, 144, 146, 116, 107],
          label: "Enrolled",
          borderColor: "rgb(109, 253, 181)",
          backgroundColor: "rgb(109, 253, 181,0.5)",
          borderWidth: 2,
        },
        {
          data: [40, 100, 44, 70, 63],
          label: "Watched",
          borderColor: "rgb(75, 192, 192)",
          backgroundColor: "rgb(75, 192, 192,0.5)",
          borderWidth: 2,
        },
        {
          data: [20, 24, 50, 34, 33],
          label: "Pass out",
          borderColor: "rgb(255, 205, 86)",
          backgroundColor: "rgb(255, 205, 86,0.5)",
          borderWidth: 2,
        },
        {
          data: [6, 20, 52, 12, 11, 78, 21],
          label: "Failed",
          borderColor: "rgb(255, 99, 132)",
          backgroundColor: "rgb(255, 99, 132,0.5)",
          borderWidth: 2,
        },
      ],
    };
    setAttendanceData(state);
    console.log("new graph data");
    console.log(state);
  }, [attendanceData]);

  //const router = useRouter();

  /* const attendanceDataSet = {
    labels: labelsData,
    idSet: ["1", "2", "3", "4", "5"],

    datasets: [
      {
        data: [66, 144, 146, 116, 107],
        label: "Enrolled",
        borderColor: "rgb(109, 253, 181)",
        backgroundColor: "rgb(109, 253, 181,0.5)",
        borderWidth: 2,
      },
      {
        data: [40, 100, 44, 70, 63],
        label: "Watched",
        borderColor: "rgb(75, 192, 192)",
        backgroundColor: "rgb(75, 192, 192,0.5)",
        borderWidth: 2,
      },
      {
        data: [20, 24, 50, 34, 33],
        label: "Pass out",
        borderColor: "rgb(255, 205, 86)",
        backgroundColor: "rgb(255, 205, 86,0.5)",
        borderWidth: 2,
      },
      {
        data: [6, 20, 52, 12, 11, 78, 21],
        label: "Failed",
        borderColor: "rgb(255, 99, 132)",
        backgroundColor: "rgb(255, 99, 132,0.5)",
        borderWidth: 2,
      },
    ],
  }; */
  const getSessionDetails = (
    event: ChartEvent,
    element: ActiveElement[],
  ): void => {
    // Log or use the index as needed
    if (element?.length > 0) {
      //const courseId = attendanceDataSet.idSet[element[0].index];
      //void router.push(`${pageUrl.sessionDetails}?course=${courseId}`);
    }
  };

  return (
    <Card>
      <CardHeader></CardHeader>
      <CardContent>
        <h1 className="w-full mx-auto mt-10 text-xl font-semibold capitalize ">
          Course Wise User Statistics
        </h1>
        <Bar
          data={attendanceData as CourseAttendancesGraphType}
          width={400}
          height={300}
          options={{
            maintainAspectRatio: false,
            onClick: getSessionDetails,
          }}
        />
      </CardContent>
    </Card>
  );
}
