import React from "react";
import type { ErrorCatch, LogUserActivityRequest, ToastType } from "@/types";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import useEnrollments from "@/hooks/useEnrollment";
import { ORG_KEY } from "@/lib/constants";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";

export default function DeleteUserEnrollment({
  onCancel,
  onSave,
  userId,
  selectCourse,
}: {
  onCancel: () => void;
  onSave: () => void;
  userId: string;
  selectCourse: string;
}): React.JSX.Element {
  const { t } = useTranslation();
  const { toast } = useToast() as ToastType;
  const { removeUser } = useEnrollments();
  const { updateUserActivity } = useLogUserActivity();

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  const handleRemoveClick = (): void => {
    void handleToastPublish();
    onCancel();
  };

  const handleToastPublish = async (): Promise<void> => {
    const params = {
      user_id: userId,
      course_id: selectCourse,
      org_id: localStorage.getItem(ORG_KEY) ?? "",
    };
    try {
      const result = await removeUser(params);
      if (result.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t(" successMessages.userRemovedTitle"),
          description: t("successMessages.userRemovedTitleMsg"),
        });
        onSave();
        const params = {
          activity_type: "Enrolment",
          screen_name: "Enrollments",
          action_details: "User unenrolled successfully",
          target_id: selectCourse as string,
          log_result: "SUCCESS",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
        onCancel();
      } else {
        const params = {
          activity_type: "Enrolment",
          screen_name: "Enrollments",
          action_details: "Failed to unenroll user",
          target_id: selectCourse as string,
          log_result: "ERROR",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
    }
    // onCancel();
  };

  const handleCancel = (): void => {
    onCancel();
  };

  return (
    <>
      <div className="mb-2 mr-4">
        <p className="ml-0 ">{String(t("enrollment.deletePrompt"))}</p>
      </div>
      <div className="flex topic-icons pr-4">
        <div className="flex items-center justify-center float-right">
          <Button
            type="button"
            variant="outline"
            className="primary"
            onClick={handleCancel}
          >
            {String(t("buttons.cancel"))}
          </Button>
          &nbsp;
          <Button type="submit" className="primary" onClick={handleRemoveClick}>
            {String(t("buttons.remove"))}
          </Button>
        </div>
      </div>
    </>
  );
}
