"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type {
  SessionViewsTableType,
  SessionViewsRowDefinition,
} from "@/types";
import moment from "moment";

export const getColumns = (
  t: (key: string) => string,
): ColumnDef<SessionViewsTableType>[] => [
  {
    accessorKey: "first_name",
    header:t("dashboard.youTubeVideos.name"),
    cell: ({ row }: SessionViewsRowDefinition): React.JSX.Element => {
      const firstName = row.original.first_name ?? " ";
      const lastName = row.original.lastname ?? " ";
      return <div className="text-align">{`${firstName} ${lastName}`}</div>;
    },
  },
  {
    accessorKey: "email",
    header: t("dashboard.newRegistrations.email"),
    cell: ({ row }: SessionViewsRowDefinition): React.JSX.Element => (
      <div>{row.original.email ?? ""}</div>
    ),
  },
  {
    accessorKey: "quiz_name",
    header: t("dashboard.youTubeVideos.attendedExam"),
    cell: ({ row }: SessionViewsRowDefinition): React.JSX.Element => (
      <div>{row.original.quiz_name}</div>
    ),
  },
  /* {
    accessorKey: "session_start_time",
    header: "Started At",
    cell: ({ row }: SessionViewsRowDefinition): React.JSX.Element => {
      const formattedDate =
        row.original !== null
          ? moment
              .utc(row.original.session_start_time)
              .local()
              .format("DD-MMM-YYYY hh:mm a")
          : "";

      return <div>{formattedDate}</div>;
    },
  }, */

  {
    accessorKey: "session_end_time",
    header: t("dashboard.youTubeVideos.sessionEnd"),
    cell: ({ row }: SessionViewsRowDefinition): React.JSX.Element => {
      const formattedDate =
        row.original !== null
          ? moment
              .utc(row.original.session_end_time)
              .local()
              .format("DD-MMM-YYYY hh:mm a")
          : "";

      return <div>{formattedDate}</div>;
    },
  },
  {
    accessorKey: "result",
    header: t("dashboard.youTubeVideos.result"),
    cell: ({ row }: SessionViewsRowDefinition): React.JSX.Element => (
      <div>{row.original.result}</div>
    ),
  },
];
