const examDetails = [
  {
    name: "Who developed Python Programming Language?",
    answers: [
      {
        slot: 1,
        answer: "<PERSON><PERSON>",
        org_id: "19579370-a028-484d-8f35-8af2023068dd",
        fraction: 0,
        answer_id: "e9be5ba7-27b5-4671-bb3e-a730f86a0f91",
        ans_format: "",
        created_at: "2023-08-04T05:11:43.915689+00:00",
        updated_at: "2023-08-04T05:11:43.915689+00:00",
        answer_type: "PLAIN_TEXT",
        question_id: "3fc072c5-ca3b-42ef-8e2d-dd7f91c4a5a1",
      },
      {
        slot: 2,
        answer: " <PERSON><PERSON>",
        org_id: "19579370-a028-484d-8f35-8af2023068dd",
        fraction: 0,
        answer_id: "2a2c5a44-e163-459d-93de-acc41e3b4ff8",
        ans_format: "",
        created_at: "2023-08-04T05:11:43.915689+00:00",
        updated_at: "2023-08-04T05:11:43.915689+00:00",
        answer_type: "PLAIN_TEXT",
        question_id: "3fc072c5-ca3b-42ef-8e2d-dd7f91c4a5a1",
      },
      {
        slot: 3,
        answer: "Guido van Rossum",
        org_id: "19579370-a028-484d-8f35-8af2023068dd",
        fraction: 1,
        answer_id: "1efe1bee-62e6-4206-821b-0773e98d0828",
        ans_format: "",
        created_at: "2023-08-04T05:11:43.915689+00:00",
        updated_at: "2023-08-04T05:11:43.915689+00:00",
        answer_type: "PLAIN_TEXT",
        question_id: "3fc072c5-ca3b-42ef-8e2d-dd7f91c4a5a1",
      },
      {
        slot: 4,
        answer: "Niene Stom",
        org_id: "19579370-a028-484d-8f35-8af2023068dd",
        fraction: 0,
        answer_id: "aab1d14a-3d5c-4615-ab0a-f814e81c2730",
        ans_format: "",
        created_at: "2023-08-04T05:11:43.915689+00:00",
        updated_at: "2023-08-04T05:11:43.915689+00:00",
        answer_type: "PLAIN_TEXT",
        question_id: "3fc072c5-ca3b-42ef-8e2d-dd7f91c4a5a1",
      },
      {
        slot: 5,
        answer: "None of the Above",
        org_id: "19579370-a028-484d-8f35-8af2023068dd",
        fraction: 0,
        answer_id: "3fc7c783-fb54-4808-b456-629f5ce14999",
        ans_format: "",
        created_at: "2023-08-04T05:11:43.915689+00:00",
        updated_at: "2023-08-04T05:11:43.915689+00:00",
        answer_type: "PLAIN_TEXT",
        question_id: "3fc072c5-ca3b-42ef-8e2d-dd7f91c4a5a1",
      },
    ],
    penalty: 0.5,
    quiz_id: "0edaf35d-288d-4a83-b9d3-97c79885554f",
    question_id: "3fc072c5-ca3b-42ef-8e2d-dd7f91c4a5a1",
    default_mark: 10,
    question_slot: 1,
    question_text: "Who developed Python Programming Language?",
    question_type: "PLAIN_TEXT",
  },
  {
    name: "Which type of Programming does Python support?",
    answers: [
      {
        slot: 1,
        answer: "object-oriented programming",
        org_id: "19579370-a028-484d-8f35-8af2023068dd",
        fraction: 0,
        answer_id: "8feb0e26-bf3e-4a29-8062-caf166872dea",
        ans_format: "",
        created_at: "2023-08-04T05:12:47.599327+00:00",
        updated_at: "2023-08-04T05:12:47.599327+00:00",
        answer_type: "PLAIN_TEXT",
        question_id: "bf4c25e2-811b-49e1-add4-a3151e0e90d4",
      },
      {
        slot: 2,
        answer: " structured programming",
        org_id: "19579370-a028-484d-8f35-8af2023068dd",
        fraction: 0,
        answer_id: "ae062964-1836-4aad-b59c-60f7418c9260",
        ans_format: "",
        created_at: "2023-08-04T05:12:47.599327+00:00",
        updated_at: "2023-08-04T05:12:47.599327+00:00",
        answer_type: "PLAIN_TEXT",
        question_id: "bf4c25e2-811b-49e1-add4-a3151e0e90d4",
      },
      {
        slot: 3,
        answer: "functional programming",
        org_id: "19579370-a028-484d-8f35-8af2023068dd",
        fraction: 0,
        answer_id: "2e2d2bc3-374b-4fe4-a599-d31fd5ab073f",
        ans_format: "",
        created_at: "2023-08-04T05:12:47.599327+00:00",
        updated_at: "2023-08-04T05:12:47.599327+00:00",
        answer_type: "PLAIN_TEXT",
        question_id: "bf4c25e2-811b-49e1-add4-a3151e0e90d4",
      },
      {
        slot: 4,
        answer: "all of the mentioned",
        org_id: "19579370-a028-484d-8f35-8af2023068dd",
        fraction: 1,
        answer_id: "285c75e6-bc29-4132-bf48-ddbfbff340da",
        ans_format: "",
        created_at: "2023-08-04T05:12:47.599327+00:00",
        updated_at: "2023-08-04T05:12:47.599327+00:00",
        answer_type: "PLAIN_TEXT",
        question_id: "bf4c25e2-811b-49e1-add4-a3151e0e90d4",
      },
      {
        slot: 5,
        answer: "None of the Above",
        org_id: "19579370-a028-484d-8f35-8af2023068dd",
        fraction: 0,
        answer_id: "25eee74f-ee26-4b20-a50a-734c46804d0d",
        ans_format: "",
        created_at: "2023-08-04T05:12:47.599327+00:00",
        updated_at: "2023-08-04T05:12:47.599327+00:00",
        answer_type: "PLAIN_TEXT",
        question_id: "bf4c25e2-811b-49e1-add4-a3151e0e90d4",
      },
    ],
    penalty: 0.5,
    quiz_id: "0edaf35d-288d-4a83-b9d3-97c79885554f",
    question_id: "bf4c25e2-811b-49e1-add4-a3151e0e90d4",
    default_mark: 10,
    question_slot: 2,
    question_text: "Which type of Programming does Python support?",
    question_type: "PLAIN_TEXT",
  },
  {
    name: "Is Python case sensitive when dealing with identifiers?",
    answers: [
      {
        slot: 1,
        answer: "No",
        org_id: "19579370-a028-484d-8f35-8af2023068dd",
        fraction: 0,
        answer_id: "57830767-0585-4eb5-a904-ef011a9931e1",
        ans_format: "",
        created_at: "2023-08-04T05:15:21.129911+00:00",
        updated_at: "2023-08-04T05:15:21.129911+00:00",
        answer_type: "PLAIN_TEXT",
        question_id: "8c081fea-5d97-4f49-8b69-a5c91dd1cf27",
      },
      {
        slot: 2,
        answer: "Yes",
        org_id: "19579370-a028-484d-8f35-8af2023068dd",
        fraction: 1,
        answer_id: "fafdf714-c2cc-4049-8da2-b325f4768aa5",
        ans_format: "",
        created_at: "2023-08-04T05:15:21.129911+00:00",
        updated_at: "2023-08-04T05:15:21.129911+00:00",
        answer_type: "PLAIN_TEXT",
        question_id: "8c081fea-5d97-4f49-8b69-a5c91dd1cf27",
      },
      {
        slot: 3,
        answer: " machine dependent",
        org_id: "19579370-a028-484d-8f35-8af2023068dd",
        fraction: 0,
        answer_id: "f680d5d2-f500-4638-84e4-a9fa1d979250",
        ans_format: "",
        created_at: "2023-08-04T05:15:21.129911+00:00",
        updated_at: "2023-08-04T05:15:21.129911+00:00",
        answer_type: "PLAIN_TEXT",
        question_id: "8c081fea-5d97-4f49-8b69-a5c91dd1cf27",
      },
      {
        slot: 4,
        answer: "none of the mentioned",
        org_id: "19579370-a028-484d-8f35-8af2023068dd",
        fraction: 0,
        answer_id: "a0feecf1-843e-436f-9d07-bae8235bc1b8",
        ans_format: "",
        created_at: "2023-08-04T05:15:21.129911+00:00",
        updated_at: "2023-08-04T05:15:21.129911+00:00",
        answer_type: "PLAIN_TEXT",
        question_id: "8c081fea-5d97-4f49-8b69-a5c91dd1cf27",
      },
      {
        slot: 5,
        answer: "None of the Above",
        org_id: "19579370-a028-484d-8f35-8af2023068dd",
        fraction: 0,
        answer_id: "74c44bf1-4737-4d28-a62e-61ca9c51a238",
        ans_format: "",
        created_at: "2023-08-04T05:15:21.129911+00:00",
        updated_at: "2023-08-04T05:15:21.129911+00:00",
        answer_type: "PLAIN_TEXT",
        question_id: "8c081fea-5d97-4f49-8b69-a5c91dd1cf27",
      },
    ],
    penalty: 0.5,
    quiz_id: "0edaf35d-288d-4a83-b9d3-97c79885554f",
    question_id: "8c081fea-5d97-4f49-8b69-a5c91dd1cf27",
    default_mark: 10,
    question_slot: 3,
    question_text: "Is Python case sensitive when dealing with identifiers?",
    question_type: "PLAIN_TEXT",
  },
  {
    name: "Which of the following is the correct extension of the Python file?",
    answers: [
      {
        slot: 1,
        answer: ".python",
        org_id: "19579370-a028-484d-8f35-8af2023068dd",
        fraction: 0,
        answer_id: "fd965c9e-00e6-43f3-85a0-3caebce98aa1",
        ans_format: "",
        created_at: "2023-08-04T05:16:10.949195+00:00",
        updated_at: "2023-08-04T05:16:10.949195+00:00",
        answer_type: "PLAIN_TEXT",
        question_id: "8fa4ce1b-b312-43c6-a48c-7050eb369ede",
      },
      {
        slot: 2,
        answer: ".pl",
        org_id: "19579370-a028-484d-8f35-8af2023068dd",
        fraction: 0,
        answer_id: "395a54ed-7840-4015-b00b-40fbe44407a4",
        ans_format: "",
        created_at: "2023-08-04T05:16:10.949195+00:00",
        updated_at: "2023-08-04T05:16:10.949195+00:00",
        answer_type: "PLAIN_TEXT",
        question_id: "8fa4ce1b-b312-43c6-a48c-7050eb369ede",
      },
      {
        slot: 3,
        answer: ".py",
        org_id: "19579370-a028-484d-8f35-8af2023068dd",
        fraction: 1,
        answer_id: "ed82828b-4c2e-4506-af8b-faf5eabdde00",
        ans_format: "",
        created_at: "2023-08-04T05:16:10.949195+00:00",
        updated_at: "2023-08-04T05:16:10.949195+00:00",
        answer_type: "PLAIN_TEXT",
        question_id: "8fa4ce1b-b312-43c6-a48c-7050eb369ede",
      },
      {
        slot: 4,
        answer: ".p",
        org_id: "19579370-a028-484d-8f35-8af2023068dd",
        fraction: 0,
        answer_id: "fbdd92f9-046e-42ee-a007-8dd59f860b62",
        ans_format: "",
        created_at: "2023-08-04T05:16:10.949195+00:00",
        updated_at: "2023-08-04T05:16:10.949195+00:00",
        answer_type: "PLAIN_TEXT",
        question_id: "8fa4ce1b-b312-43c6-a48c-7050eb369ede",
      },
      {
        slot: 5,
        answer: "None of the Above",
        org_id: "19579370-a028-484d-8f35-8af2023068dd",
        fraction: 0,
        answer_id: "b6da3575-e59d-4a54-b30e-e9c612bf3a0d",
        ans_format: "",
        created_at: "2023-08-04T05:16:10.949195+00:00",
        updated_at: "2023-08-04T05:16:10.949195+00:00",
        answer_type: "PLAIN_TEXT",
        question_id: "8fa4ce1b-b312-43c6-a48c-7050eb369ede",
      },
    ],
    penalty: 0.5,
    quiz_id: "0edaf35d-288d-4a83-b9d3-97c79885554f",
    question_id: "8fa4ce1b-b312-43c6-a48c-7050eb369ede",
    default_mark: 10,
    question_slot: 4,
    question_text:
      "Which of the following is the correct extension of the Python file?",
    question_type: "PLAIN_TEXT",
  },
  {
    name: "All keywords in Python are in _________",
    answers: [
      {
        slot: 1,
        answer: "Capitalized",
        org_id: "19579370-a028-484d-8f35-8af2023068dd",
        fraction: 0,
        answer_id: "85d46381-1535-4278-bdc6-b58e61d3c09e",
        ans_format: "",
        created_at: "2023-08-04T05:17:21.695397+00:00",
        updated_at: "2023-08-04T05:17:21.695397+00:00",
        answer_type: "PLAIN_TEXT",
        question_id: "057f488e-c1fd-4026-95b9-9260e1927688",
      },
      {
        slot: 2,
        answer: "lower case",
        org_id: "19579370-a028-484d-8f35-8af2023068dd",
        fraction: 0,
        answer_id: "e6ed2e58-aa74-45e8-bbf7-fccd3470cb7d",
        ans_format: "",
        created_at: "2023-08-04T05:17:21.695397+00:00",
        updated_at: "2023-08-04T05:17:21.695397+00:00",
        answer_type: "PLAIN_TEXT",
        question_id: "057f488e-c1fd-4026-95b9-9260e1927688",
      },
      {
        slot: 3,
        answer: " UPPER CASE",
        org_id: "19579370-a028-484d-8f35-8af2023068dd",
        fraction: 0,
        answer_id: "2a53d618-feb9-4b62-a615-ebfa8082df49",
        ans_format: "",
        created_at: "2023-08-04T05:17:21.695397+00:00",
        updated_at: "2023-08-04T05:17:21.695397+00:00",
        answer_type: "PLAIN_TEXT",
        question_id: "057f488e-c1fd-4026-95b9-9260e1927688",
      },
      {
        slot: 4,
        answer: " None of the mentioned",
        org_id: "19579370-a028-484d-8f35-8af2023068dd",
        fraction: 1,
        answer_id: "ab8e7313-cf1e-4820-84dd-7b02c6a93156",
        ans_format: "",
        created_at: "2023-08-04T05:17:21.695397+00:00",
        updated_at: "2023-08-04T05:17:21.695397+00:00",
        answer_type: "PLAIN_TEXT",
        question_id: "057f488e-c1fd-4026-95b9-9260e1927688",
      },
      {
        slot: 5,
        answer: "None of the Above",
        org_id: "19579370-a028-484d-8f35-8af2023068dd",
        fraction: 0,
        answer_id: "7a0aff05-9be8-4a48-9c18-05ea7c91fd72",
        ans_format: "",
        created_at: "2023-08-04T05:17:21.695397+00:00",
        updated_at: "2023-08-04T05:17:21.695397+00:00",
        answer_type: "PLAIN_TEXT",
        question_id: "057f488e-c1fd-4026-95b9-9260e1927688",
      },
    ],
    penalty: 0.5,
    quiz_id: "0edaf35d-288d-4a83-b9d3-97c79885554f",
    question_id: "057f488e-c1fd-4026-95b9-9260e1927688",
    default_mark: 10,
    question_slot: 5,
    question_text: "All keywords in Python are in _________",
    question_type: "PLAIN_TEXT",
  },
  {
    name: "What will be the value of the following Python expression?",
    answers: [
      {
        slot: 1,
        answer: "7",
        org_id: "19579370-a028-484d-8f35-8af2023068dd",
        fraction: 1,
        answer_id: "2321c534-5f03-446b-9ed9-0090c541b153",
        ans_format: "",
        created_at: "2023-08-04T05:18:09.703798+00:00",
        updated_at: "2023-08-04T05:18:09.703798+00:00",
        answer_type: "PLAIN_TEXT",
        question_id: "f79ae371-5db3-43fb-b9ef-c911bfac4f3c",
      },
      {
        slot: 2,
        answer: "2",
        org_id: "19579370-a028-484d-8f35-8af2023068dd",
        fraction: 0,
        answer_id: "da01eb81-f8e5-4b7d-956b-6d4ebe5db751",
        ans_format: "",
        created_at: "2023-08-04T05:18:09.703798+00:00",
        updated_at: "2023-08-04T05:18:09.703798+00:00",
        answer_type: "PLAIN_TEXT",
        question_id: "f79ae371-5db3-43fb-b9ef-c911bfac4f3c",
      },
      {
        slot: 3,
        answer: "4",
        org_id: "19579370-a028-484d-8f35-8af2023068dd",
        fraction: 0,
        answer_id: "7b67d3e8-4420-4143-92ea-77becaf51135",
        ans_format: "",
        created_at: "2023-08-04T05:18:09.703798+00:00",
        updated_at: "2023-08-04T05:18:09.703798+00:00",
        answer_type: "PLAIN_TEXT",
        question_id: "f79ae371-5db3-43fb-b9ef-c911bfac4f3c",
      },
      {
        slot: 4,
        answer: "1",
        org_id: "19579370-a028-484d-8f35-8af2023068dd",
        fraction: 0,
        answer_id: "854d47a8-68b1-4d74-909c-d401a63327b6",
        ans_format: "",
        created_at: "2023-08-04T05:18:09.703798+00:00",
        updated_at: "2023-08-04T05:18:09.703798+00:00",
        answer_type: "PLAIN_TEXT",
        question_id: "f79ae371-5db3-43fb-b9ef-c911bfac4f3c",
      },
      {
        slot: 5,
        answer: "None of the Above",
        org_id: "19579370-a028-484d-8f35-8af2023068dd",
        fraction: 0,
        answer_id: "8c1f72cd-5b4f-4da5-836b-2128ae414c27",
        ans_format: "",
        created_at: "2023-08-04T05:18:09.703798+00:00",
        updated_at: "2023-08-04T05:18:09.703798+00:00",
        answer_type: "PLAIN_TEXT",
        question_id: "f79ae371-5db3-43fb-b9ef-c911bfac4f3c",
      },
    ],
    penalty: 0.5,
    quiz_id: "0edaf35d-288d-4a83-b9d3-97c79885554f",
    question_id: "f79ae371-5db3-43fb-b9ef-c911bfac4f3c",
    default_mark: 10,
    question_slot: 6,
    question_text:
      '<p>What will be the value of the following Python expression?</p>\n<pre class="de1"><span class="nu0">4</span> + <span class="nu0">3</span> % <span class="nu0">5</span></pre>',
    question_type: "PLAIN_TEXT",
  },
];
export default examDetails;
