"use client";
import React, { useState } from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "./ui/button";
import { supabase } from "@/lib/client";
import { ORG_KEY } from "@/lib/constants";
import { useToast } from "@/components/ui/use-toast";
import { type ToastType } from "@/types";
import { useTranslation } from "react-i18next";
import { ERROR_MESSAGES } from "@/lib/messages";

export default function UploadPdfUrl({
  onCancel,
  onUploadComplete,
}: {
  onCancel: () => void;
  onUploadComplete: (url: string) => void;
}): JSX.Element {
  const { t } = useTranslation();
  const [isPdfSelected, setIsPdfSelected] = useState<boolean>(false);
  const { toast } = useToast() as ToastType;

  const handleCancel = (): void => {
    onCancel();
  };

  const handleFileUpload = async (
    e: React.ChangeEvent<HTMLInputElement>,
  ): Promise<void> => {
    const file = e.target.files?.[0];
    const org_id = localStorage.getItem(ORG_KEY);

    if (!file) return;
    const maxSizeInBytes = 1 * 1024 * 1024; // 1 MB
    if (file.size > maxSizeInBytes) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.exceedFileSize"),
      });
      return;
    }
    if (file.type === "application/pdf") {
      setIsPdfSelected(true);
      const reader = new FileReader();
      reader.readAsDataURL(file);
      const fileExt = file.name.split(".").pop();
      const fileName = `${Math.random()}.${fileExt}`;

      try {
        const result = await supabase.storage
          .from(org_id as string)
          .upload(`resources/${fileName}`, file, {
            cacheControl: "3600",
            upsert: true,
          });
        console.log("result", result);
      } catch (error) {
        console.error("Error uploading avatar: ", error);
      }
      const { data } = supabase.storage
        .from(org_id as string)
        .getPublicUrl(`resources/${fileName}`);
      onUploadComplete(data.publicUrl);
    }
  };
  return (
    <div>
      <div className="min-w-[370px]">
        <Label>
          {t("resourceLibrary.uploadPdfFile")}{" "}
          <span className="text-red-700">*</span>
        </Label>
        <Input
          type="file"
          autoComplete="off"
          accept=".pdf"
          onChange={(e) => {
            void handleFileUpload(e);
          }}
        />
      </div>
      <div className="w-full flex justify-end mt-4 gap-3">
        <Button
          className="w-full sm:w-auto bg-[#33363F]"
          onClick={handleCancel}
        >
          {t("buttons.close")}
        </Button>
        <Button
          className="bg-[#9FC089]"
          onClick={handleCancel}
          disabled={!isPdfSelected}
        >
          {t("buttons.submit")}
        </Button>
      </div>
    </div>
  );
}
