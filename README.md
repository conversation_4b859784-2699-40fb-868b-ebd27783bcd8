# SmartLearn Administration Dashboard

### Folder Structure

```
smartlearn-administration-dashboard/
|-- public/
|   |-- images/
|   |-- favicon.ico
|-- src/
|   |-- components/ui/
|   |   |-- Header.tsx
|   |   |-- Footer.tsx
|   |   |-- ...
|   |-- app/
|   |   |-- about
|   |   |   |-- page.tsx
|   |   |   |-- ...
|   |   |-- page.tsx
|   |   |-- layout.tsx
|   |   |-- ...
|   |-- styles/
|   |   |-- global.css
|   |   |-- theme.css
|   |   |-- ...
|   |-- lib/
|   |   |-- api.ts
|   |   |-- helpers.ts
|   |   |-- constants.ts
|   |   |-- utils.ts
|   |   |-- ...
|   |-- hooks/
|   |   |-- useHeader.ts
|   |   |-- useFooter.ts
|   |   |-- useLogin.ts
|   |   |-- ...
|   |-- __test__/
|   |   |-- useHeader.test.ts
|   |   |-- Footer.test.tsx
|   |   |-- api.test.ts
|   |   |-- ...
|-- .gitignore
|-- package.json
|-- components.json
|-- README.md
|-- next.config.js
|-- tailwind.config.ts
|-- .eslintrc.json
|-- ...

```

#### Environment Variables

```bash
NEXT_PUBLIC_SUPABASE_URL="XXXXX"
NEXT_PUBLIC_SUPABASE_ANON_KEY="XXXXX"
```

### Test and Deploy

Use the built-in continuous integration in GitLab.

- [ ] [Get started with GitLab CI/CD](https://docs.gitlab.com/ee/ci/quick_start/index.html)
- [ ] [Analyze your code for known vulnerabilities with Static Application Security Testing(SAST)](https://docs.gitlab.com/ee/user/application_security/sast/)
- [ ] [Deploy to Kubernetes, Amazon EC2, or Amazon ECS using Auto Deploy](https://docs.gitlab.com/ee/topics/autodevops/requirements.html)
- [ ] [Use pull-based deployments for improved Kubernetes management](https://docs.gitlab.com/ee/user/clusters/agent/)
- [ ] [Set up protected environments](https://docs.gitlab.com/ee/ci/environments/protected_environments.html)
