import React, { useState, useEffect } from "react";
import { Label } from "@/components/ui/label";
import { Combobox } from "@/components/ui/combobox";
import { useToast } from "@/components/ui/use-toast";
import type {
  ErrorCatch,
  GetPrivilegeListResponse,
  LogUserActivityRequest,
  ToastType,
  UsersDataType,
  UserUpdate,
} from "@/types";
import { API_RESPONSE_SUCCESS, ORG_KEY } from "@/lib/constants";
import useUsers from "@/hooks/useUsers";
import { Button } from "@/components/ui/button";
import usePrivileges from "@/hooks/usePrivileges";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";

export default function ChangeUserRole({
  userData,
  onCancel,
  usersList,
}: {
  userData: UsersDataType;
  onCancel: () => void;
  usersList: (value: boolean) => void;
}): React.JSX.Element {
  const { t } = useTranslation();
  const { toast } = useToast() as ToastType;
  const { getRoles, updateUser } = useUsers();
  const { getListOfPrivilege, updatePrivileges } = usePrivileges();
  const { updateUserActivity } = useLogUserActivity();

  const [roleData, setRoles] = useState<{ value: string; label: string }[]>([]);
  const [privilegeDatas, setPrivilageData] = useState<
    GetPrivilegeListResponse[]
  >([]);
  const [selectedRole, setSelectedRole] = useState<string>("");

  useEffect(() => {
    const fetchRolesAndUsers = async (): Promise<void> => {
      try {
        const org_id = localStorage.getItem(ORG_KEY) as string;
        const roles = await getRoles(org_id);
        const userRole = roles.filter(
          (role) => !userData.roles.includes(role.name),
        );
        if (roles !== null && roles.length > 0) {
          const filteredRoles = userRole.map((role) => ({
            value: role.id,
            label: role.display_name ?? role.name,
          }));
          setRoles(filteredRoles);
        } else {
          setRoles([]);
        }
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
      }
    };

    fetchRolesAndUsers().catch((error) => console.log(error));
  }, []);

  const handleRoleChange = (selectedOption: string): void => {
    setSelectedRole(selectedOption);
    fetchPrivilegeList(selectedOption).catch((error) => console.log(error));
  };

  const fetchPrivilegeList = async (selectedRoleId: string): Promise<void> => {
    try {
      const orgId = localStorage.getItem("orgId") as string;
      const roleId = selectedRoleId;

      const params = {
        org_id: orgId ?? "",
        role_id: roleId ?? "",
      };
      const privilegeList = await getListOfPrivilege(params);
      if (privilegeList !== null && privilegeList !== undefined) {
        const privilegeListData = privilegeList.privileges_data;
        setPrivilageData(privilegeListData as GetPrivilegeListResponse[]);
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
    }
  };

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  const handleSubmit = async (): Promise<void> => {
    const orgId = localStorage.getItem("orgId") as string;
    const requestBody: UserUpdate = {
      org_id: orgId ?? "",
      role_id: selectedRole ?? "",
      user_ids: [userData.id],
    };
    try {
      const result = await updateUser(requestBody);
      if (result.status === API_RESPONSE_SUCCESS) {
        const params = {
          org_id: orgId,
          user_role_id: selectedRole,
          role_privileges: privilegeDatas
            .filter((privilege) => privilege.is_part_of_role)
            .map((privilege) => ({ privilege_id: privilege.id })),
        };
        try {
          const result = await updatePrivileges(params);
          if (result.status === "success") {
            toast({
              variant: SUCCESS_MESSAGES.toast_variant_default,
              title: t("successMessages.toast_success_title"),
              description: t("successMessages.user_role_updated"),
            });
            const params = {
              activity_type: "SignUp",
              screen_name: "User",
              action_details: "User updated successfully",
              target_id: userData.id as string,
              log_result: "SUCCESS",
            };
            void updateLogUserActivity(params).catch((error) => {
              console.error(error);
            });
            usersList(true);
            onCancel();
          } else if (result.status === "error") {
            toast({
              variant: ERROR_MESSAGES.toast_variant_destructive,
              title: t("errorMessages.toast_error_title"),
              description: result.status,
            });
            const params = {
              activity_type: "SignUp",
              screen_name: "User",
              action_details: "Failed to update user",
              target_id: userData.id as string,
              log_result: "ERROR",
            };
            void updateLogUserActivity(params).catch((error) => {
              console.error(error);
            });
          }
        } catch (error) {
          const err = error as ErrorCatch;
          toast({
            variant: ERROR_MESSAGES.toast_variant_destructive,
            title: t("errorMessages.toast_error_title"),
            description: err?.message,
          });
          console.error("An unexpected error occurred:", error);
        }
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.toast_success_title"),
          description: t("successMessages.update_user_msg"),
        });
      } else {
        const params = {
          activity_type: "SignUp",
          screen_name: "User",
          action_details: "Failed to update user",
          target_id: userData.id as string,
          log_result: "ERROR",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
    }
  };

  const handleCancel = (): void => {
    onCancel();
  };

  return (
    <>
      <div>
        <Label>{t("usersList.changeUserRole.selectNewRole")}</Label>
        <Combobox data={roleData} onSelectChange={handleRoleChange} />
      </div>
      <div className="flex topic-icons pr-4">
        <div className="flex items-center justify-center float-right gap-3">
          <Button type="button" className="bg-[#33363F]" onClick={handleCancel}>
            {t("usersList.changeUserRole.cancel")}
          </Button>
          <Button
            type="submit"
            className="bg-[#9FC089]"
            onClick={() => {
              handleSubmit().catch((error) => console.log(error));
            }}
          >
            {t("usersList.changeUserRole.update")}
          </Button>
        </div>
      </div>
    </>
  );
}
