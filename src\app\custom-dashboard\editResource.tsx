import React, { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import type {
  ComboData,
  CourseDetailsRequest,
  CustomResource,
  ErrorCatch,
  moduleList,
  ResourceLibrary,
  ToastType,
} from "@/types";
import { Combobox } from "@/components/ui/combobox";
import useCourse from "@/hooks/useCourse";
import { useToast } from "@/components/ui/use-toast";
import useResourceLibrary from "@/hooks/useResourceLibrary";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { DEFAULT_FOLDER_ID, ORG_KEY } from "@/lib/constants";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import useAddExams from "@/hooks/useAddExams";
import { DataTable } from "@/components/ui/data-table/data-table";
import { columns } from "./columns";
import { useTranslation } from "react-i18next";
import { ERROR_MESSAGES } from "@/lib/messages";
interface EditResourcesProps {
  onCancel: () => void;
  setVisibleResources: React.Dispatch<React.SetStateAction<CustomResource[]>>;
  setResourceLibrary: React.Dispatch<React.SetStateAction<CustomResource[]>>;
  visibleResources: CustomResource[];
  editIndex: number | null;
  onSubmit: (resources: CustomResource[]) => Promise<void>;
}

export default function EditResources({
  onCancel,
  setVisibleResources,
  visibleResources,
  editIndex,
  onSubmit,
}: EditResourcesProps): React.JSX.Element {
  const { t } = useTranslation();
  const [comboResources, setComboResources] = useState<ComboData[]>([]);
  const [courseData, setCourseData] = useState<
    { value?: string; label?: string }[]
  >([]);
  const [defaultCourseLabel, setDefaultCourseLabel] = useState<string>("");
  const [comboData, setComboData] = useState<ComboData[]>([]);
  const [selResource, setSelResource] = useState("");
  const [sectionData, setSectionData] = React.useState<
    { value: string; label: string }[]
  >([]);
  const [resourceCollection, setResourceCollection] = useState<
    CustomResource[]
  >([]);
  const [resourceLabel, setResourceLabel] = useState<string>("File");
  const [selectedFilter, setSelectedFilter] = useState("folder_and_type");
  const [filterApplied, setFilterApplied] = useState(false);
  const [allResources, setAllResources] = useState<CustomResource[]>([]);
  const [selFolder, setSelFolder] = useState<string>(DEFAULT_FOLDER_ID);
  const { getCourseList, listFolderFromLibrary } = useCourse();
  const { getAllResoures, getResourceList } = useResourceLibrary();
  const { getSections } = useAddExams();
  const { toast } = useToast() as ToastType;

  useEffect(() => {
    const data = localStorage.getItem("moduleList") as string;
    const displayData = JSON.parse(data) as moduleList[];
    const datas: ComboData[] = displayData.map((item: moduleList) => ({
      value: item.id,
      label: item.display_name,
    }));
    setComboResources(datas as ComboData[]);
    setSelResource(datas[0]?.value as string);
    setResourceLabel(datas[0]?.label as string);
    getFoldersList().catch((error) => console.log(error));
  }, []);

  useEffect(() => {
    const fetchCourseData = async (): Promise<void> => {
      try {
        const courses = await getCourseList("");
        if (
          courses !== null &&
          courses !== undefined &&
          Object.keys(courses).length > 0
        ) {
          const filteredCourses = courses
            .filter((course) => course.course_id != null && course.short_name)
            .map((course) => ({
              value: course.course_id,
              defaultCourseLabel,
              label: course.short_name,
            }));
          setCourseData(filteredCourses);
          if (filteredCourses?.length > 0) {
            setDefaultCourseLabel(filteredCourses[0].label ?? "");
          }
        } else {
          setCourseData([]);
        }
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
      }
    };

    fetchCourseData().catch((error) => console.log(error));
  }, []);

  useEffect(() => {
    if (selFolder?.length > 0 && selResource?.length > 0) {
      getResourceLibrary().catch((error) => console.log(error));
    }
  }, [selFolder, selResource]);

  const getResourceLibrary = async (): Promise<void> => {
    try {
      const requestBody = {
        org_id: localStorage.getItem("orgId") ?? "",
        module_id: selResource,
        linked: "0",
        limit_val: 1000,
        offset_val: 0,
        folder_id: DEFAULT_FOLDER_ID ?? selFolder,
      };

      const resource: ResourceLibrary = await getResourceList(requestBody);
      if (resource.resource_list != null) {
        resource.resource_list.map((item) => {
          item.file_type = selResource;
        });
        resource.resource_list.sort((item, data) => {
          return item.name.localeCompare(data.name);
        });
        const data = resource.resource_list.map((item) => ({
          resource_id: item.id,
          resource_name: item.name,
          thumbnail_url: item.thumbnail_url ?? "",
          resource_type: item.file_type,
        }));
        setAllResources(data as CustomResource[]);
        setResourceCollection(data as CustomResource[]);
        setFilterApplied(true);
      } else {
        setResourceCollection([]);
        setAllResources([]);
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
      console.error("Error fetching data:");
    }
  };

  const sectionDetails = async (courseId: string): Promise<void> => {
    try {
      if (courseId !== null && courseId !== "") {
        const orgId = localStorage.getItem(ORG_KEY);
        const reqParams: CourseDetailsRequest = {
          course_id: courseId,
          org_id: orgId ?? "",
        };
        const data = await getSections(reqParams);

        if (data.length > 0) {
          const filterSections = [
            { value: "all", label: "All" },
            ...data[0].sections.map((section) => ({
              value: section.section_id,
              label: section.name,
            })),
          ];
          setSectionData(filterSections);
        } else {
          setSectionData([{ value: "all", label: "All" }]);
        }
      }
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  };

  const getFoldersList = async (): Promise<void> => {
    try {
      const orgId = localStorage.getItem("orgId");
      const folderList = await listFolderFromLibrary(orgId as string);
      const comboData: ComboData[] = folderList
        ?.map((cat) => ({
          value: cat.folder_id,
          label: cat.folder_name,
        }))
        .sort((a, b) => a.label.localeCompare(b.label));
      setComboData(comboData);
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  };

  const handleResourceChange = (resType: string): void => {
    const filteredResources = allResources.filter(
      (res) => res.resource_type === resType,
    );

    setResourceCollection(filteredResources);
    if (resType === "") return;
    const labelName = findLabelByValue(resType);

    setResourceLabel(labelName);
    setSelResource(resType);
  };

  const findLabelByValue = (value: string): string => {
    if (value === "") return "";
    const resourceTypes = ["File", "URL", "Quiz", "Page"];
    if (resourceTypes.includes(value)) {
      return value;
    }
    const resource = comboResources.find((item) => item.value === value)?.label;
    return resource as string;
  };

  const formatResourceTypeLabel = (type: string): string => {
    const normalizedType = type.trim().toLowerCase();
    if (type === "") return "";
    const typeMap: Record<string, string> = {
      File: "file",
      URL: "url",
      Quiz: "quiz",
      Page: "page",
    };
    if (normalizedType === "") {
      return "";
    }
    return typeMap[normalizedType] ?? normalizedType;
  };

  const handleUpdateClick = async (): Promise<void> => {
    const currentVisibleResources = visibleResources ?? [];
    const selectedResources = resourceCollection
      .filter((resource) => resource.isResourceAdded)
      .map((resource) => ({
        ...resource,
        resource_type: formatResourceTypeLabel(
          findLabelByValue(resource.resource_type),
        ),
      }));

    // Check for duplicates
    const duplicate = selectedResources.find((newRes) =>
      currentVisibleResources.some(
        (existingRes) => existingRes.resource_id === newRes.resource_id,
      ),
    );

    if (duplicate) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.resourceAlreadyAdded"),
      });
      return;
    }

    console.log("selectedResources", selectedResources);

    let newVisibleResources = [...currentVisibleResources];
    if (editIndex !== null) {
      // Update existing resource at the current index
      newVisibleResources[editIndex] = {
        ...selectedResources[0],
        resource_order: editIndex + 1, // Pass current index as resource_order
      };
      if (selectedResources.length > 1) {
        // Add additional resources at the end with proper ordering
        const additionalResources = selectedResources
          .slice(1)
          .map((resource, index) => ({
            ...resource,
            resource_order: newVisibleResources.length + index + 1,
          }));
        newVisibleResources = [...newVisibleResources, ...additionalResources];
      }
    } else {
      // Add new resources at the end with proper ordering
      const newResources = selectedResources.map((resource, index) => ({
        ...resource,
        resource_order: currentVisibleResources.length + index + 1, // Pass index as resource_order for new resources
      }));
      newVisibleResources = [...newVisibleResources, ...newResources];
    }
    setVisibleResources(newVisibleResources);
    await onSubmit(newVisibleResources);
    onCancel();
  };

  const handleCourseChange = (selectedOption: string): void => {
    setFilterApplied(true);
    sectionDetails(selectedOption).catch((error) => console.log(error));
    fetchResourceData(selectedOption).catch((error) => console.log(error));
  };

  const fetchResourceData = async (courseId: string): Promise<void> => {
    try {
      const requestBody = {
        org_id: localStorage.getItem("orgId") ?? "",
        course_id: courseId,
        user_id: null,
      };
      const response = await getAllResoures(requestBody);
      if (response.result.length > 0) {
        const resources = response.result.map((item) => ({
          resource_id: item.resource_id,
          resource_name: item.resource_name,
          thumbnail_url: item.thumbnail_url ?? "",
          resource_type: item.resource_type,
          file_extension: item.file_extension,
          section_name: item.section_name,
        }));
        setResourceCollection(resources);
        setAllResources(resources);
      } else {
        setResourceCollection([]);
        setAllResources([]);
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
      console.error("Error fetching data:");
    }
  };

  const handleSectionChange = (selectedOption: string): void => {
    if (selectedOption === "all") {
      setResourceCollection(allResources);
      return;
    }
    const sectionName = sectionData.find(
      (item) => item.value === selectedOption,
    )?.label;
    const filteredResources = allResources.filter(
      (res) => res.section_name === sectionName,
    );
    setResourceCollection(filteredResources);
  };

  const getAddedItem = (dataIndex: string, isSelect: boolean): void => {
    const index = parseInt(dataIndex);
    if (isSelect === true) {
      resourceCollection[index].isResourceAdded = true;
    } else {
      resourceCollection[index].isResourceAdded = false;
    }
  };

  const handleFolderChange = (value: string): void => {
    setFilterApplied(true);
    setSelFolder(value);
    getResourceLibrary().catch((error) => console.log(error));
  };

  return (
    <>
      <div>
        <Label className="block text-base font-medium mb-2">
          {String(t("customDashboard.resourceEdit.selectFilter"))}
        </Label>
        <RadioGroup
          value={selectedFilter}
          onValueChange={(val) => {
            console.log("val", val);

            setSelectedFilter(val);
            setResourceCollection([]);
            if (val === "folder_and_type") {
              setSelFolder(DEFAULT_FOLDER_ID);
              getResourceLibrary().catch((error) => console.log(error));
            } else if (val === "course_and_section") {
              setAllResources([]);
              setResourceCollection([]);
            }
            setFilterApplied(true);
          }}
          className="space-y-6"
        >
          {/* Filter by Folder & Resource Type */}
          <div className="flex items-start space-x-4">
            <RadioGroupItem value="folder_and_type" id="r1" className="mt-9" />
            <div className="flex flex-col gap-y-2">
              <Label
                htmlFor="r1"
                className="text-base text-gray-700 font-medium"
              >
                {String(
                  t(
                    "customDashboard.resourceEdit.filterByFolderAndResourceType",
                  ),
                )}
              </Label>
              {selectedFilter === "folder_and_type" && (
                <div className="flex gap-x-6 flex-wrap">
                  <div className="md:min-w-[300px]">
                    <Label className="block text-sm font-medium text-gray-700 mb-1">
                      {String(t("customDashboard.resourceEdit.selectFolder"))}
                    </Label>
                    <Select
                      onValueChange={handleFolderChange}
                      defaultValue={DEFAULT_FOLDER_ID}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value={DEFAULT_FOLDER_ID}>All</SelectItem>
                        {comboData.map((item) => (
                          <SelectItem key={item.value} value={item.value ?? ""}>
                            {item.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="md:min-w-[300px]">
                    <Label className="block text-sm font-medium text-gray-700 mb-1">
                      {String(
                        t("customDashboard.resourceEdit.selectResourceType"),
                      )}
                    </Label>
                    <Combobox
                      data={comboResources}
                      onSelectChange={handleResourceChange}
                      placeHolder={String(
                        t("customDashboard.resourceEdit.selectResourceType"),
                      )}
                      defaultLabel={resourceLabel}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Filter by Course & Section */}
          <div className="flex items-start space-x-4">
            <RadioGroupItem
              value="course_and_section"
              id="r2"
              className="mt-9"
            />
            <div className="flex flex-col gap-y-2">
              <Label
                htmlFor="r2"
                className="text-base text-gray-700 font-medium"
              >
                {String(
                  t("customDashboard.resourceEdit.filterByCourseAndSection"),
                )}
              </Label>
              {selectedFilter === "course_and_section" && (
                <div className="flex gap-x-6 flex-wrap">
                  <div className="md:min-w-[300px]">
                    <Label className="block text-sm font-medium text-gray-700 mb-1">
                      {String(t("customDashboard.resourceEdit.selectCourse"))}
                    </Label>
                    <Combobox
                      data={courseData}
                      defaultLabel={"All"} //defaultCourseLabel
                      onSelectChange={handleCourseChange}
                      placeHolder={String(
                        t("customDashboard.resourceEdit.selectCourse"),
                      )}
                    />
                  </div>
                  <div className="md:min-w-[300px]">
                    <Label className="block text-sm font-medium text-gray-700 mb-1">
                      {String(t("customDashboard.resourceEdit.selectSection"))}
                    </Label>
                    <Combobox
                      data={sectionData}
                      onSelectChange={handleSectionChange}
                      defaultLabel={"All"}
                      placeHolder={String(
                        t("customDashboard.resourceEdit.selectSection"),
                      )}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        </RadioGroup>
      </div>
      <div>
        {filterApplied === true && (
          <DataTable
            columns={columns(selectedFilter)}
            data={resourceCollection}
            isSelectedColumn={"isResourceAdded"}
            FilterLabel={String(
              t("customDashboard.resourceEdit.selectSection"),
            )}
            FilterBy={"resource_name"}
            actions={[]}
            onSetCheckedStatus={(index: string, checkedStatus: boolean) => {
              getAddedItem(index as string, checkedStatus as boolean);
            }}
          />
        )}
      </div>

      <div className="flex items-center justify-end float-right">
        <Button type="button" className="bg-[#33363F]" onClick={onCancel}>
          {String(t("buttons.cancel"))}
        </Button>
        &nbsp;
        <Button
          type="submit"
          className="bg-[#9FC089]"
          disabled={!filterApplied}
          onClick={() => {
            handleUpdateClick().catch((error) => {
              console.error("Error submitting resources:", error);
            });
          }}
        >
          {String(t("buttons.update"))}
        </Button>
      </div>
    </>
  );
}
