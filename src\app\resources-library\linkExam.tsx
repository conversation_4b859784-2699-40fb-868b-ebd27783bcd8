"use client";
import React, { useEffect, useState } from "react";
import { Spinner } from "@/components/ui/progressiveLoader";
import { useToast } from "@/components/ui/use-toast";
import type {
  ToastType,
  ResourceList,
  CourseListResponse,
  LogUserActivityRequest,
  ErrorCatch,
  FolderData,
} from "@/types";
import { ArrowUpDown, ChevronLeft, ChevronRight } from "lucide-react";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import { Button } from "@/components/ui/button";
import useExams from "@/hooks/useExams";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import useAddExams from "@/hooks/useAddExams";
import { DEFAULT_FOLDER_ID } from "@/lib/constants";
import moment from "moment";
import { Modal } from "@/components/ui/modal";
import SelectFolder from "./selectFolder";
import { useTranslation } from "react-i18next";

export default function LinkExam({
  onCancel,
  onSave,
  examData,
}: {
  onCancel: () => void;
  onSave: () => void;
  examData: ResourceList;
}): React.JSX.Element {
  const { t } = useTranslation();
  const { getCourseList } = useExams();
  const { addQuiz } = useAddExams();
  const { toast } = useToast() as ToastType;
  const { updateUserActivity } = useLogUserActivity();

  const [isLoading, setIsLoading] = useState(true);
  const [courseData, setCourseData] = useState<CourseListResponse[]>([]);
  const [selected, setSelected] = useState<CourseListResponse | null>(null);
  const [sortKey, setSortKey] = useState<keyof CourseListResponse | null>(null);
  const [sortAsc, setSortAsc] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedSections, setSelectedSections] = useState<
    Record<string, string | undefined>
  >({});
  const [openFolderPrompt, setOpenFolderPrompt] = useState<boolean>(false);
  const [selectedFolderData, setSelectedFolderData] = useState<FolderData[]>(
    [],
  );
  const [selectedFolderId, setSelectedFolderId] = useState<string | null>(null);
  const itemsPerPage = 10;

  useEffect(() => {
    getCourseListForExamMap();
  }, []);

  const getCourseListForExamMap = (): void => {
    const fetchCourseData = async (): Promise<void> => {
      setIsLoading(true);
      const orgId = localStorage.getItem("orgId");
      const params = {
        org_id: orgId ?? "",
        exam_id: examData.id,
      };
      try {
        const courses = await getCourseList(params);
        setIsLoading(false);
        if (courses !== null && courses !== undefined) {
          setCourseData(courses);
        }
      } catch (error) {
        setIsLoading(false);
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
      }
    };
    fetchCourseData().catch((error) => console.log(error));
  };

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  const toggleSort = (key: keyof CourseListResponse): void => {
    if (sortKey === key) {
      setSortAsc(!sortAsc);
    } else {
      setSortKey(key);
      setSortAsc(true);
    }
  };

  const filteredData = courseData?.filter((item) =>
    item.short_name?.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  const sortedData = [...filteredData].sort((a, b) => {
    if (sortKey === null) return 0;
    const valA = a[sortKey];
    const valB = b[sortKey];
    if ((valA ?? "") < (valB ?? "")) return sortAsc ? -1 : 1;
    if ((valA ?? "") > (valB ?? "")) return sortAsc ? 1 : -1;
    return 0;
  });

  const paginatedData = sortedData.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage,
  );

  const handleSelect = (item: CourseListResponse): void => {
    if (selected?.course_id === item.course_id) {
      setSelected(null);
    } else {
      setSelected(item);
    }
  };

  // const handleSelectAll = (checked: boolean): void => {
  //   if (checked) {
  //     const allSelected = courseData.map((item) => ({
  //       ...item,
  //     }));
  //     setSelected(allSelected);
  //   } else {
  //     setSelected([]);
  //   }
  // };

  const totalPages = Math.ceil(sortedData.length / itemsPerPage);

  const onSubmit = async (): Promise<void> => {
    if (selected === null) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.noCourseSelected"),
      });
      return;
    }
    const org_id = localStorage.getItem("orgId") as string;
    const selectedData = [
      {
        course_id: selected.course_id,
        section_id:
          selectedSections[selected.course_id] ??
          selected.sections[0]?.section_id,
      },
    ];
    console.log("selectedData", selectedData);

    const requestBody = {
      course_id: selected.course_id ?? DEFAULT_FOLDER_ID,
      section_id:
        selectedSections[selected.course_id] ??
        selected.sections[0]?.section_id,
      org_id: org_id.toLowerCase(),
      folder_id: selectedFolderId,
      quiz_data: {
        name: examData.name,
        description: examData.description ?? "",
        main_topic: examData.main_topic ?? "",
        num_of_questions: examData.num_of_questions ?? 0,
        total_mark: examData.total_mark ?? 0,
        pass_mark: examData.pass_mark ?? 0,
        start_time: moment(selected.valid_from).add(1, "days").format(),
        end_time: moment(selected.valid_to).subtract(1, "days").format(),
        duration: examData.duration ?? 0,
        allowed_attempts: examData.allowed_attempts ?? 0,
        quiz_type: examData.quiz_type ?? "",
        penalty_available: examData.penalty_available ?? false,
        penalty_type: examData.penalty_type ?? "",
        calculation_type: examData.calculation_type ?? "",
        is_equal_weightage: examData.is_equal_weightage ?? false,
        eq_weightage_marks: examData.eq_weightage_marks ?? 0,
        status: examData.status ?? "",
        minus_mark_applicable: examData.minus_mark_applicable ?? 0,
        no_wrong_answers: examData.no_wrong_answers ?? 0,
      },
    };
    try {
      const response = await addQuiz(requestBody);
      if (response.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.toast_success_title"),
          description: t("successMessages.exam_linked_to_course"),
        });
        onSave();
        const params = {
          activity_type: "Exam",
          screen_name: "Exam",
          action_details: "Exam export to courses successfully",
          target_id: examData.id as string,
          log_result: "SUCCESS",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
        onCancel();
      } else {
        const params = {
          activity_type: "Exam",
          screen_name: "Exam",
          action_details: "Failed to export exam to courses",
          target_id: examData.id as string,
          log_result: "ERROR",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      }
    } catch (error) {
      const err = error as ErrorCatch;

      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
      console.error("Error fetching data:", error);
    }
  };

  const dialogClose = (): void => {
    setOpenFolderPrompt(false);
  };

  return (
    <div className="rounded-lg ">
      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <Spinner />
        </div>
      ) : (
        <div className="mt-4">
          <div className="flex mb-3">
            <input
              type="text"
              placeholder={t("resourceLibrary.searchCourse")}
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                setCurrentPage(1); // Reset to page 1 when searching
              }}
              className="p-2 border border-gray-300 rounded-md w-64"
            />
          </div>
          <div className="overflow-x-auto rounded-lg border border-gray-200">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-[#5BCED6] text-white">
                <tr>
                  <th className="px-4 py-3 text-left" style={{ width: "50px" }}>
                    <div className="flex items-center">
                      {/* <input
                        type="checkbox"
                        onChange={(e) => handleSelectAll(e.target.checked)}
                        checked={
                          selected.length === courseData.length &&
                          courseData.length > 0
                        }
                        className="h-4 w-4"
                      /> */}
                    </div>
                  </th>
                  <th
                    className="px-4 py-3 text-left text-xs font-medium text-white tracking-wider cursor-pointer"
                    style={{ width: "200px" }}
                    onClick={() => toggleSort("short_name")}
                  >
                    <div className="flex items-center text-base">
                      {t("resourceLibrary.courseName")}
                      <ArrowUpDown className="ml-2 h-4 w-4" />
                    </div>
                  </th>
                  <th
                    className="px-4 py-3 text-left text-xs font-medium text-white tracking-wider"
                    style={{ width: "150px" }}
                  >
                    <div className="flex items-center text-base">
                      {t("resourceLibrary.selectSection")}
                    </div>
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {paginatedData.length > 0 ? (
                  paginatedData.map((item) => (
                    <tr key={item.course_id} className="">
                      <td
                        className="px-4 py-3 whitespace-nowrap"
                        style={{ width: "50px" }}
                      >
                        <input
                          type="radio"
                          checked={selected?.course_id === item.course_id}
                          onChange={() => handleSelect(item)}
                          className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                      </td>
                      <td
                        className="px-4 py-3 whitespace-nowrap text-sm font-normal text-gray-900"
                        style={{ width: "100px" }}
                      >
                        {item.short_name}
                      </td>
                      <td
                        className="px-4 py-3 whitespace-nowrap text-sm"
                        style={{ width: "150px" }}
                      >
                        <select
                          onChange={(e) => {
                            const selSection = item.sections.find(
                              (sec) => sec.section_id === e.target.value,
                            );
                            if (
                              selSection?.folderDtls &&
                              selSection?.folderDtls.length > 0
                            ) {
                              setOpenFolderPrompt(true);
                              setSelectedFolderData(
                                selSection.folderDtls.map((folder) => ({
                                  folder_id: folder.folder_id,
                                  folder_name: folder.folder_name,
                                })),
                              );
                            } else {
                              setOpenFolderPrompt(false);
                              setSelectedFolderData([]);
                            }
                            setSelectedSections(
                              (prev: Record<string, string | undefined>) => ({
                                ...prev,
                                [item.course_id]:
                                  e.target.value === ""
                                    ? item.sections[0]?.section_id
                                    : e.target.value,
                              }),
                            );
                          }}
                          className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm p-2 bg-white border"
                          defaultValue={item.sections[0]?.section_id}
                        >
                          {item.sections.map((sec) => (
                            <option key={sec.section_id} value={sec.section_id}>
                              {sec.name}
                            </option>
                          ))}
                        </select>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td
                      className="px-4 py-4 whitespace-nowrap text-sm text-gray-500 text-center"
                      colSpan={4}
                    >
                      {t("resourceLibrary.noCoursesAvailable")}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>

          <div className="flex items-center justify-center mt-6">
            <div className="flex items-center gap-2">
              <Button
                size="sm"
                variant="outline"
                disabled={currentPage === 1}
                onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                className="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                <ChevronLeft className="h-4 w-4 mr-1" />
              </Button>

              <span className="px-4 py-1 text-sm text-gray-700 bg-gray-100 rounded-md">
                {currentPage} of {totalPages}
              </span>

              <Button
                size="sm"
                variant="outline"
                disabled={currentPage === totalPages || totalPages === 0}
                onClick={() =>
                  setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                }
                className="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                <ChevronRight className="h-4 w-4 ml-1" />
              </Button>
            </div>
          </div>
        </div>
      )}
      <div className="flex justify-end gap-4 mt-8 pt-6">
        <Button className="bg-[#33363F]" onClick={onCancel}>
          {t("buttons.cancel")}
        </Button>
        <Button
          onClick={() => {
            void onSubmit();
          }}
          className="bg-[#9FC089]"
        >
          {t("buttons.submit")}
        </Button>
      </div>
      {openFolderPrompt && (
        <Modal
          title={t("resourceLibrary.folderPrompt")}
          header=""
          openDialog={openFolderPrompt}
          closeDialog={dialogClose}
          type="max-w-5xl"
        >
          <SelectFolder
            onCancel={dialogClose}
            folderData={selectedFolderData}
            onSave={(folderId) => {
              setSelectedFolderId(folderId);
              setOpenFolderPrompt(false);
            }}
          />
        </Modal>
      )}
    </div>
  );
}
