"use client";
import type { BaseSyntheticEvent } from "react";
import React, { useEffect } from "react";
import type { ErrorCatch, LogUserActivityRequest, ToastType } from "@/types";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import useCourse from "@/hooks/useCourse";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";

export default function DeleteResource({
  onCancel,
  onSave,
  courseModuleId,
  courseId,
}: {
  onSave: () => void;
  onCancel: () => void;
  isModal?: boolean;
  courseModuleId?: string;
  courseId?: string;
}): React.JSX.Element {
  const { t } = useTranslation();
  const { toast } = useToast() as ToastType;
  const { deleteCourseLinkedResource } = useCourse();
  const { updateUserActivity } = useLogUserActivity();

  useEffect(() => {
    console.log(courseModuleId);
  }, []);

  const handleDeleteClick = async (e: BaseSyntheticEvent): Promise<void> => {
    console.log(e);
    const params = {
      org_id: localStorage.getItem("orgId") as string,
      course_module_id: courseModuleId as string,
      course_id: courseId as string,
    };
    const fetchData = async (): Promise<void> => {
      try {
        const result = await deleteCourseLinkedResource(params);
        if (result.status === "success") {
          toast({
            variant: SUCCESS_MESSAGES.toast_variant_default,
            title: t("successMessages.deleteResourceTitle"),
            description: t("successMessages.deleteResourceMsg"),
          });
          const params = {
            activity_type: "Course",
            screen_name: "Course",
            action_details: "Resource deleted successfully",
            target_id: courseId as string,
            log_result: "SUCCESS",
          };
          void updateLogUserActivity(params).catch((error) => {
            console.error(error);
          });
          onSave();
        } else {
          toast({
            variant: SUCCESS_MESSAGES.toast_variant_default,
            title: t("successMessages.deleteResourceTitle"),
            description: t("successMessages.deleteResourceMsg"),
          });
          const params = {
            activity_type: "Course",
            screen_name: "Course",
            action_details: "Failed to delete resource",
            target_id: courseId as string,
            log_result: "ERROR",
          };
          void updateLogUserActivity(params).catch((error) => {
            console.error(error);
          });
        }
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
      }
    };
    fetchData().catch((error) => console.log(error));
  };

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  const handleCancel = (): void => {
    onCancel();
  };

  return (
    <>
      <div className="mb-2 mr-4">
        <p className="ml-0 ">
          {t(SUCCESS_MESSAGES.deleteResourceNotification)}
        </p>
      </div>
      <div className="flex topic-icons pr-4">
        <div className="flex items-center justify-center float-right">
          <Button
            type="button"
            variant="outline"
            className="primary"
            onClick={handleCancel}
          >
            {String(t("buttons.cancel"))}
          </Button>
          &nbsp;
          <Button
            type="submit"
            className="primary"
            onClick={(e: BaseSyntheticEvent) => {
              handleDeleteClick(e).catch((error) => console.log(error));
            }}
          >
            {String(t("buttons.delete"))}
          </Button>
        </div>
      </div>
    </>
  );
}
