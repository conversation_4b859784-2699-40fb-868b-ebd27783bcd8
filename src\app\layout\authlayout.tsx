"use client";

import React, { type PropsWithChildren } from "react";
import Image from "next/image";
import "../../styles/auth.css";
const AuthLayout = ({ children }: PropsWithChildren): React.JSX.Element => {
  return (
    <>
      <div className="min-h-screen flex items-center justify-center md:grid lg:max-w-none lg:grid-cols-2 bg-[#f5f5f5]">
        <div className="bg-image relative hidden h-full flex-col bg-muted p-10 text-white lg:flex">
          <div className="absolute inset-0 " />
          <div className="relative z-20 flex items-center text-lg font-medium">
            {/* <div className="logo-container">
              <div className="circle-background"> */}
            <Image
              src="/static/images/smartlearn.png"
              alt="Card Image"
              width={100}
              height={100}
              // className="logo-image"
            />
            {/* </div>
            </div> */}
            {/* <div className="label-container">
              <h2 className="text-2xl font-semibold tracking-tight">LMS</h2>
              <p className="label-description">SmartLearn Admin</p>
            </div> */}
            {/* <label htmlFor="title" className="leading-none peer-disabled:cursor-not-allowed text-lg peer-disabled:opacity-70">LMS SmartLearn Admin</label> */}
          </div>
        </div>
        <div>{children}</div>
      </div>
    </>
  );
};
export default AuthLayout;
