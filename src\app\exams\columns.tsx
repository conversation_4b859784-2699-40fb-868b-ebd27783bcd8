"use client";

import type { ColumnDef } from "@tanstack/react-table";
// import { ArrowUpDown } from "lucide-react";
// import { Button } from "@/components/ui/button";
import moment from "moment";
import type {
  EnrollmentTableType,
  // EnrollmentColumnDefinition,
  EnrollmentRowDefinition,
} from "@/types";

export const getColumns = (
  t: (key: string) => string,
): ColumnDef<EnrollmentTableType>[] => [
  {
    accessorKey: "name",
    header: t("exams.examName"),
    // header: ({ column }: EnrollmentColumnDefinition): React.JSX.Element => {
    //   return (
    //     <Button
    //       className="px-0"
    //       variant="ghost"
    //       onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
    //     >
    //       Exam name
    //       <ArrowUpDown className="ml-2 h-4 w-4" />
    //     </Button>
    //   );
    // },
  },
  {
    accessorKey: "start_time",
    header: t("exams.validFrom"),
    cell: ({ row }: EnrollmentRowDefinition): React.JSX.Element => {
      const formattedDate = moment(row.original.start_time).format(
        "DD-MMM-YYYY hh:mm a",
      );

      return <div>{formattedDate}</div>;
    },
  },
  {
    accessorKey: "end_time",
    header: t("exams.validTo"),
    cell: ({ row }: EnrollmentRowDefinition): React.JSX.Element => {
      const formattedDate = moment(row.original.end_time).format(
        "DD-MMM-YYYY hh:mm a",
      );
      return <div>{formattedDate}</div>;
    },
  },
  // {
  //   accessorKey: "created_at",
  //   header: "Created on",
  //   cell: ({ row }: EnrollmentRowDefinition): React.JSX.Element => {
  //     const formattedDate = moment
  //       .utc(row.original.created_at)
  //       .local()
  //       .format("DD-MMM-YYYY hh:mm a");

  //     return <div>{formattedDate}</div>;
  //   },
  // },
  {
    accessorKey: "duration",
    header: t("exams.duration"),
    cell: ({ row }: EnrollmentRowDefinition): React.JSX.Element => (
      <div className="text-center">{row.original.duration}</div>
    ),
  },
  {
    accessorKey: "num_of_questions",
    header: t("exams.noOfQuestions"),
    cell: ({ row }: EnrollmentRowDefinition): React.JSX.Element => (
      <div className="text-center">
        {row.original.questions_exists}/{row.original.num_of_questions}
      </div>
    ),
  },
  {
    accessorKey: "total_mark",
    header: t("exams.totalMarks"),
    cell: ({ row }: EnrollmentRowDefinition): React.JSX.Element => (
      <div className="text-center">{row.original.total_mark}</div>
    ),
  },
  {
    accessorKey: "quiz_type",
    header: t("exams.type"),
  },
  {
    accessorKey: "penalty_available",
    header: t("exams.penalty"),
  },
];
