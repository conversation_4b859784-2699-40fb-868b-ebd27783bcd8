"use client";
import React from "react";
import type {
  // Column,
  ColumnDef,
  Row,
} from "@tanstack/react-table";
// import { ArrowUpDown } from "lucide-react";
// import { Button } from "@/components/ui/button";
import type { UserLogList } from "@/types";
import moment from "moment";
import { DATE_FORMAT_DMY_HM_AM_PM } from "@/lib/constants";

interface RowDefinition {
  row: Row<UserLogList>;
}
// interface ColumnDefinition {
//   column: Column<UserLogList, unknown>;
// }

export const getColumns = (
  t: (key: string) => string,
): ColumnDef<UserLogList>[] => [
  {
    accessorKey: "activity_type",
    header: t("activityLog.screen"),
    // header: ({ column }: ColumnDefinition): React.JSX.Element => {
    //   return (
    //     <Button
    //       className="px-0"
    //       variant="ghost"
    //       onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
    //     >
    //       Module
    //       <ArrowUpDown className="ml-2 h-4 w-4" />
    //     </Button>
    //   );
    // },
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div>{row.original.activity_type}</div>
    ),
  },
  {
    header: t("activityLog.comment"),
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div>{row.original.comment}</div>
    ),
  },
  {
    header: t("activityLog.activityStatus"),
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div>{row.original.status_comment}</div>
    ),
  },
  {
    header: t("activityLog.userAgent"),
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div>{row.original.user_agent}</div>
    ),
  },

  {
    header: t("activityLog.date"),
    cell: ({ row }: RowDefinition): React.JSX.Element => {
      const formattedDate = moment
        .utc(row.original.created_at)
        .local()
        .format(DATE_FORMAT_DMY_HM_AM_PM);
      return <div>{formattedDate}</div>;
    },
  },

  // {
  //   header: "Comments",
  //   cell: ({ row }: RowDefinition): React.JSX.Element => (
  //     <div>{row.original.comment}</div>
  //   ),
  // },
];
