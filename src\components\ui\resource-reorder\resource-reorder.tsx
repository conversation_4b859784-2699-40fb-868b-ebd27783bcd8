"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Modal } from "@/components/ui/modal";
import { Card, CardContent } from "@/components/ui/card";
import { GripVertical } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import type { ResourceItem, ResourceReorderProps } from "@/types";

export const ResourceReorderModal: React.FC<ResourceReorderProps> = ({
  isOpen,
  onClose,
  resources,
  onSave,
}) => {
  const [reorderedResources, setReorderedResources] = useState<ResourceItem[]>(
    [...resources].sort((a, b) => a.module_order - b.module_order),
  );
  const [isLoading, setIsLoading] = useState(false);
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null);
  const { toast } = useToast();

  // Handle drag start
  const handleDragStart = (e: React.DragEvent, index: number): void => {
    setDraggedIndex(index);
    e.dataTransfer.effectAllowed = "move";
    e.dataTransfer.setData("text/html", "");
  };

  // Handle drag over
  const handleDragOver = (e: React.DragEvent, index: number): void => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "move";
    setDragOverIndex(index);
  };

  // Handle drag leave
  const handleDragLeave = (): void => {
    setDragOverIndex(null);
  };

  // Handle drop
  const handleDrop = (e: React.DragEvent, dropIndex: number): void => {
    e.preventDefault();

    if (draggedIndex === null || draggedIndex === dropIndex) {
      setDraggedIndex(null);
      setDragOverIndex(null);
      return;
    }

    const newResources = [...reorderedResources];
    const draggedItem = newResources[draggedIndex];

    // Remove the dragged item
    newResources.splice(draggedIndex, 1);

    // Insert it at the new position
    newResources.splice(dropIndex, 0, draggedItem);

    // Update the module_order for all resources
    const updatedResources = newResources.map((resource, index) => ({
      ...resource,
      module_order: index + 1,
    }));

    setReorderedResources(updatedResources);
    setDraggedIndex(null);
    setDragOverIndex(null);

    toast({
      title: "Position Updated",
      description: `"${draggedItem.module_name}" moved to position ${dropIndex + 1}.`,
    });
  };

  // Simple move up/down functions as fallback
  const moveUp = (index: number): void => {
    if (index === 0) return;

    const newResources = [...reorderedResources];
    [newResources[index], newResources[index - 1]] = [
      newResources[index - 1],
      newResources[index],
    ];

    const updatedResources = newResources.map((resource, idx) => ({
      ...resource,
      module_order: idx + 1,
    }));

    setReorderedResources(updatedResources);
  };

  const moveDown = (index: number): void => {
    if (index === reorderedResources.length - 1) return;

    const newResources = [...reorderedResources];
    [newResources[index], newResources[index + 1]] = [
      newResources[index + 1],
      newResources[index],
    ];

    const updatedResources = newResources.map((resource, idx) => ({
      ...resource,
      module_order: idx + 1,
    }));

    setReorderedResources(updatedResources);
  };

  const handleSave = async (): Promise<void> => {
    setIsLoading(true);
    try {
      await onSave(reorderedResources);

      onClose();
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to update resource order. Please try again.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = (): void => {
    setReorderedResources(
      [...resources].sort((a, b) => a.module_order - b.module_order),
    );
    setDraggedIndex(null);
    setDragOverIndex(null);
    onClose();
  };

  return (
    <Modal
      title={`Rearrange `}
      header=""
      openDialog={isOpen}
      closeDialog={onClose}
      type="max-w-2xl"
    >
      <div className="space-y-4">
        <div className="max-h-96 overflow-y-auto space-y-2">
          {reorderedResources.map((resource, index) => {
            const isDragging = draggedIndex === index;
            const isDragOver = dragOverIndex === index;

            return (
              <div key={resource.course_module_id}>
                {/* Drop indicator */}
                {isDragOver &&
                  draggedIndex !== null &&
                  draggedIndex !== index && (
                    <div className="h-1 bg-blue-500 rounded-full mx-4 mb-2 shadow-sm animate-pulse"></div>
                  )}

                <Card
                  draggable
                  onDragStart={(e) => handleDragStart(e, index)}
                  onDragOver={(e) => handleDragOver(e, index)}
                  onDragLeave={handleDragLeave}
                  onDrop={(e) => handleDrop(e, index)}
                  className={`border transition-all duration-200 cursor-move hover:shadow-md ${
                    isDragging
                      ? "opacity-50 scale-105 border-blue-400 shadow-lg"
                      : isDragOver
                        ? "border-blue-500 bg-blue-50 shadow-md"
                        : "border-gray-200 hover:border-gray-300"
                  }`}
                >
                  <CardContent className="p-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3 flex-1">
                        <div className="flex items-center space-x-2">
                          <GripVertical className="h-5 w-5 text-gray-400 cursor-grab active:cursor-grabbing" />

                          <span className="text-sm font-medium text-gray-500 min-w-[2rem]">
                            #{index + 1}
                          </span>
                        </div>
                        <div className="flex-1">
                          <div className="font-medium text-gray-900">
                            {resource.module_name}
                          </div>
                          <div className="text-sm text-gray-500 capitalize">
                            {resource.module_type}
                          </div>
                        </div>
                      </div>

                      {/* Simple Arrow Buttons as Fallback */}
                      <div className="flex items-center space-x-1">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            moveUp(index);
                          }}
                          disabled={index === 0}
                          className="h-7 w-7 p-0 hover:bg-gray-100"
                          title="Move up"
                        >
                          ↑
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            moveDown(index);
                          }}
                          disabled={index === reorderedResources.length - 1}
                          className="h-7 w-7 p-0 hover:bg-gray-100"
                          title="Move down"
                        >
                          ↓
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            );
          })}
        </div>

        {reorderedResources.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            No resources found in this section.
          </div>
        )}

        <div className="flex justify-end space-x-3 pt-4 border-t">
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={isLoading}
            className="flex items-center space-x-2"
          >
            Cancel
          </Button>
          <Button
            variant="default"
            onClick={() => {
              void handleSave();
            }}
            disabled={isLoading || reorderedResources.length === 0}
            className="bg-[#33363F]"
          >
            Save
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default ResourceReorderModal;
