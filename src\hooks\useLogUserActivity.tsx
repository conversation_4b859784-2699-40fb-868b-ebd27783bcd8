import { supabase } from "@/lib/client";
import { rpc } from "@/lib/apiConfig";
import type {
  LoginUserData,
  LogUserActivityRequest,
  LogUserActivityResponse,
} from "@/types";
import { ACCESS_TOKEN } from "@/lib/constants";

interface useLogUserActivityReturn {
  updateUserActivity: (
    params: LogUserActivityRequest,
  ) => Promise<LogUserActivityResponse>;
}

const useLogUserActivity = (): useLogUserActivityReturn => {
  async function updateUserActivity(
    params: LogUserActivityRequest,
  ): Promise<LogUserActivityResponse> {
    let userId = "";
    const userDetails = localStorage.getItem("userDetails");

    if (userDetails !== null && userDetails !== undefined) {
      const users = JSON.parse(userDetails) as LoginUserData;
      userId = users?.id;
    }
    const reqquestParams = {
      org_id: params.org_id ?? (localStorage.getItem("orgId") as string),
      activity_type: params.activity_type,
      screen_name: params.screen_name,
      action_details: params.action_details,
      target_id: params.target_id ?? userId,
      session_id: localStorage.getItem(ACCESS_TOKEN) ?? "",
      action_comment:
        params.action_details + "at " + new Date().toLocaleString(),
      user_agent: navigator.userAgent,
      log_source: "Admin",
      log_result: params.log_result,
      user_id: params.user_id ?? userId,
    };

    try {
      const { data, error } = await supabase.rpc<string, null>(
        rpc.logUserActivity,
        reqquestParams,
      );
      if (error) {
        throw new Error(error.details);
      }
      return data as LogUserActivityResponse;
    } catch (error) {
      console.error("Error", error);
      throw error;
    }
  }

  return { updateUserActivity };
};

export default useLogUserActivity;
