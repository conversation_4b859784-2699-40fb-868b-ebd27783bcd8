import { supabase } from "@/lib/client";
import { rpc } from "@/lib/apiConfig";
import type {
  CommentResponse,
  ErrorType,
  CommentsListRequest,
  UpdateCommentStatusType,
  SuccessMessage,
  AddCommentRequest,
  AddCommentResponse,
} from "@/types";

interface useCommentsReturn {
  getCommentsList: (params: CommentsListRequest) => Promise<CommentResponse[]>;
  commentStatusUpdate: (
    params: UpdateCommentStatusType,
  ) => Promise<SuccessMessage>;
  addComments: (params: AddCommentRequest) => Promise<AddCommentResponse>;
}

const useComments = (): useCommentsReturn => {
  async function getCommentsList(
    params?: CommentsListRequest,
  ): Promise<CommentResponse[]> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.getCommentsForApproval,
        params,
      )) as { data: CommentResponse[]; error: ErrorType | null };
      if (error) {
        throw new Error(error.details);
      }
      return data as CommentResponse[];
    } catch (error) {
      console.error("Error", error);
      throw error;
    }
  }

  async function commentStatusUpdate(
    params: UpdateCommentStatusType,
  ): Promise<SuccessMessage> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.updateCommentStatus,
        params,
      )) as { data: SuccessMessage; error: ErrorType | null };
      if (error) {
        throw new Error(error.details);
      }
      return data as SuccessMessage;
    } catch (error) {
      console.error("Error", error);
      throw error;
    }
  }

  async function addComments(
    params: AddCommentRequest,
  ): Promise<AddCommentResponse> {
    try {
      const { data, error } = await supabase.rpc<string, null>(
        rpc.addComments,
        params,
      );
      if (error) {
        throw new Error(error.details);
      }
      return data as AddCommentResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  return { getCommentsList, commentStatusUpdate, addComments };
};

export default useComments;
