"use client";
import React, { useEffect, useState } from "react";
import {
  ChevronDown,
  Book,
  Users,
  Settings as Setting<PERSON><PERSON><PERSON>,
  Bar<PERSON>hart,
  Trash,
  // SquareCodeIcon,
} from "lucide-react";
import MainLayout from "../layout/mainlayout";
import ModuleDetails from "../course-resource/module-details";
import { ORG_KEY, pageUrl, privilegeData } from "@/lib/constants";
import type { InnerItem, OrderSectionRequest, ResourceItem } from "@/types";
import {
  type CoursePlanResult,
  type CourseDetailsRequest,
  type CourseDetailsResultType,
  type CourseDetailsValueType,
  type Enrollment,
  type ErrorCatch,
  type ToastType,
} from "@/types";
import { useRouter, useSearchParams } from "next/navigation";
import useCourse from "@/hooks/useCourse";
import moment from "moment";
import { Editor } from "primereact/editor";
import getPrivilegeList from "@/hooks/useCheckPrivilege";
import { Button } from "@/components/ui/button";
import useEnrollments from "@/hooks/useEnrollment";
import { useToast } from "@/components/ui/use-toast";
import { DataTable } from "../../components/ui/data-table/data-table";
import { getParticipantColumns } from "./columns";
import { getPlansColumns } from "./column";
import { Spinner } from "@/components/ui/progressiveLoader";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumb";
import { Modal } from "@/components/ui/modal";
import DeleteSection from "./deleteSection";
import { ResourceReorderModal } from "@/components/ui/resource-reorder/resource-reorder";
import { ArrowUpDown } from "lucide-react";
import { SUCCESS_MESSAGES } from "@/lib/messages";
import { useTranslation } from "react-i18next";
import { ERROR_MESSAGES } from "@/lib/messages";

export default function CourseManagement(): JSX.Element {
  const { t } = useTranslation();
  const participantsColumns = getParticipantColumns(t);
  const plansColumns = getPlansColumns(t);
  const [expandedModule, setExpandedModule] = useState<number | null>(null);
  const searchParams = useSearchParams();
  const [isLoading, setIsLoading] = useState(true);
  const [reloadData, setReloadData] = useState(false);
  const courseId = searchParams.get("courseId");
  const tabValue = searchParams.get("tab");
  const [selectedTab, setSelectedTab] = useState("Course Details");

  const { courseDetails, getPlanList, orderSection } = useCourse();
  const [courseDetailsData, setCourseDetailsData] =
    React.useState<CourseDetailsValueType | null>();
  const expiry = searchParams.get("expiry");

  const is_premium = searchParams.get("is_premium");
  const [courseDuration, setCourseDuration] = useState(0);
  const disableEdit = getPrivilegeList(
    "Course",
    privilegeData.Course.updateCourse,
  );
  const [data, setData] = useState<Enrollment[]>([]);
  const { getEnrollments } = useEnrollments();
  const { toast } = useToast() as ToastType;
  const [categoryId, setCategoryId] = useState("");
  const [planList, setPlanList] = useState<CoursePlanResult[]>([]);
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);
  const router = useRouter();

  let Expiry = false;
  const enableExpiry = localStorage?.getItem("expiryCourse") ?? true;
  Expiry = enableExpiry === "true" ? true : false;
  const [openDelete, setOpenDelete] = useState(false);
  const navigationTabs = [
    { id: t("courses.courseDetails"), icon: Book },
    { id: t("courses.modules"), icon: SettingsIcon },
    { id: t("courses.participants"), icon: Users },
    { id: t("courses.membershipPlans"), icon: BarChart },
    // { id: "More", icon: SquareCodeIcon },
  ];
  const [sectionId, setSectionId] = useState("");

  // Section reorder states
  const [isSectionReorderOpen, setIsSectionReorderOpen] =
    useState<boolean>(false);

  useEffect(() => {
    localStorage.setItem("courseId", courseId ?? "");
    localStorage.setItem("expiryCourse", expiry ?? "");

    if (tabValue !== null || tabValue !== undefined) {
      const tab = formatString(tabValue as string);
      setSelectedTab(tab);
    } else {
      setSelectedTab("Course Details");
    }
    setBreadcrumbItems(
      getBreadCrumbItems(t, t("breadcrumb.viewCourse"), { "": "" }),
    );
  }, [t]);

  useEffect(() => {
    if (selectedTab === "Modules") {
      router.push(
        `${pageUrl.CourseManagement}?courseId=${courseId}&&expiry=${expiry}&&is_premium=${is_premium}&&tab=Modules`,
      );
    } else if (selectedTab === "Course Details") {
      const name = "Course Details";
      router.push(
        `${pageUrl.CourseManagement}?courseId=${courseId}&&expiry=${expiry}&&is_premium=${is_premium}&&tab=${name}`,
      );
    }
  }, [selectedTab]);

  useEffect(() => {
    const fetchData = async (): Promise<void> => {
      const orgId = localStorage.getItem(ORG_KEY);
      const reqParams: CourseDetailsRequest = {
        course_id: courseId as string,
        org_id: orgId ?? "",
      };
      try {
        const response = await courseDetails(reqParams);
        setIsLoading(false);
        setCategoryId(response[0].category_id);
        setCourseDetailsData(response[0] as CourseDetailsResultType);
        const duration = moment(response[0].end_date).diff(
          response[0].start_date,
          "days",
        );
        setCourseDuration(duration);
      } catch (error) {
        setIsLoading(false);
        console.error("Error fetching data:", error);
      }
    };
    const fetchEnrollmentData = async (): Promise<void> => {
      try {
        const enrollments = await getEnrollments(courseId as string);

        if (
          enrollments !== null &&
          enrollments !== undefined &&
          Object.keys(enrollments).length > 0
        ) {
          setData(enrollments);
        } else {
          setData([]);
        }
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
      }
    };
    const fetchPlans = async (): Promise<void> => {
      const orgId = localStorage.getItem(ORG_KEY);

      try {
        const response = await getPlanList(courseId as string, orgId as string);
        const filteredResult = response?.result?.filter(
          (item, index, self) =>
            index ===
            self.findIndex(
              (t) => t.subscription_plan_id === item.subscription_plan_id,
            ),
        );
        setPlanList(filteredResult);
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };
    void fetchData();
    void fetchEnrollmentData();
    void fetchPlans();
  }, [courseId, reloadData]);

  const formatString = (str?: string): string => {
    if (str == null) return "Course Details";
    return str
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  };

  const editClickHandler = (): void => {
    router.push(
      `${pageUrl.editCourse}?courseId=${courseId}&categoryId=${categoryId}&&expiry=${expiry}&&is_premium=${is_premium}`,
    );
  };
  const BackToList = (): void => {
    router.push(`${pageUrl.courseList}`);
  };

  const toggleModule = (index: number): void => {
    setExpandedModule((prev) => (prev === index ? null : index));
  };

  const cancelDeleteModal = (): void => {
    setOpenDelete(false);
    setReloadData(true);
  };

  // Section reorder handlers
  const handleSectionReorder = (): void => {
    const sectionCount = courseDetailsData?.sections?.length ?? 0;

    if (!courseDetailsData?.sections || sectionCount < 2) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("successMessages.cannotRearrange"),
        description: t("successMessages.need_at_least_two_sections", {
          count: sectionCount,
          verb: sectionCount === 1 ? "is" : "are",
          plural: sectionCount === 1 ? "" : "s",
        }),
      });
      return;
    }

    setIsSectionReorderOpen(true);
  };

  const handleSectionReorderSave = async (
    reorderedSections: ResourceItem[],
  ): Promise<void> => {
    const orgId = localStorage.getItem(ORG_KEY);
    const params: OrderSectionRequest = {
      org_id: orgId ?? "",
      course_id: courseId ?? "",
      sections: reorderedSections.map(
        (
          section: {
            module_id: string;
            module_name: string;
            module_type: string;
            module_order: number;
            course_module_id: string;
            instance: string;
          },
          index,
        ) => ({
          section_id: section.module_id as string,
          section_order: index + 1,
        }),
      ),
    };
    try {
      const response = await orderSection(params);
      if (response.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.toast_success_title"),
          description: t("successMessages.order_section"),
        });
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
    }
    setReloadData(!reloadData);
  };

  return (
    <MainLayout>
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      <div className="">
        <div className="flex">
          {/* Main Content Area */}
          <main className="flex-1 pl-2">
            {/* Navigation Tabs */}
            <div className="bg-white border-b border-slate-200 px-6">
              <div className="flex items-center h-14">
                {navigationTabs.map((tab) => (
                  <button
                    key={tab.id}
                    className={`flex items-center gap-2 px-4 py-2 -mb-px h-14
                      ${
                        selectedTab === tab.id
                          ? "text-[#218faa] border-b-2 border-[#218faa] font-medium"
                          : "text-slate-600 hover:text-slate-900"
                      }`}
                    onClick={() => setSelectedTab(tab.id)}
                  >
                    <tab.icon className="w-4 h-4" />
                    {tab.id}
                  </button>
                ))}
              </div>
            </div>

            {/* Content Area Based on Selected Tab */}
            {!isLoading ? (
              <div className="pt-6">
                {selectedTab === "Course Details" && (
                  <div className="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
                    <h2 className="text-2xl font-semibold text-slate-800">
                      {courseDetailsData?.short_name ?? ""}
                      <span className="text-orange-500 font-semibold ml-2">
                        [{courseDetailsData?.full_name ?? ""}]
                      </span>
                    </h2>

                    <div className="flex justify-between items-center  pt-4">
                      {/* From Section */}
                      <div className="flex items-center space-x-2">
                        <div className="font-semibold">
                          {t("courses.from")}:
                        </div>
                        <div className="text-[#00AFBB]">
                          {moment(courseDetailsData?.start_date).format(
                            "DD MMM YY hh:mm A",
                          )}
                        </div>
                      </div>

                      {/* To Section */}
                      <div className="flex items-center space-x-2">
                        <div className="font-semibold">{t("courses.to")}:</div>
                        <div className="text-[#00AFBB]">
                          {moment(courseDetailsData?.end_date).format(
                            "DD MMM YY hh:mm A",
                          )}
                        </div>
                      </div>

                      {/* Duration Section */}
                      <div className="flex items-center space-x-2">
                        <div className="font-semibold">
                          {t("courses.duration")}:
                        </div>
                        <div className="text-[#00AFBB]">
                          {courseDuration} Days
                        </div>
                      </div>

                      {/* Edit Button */}
                    </div>

                    {/* Description Editor Section */}
                    <div className="border-none shadow-none pt-6 w-full">
                      <div className="pb-4">
                        <h3 className="text-lg font-bold">
                          {t("courses.description")}
                        </h3>
                      </div>
                      <Editor
                        value={courseDetailsData?.summary}
                        style={{
                          height: "320px",
                          background: "#fff",
                          width: "100%",
                        }}
                        readOnly={true}
                        className="bg-white w-full"
                      />
                    </div>
                    <div className="flex items-end justify-end pt-2">
                      {disableEdit && (
                        <Button
                          className="bg-[#155264]"
                          onClick={editClickHandler}
                        >
                          {t("buttons.edit")}
                        </Button>
                      )}
                    </div>
                  </div>
                )}

                {selectedTab === "Modules" && (
                  <div className="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
                    {/* Rearrange Sections Button */}
                    {Expiry === false && courseDetailsData?.sections && (
                      <div className="mb-6 flex justify-end">
                        <Button
                          className="bg-[#9fc089] hover:bg-[#8fb079] text-white"
                          onClick={handleSectionReorder}
                          disabled={courseDetailsData.sections.length < 2}
                        >
                          <ArrowUpDown className="h-4 w-4 mr-2" />
                          Rearrange Sections
                          {courseDetailsData.sections.length < 2 && (
                            <span className="ml-1 text-xs opacity-75">
                              ({courseDetailsData.sections.length} section
                              {courseDetailsData.sections.length !== 1
                                ? "s"
                                : ""}
                              )
                            </span>
                          )}
                        </Button>
                      </div>
                    )}

                    <ul className="space-y-4">
                      {courseDetailsData?.sections?.map((module, index) => (
                        <li
                          key={index}
                          className="border-b border-slate-200 pb-2 mb-2"
                        >
                          <div
                            className="flex justify-between items-center cursor-pointer"
                            onClick={() => toggleModule(index)}
                          >
                            <span className="text-xl font-semibold tracking-tight">
                              {module.name}
                            </span>

                            <div className="flex items-center gap-2">
                              <Trash
                                className="w-5 h-5  cursor-pointer"
                                onClick={(event) => {
                                  setOpenDelete(true);
                                  setReloadData(false);
                                  setSectionId(module.section_id);
                                  event.stopPropagation();
                                }}
                              />
                              <ChevronDown
                                className={`w-5 h-5 transition-transform ${
                                  expandedModule === index ? "rotate-180" : ""
                                }`}
                              />
                            </div>
                          </div>
                          {expandedModule === index && (
                            <>
                              <ModuleDetails
                                sectionId={module.section_id as string}
                                expiry={Expiry}
                                courseIds={module.course_id}
                                is_premium={is_premium as string}
                              ></ModuleDetails>
                            </>
                          )}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
                {selectedTab === "Participants" && (
                  <div className="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
                    <div className="">
                      <DataTable
                        columns={participantsColumns}
                        data={data}
                        FilterLabel={t("courses.filterByFirstName")}
                        FilterBy={"first_name"}
                        actions={[]}
                      />
                    </div>
                  </div>
                )}
                {selectedTab === "Membership Plans" && (
                  <div className="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
                    <div className="">
                      <DataTable
                        columns={plansColumns}
                        data={planList}
                        FilterLabel={t("courses.filterByPlanName")}
                        FilterBy={"name"}
                        actions={[]}
                      />
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <Spinner></Spinner>
            )}
          </main>
        </div>
        <div className="flex items-end justify-end pt-2">
          <Button type="button" className="bg-[#33363F]" onClick={BackToList}>
            {t("buttons.cancel")}
          </Button>
        </div>
      </div>
      {openDelete && (
        <Modal
          title={t("courses.courseModule.deleteSection")}
          header=""
          openDialog={openDelete}
          closeDialog={cancelDeleteModal}
          type="max-w-5xl"
        >
          <DeleteSection
            courseId={courseId as string}
            sectionId={sectionId}
            onCancel={cancelDeleteModal}
          ></DeleteSection>
        </Modal>
      )}

      {/* Section Reorder Modal */}
      {isSectionReorderOpen && courseDetailsData?.sections && (
        <ResourceReorderModal
          isOpen={isSectionReorderOpen}
          onClose={() => setIsSectionReorderOpen(false)}
          resources={courseDetailsData.sections.map((section) => ({
            module_id: section.section_id,
            module_name: section.name,
            module_type: "Section",
            module_order: section.section_order ?? 0,
            course_module_id: section.section_id,
            instance: section.section_id,
          }))}
          sectionTitle="Course Sections"
          onSave={handleSectionReorderSave}
        />
      )}
    </MainLayout>
  );
}
