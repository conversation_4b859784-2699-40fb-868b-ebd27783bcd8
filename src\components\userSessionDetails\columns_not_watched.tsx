"use client";
import type { ColumnDef } from "@tanstack/react-table";
import { ArrowUpDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import type {
  CheckpointsUserStatsReturnType,
  UserSessionColumnDefinition,
  UserSessionRowDefinition,
} from "@/types";
import moment from "moment";

export const columns_not_watched: ColumnDef<CheckpointsUserStatsReturnType>[] =
  [
    {
      accessorKey: "first_name",
      header: ({ column }: UserSessionColumnDefinition): React.JSX.Element => {
        return (
          <Button
            className="px-0"
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            First Name
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }: UserSessionRowDefinition): React.JSX.Element => {
        const firstName = row.original.first_name ?? " ";
        return <div className="text-align">{firstName}</div>;
      },
    },
    {
      accessorKey: "last_name",
      header: "Last Name",
      cell: ({ row }: UserSessionRowDefinition): React.JSX.Element => {
        const lastName = row.original.last_name ?? " ";
        return <div className="text-align">{lastName}</div>;
      },
    },
    {
      accessorKey: "email",
      header: "Email",
      cell: ({ row }: UserSessionRowDefinition): React.JSX.Element => (
        <div>{row.original.email ?? ""}</div>
      ),
    },
    {
      accessorKey: "enrolled_date",
      header: "Enrolled Date",
      cell: ({ row }: UserSessionRowDefinition): React.JSX.Element => {
        const formattedDate = moment
          .utc(row.original.enrollment_time)
          .local()
          .format("DD-MMM-YYYY hh:mm a");
        return <div>{formattedDate}</div>;
      },
    },
  ];
