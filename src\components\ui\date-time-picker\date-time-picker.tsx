"use client";

import { CalendarIcon } from "lucide-react";
import React, { useEffect, useRef, useState } from "react";
import type { DateValue, TimeValue } from "react-aria";
import { useButton, useDatePicker, useInteractOutside } from "react-aria";
import type { DatePickerStateOptions } from "react-stately";
import { useDatePickerState } from "react-stately";
import { useForwardedRef } from "@/lib/useForwardedRef";
import { cn } from "@/lib/utils";
import { Button } from "../button";
import { Popover, PopoverContent, PopoverTrigger } from "../popover";
import { Calendar } from "./calendar";
import { DateField } from "./date-field";
import { TimeField } from "./time-field";
import { X } from "lucide-react";
const DateTimePicker = React.forwardRef<
  HTMLDivElement,
  DatePickerStateOptions<DateValue>
>((props, forwardedRef) => {
  const ref = useForwardedRef(forwardedRef);
  const buttonRef = useRef<HTMLButtonElement | null>(null);
  const contentRef = useRef<HTMLDivElement | null>(null);

  const [open, setOpen] = useState(false);

  const state = useDatePickerState(props);
  const {
    groupProps,
    fieldProps,
    buttonProps: _buttonProps,
    dialogProps,
    calendarProps,
  } = useDatePicker(props, state, ref);
  const { buttonProps } = useButton(_buttonProps, buttonRef);
  useInteractOutside({
    ref: contentRef,
    onInteractOutside: (e) => {
      // Don't close if clicking on a select dropdown or its content
      const target = e.target as Element;
      const isSelectInteraction =
        target.closest("[data-radix-select-item]") ??
        target.closest("[data-radix-popper-content-wrapper]") ??
        target.closest('[role="listbox"]') ??
        target.closest('[role="option"]');

      if (isSelectInteraction) {
        return;
      }
      setOpen(false);
    },
  });
  const handleClose = (): void => {
    setOpen(false);
  };
  useEffect(() => {
    if (state.value) {
      setOpen(false); // Close the popover when a date is selected
    }
  }, [state.value]);
  return (
    <div
      {...groupProps}
      ref={ref}
      className={cn(
        groupProps.className,
        "flex items-center rounded-md ring-offset-background focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2",
      )}
    >
      <DateField {...fieldProps} />
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            {...buttonProps}
            variant="outline"
            className="rounded-l-none"
            disabled={props.isDisabled}
            onClick={() => setOpen(true)}
          >
            <CalendarIcon className="h-5 w-5" />
          </Button>
        </PopoverTrigger>
        <PopoverContent ref={contentRef} className="w-full">
          <div className="flex justify-end mb-4" onClick={handleClose}>
            <X />
          </div>
          <div {...dialogProps} className="space-y-3">
            <Calendar {...calendarProps} />
            {!!state.hasTime && (
              <TimeField
                value={state.timeValue}
                onChange={(value) => state.setTimeValue(value as TimeValue)}
                hideTimeZone={true}
              />
            )}
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
});

DateTimePicker.displayName = "DateTimePicker";

export { DateTimePicker };
