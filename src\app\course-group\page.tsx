"use client";
import React, { useEffect, useState } from "react";
import "../../styles/main.css";
import MainLayout from "../layout/mainlayout";
import { Label } from "@/components/ui/label";
import { useTranslation } from "react-i18next";
// import { Combobox } from "@/components/ui/combobox";

import type {
  AddCourseToGroupType,
  // ComboData,
  CourseGroup,
  ErrorCatch,
  InnerItem,
  LogUserActivityRequest,
  ToastType,
} from "@/types";
import { DataTable } from "@/components/ui/data-table/data-table";
import { getColumns } from "./column";
import { Button } from "@/components/ui/button";
import useGroups from "@/hooks/useGroups";
import { useToast } from "@/components/ui/use-toast";
import { pageUrl, privilegeData } from "@/lib/constants";
import { useRouter } from "next/navigation";
import { useSearchParams } from "next/navigation";
import ComfirmSubmit from "@/components/ui/confirmationmodal";
import { Modal } from "@/components/ui/modal";
import getPrivilegeList from "@/hooks/useCheckPrivilege";
import { Input } from "@/components/ui/input";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumb";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";

export default function CourseGroupPage(): React.JSX.Element {
  const { t } = useTranslation();
  const columns = getColumns(t);
  const [courseGroupData, setCourseData] = useState<CourseGroup[]>([]);
  // const [comboData, setComboData] = useState<ComboData[]>([]);
  const [groupId, setGroupId] = useState<string>();
  const [emptyChoice, setEmptyChoice] = useState<boolean>(false);
  const { getUserCourses, addCourseToGroups } = useGroups();
  const { toast } = useToast() as ToastType;
  const router = useRouter();
  const searchParams = useSearchParams();
  const defaultGroupId = searchParams.get("group") ?? "";
  const defaultGroupName = searchParams.get("name") ?? "";
  const [disableBtn, setDisableBtn] = useState<boolean>(false);
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);
  const { updateUserActivity } = useLogUserActivity();

  const fetchCourseData = async (groupId: string): Promise<void> => {
    const orgId = localStorage.getItem("orgId");
    const groupData = {
      org_id: orgId ?? "",
      group_id: groupId,
    };
    try {
      const course = await getUserCourses(groupData);
      if (course !== null && course !== undefined) {
        const CourseData = course.course_data;
        setCourseData(CourseData as CourseGroup[]);
      }
    } catch (error) {
      console.log(t("groups.courseGroup.errorFetchingGroups"));
    }
  };

  useEffect(() => {
    setBreadcrumbItems(
      getBreadCrumbItems(t, t("breadcrumb.courseGroup"), { "": "" }),
    );
    // fetchGroupData().catch((error) => console.log(error));
    fetchCourseData(defaultGroupId as string).catch((error) =>
      console.log(error),
    );
    setGroupId(defaultGroupId);
    const canPerformAction = getPrivilegeList(
      "Group",
      privilegeData.Group.updateGroupCourse,
    );
    setDisableBtn(canPerformAction);
  }, [defaultGroupId, t]);

  // const handleGroupChange = (selectedOption: string): void => {
  //   setGroupId(selectedOption);
  //   fetchCourseData(selectedOption as string).catch((error) =>
  //     console.log(error),
  //   );
  // };

  const onSubmit = (): void => {
    addCourseToGroup([]);
  };

  const closeConfirmation = (): void => {
    setEmptyChoice(false);
  };

  const getAddedItem = (dataIndex: string, isSelect: boolean): void => {
    const index = parseInt(dataIndex);
    if (isSelect === true) {
      courseGroupData[index].is_part_of_group = true;
    } else {
      courseGroupData[index].is_part_of_group = false;
    }
  };

  const updateCourseData = (): void => {
    const isPartOfGroup = courseGroupData?.filter(
      (item) => item.is_part_of_group === true,
    );
    const idList = isPartOfGroup.map((item) => item.id);
    if (idList?.length !== 0) {
      setEmptyChoice(false);
      addCourseToGroup(idList as string[]);
    } else {
      setEmptyChoice(true);
    }
  };

  const addCourseToGroup = (idList: string[]): void => {
    const addToGroups = async (): Promise<void> => {
      const orgId = localStorage.getItem("orgId");
      const courseGroupData = {
        org_id: orgId ?? "",
        group_id: groupId,
        course_ids: idList,
      };
      try {
        const users = await addCourseToGroups(
          courseGroupData as AddCourseToGroupType,
        );
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.groupsUpdated"),
          description: t("successMessages.successfullyUpdatedGroups"),
        });
        console.log(users);
        router.push(pageUrl.teams);
        const params = {
          activity_type: "Group",
          screen_name: t("groups.courseGroup.title"),
          action_details: "Added course to group ",
          target_id: groupId as string,
          log_result: "SUCCESS",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
        const params = {
          activity_type: "Group",
          screen_name: t("groups.courseGroup.title"),
          action_details: "Failed to add course to group ",
          target_id: groupId as string,
          log_result: "ERROR",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      }
    };
    addToGroups().catch((error) => console.log(error));
  };

  // const fetchGroupData = async (): Promise<void> => {
  //   try {
  //     const groups = await getGroups();
  //     if (groups !== null && groups !== undefined) {
  //       const comboData: ComboData[] = groups.map((cat) => ({
  //         value: cat.id,
  //         label: cat.name,
  //       }));
  //       setComboData(comboData);
  //     }
  //   } catch (error) {
  //     console.log("Error fetching groups");
  //   }
  // };

  const addCourseCancel = (): void => {
    router.push(pageUrl.teams);
  };
  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };
  return (
    <MainLayout>
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      <h1 className="text-2xl font-semibold tracking-tight pb-4">
        {t("groups.courseGroup.title")}
      </h1>
      <div className="border rounded-md p-4  bg-[#fff]">
        <div className="w-full flex flex-wrap justify-between space-x-4">
          <div className="w-full sm:w-1/2 md:w-1/3 lg:w-1/4 xl:w-1/5 2xl:w-1/6 flex">
            <div className="w-full">
              <Label className="block">
                {t("groups.courseGroup.selectGroup")}
              </Label>
              <div className="w-full course-width mt-2">
                <Input type="text" value={defaultGroupName} disabled />
                {/* <Combobox
                  data={comboData}
                  onSelectChange={handleGroupChange}
                  defaultLabel={defaultGroupName}
                /> */}
              </div>
            </div>
          </div>
        </div>
        <div>
          {courseGroupData?.length > 0 && (
            <DataTable
              columns={columns}
              data={courseGroupData as CourseGroup[]}
              isSelectedColumn={"is_part_of_group"}
              FilterLabel={t("groups.courseGroup.filterByCourse")}
              FilterBy={"short_name"}
              onSetCheckedStatus={(index: string, checkedStatus: boolean) => {
                getAddedItem(index as string, checkedStatus as boolean);
              }}
              actions={[]}
            />
          )}
        </div>

        <div className="flex flex-wrap justify-end mt-8">
          <div className="mt-6 flex items-center justify-end gap-x-3">
            <Button
              type="button"
              className="bg-[#33363F]"
              onClick={addCourseCancel}
            >
              {t("buttons.cancel")}
            </Button>
            <Button
              type="submit"
              className="bg-[#9FC089]"
              onClick={updateCourseData}
              disabled={!disableBtn}
            >
              {t("buttons.submit")}
            </Button>
          </div>
        </div>
      </div>
      {emptyChoice && (
        <Modal
          title={""}
          header=""
          openDialog={emptyChoice}
          closeDialog={closeConfirmation}
        >
          <ComfirmSubmit
            onSave={onSubmit}
            onCancel={closeConfirmation}
            isModal={true}
            data={t("groups.courseGroup.columns.course")}
          />
        </Modal>
      )}
    </MainLayout>
  );
}
