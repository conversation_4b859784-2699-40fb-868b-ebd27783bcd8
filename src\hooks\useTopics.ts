import type {
  // CategoryError,
  CategoryRequest,
  CategoryResponse,
  ErrorType,
  DeleteCategory,
  PublishCategory,
  Topics,
} from "@/types";
import { type TreeDataItem } from "../components/ui/tree";
import { supabase } from "../lib/client";
import { rpc } from "@/lib/apiConfig";
interface UseTopicReturn {
  getCategoryHierarchy: (param: Topics) => Promise<TreeDataItem[]>;
  addCategory: (formData: CategoryRequest) => Promise<CategoryResponse>;
  editCategory: (formData: CategoryRequest) => Promise<CategoryResponse>;
  deleteCategory: (params: DeleteCategory) => Promise<CategoryResponse>;
  publishCategory: (params: PublishCategory) => Promise<CategoryResponse>;
}

const useTopic = (): UseTopicReturn => {
  async function getCategoryHierarchy(param: Topics): Promise<TreeDataItem[]> {
    try {
      const { data, error } = await supabase.rpc<string, null>(
        rpc.getCategoryHierarchy,
        param,
      );

      if (error) {
        throw new Error(error.details);
      }
      return data as TreeDataItem[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function addCategory(
    formData: CategoryRequest,
  ): Promise<CategoryResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.insertCourseCategory,
        formData,
      )) as {
        data: CategoryResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as CategoryResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function editCategory(
    formData: CategoryRequest,
  ): Promise<CategoryResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.updateCategory,
        formData,
      )) as {
        data: CategoryResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.message);
      }
      return data as CategoryResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function deleteCategory(
    params: DeleteCategory,
  ): Promise<CategoryResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.deleteCategory,
        params,
      )) as {
        data: CategoryResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as CategoryResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function publishCategory(
    params: PublishCategory,
  ): Promise<CategoryResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.publishCategory,
        params,
      )) as {
        data: CategoryResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as CategoryResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  return {
    getCategoryHierarchy,
    addCategory,
    editCategory,
    deleteCategory,
    publishCategory,
  };
};

export default useTopic;
