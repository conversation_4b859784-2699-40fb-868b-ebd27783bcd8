"use client";

import React, { useEffect, useState } from "react";
import { getColumns } from "./columns";
import { DataTable } from "../../components/ui/data-table/data-table";
import MainLayout from "../layout/mainlayout";

import { Combobox } from "@/components/ui/combobox";
import "../../styles/main.css";
import { Label } from "@/components/ui/label";
import { Eye } from "lucide-react";
import { Modal } from "@/components/ui/modal";
import ExamInfoModal from "./examInfoModal";
import type {
  ComboData,
  ErrorCatch,
  ExamInfoType,
  ToastType,
  AttendedExamsType,
  InnerItem,
} from "@/types";
import { useToast } from "@/components/ui/use-toast";
import useAttendExams from "@/hooks/useAttendExams";
import useCourse from "@/hooks/useCourse";
import { Spinner } from "@/components/ui/progressiveLoader";
import getPrivilegeList from "@/hooks/useCheckPrivilege";
import { privilegeData } from "@/lib/constants";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumb";
import { useTranslation } from "react-i18next";
import { ERROR_MESSAGES } from "@/lib/messages";

export default function AttendedExamsPage(): React.JSX.Element {
  const { t } = useTranslation();
  const columns = getColumns(t);
  const { toast } = useToast() as unknown as ToastType;
  const [courseData, setCourseData] = useState<ComboData[]>([]);
  const [courseId, setCourseId] = useState("");
  const { getCourseList } = useCourse();
  const [attendExamData, setAttendExamData] = useState<AttendedExamsType[]>([]);
  const [isLoading, setIsLoading] = React.useState<boolean>(true);
  const { getAttendExams, getUsers, getSubmittedAnswers } = useAttendExams();
  const [users, setUsers] = useState<{ value: string; label: string }[]>([]);
  const [userId, setUserId] = useState("");
  const [isDialogOpen, setIsDialogOpen] = React.useState(false);
  const [selectedExam, setSelectedExam] = React.useState<ExamInfoType[]>([]);
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);

  useEffect(() => {
    setBreadcrumbItems(
      getBreadCrumbItems(t, t("breadcrumb.attendedExams"), { "": "" }),
    );
  }, [t]);

  useEffect(() => {
    const fetchCourseData = async (): Promise<void> => {
      setIsLoading(true);
      try {
        const courseList = await getCourseList();
        setIsLoading(false);
        const filteredCourses = courseList
          .filter((course) => course.course_id != null && course.short_name)
          .map((course) => ({
            value: course.course_id,
            label: course.short_name,
          }));

        if (filteredCourses.length > 0) {
          setCourseData(filteredCourses);
        }
      } catch (error: unknown) {
        setIsLoading(false);
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err.message,
        });
        console.error("Error fetching data:");
      }
    };

    const fetchUsers = async (): Promise<void> => {
      try {
        const org_id = localStorage.getItem("orgId");
        const orgID = org_id ?? "";
        const fetchedUsers = await getUsers(orgID);
        const filteredUsers = fetchedUsers
          .filter(
            (user) =>
              user.id !== null &&
              user.first_name !== null &&
              user.last_name !== null,
          )
          .map((user) => ({
            value: user.id,
            label: user.first_name + " " + user.last_name,
          }));
        if (filteredUsers.length > 0) {
          setUsers(filteredUsers);
        } else {
          setUsers([]);
        }
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err.message,
        });
      }
    };

    const fetchAttendedExams = async (): Promise<void> => {
      try {
        if (courseId !== null) {
          const attendedExamsData = await getAttendExams(courseId, userId);
          if (attendedExamsData.length > 0) {
            setAttendExamData(attendedExamsData);
          } else {
            setAttendExamData([]);
          }
        }
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err.message,
        });
      }
    };

    fetchCourseData().catch((error) => console.log(error));
    fetchUsers().catch((error) => console.log(error));
    fetchAttendedExams().catch((error) => console.log(error));
  }, [courseId, userId]);

  const handleCourseData = (data: string): void => {
    setCourseId(data);
  };

  const handleUserData = (data: string): void => {
    setUserId(data);
  };

  const openDialog = (quiz_attempt_id: string): void => {
    fetchSubmittedAnswers(quiz_attempt_id).catch((error) => console.log(error));
    setIsDialogOpen(true);
  };

  const closeDialog = (): void => {
    setSelectedExam([]);
    setIsDialogOpen(false);
  };

  const fetchSubmittedAnswers = async (attemptId: string): Promise<void> => {
    try {
      const attempt_Id = attemptId;
      const submittedAnswersData = await getSubmittedAnswers(attempt_Id);
      if (submittedAnswersData?.length > 0) {
        setSelectedExam(submittedAnswersData);
      } else {
        setSelectedExam([]);
      }
    } catch (error) {
      console.log("Error", error);
    }
  };

  return (
    <MainLayout>
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      <div>
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">
            {t("attendedExams.title")}
          </h1>
        </div>
        <div className="border rounded-md p-4 mt-4 bg-[#fff]">
          <div className="w-full flex justify-between space-x-4">
            <div className="w-full flex">
              <div className="w-full sm:w-1/2 md:w-1/4">
                <Label className="text-sm w-1/2">
                  {t("attendedExams.selectCourse")}
                </Label>

                <div className="w-full">
                  <Combobox
                    data={courseData}
                    onSelectChange={handleCourseData}
                  />
                </div>
              </div>
              <div className="w-full sm:w-1/2 md:w-1/4 ms-4">
                <Label>{t("attendedExams.selectUser")}</Label>
                <div className="w-ful">
                  {/* <Combobox data={userData} onSelectChange={handleOnchange}/> */}
                  <Combobox data={users} onSelectChange={handleUserData} />
                </div>
              </div>
            </div>
          </div>
          {isLoading ? (
            <Spinner />
          ) : (
            <div>
              <DataTable
                columns={columns}
                data={attendExamData}
                FilterLabel={t("attendedExams.filterByUserName")}
                FilterBy={"first_name"}
                actions={[
                  {
                    title: t("attendedExams.view"),
                    icon: Eye,
                    color: "#9bbb5c",
                    varient: "icon",
                    isEnable: getPrivilegeList(
                      "Exam",
                      privilegeData.Exam.fetchReviewList,
                    ),
                    handleClick: (val: unknown) => {
                      openDialog((val as ExamInfoType).quiz_attempt_id);
                      // fetchSubmittedAnswers().catch((error) =>
                      //   console.log(error),
                      // );
                    },
                  },
                ]}
              />
            </div>
          )}
        </div>
      </div>
      {isDialogOpen && selectedExam !== null && (
        <Modal
          title={t("attendedExams.userAnswerSheet")}
          header=""
          openDialog={isDialogOpen}
          closeDialog={closeDialog}
          type="max-w-5xl"
        >
          <ExamInfoModal exam={selectedExam as ExamInfoType[]} />
        </Modal>
      )}
    </MainLayout>
  );
}
