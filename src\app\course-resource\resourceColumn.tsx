"use client";
import type { Column, ColumnDef, Row } from "@tanstack/react-table";
import { ArrowUpDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import React from "react";
import type { ResourceList } from "@/types";
import moment from "moment";
interface RowDefinition {
  row: Row<ResourceList>;
}
interface ColumnDefinition {
  column: Column<ResourceList, unknown>;
}
export const resourceColumns: ColumnDef<ResourceList>[] = [
  {
    id: "select",
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "name",
    header: ({ column }: ColumnDefinition): React.JSX.Element => {
      return (
        <Button
          className="px-0"
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Resource Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div>{row.original.name}</div>
    ),
  },
  {
    header: "Created On",
    cell: ({ row }: RowDefinition): React.JSX.Element => {
      const formattedDate = moment
        .utc(row.original.created_at)
        .local()
        .format("DD-MMM-YYYY hh:mm a");

      return <div className="text-align">{formattedDate}</div>;
    },
  },
];
