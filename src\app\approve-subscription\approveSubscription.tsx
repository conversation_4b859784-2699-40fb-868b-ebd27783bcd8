"use client";
import React, { useState, useEffect } from "react";
import { DataTable } from "../../components/ui/data-table/data-table";
import { getColumns } from "./columns";
import { Button } from "@/components/ui/button";
import useSubscription from "@/hooks/useSubscription";
import type {
  SubscriptionValidity,
  PendingSubscription,
  ErrorCatch,
  ToastType,
  ComboData,
  subscriptionListRequest,
  LogUserActivityRequest,
} from "@/types";
import { useToast } from "@/components/ui/use-toast";
import { ORG_KEY, DATE_FORMAT_DMY_HM_AM_PM } from "@/lib/constants";
import { Combobox } from "../../components/ui/combobox";
import { Label } from "@/components/ui/label";
import { Spinner } from "@/components/ui/progressiveLoader";
import type { BaseSyntheticEvent } from "react";
import { Input } from "@/components/ui/input";
import moment from "moment";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";

export default function ApproveSubscription({
  onCancel,
}: {
  onCancel: () => void;
}): React.JSX.Element {
  const { t } = useTranslation();
  const columns = getColumns(t);
  const { toast } = useToast() as ToastType;
  const {
    getSubscriptionListForAdmin,
    getPendingSubscriptions,
    addPendingUsersToPlan,
  } = useSubscription();
  const [pendingSubscriptions, setPendingSubscriptions] = useState<
    PendingSubscription[]
  >([]);
  const [orgId, setOrgId] = useState<string>("");
  const [comboData, setComboData] = useState<ComboData[]>([]);
  const [isLoading, setIsLoading] = React.useState<boolean>(true);
  const [selectedPlanId, setSelectedPlanId] = React.useState<string>("true");
  const [selectedUsers, setSelectedUsers] = React.useState<string[]>([]);
  const [selectedValidity, setSelectedValidity] = React.useState<
    SubscriptionValidity[]
  >([]);
  const [validFrom, setValidFrom] = useState<string>("");
  const [validTo, setValidTo] = useState<string>("");
  const { updateUserActivity } = useLogUserActivity();
  useEffect(() => {
    const orgId = localStorage.getItem(ORG_KEY) ?? "";
    setOrgId(orgId);
    const fetchSubscriptionList = async (): Promise<void> => {
      {
        const org_id = localStorage.getItem(ORG_KEY) ?? "";
        const reqParams = {
          org_id: org_id,
          limit_param: null, //to do
          offset_param: 0, //to do
        };
        try {
          const subscriptions = await getSubscriptionListForAdmin(
            reqParams as subscriptionListRequest,
          );
          setIsLoading(false);

          if (subscriptions.result != null) {
            const comboSubscriptions: ComboData[] = subscriptions.result
              .map((item) => ({
                value: item.id,
                label: item.name,
                isDisabled:
                  item.is_expired ||
                  item.subscription_plan_status === "inactive",
              }))
              .sort((item, data) => item.label.localeCompare(data.label));
            setComboData(comboSubscriptions);
            const planValidity: SubscriptionValidity[] =
              subscriptions.result.map((item) => ({
                id: item.id,
                valid_from: item.valid_from,
                valid_to: item.valid_to,
              }));
            setSelectedValidity(planValidity);
          }
        } catch (error) {
          const err = error as ErrorCatch;
          toast({
            variant: ERROR_MESSAGES.toast_variant_destructive,
            title: t("errorMessages.toast_error_title"),
            description: err?.details,
          });
        }
      }
    };
    fetchSubscriptionList().catch((error) => console.log(error));
  }, []);

  const handleSeletedValue = (selectedValue: string): void => {
    setSelectedPlanId(selectedValue);
    const validity = selectedValidity.find((item) => item.id === selectedValue);
    const fromDate = validity?.valid_from ?? "";
    const formattedFromDate = moment
      .utc(fromDate)
      // .local()
      .format(DATE_FORMAT_DMY_HM_AM_PM);
    setValidFrom(formattedFromDate);
    const toDate = validity?.valid_to ?? "";
    const formattedtoDate = moment
      .utc(toDate)
      // .local()
      .format(DATE_FORMAT_DMY_HM_AM_PM);
    setValidTo(formattedtoDate);
    fetchPendingSubscriptions(selectedValue).catch((error) =>
      console.log(error),
    );
  };

  const fetchPendingSubscriptions = async (planId: string): Promise<void> => {
    try {
      const params = {
        org_id: orgId,
        plan_id: planId,
      };
      const pendingSubscriptionData = await getPendingSubscriptions(params);
      setIsLoading(false);
      if (pendingSubscriptionData !== null) {
        const userList = pendingSubscriptionData?.result?.user_list;
        setPendingSubscriptions(userList);
      } else {
        setPendingSubscriptions([]);
      }
    } catch (error) {
      const err = error as ErrorCatch;
      console.error(err);
    }
  };

  const handleSelectedData = (selData: PendingSubscription[]): void => {
    const selectedUsers = selData.map((item) => item.user_id);
    setSelectedUsers(selectedUsers);
  };

  const addPendingSubscriptionsToPlan = async (
    e: BaseSyntheticEvent,
  ): Promise<void> => {
    console.log(e);
    if (selectedUsers.length == 0) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.user_not_select_msg"),
      });
    } else {
      try {
        const params = {
          org_id: orgId,
          plan_id: selectedPlanId,
          user_ids: selectedUsers,
        };
        const result = await addPendingUsersToPlan(params);
        if (result.status === "success") {
          toast({
            variant: SUCCESS_MESSAGES.toast_variant_default,
            title: t("successMessages.pending_subscription"),
            description: t("successMessages.subscription_approved_msg"),
          });
          onCancel();
          const params = {
            activity_type: "Subscription",
            screen_name: "Subscription ",
            action_details: "Subscription Approved  ",
            target_id: selectedPlanId,
            log_result: "SUCCESS",
          };
          void updateLogUserActivity(params).catch((error) => {
            console.error(error);
          });
        }
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.details,
        });
        const params = {
          activity_type: "Subscription",
          screen_name: "Subscription ",
          action_details: "Failed To Approve Subscription  ",
          target_id: selectedPlanId,
          log_result: "ERROR",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      }
    }
  };
  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };
  return (
    <div className="w-full border rounded-md p-4 ">
      <div className="flex space-x-4 items-center">
        <div className="w-full sm:w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2">
          <Label>{t("subscriptionPlan.selectPlan")}</Label>
          <Combobox data={comboData} onSelectChange={handleSeletedValue} />
        </div>
        <div className="w-full sm:w-1/2 md:w-1/4 lg:w-1/4 xl:w-1/4 mb-4 sm:mb-0">
          <Label>{t("subscriptionPlan.validFrom")}</Label>
          <Input type="text" autoComplete="off" value={validFrom} />
        </div>
        <div className="w-full sm:w-1/2 md:w-1/4 lg:w-1/4 xl:w-1/4 mb-4 sm:mb-0">
          <Label>{t("subscriptionPlan.validTo")}</Label>
          <Input type="text" autoComplete="off" value={validTo} />
        </div>
      </div>

      {isLoading ? (
        <Spinner />
      ) : (
        <div className="mt-4">
          <DataTable
            columns={columns}
            data={pendingSubscriptions ?? []}
            FilterLabel={t("subscriptionPlan.filterByUserName")}
            FilterBy={"user_name"}
            actions={[]}
            onSelectedDataChange={(value: unknown) =>
              handleSelectedData(value as PendingSubscription[])
            }
          />
          {pendingSubscriptions !== null &&
            pendingSubscriptions?.length > 0 && (
              <div className="w-full flex justify-end mt-6 gap-3">
                <div>
                  <Button
                    className="w-full sm:w-auto bg-[#33363F]"
                    onClick={onCancel}
                  >
                    {t("buttons.cancel")}
                  </Button>
                </div>
                <div>
                  <Button
                    className="w-full sm:w-auto bg-[#9FC089]"
                    type="submit"
                    onClick={(e: BaseSyntheticEvent) => {
                      addPendingSubscriptionsToPlan(e).catch((error) =>
                        console.log(error),
                      );
                    }}
                  >
                    {t("subscriptionPlan.approve")}
                  </Button>
                </div>
              </div>
            )}
        </div>
      )}
    </div>
  );
}
