import { supabase } from "../lib/client";
import { views, rpc } from "../lib/apiConfig";
import type {
  AddQuestionApiResultType,
  QuestionFormSubmitType,
  ErrorType,
  QuestionBankData,
  TopicDataType,
  QuestionCategoryRequest,
  QuestionCategoryResult,
  EditQuestionCategory,
  EditQuestionCategoryResponse,
  DeleteQuestionCategory,
  SuccessMessage,
  PublishQuestionCategory,
  PublishQuestion,
  PublishQuestions,
  ExcelImportQuestion,
  ExcelQuestionResponse,
} from "@/types";

interface UseQuestionBankeReturn {
  getQuestionBankList: (qcatId?: string) => Promise<QuestionBankData[]>;
  getUnPublishedQuestions: (qcatId?: string) => Promise<QuestionBankData[]>;
  getPublishedQuestions: (qcatId?: string) => Promise<QuestionBankData[]>;
  getQuestionCategory: () => Promise<TopicDataType[]>;
  getPublishedQuestionCategory: () => Promise<TopicDataType[]>;
  addQuestions: (
    param?: QuestionFormSubmitType,
  ) => Promise<AddQuestionApiResultType>;
  addQuestionCategory: (
    param?: QuestionCategoryRequest,
  ) => Promise<QuestionCategoryResult>;
  updateQuestionCategory: (
    params?: EditQuestionCategory,
  ) => Promise<EditQuestionCategoryResponse>;
  deleteQuestionCategory: (
    params?: DeleteQuestionCategory,
  ) => Promise<SuccessMessage>;
  editQuestions: (
    param?: QuestionFormSubmitType,
  ) => Promise<AddQuestionApiResultType>;
  publishQuestionCategory: (
    params?: PublishQuestionCategory,
  ) => Promise<SuccessMessage>;
  publishQuestion: (params?: PublishQuestion) => Promise<SuccessMessage>;
  publishQuestions: (params?: PublishQuestions) => Promise<SuccessMessage>;
  addExcelQuestions: (
    params?: ExcelImportQuestion,
  ) => Promise<ExcelQuestionResponse>;
}

const useQuestionBank = (): UseQuestionBankeReturn => {
  async function getQuestionBankList(
    qcatId?: string,
    questId?: string,
  ): Promise<QuestionBankData[]> {
    try {
      const questionBankView = views?.questionBank;
      const org_id = localStorage.getItem("orgId");

      const exeQuery = supabase
        .from(questionBankView)
        .select()
        .eq("org_id", org_id);

      if (qcatId != null) {
        await exeQuery.eq("question_category_id", qcatId);
      }
      if (questId != null) {
        await exeQuery.eq("question_id", questId);
      }
      const { data, error } = await exeQuery;
      if (error) {
        throw new Error(error.details);
      }

      return data as QuestionBankData[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function getUnPublishedQuestions(
    qcatId?: string,
    questId?: string,
  ): Promise<QuestionBankData[]> {
    try {
      const questionBankView = views?.questionBank;
      const org_id = localStorage.getItem("orgId");

      const exeQuery = supabase
        .from(questionBankView)
        .select()
        .eq("org_id", org_id)
        .eq("question_publish_status", "Draft");

      if (qcatId != null) {
        await exeQuery.eq("question_category_id", qcatId);
      }
      if (questId != null) {
        await exeQuery.eq("question_id", questId);
      }
      const { data, error } = await exeQuery;
      if (error) {
        throw new Error(error.details);
      }

      return data as QuestionBankData[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function getPublishedQuestions(
    qcatId?: string,
    questId?: string,
  ): Promise<QuestionBankData[]> {
    try {
      const questionBankView = views?.questionBank;
      const org_id = localStorage.getItem("orgId");

      const exeQuery = supabase
        .from(questionBankView)
        .select()
        .eq("org_id", org_id)
        .eq("question_publish_status", "Published");

      if (qcatId != null) {
        await exeQuery.eq("question_category_id", qcatId);
      }
      if (questId != null) {
        await exeQuery.eq("question_id", questId);
      }
      const { data, error } = await exeQuery;
      if (error) {
        throw new Error(error.details);
      }

      return data as QuestionBankData[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function getPublishedQuestionCategory(): Promise<TopicDataType[]> {
    try {
      const requestBody = {
        org_id: localStorage.getItem("orgId"),
        filter_data: 1,
      };
      const exeQuery = supabase.rpc<string, null>(
        rpc.getQuesCategoryHierarchy,
        requestBody,
      );
      const { data, error } = await exeQuery;

      if (error) {
        throw new Error(error.details);
      }
      const publishedCategories = (data as TopicDataType[]).filter(
        (category) => category.publish_status === "Published",
      );
      return publishedCategories;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function getQuestionCategory(): Promise<TopicDataType[]> {
    try {
      const requestBody = {
        org_id: localStorage.getItem("orgId"),
      };
      const exeQuery = supabase.rpc<string, null>(
        rpc.getQuesCategoryHierarchy,
        requestBody,
      );
      const { data, error } = await exeQuery;

      if (error) {
        throw new Error(error.details);
      }
      return data as TopicDataType[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function addQuestions(
    param?: QuestionFormSubmitType,
  ): Promise<AddQuestionApiResultType> {
    console.log(param);
    try {
      const { data, error } = (await supabase.rpc(
        rpc.insertQuizQuestion,
        param,
      )) as { data: AddQuestionApiResultType; error: ErrorType | null };
      console.log(data);
      if (error) {
        throw new Error(error.details);
      }
      return data as AddQuestionApiResultType;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function editQuestions(
    param?: QuestionFormSubmitType,
  ): Promise<AddQuestionApiResultType> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.updateQuizQuestion,
        param,
      )) as { data: AddQuestionApiResultType; error: ErrorType | null };
      console.log(data);
      if (error) {
        throw new Error(error.details);
      }
      return data as AddQuestionApiResultType;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function addQuestionCategory(
    param?: QuestionCategoryRequest,
  ): Promise<QuestionCategoryResult> {
    console.log(param);
    try {
      const { data, error } = (await supabase.rpc(
        rpc.insertQuestionCategory,
        param,
      )) as { data: QuestionCategoryResult; error: ErrorType | null };
      console.log(data);
      if (error) {
        throw new Error(error.details);
      }
      return data as QuestionCategoryResult;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function updateQuestionCategory(
    params?: EditQuestionCategory,
  ): Promise<EditQuestionCategoryResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.updateQuestionCategory,
        params,
      )) as { data: EditQuestionCategoryResponse; error: ErrorType | null };
      if (error) {
        throw new Error(error.details);
      }
      return data as EditQuestionCategoryResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function deleteQuestionCategory(
    params?: DeleteQuestionCategory,
  ): Promise<SuccessMessage> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.deleteQuestionCategory,
        params,
      )) as { data: SuccessMessage; error: ErrorType | null };
      if (error) {
        throw new Error(error.details);
      }
      return data as SuccessMessage;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function publishQuestionCategory(
    params?: PublishQuestionCategory,
  ): Promise<SuccessMessage> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.publishQuestionCategory,
        params,
      )) as { data: SuccessMessage; error: ErrorType | null };
      if (error) {
        throw new Error(error.details);
      }
      return data as SuccessMessage;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function publishQuestion(
    params?: PublishQuestion,
  ): Promise<SuccessMessage> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.publishQuestion,
        params,
      )) as { data: SuccessMessage; error: ErrorType | null };
      if (error) {
        throw new Error(error.details);
      }
      return data as SuccessMessage;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function publishQuestions(
    params?: PublishQuestions,
  ): Promise<SuccessMessage> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.publishQuestions,
        params,
      )) as { data: SuccessMessage; error: ErrorType | null };
      if (error) {
        throw new Error(error.details);
      }
      return data as SuccessMessage;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function addExcelQuestions(
    params?: ExcelImportQuestion,
  ): Promise<ExcelQuestionResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.insertQuizQuestionsExcelArray,
        params,
      )) as { data: ExcelQuestionResponse; error: ErrorType | null };
      if (error) {
        throw new Error(error.details);
      }
      return data as ExcelQuestionResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  return {
    getQuestionBankList,
    getUnPublishedQuestions,
    getPublishedQuestions,
    getQuestionCategory,
    addQuestions,
    addQuestionCategory,
    updateQuestionCategory,
    deleteQuestionCategory,
    editQuestions,
    publishQuestionCategory,
    publishQuestion,
    publishQuestions,
    getPublishedQuestionCategory,
    addExcelQuestions,
  };
};

export default useQuestionBank;
