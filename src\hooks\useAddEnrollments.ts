import { supabase } from "../lib/client";
import type {
  AddEnrollments,
  AddEnrollmentsResult,
  AddEnrollmentResponse,
  AddUserEnrollment,
  ErrorType,
} from "@/types";
import { rpc } from "@/lib/apiConfig";

interface UseAddEnrollmentsReturn {
  getNotEnrolledUsers: (
    params: AddEnrollments,
  ) => Promise<AddEnrollmentsResult[]>;

  addNewEnrollment: (
    params: AddUserEnrollment,
  ) => Promise<AddEnrollmentResponse>;
}

const useAddEnrollments = (): UseAddEnrollmentsReturn => {
  async function getNotEnrolledUsers(
    params: AddEnrollments,
  ): Promise<AddEnrollmentsResult[]> {
    try {
      const { data, error } = (await supabase.rpc<string, null>(
        rpc.notEnrolledUsers,
        params,
      )) as {
        data: AddEnrollmentsResult[];
        error: ErrorType | null;
      };

      if (error?.details !== null && error?.details !== undefined) {
        throw new Error(error?.details);
      }
      return data as AddEnrollmentsResult[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function addNewEnrollment(
    params: AddUserEnrollment,
  ): Promise<AddEnrollmentResponse> {
    try {
      const { data, error } = (await supabase.rpc<string, null>(
        rpc.addEnrollment,
        params,
      )) as {
        data: AddEnrollmentResponse;
        error: ErrorType | null;
      };

      if (error) {
        throw new Error(error.details);
      }
      return data as AddEnrollmentResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  return {
    getNotEnrolledUsers,
    addNewEnrollment,
  };
};

export default useAddEnrollments;
