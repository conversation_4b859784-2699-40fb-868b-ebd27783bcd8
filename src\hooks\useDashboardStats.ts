import { supabase } from "../lib/client";
import { functions, rpc, views } from "../lib/apiConfig";
import type {
  CheckpointsCountReturnType,
  CheckpointsCourseStats,
  CheckpointsUserStatsReturnType,
  CheckpointsprogressReturnType,
  DashboardStatsReturnType,
  ErrorType,
  LatestEnrollmentResponse,
  MostWatchedUsersReturnType,
  VideoProgresslistReturnType,
  ConfigurationRequest,
  ConfigurationResponse,
  SentEmailRequest,
  SentEmailResponse,
  CourseProgressRequest,
  CourseProgressResponse,
  UsersNotEnrolled,
} from "@/types";
import { ACCESS_TOKEN } from "@/lib/constants";

interface UseDashboardStatsReturn {
  getStatsCount: (org_id?: string | null) => Promise<DashboardStatsReturnType>;
  getMostWatchedUsers: (
    orgId?: string | null,
    courseId?: string | null,
  ) => Promise<MostWatchedUsersReturnType>;
  getCheckPointsProgress: (
    orgId?: string | null,
    courseId?: string | null,
  ) => Promise<CheckpointsprogressReturnType>;
  getCheckPointsProgressUserWise: (
    orgId?: string | null,
    userId?: string | null,
  ) => Promise<CheckpointsprogressReturnType>;
  getCheckPointsCount: (
    orgId?: string | null,
    courseId?: string | null,
  ) => Promise<CheckpointsCountReturnType>;
  getCheckPointsCountUserWise: (
    orgId?: string | null,
    userId?: string | null,
  ) => Promise<CheckpointsCountReturnType>;
  getCourseWiseStatistics: (
    orgId?: string | null,
    userId?: string | null,
  ) => Promise<CheckpointsCourseStats[]>;
  getUserWiseStatistics: (
    orgId?: string | null,
    courseId?: string | null,
    userId?: string | null,
  ) => Promise<CheckpointsUserStatsReturnType>;
  getWatchedUserStatistics: (
    orgId?: string | null,
    courseId?: string | null,
  ) => Promise<VideoProgresslistReturnType>;
  getLatestEnrollments: () => Promise<LatestEnrollmentResponse[]>;
  getConfigurationData: (
    params?: ConfigurationRequest,
  ) => Promise<ConfigurationResponse>;
  sentEmail: (params: SentEmailRequest) => Promise<SentEmailResponse>;
  getCourseCompletedProgress: (
    params: CourseProgressRequest,
  ) => Promise<CourseProgressResponse>;
  getNotEnrolledUsers: (
    params: CourseProgressRequest,
  ) => Promise<UsersNotEnrolled[]>;
}

const useDashboardStatsViews = (): UseDashboardStatsReturn => {
  async function getStatsCount(
    org_id?: string | null,
  ): Promise<DashboardStatsReturnType> {
    try {
      if (org_id !== null && org_id !== "") {
        const requestBody = {
          org_id: org_id,
        };

        const { data, error } = await supabase.rpc<string, null>(
          views?.dashboardStatsCount,
          requestBody,
        );
        if (error) {
          throw new Error(error.message);
        }

        return data as DashboardStatsReturnType;
      } else {
        throw new Error("Org Id null");
      }
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function getMostWatchedUsers(
    orgId?: string | null,
    courseId?: string | null,
  ): Promise<MostWatchedUsersReturnType> {
    try {
      if (orgId !== null && orgId !== "") {
        const requestBody = {
          org_id: orgId,
          course_id: courseId,
        };

        const { data, error } = await supabase.rpc<string, null>(
          views?.mostWatcchedUsers,
          requestBody,
        );
        if (error) {
          throw new Error(error.message);
        }

        return data as MostWatchedUsersReturnType;
      } else {
        throw new Error("Org Id null");
      }
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function getCheckPointsProgress(
    orgId?: string | null,
    courseId?: string | null,
  ): Promise<CheckpointsprogressReturnType> {
    try {
      if (orgId !== null && orgId !== "") {
        const requestBody = {
          org_id: orgId,
          course_id: courseId,
        };

        const { data, error } = await supabase.rpc<string, null>(
          views?.checkpointsProgress,
          requestBody,
        );
        if (error) {
          throw new Error(error.message);
        }

        return data as CheckpointsprogressReturnType;
      } else {
        throw new Error("Org Id null");
      }
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function getCheckPointsProgressUserWise(
    orgId?: string | null,
    userId?: string | null,
  ): Promise<CheckpointsprogressReturnType> {
    try {
      if (orgId !== null && orgId !== "") {
        const requestBody = {
          org_id: orgId,
          user_id: userId,
        };

        const { data, error } = await supabase.rpc<string, null>(
          views?.checkpointsProgress,
          requestBody,
        );
        if (error) {
          throw new Error(error.message);
        }

        return data as CheckpointsprogressReturnType;
      } else {
        throw new Error("Org Id null");
      }
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function getCheckPointsCount(
    orgId?: string | null,
    courseId?: string | null,
  ): Promise<CheckpointsCountReturnType> {
    try {
      if (orgId !== null && orgId !== "") {
        const requestBody = {
          org_id: orgId,
          course_id: courseId,
        };

        const { data, error } = await supabase.rpc<string, null>(
          views?.checkpointsCount,
          requestBody,
        );
        if (error) {
          throw new Error(error.message);
        }

        return data as CheckpointsCountReturnType;
      } else {
        throw new Error("Org Id null");
      }
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function getCheckPointsCountUserWise(
    orgId?: string | null,
    userId?: string | null,
  ): Promise<CheckpointsCountReturnType> {
    try {
      if (orgId !== null && orgId !== "") {
        const requestBody = {
          org_id: orgId,
          user_id: userId,
        };

        const { data, error } = await supabase.rpc<string, null>(
          views?.checkpointsCount,
          requestBody,
        );
        if (error) {
          throw new Error(error.message);
        }

        return data as CheckpointsCountReturnType;
      } else {
        throw new Error("Org Id null");
      }
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function getCourseWiseStatistics(
    orgId?: string | null,
    userId?: string | null,
  ): Promise<CheckpointsCourseStats[]> {
    try {
      if (orgId !== null && orgId !== "") {
        const requestBody = {
          org_id: orgId,
          user_id: userId,
        };

        const { data, error } = await supabase.rpc<string, null>(
          views?.courseWiseStatistics,
          requestBody,
        );
        if (error) {
          throw new Error(error.message);
        }

        return data as CheckpointsCourseStats[];
      } else {
        throw new Error("Org Id null");
      }
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function getUserWiseStatistics(
    orgId?: string | null,
    courseId?: string | null,
    userId?: string | null,
  ): Promise<CheckpointsUserStatsReturnType> {
    try {
      if (orgId !== null && orgId !== "") {
        const requestBody = {
          org_id: orgId,
          course_id: courseId,
          user_id: userId,
        };

        const { data, error } = await supabase.rpc<string, null>(
          views?.userWiseStatistics,
          requestBody,
        );
        if (error) {
          throw new Error(error.message);
        }

        return data as CheckpointsUserStatsReturnType;
      } else {
        throw new Error("Org Id null");
      }
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function getWatchedUserStatistics(
    orgId?: string | null,
    courseId?: string | null,
  ): Promise<VideoProgresslistReturnType> {
    const params = {
      org_id: orgId,
      course_id: courseId,
      course_module_id: null,
    };
    try {
      const { data, error } = (await supabase.rpc<string, null>(
        rpc.getCourseVideoProgressList,
        params,
      )) as { data: VideoProgresslistReturnType; error: ErrorType | null };
      if (error) {
        throw new Error(error.details);
      }
      console.log("data", data);

      return data as VideoProgresslistReturnType;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function getLatestEnrollments(): Promise<LatestEnrollmentResponse[]> {
    try {
      const foldersList = views?.latestEnrollments ?? "";
      const exeQuery = supabase.from(foldersList).select();
      const { data, error } = await exeQuery;
      if (error) {
        throw new Error(error.details);
      }
      return data as LatestEnrollmentResponse[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function getConfigurationData(
    params?: ConfigurationRequest,
  ): Promise<ConfigurationResponse> {
    try {
      const { data, error } = (await supabase.rpc<string, null>(
        rpc.getConfigurations,
        params,
      )) as { data: ConfigurationResponse; error: ErrorType | null };
      if (error) {
        throw new Error(error.details);
      }
      return data as ConfigurationResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function sentEmail(
    params: SentEmailRequest,
  ): Promise<SentEmailResponse> {
    try {
      const formData = new FormData();
      formData.append("recipient", params.email);
      formData.append("subject", params.subject as string);
      formData.append("file", params.file as File); // Assuming params.file is a File or Blob
      formData.append("mail_content", params.mail_content as string);
      const response = await fetch(functions.sendEmailWithAttachment, {
        method: "POST",
        headers: {
          apikey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY as string,
          Authorization: "Bearer " + localStorage.getItem(ACCESS_TOKEN),
        },
        body: formData,
      });

      if (response.ok) {
        const data = (await response.json()) as SentEmailResponse;
        return data;
      } else {
        throw new Error("Failed to send notification");
      }
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function getCourseCompletedProgress(
    params?: CourseProgressRequest,
  ): Promise<CourseProgressResponse> {
    try {
      const { data, error } = (await supabase.rpc<string, null>(
        rpc.courseCompletedUsers,
        params,
      )) as { data: CourseProgressResponse; error: ErrorType | null };
      if (error) {
        throw new Error(error.details);
      }
      return data as CourseProgressResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function getNotEnrolledUsers(
    params?: CourseProgressRequest,
  ): Promise<UsersNotEnrolled[]> {
    try {
      const { data, error } = (await supabase.rpc<string, null>(
        rpc.getNotEnrolledUsersList,
        params,
      )) as { data: UsersNotEnrolled[]; error: ErrorType | null };
      if (error) {
        throw new Error(error.details);
      }
      return data as UsersNotEnrolled[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  return {
    getStatsCount,
    getMostWatchedUsers,
    getCheckPointsProgress,
    getCheckPointsProgressUserWise,
    getCheckPointsCount,
    getCheckPointsCountUserWise,
    getCourseWiseStatistics,
    getUserWiseStatistics,
    getWatchedUserStatistics,
    getLatestEnrollments,
    getConfigurationData,
    sentEmail,
    getCourseCompletedProgress,
    getNotEnrolledUsers,
  };
};

export default useDashboardStatsViews;
