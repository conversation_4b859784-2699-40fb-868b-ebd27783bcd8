"use client";
import type { CheckpointsCourseStats } from "@/types";
import React from "react";
import { DataTable } from "@/components/ui/data-table/data-table";
import { columns } from "./courseStatisticsColumns";
import { Button } from "@/components/ui/button";
import { useTranslation } from "react-i18next";

const ViewAllCourseResources = ({
  courseStatisticsData,
  onCancel,
}: {
  courseStatisticsData: CheckpointsCourseStats[];
  onCancel: () => void;
}): React.JSX.Element => {
  const { t } = useTranslation();

  const handleCancel = (): void => {
    onCancel();
  };

  return (
    <>
      <DataTable
        columns={columns}
        data={courseStatisticsData}
        FilterLabel={"Course Name"}
        FilterBy={"course_short_name"}
        actions={[]}
        onSelectedDataChange={() => {}}
      />
      <div className="flex justify-end mt-4">
        <Button
          className="w-full sm:w-auto bg-[#33363F]"
          onClick={handleCancel}
        >
          {String(t("buttons.cancel"))}
        </Button>
      </div>
    </>
  );
};

export default ViewAllCourseResources;
