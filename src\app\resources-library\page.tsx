"use client";
import React, { useState, useEffect } from "react";
import { getColumns } from "./columns";
import { DataTable } from "../../components/ui/data-table/data-table";
import MainLayout from "../layout/mainlayout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Eye, Link, PenLineIcon, PlusIcon, Repeat } from "lucide-react";
import type {
  ComboData,
  moduleList,
  ErrorCatch,
  ResourceLibrary,
  ResourceList,
  ResourceLibraryData,
  InnerItem,
  ToastType,
} from "@/types";
import "../../styles/main.css";
import { Spinner } from "@/components/ui/progressiveLoader";
import { Combobox } from "@/components/ui/combobox";
import { Modal } from "@/components/ui/modal";
import AddResources from "./add-resources";
import { Edit, Archive } from "lucide-react";
import CourseResources from "@/components/courseResources/courseResources";
import useResourceLibrary from "@/hooks/useResourceLibrary";
import { useToast } from "@/components/ui/use-toast";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { CourseResourcesAddfile } from "@/components/resources-add-file";
import ResourcesAddPage from "@/components/resources-add-page";
import DeleteResources from "./delete-resources";
import ApproveResource from "./approve-resources";
import getPrivilegeList from "@/hooks/useCheckPrivilege";
import { DEFAULT_FOLDER_ID, privilegeData } from "@/lib/constants";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumb";
import LinkResources from "@/components/link-resources/link-resources";
import useCourse from "@/hooks/useCourse";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { UpdateUrl } from "@/components/updateUrl";
import UpdatePgaeContent from "@/components/updatePageContent";
import AddExamFromResource from "@/components/resources-add-exam";
import LinkExam from "./linkExam";
import { getExamColumns } from "./examColumns";
import FolderManager from "./FolderManager";
import { SUCCESS_MESSAGES } from "@/lib/messages";
// import ExamDetails from "@/components/examDetails/examDetails";
import { useTranslation } from "react-i18next";
import { ERROR_MESSAGES } from "@/lib/messages";

export default function GroupsAddPage(): React.JSX.Element {
  const { t } = useTranslation();
  const { toast } = useToast() as ToastType;
  const examColumns = getExamColumns(t);
  const columns = getColumns(t);
  const [isLoading, setIsLoading] = React.useState<boolean>(true);
  const [label, setLabel] = useState<string>("File");
  const [file, setFile] = useState<string>("");
  const [status, setStatus] = useState<string>("0");
  const [isOpenModal, setIsOpenModal] = useState<boolean>(false);
  const [isDialogOpen, setIsDialogOpen] = useState<boolean>(false);
  const [resourceLibrary, setResourceLibrary] = useState<ResourceList[]>([]);
  const [selectedExamData, setSelectedExamData] = useState<ResourceList>();
  const [rescourceId, setResourceId] = useState<string>("");
  const [comboResources, setComboResources] = useState<ComboData[]>([]);
  const [selectedData, setSelectedData] = useState<ResourceLibraryData>();
  const [isOpenEditDialog, setIsOpenEditDialog] = useState<boolean>(false);
  const [isOpenDeleteDialog, setIsOpenDeleteDialog] = useState<boolean>(false);
  const [isCourseLinkModalOpen, setIsCourseLinkModalOpen] =
    useState<boolean>(false);
  const [isExamLinkOpen, setIsExamLinkOpen] = useState<boolean>(false);
  const [deleteId, setDeleteId] = useState<string>("");
  const [isEditStatus, setEditStatus] = useState<boolean>(false);
  const [approveDialogOpen, setApproveDialogOpen] = useState<boolean>(false);
  const [reloadOnApprove, setReloadOnApprove] = useState<boolean>(false);
  const { getResourceList, manageFolder } = useResourceLibrary();
  const [approveId, setApproveId] = useState<string>("");
  const [publishedStatus, setPublishedStatus] = useState<string>("");
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);
  const { listFolderFromLibrary } = useCourse();
  const [comboData, setComboData] = useState<ComboData[]>([]);
  const [isOpenChnageUrlModal, setOpenChangeUrlModal] =
    useState<boolean>(false);
  const [folderId, setFolderId] = useState<string>(DEFAULT_FOLDER_ID);
  const [isOpenExam, setIsOpenExam] = useState<boolean>(false);
  const [isFolderModalOpen, setIsFolderModalOpen] = useState(false);
  const [editingFolderId, setEditingFolderId] = useState<string | null>(null);
  const [editingFolderName, setEditingFolderName] = useState<string>("");

  useEffect(() => {
    setBreadcrumbItems(
      getBreadCrumbItems(t, t("breadcrumb.resources"), { "": "" }),
    );

    const data = localStorage.getItem("moduleList") as string;
    const displayData = JSON.parse(data) as moduleList[];
    const datas: ComboData[] = displayData.map((item: moduleList) => ({
      value: item.id,
      label: item.display_name,
    }));
    setComboResources(datas as ComboData[]);
    setFile(datas[0]?.value as string);
    setLabel(datas[0]?.label as string);
    setIsLoading(false);
    setIsOpenModal(false);
    void getFoldersList();
  }, [t]);

  useEffect(() => {
    if (folderId?.length > 0 && file?.length > 0) {
      getResourceLibrary();
    }
  }, [
    file,
    folderId,
    status,
    isOpenModal,
    isOpenDeleteDialog,
    reloadOnApprove,
  ]);
  const getFoldersList = async (): Promise<void> => {
    try {
      const orgId = localStorage.getItem("orgId");
      const folderList = await listFolderFromLibrary(orgId as string);
      const comboData: ComboData[] = folderList
        ?.map((cat) => ({
          value: cat.folder_id,
          label: cat.folder_name,
        }))
        .sort((a, b) => a.label.localeCompare(b.label));
      setComboData(comboData);
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  };

  const addResourceModal = (): void => {
    setIsOpenModal(true);
  };
  const getCourseStatus = (res: string): void => {
    setStatus(res);
  };
  const getResourceLibrary = (): void => {
    const fetchData = async (): Promise<void> => {
      try {
        const requestBody = {
          org_id: localStorage.getItem("orgId") ?? "",
          module_id: file,
          linked: status,
          limit_val: 1000,
          offset_val: 0,
          folder_id: folderId,
        };

        const resource: ResourceLibrary = await getResourceList(requestBody);
        if (resource.resource_list != null) {
          resource.resource_list.map((item) => {
            item.file_type = label;
          });
          resource.resource_list.sort((item, data) => {
            return item.name.localeCompare(data.name);
          });
          resource.resource_list.map((item) => {
            if (item.status === "Draft") {
              item.hideIcon = false;
            } else {
              item.hideIcon = true;
            }
          });
          resource.resource_list.map((item) => {
            if (item.status === "Draft") {
              item.hideEditDelete = false;
            } else {
              item.hideEditDelete = true;
            }
          });
          resource.resource_list.map((item) => {
            if (item.is_linked === 1) {
              item.hideViews = false;
            } else {
              item.hideViews = true;
            }
          });
          resource.resource_list.map((item) => {
            if (item.status === "Draft") {
              item.hideEdit = false;
            } else {
              item.hideEdit = true;
            }
          });
          setResourceLibrary(resource.resource_list);
        } else {
          setResourceLibrary([]);
        }
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
        console.error("Error fetching data:");
      }
    };
    fetchData().catch((error) => console.log(error));
  };

  const reloadModal = (): void => {
    setIsOpenModal(false);
    setReloadOnApprove(!reloadOnApprove);
    void getFoldersList();
  };
  const onCancelModal = (): void => {
    setIsOpenModal(false);
  };
  const handleResourceChange = (res: string): void => {
    const label = findLabelByValue(res);
    setLabel(label);
    setFile(res);
  };

  const findLabelByValue = (value: string): string => {
    const resource = comboResources.find((item) => item.value === value)?.label;
    return resource as string;
  };

  const dialogOpen = (resourceID: string): void => {
    setResourceId(resourceID);
    setIsDialogOpen(true);
  };
  const linkDialogOpen = (data: ResourceList): void => {
    setSelectedExamData(data);
    setResourceId(data.id);
    setIsCourseLinkModalOpen(true);
  };

  const examLinkDialogOpen = (data: ResourceList): void => {
    setSelectedExamData(data);
    setResourceId(data.id);
    setIsExamLinkOpen(true);
  };

  const dialogClose = (): void => {
    setIsDialogOpen(false);
    setIsCourseLinkModalOpen(false);
  };
  const linkDialogClose = (): void => {
    setIsCourseLinkModalOpen(false);
  };
  const editResources = (data: ResourceLibraryData): void => {
    setSelectedData(data);
    setIsOpenEditDialog(true);
    setEditStatus(true);
    setOpenChangeUrlModal(false);
  };
  const updateUrl = (data: ResourceLibraryData): void => {
    setSelectedData(data);
    setOpenChangeUrlModal(true);
  };
  const closeEditDialog = (): void => {
    setIsOpenEditDialog(false);
    setOpenChangeUrlModal(false);
  };

  const deleteResources = (data: ResourceList): void => {
    setDeleteId(data.id);
    setIsOpenDeleteDialog(true);
  };

  const closeDeleteDialog = (): void => {
    setIsOpenDeleteDialog(false);
  };
  const approveDialog = (data: ResourceList): void => {
    console.log(data);
    setPublishedStatus(data.status as string);
    setApproveDialogOpen(!approveDialogOpen);
    setApproveId(data.id);
  };
  const isDisabledApprove = (resourceID: string): void => {
    setApproveId(resourceID);
  };
  const closeApproveDialog = (): void => {
    setApproveDialogOpen(!approveDialogOpen);
  };
  const onReloadAfterLink = (): void => {
    setReloadOnApprove(!reloadOnApprove);
    setIsCourseLinkModalOpen(false);
  };
  const saveApprovalDialog = (): void => {
    setReloadOnApprove(!reloadOnApprove);
  };
  const getColor = (): string => {
    return resourceLibrary.some((item) => item.is_linked < 1)
      ? "#808080"
      : "#008000";
  };

  const handleAddExam = (): void => {
    setIsOpenExam(true);
  };

  const closeExamDialog = (): void => {
    setIsOpenExam(false);
    setReloadOnApprove(!reloadOnApprove);
  };

  const closeExamLinkDialog = (): void => {
    setIsExamLinkOpen(false);
  };

  const handleEditClick = (folderId: string, currentName: string): void => {
    setEditingFolderId(folderId);
    setEditingFolderName(currentName);
  };

  const handleEditCancel = (): void => {
    setEditingFolderId(null);
    setEditingFolderName("");
  };

  const handleEditSave = async (): Promise<void> => {
    const reqParam = {
      folder_id: editingFolderId as string,
      folder_name: editingFolderName as string,
    };
    const res = await manageFolder(reqParam);
    toast({
      variant: SUCCESS_MESSAGES.toast_variant_default,
      title: t("successMessages.toast_success_title"),
      description: t("successMessages.folderNameUpdate"),
    });
    await getFoldersList();
    console.log(res);
    setEditingFolderId(null);
    setEditingFolderName("");
  };

  return (
    <MainLayout>
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      <div className="flex items-center pb-5">
        <h1 className="text-2xl font-semibold tracking-tight pe-2">
          {t("resourceLibrary.title")}
        </h1>

        <div className="flex items-center space-x-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  onClick={addResourceModal}
                  className="bg-ring bg-[#fb8500] hover:bg-[#fb5c00] py-2 md:py-3 px-4 md:px-6 whitespace-nowrap"
                >
                  <PlusIcon className="h-4 w-4 md:h-5 md:w-5" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{t("resourceLibrary.addNewResource")}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <Button
            onClick={handleAddExam}
            className="bg-ring bg-[#fb8500] hover:bg-[#fb5c00] py-2 md:py-3 px-4 md:px-6 whitespace-nowrap"
          >
            {t("resourceLibrary.addNewExam")}
          </Button>
          <Button
            onClick={() => setIsFolderModalOpen(true)}
            className="bg-ring bg-[#fb8500] hover:bg-[#fb5c00] py-2 md:py-3 px-4 md:px-6 whitespace-nowrap"
          >
            Manage Folders
          </Button>
        </div>
      </div>

      <div className="border rounded-md p-4 bg-[#fff] ">
        <div className="flex gap-x-4 mt-4">
          <div className="flex md:mr-auto items-center">
            <div className="md:min-w-[430px]">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t("resourceLibrary.selectFolder")}
              </label>
              <select
                className="border p-2 rounded-md w-full"
                onChange={(e) => setFolderId(e.target.value)}
                value={folderId}
              >
                <option value={DEFAULT_FOLDER_ID}>All</option>
                {comboData.map((item) => (
                  <option key={item.value} value={item.value}>
                    {item.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
          <div className="sm:w-1/2 md:w-1/4 gap-x-4 mt-6">
            <Combobox
              data={comboResources}
              onSelectChange={handleResourceChange}
              placeHolder="Select Resource Type"
              defaultLabel={label}
            />
          </div>
          <div className="flex-grow flex justify-end gap-x-4 pt-6">
            <div className="flex-shrink-0 mr-2" style={{ width: "200px" }}>
              <Select defaultValue="0" onValueChange={getCourseStatus}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Course Status" />
                </SelectTrigger>

                <SelectContent>
                  <SelectItem value="0">All</SelectItem>
                  <SelectItem value="1">Linked</SelectItem>
                  <SelectItem value="2">Not Linked</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
        <div>
          <div className="w-full flex flex-wrap justify-between space-x-4">
            <div className="w-full sm:w-1/2 md:w-1/3 lg:w-1/4 xl:w-1/5 2xl:w-1/6 flex">
              <div className="w-full"></div>
            </div>
          </div>
          {isLoading ? (
            <Spinner />
          ) : (
            <div>
              {label === "Quiz" ? (
                <DataTable
                  columns={examColumns}
                  data={resourceLibrary}
                  FilterLabel={t("resourceLibrary.filterByResourceName")}
                  FilterBy={"name"}
                  disableIcon={"hideIcon"}
                  disableEditDelete={"hideEditDelete"}
                  hideEdit={"hideEdit"}
                  actions={[
                    // {
                    //   title: "View",
                    //   icon: Eye,
                    //   varient: "icon",
                    //   isEnable: getPrivilegeList(
                    //     "Exam",
                    //     privilegeData.Exam.getExamList,
                    //   ),
                    //   handleClick: (val: unknown) => {
                    //     examDetailView(val as ResourceList);
                    //   },
                    //   color: "#9bbb5c",
                    //   className: "flex justify-center items-center",
                    // },
                    {
                      title: t("resourceLibrary.linkToCourse"),
                      icon: Link,
                      varient: "icon",
                      handleClick: (val: unknown) => {
                        examLinkDialogOpen(val as ResourceList);
                      },
                      color: getColor(),
                      className: "flex justify-center items-center",
                    },
                  ]}
                />
              ) : (
                <DataTable
                  columns={columns}
                  data={resourceLibrary}
                  FilterLabel={t("resourceLibrary.filterByResourceName")}
                  FilterBy={"name"}
                  disableIcon={"hideIcon"}
                  disableEditDelete={"hideEditDelete"}
                  disableView={"hideViews"}
                  hideEdit={"hideEdit"}
                  actions={[
                    {
                      title: t("resourceLibrary.linkToCourse"),
                      icon: Link,
                      varient: "icon",
                      handleClick: (val: unknown) => {
                        linkDialogOpen(val as ResourceList);
                      },
                      color: getColor(),
                      className: "flex justify-center items-center",
                    },
                    {
                      title: t("resourceLibrary.viewLinkedCourse"),
                      icon: Eye,
                      varient: "icon",
                      handleClick: (val: unknown) => {
                        dialogOpen((val as ResourceList).id);
                      },
                      color: getColor(),
                      className: "flex justify-center items-center",
                    },

                    {
                      title: t("resourceLibrary.edit"),
                      icon: Edit,
                      varient: "icon",
                      handleClick: (val: unknown) => {
                        editResources(val as ResourceLibraryData);
                      },
                      color: "#fb8500",
                    },
                    {
                      title: t("resourceLibrary.delete"),
                      icon: Archive,
                      varient: "icon",
                      handleClick: (val: unknown) => {
                        deleteResources(val as ResourceList);
                      },
                      color: "#ff0000",
                    },
                    {
                      title: t("resourceLibrary.changeUrl"),
                      icon: Repeat,
                      varient: "icon",
                      handleClick: (val: unknown) => {
                        updateUrl(val as ResourceLibraryData);
                      },
                      color: "#fb8500",
                    },
                    {
                      title: t("resourceLibrary.publishedStatus"),
                      icon: PenLineIcon,
                      varient: "icon",
                      isEnable: getPrivilegeList(
                        "Resource_Bank",
                        privilegeData.Resource_Bank.approveResource,
                      ),

                      handleClick: (val: unknown) => {
                        approveDialog(val as ResourceList);
                      },
                      disabled: (val: unknown) => {
                        isDisabledApprove((val as ResourceList).id);
                      },
                    },
                  ]}
                />
              )}
            </div>
          )}

          {isDialogOpen && (
            <Modal
              title={t("resourceLibrary.coursesLinkedToResource")}
              header=""
              openDialog={isDialogOpen}
              closeDialog={dialogClose}
              type="max-w-5xl"
            >
              <CourseResources onCancel={dialogClose} resource={rescourceId} />
            </Modal>
          )}
          {isCourseLinkModalOpen && (
            <Modal
              title={t("resourceLibrary.linkResourceToCourse")}
              header=""
              openDialog={isCourseLinkModalOpen}
              closeDialog={linkDialogClose}
              type="max-w-4xl"
            >
              <LinkResources
                onCancel={dialogClose}
                resource={rescourceId}
                moduleType={label}
                onSave={onReloadAfterLink}
              />
            </Modal>
          )}
        </div>
      </div>
      {isOpenModal && (
        <Modal
          title={t("resourceLibrary.addResource")}
          header=""
          openDialog={isOpenModal}
          closeDialog={() => {
            onCancelModal();
          }}
          type="max-w-4xl"
        >
          <AddResources
            onCancel={onCancelModal}
            onSave={reloadModal}
            isModal={true}
          />
        </Modal>
      )}

      {isOpenEditDialog && isEditStatus && (
        <Modal
          title=""
          header=""
          openDialog={isOpenEditDialog}
          closeDialog={() => {
            closeEditDialog();
          }}
          type="max-w-4xl"
        >
          {label === "File" || label === "Video" ? (
            <CourseResourcesAddfile
              resourceType={label}
              onCancel={() => {
                closeEditDialog();
              }}
              resourceData={selectedData as ResourceLibraryData}
              uploadData=""
              onSave={() => {
                closeEditDialog();
                setReloadOnApprove(!reloadOnApprove);
              }}
            />
          ) : (
            <ResourcesAddPage
              resourceData={selectedData as ResourceLibraryData}
              onCancel={() => {
                closeEditDialog();
              }}
              onSave={() => {
                closeEditDialog();
                setReloadOnApprove(!reloadOnApprove);
              }}
            />
          )}
        </Modal>
      )}
      {isOpenChnageUrlModal && (
        <Modal
          title={t(
            label === "File" || label === "Video"
              ? "resourceLibrary.updateUrl"
              : "resourceLibrary.updatePageContent",
          )}
          // title={
          //   label === "File" || label === "Video"
          //     ? "Update Url"
          //     : "Update Page Content"
          // }
          header=""
          openDialog={isOpenChnageUrlModal}
          closeDialog={() => {
            closeEditDialog();
          }}
          type="max-w-4xl"
        >
          {label === "File" || label === "Video" ? (
            <UpdateUrl
              resourceType={label}
              onCancel={() => {
                closeEditDialog();
              }}
              resourceData={selectedData as ResourceLibraryData}
              uploadData=""
              onSave={() => {
                closeEditDialog();
                setReloadOnApprove(!reloadOnApprove);
              }}
            />
          ) : (
            <UpdatePgaeContent
              resourceData={selectedData as ResourceLibraryData}
              onCancel={() => {
                closeEditDialog();
              }}
              onSave={() => {
                closeEditDialog();
                setReloadOnApprove(!reloadOnApprove);
              }}
            />
          )}
        </Modal>
      )}
      {isOpenDeleteDialog && (
        <Modal
          title={t("resourceLibrary.deleteResource")}
          header=""
          openDialog={isOpenDeleteDialog}
          closeDialog={closeDeleteDialog}
        >
          <DeleteResources
            onCancel={closeDeleteDialog}
            resourceId={deleteId}
            resourceType={file}
          />
        </Modal>
      )}
      {approveDialogOpen && (
        <Modal
          title={t("resourceLibrary.updateResourceStatus")}
          header=""
          openDialog={approveDialogOpen}
          closeDialog={setApproveDialogOpen}
        >
          <ApproveResource
            onCancel={closeApproveDialog}
            resourceId={approveId}
            resourceType={file}
            onSave={saveApprovalDialog}
            publishedStatus={publishedStatus}
          />
        </Modal>
      )}
      {isOpenExam && (
        <Modal
          title={t("resourceLibrary.addNewExam")}
          header=""
          openDialog={isOpenExam}
          closeDialog={closeExamDialog}
          type="max-w-7xl"
        >
          <AddExamFromResource
            onCancel={closeExamDialog}
            folderList={comboData}
            onSave={() => {
              closeExamDialog();
              setReloadOnApprove(!reloadOnApprove);
            }}
          />
        </Modal>
      )}
      {isExamLinkOpen && (
        <Modal
          title={t("resourceLibrary.linkExamToCourse")}
          header=""
          openDialog={isExamLinkOpen}
          closeDialog={closeExamLinkDialog}
          type="max-w-7xl"
        >
          <LinkExam
            onCancel={closeExamLinkDialog}
            onSave={() => {}}
            examData={selectedExamData as ResourceList}
          />
        </Modal>
      )}
      {isFolderModalOpen && (
        <Modal
          title="Manage Folders"
          header=""
          openDialog={isFolderModalOpen}
          closeDialog={() => setIsFolderModalOpen(false)}
          type="max-w-5xl"
        >
          <div>
            <FolderManager
              comboData={
                comboData.filter((f) => typeof f.value === "string") as {
                  value: string;
                  label: string;
                }[]
              }
              editingFolderId={editingFolderId}
              editingFolderName={editingFolderName}
              onEditClick={handleEditClick}
              onEditCancel={handleEditCancel}
              onEditSave={() => {
                void handleEditSave();
              }}
              setEditingFolderName={setEditingFolderName}
            />
            <div className="flex justify-end mt-6">
              <Button
                onClick={() => setIsFolderModalOpen(false)}
                className="primary bg-[#33363F]"
              >
                Cancel
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </MainLayout>
  );
}
