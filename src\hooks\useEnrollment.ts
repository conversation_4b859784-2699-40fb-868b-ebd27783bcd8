import { supabase } from "@/lib/client";
import type {
  EnrollementResponse,
  Enrollment,
  EnrollmentRequest,
  ErrorType,
  fetchTokenRequest,
  fetchTokenResponse,
  insertMessageRequest,
  insertMessageResponse,
  pushNotificationRequest,
  pushNotificationResponse,
  SentEmailRequest,
  SentEmailResponse,
} from "@/types";
import { functions, rpc } from "../lib/apiConfig";
import { ACCESS_TOKEN } from "@/lib/constants";
interface UseEnrollmentsReturn {
  getEnrollments: (courseId: string) => Promise<Enrollment[]>;
  removeUser: (params: EnrollmentRequest) => Promise<EnrollementResponse>;
  fetDeviceToken: (params: fetchTokenRequest) => Promise<fetchTokenResponse>;
  pushNotification: (
    params: pushNotificationRequest,
  ) => Promise<pushNotificationResponse>;
  insertMessage: (
    params: insertMessageRequest,
  ) => Promise<insertMessageResponse>;
  sentEmailNotification: (
    params: SentEmailRequest,
  ) => Promise<SentEmailResponse>;
}

const useEnrollments = (): UseEnrollmentsReturn => {
  async function getEnrollments(courseId: string): Promise<Enrollment[]> {
    try {
      const requestBody = {
        org_id: localStorage.getItem("orgId"),
        course_id: courseId,
      };
      const { data, error } = await supabase.rpc<string, null>(
        rpc.enrolledUsers,
        requestBody,
      );
      if (error) {
        throw new Error(error.details);
      }
      return data as Enrollment[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function removeUser(
    params: EnrollmentRequest,
  ): Promise<EnrollementResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.unenrollUser,
        params,
      )) as {
        data: EnrollementResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as EnrollementResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function fetDeviceToken(
    params: fetchTokenRequest,
  ): Promise<fetchTokenResponse> {
    const { org_id, user_id } = params;
    try {
      const { data, error } = (await supabase.rpc(rpc.fetDeviceToken, {
        org_id: org_id,
        user_id: user_id,
      })) as {
        data: fetchTokenResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw error;
      }
      return data;
    } catch (error) {
      console.error("Error fetching device token:", error);
      throw error;
    }
  }
  async function pushNotification(
    params: pushNotificationRequest,
  ): Promise<pushNotificationResponse> {
    console.log("params", params);
    try {
      const response = await fetch(functions.pushNotification, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          apikey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY as string,
          Authorization: "Bearer " + localStorage.getItem(ACCESS_TOKEN),
        },
        body: JSON.stringify(params),
      });

      if (response.ok) {
        const data = (await response.json()) as pushNotificationResponse;

        return data;
      } else {
        throw new Error("Failed to send notification");
      }
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function insertMessage(
    params: insertMessageRequest,
  ): Promise<insertMessageResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.insertMessage,
        params,
      )) as {
        data: insertMessageResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw error;
      }

      return data;
    } catch (error) {
      console.error("Error fetching device token:", error);
      throw error;
    }
  }
  async function sentEmailNotification(
    params: SentEmailRequest,
  ): Promise<SentEmailResponse> {
    try {
      const response = await fetch(functions.sendEmailNotification, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          apikey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY as string,
          Authorization: "Bearer " + localStorage.getItem(ACCESS_TOKEN),
        },
        body: JSON.stringify(params),
      });

      if (response.ok) {
        const data = (await response.json()) as SentEmailResponse;
        return data;
      } else {
        throw new Error("Failed to send notification");
      }
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  return {
    getEnrollments,
    removeUser,
    fetDeviceToken,
    pushNotification,
    insertMessage,
    sentEmailNotification,
  };
};

export default useEnrollments;
