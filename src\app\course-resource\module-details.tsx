"use client";
import React, { type BaseSyntheticEvent, useEffect, useState } from "react";
import { Modal } from "@/components/ui/modal";
import { CourseResourceAddForm } from "@/app/course-resource/course-resource-add-form";
import type {
  CheckPointData,
  CheckPointForm,
  CheckPointList,
  CheckPointRequest,
  CheckPointsResponse,
  ErrorCatch,
  Milestone,
  ResoureceModuleType,
  ToastType,
  ViewResourcePageType,
  CommentResponse,
  ExamDetails,
  PPTCheckPointForm,
  PPTCheckPointList,
  PPTCheckPointData,
  PPTCheckPointRequest,
  OrderFolderRequest,
  OrderResourceRequest,
  ResourceItem,
} from "@/types";
import useCourse from "@/hooks/useCourse";
// import { useSearchParams } from "next/navigation";
import {
  CheckIcon,
  ChevronDown,
  ChevronLeftIcon,
  ChevronRightIcon,
  HourglassIcon,
  Import,
  LockIcon,
  // PlusIcon,
  Trash,
  icons,
  // Trash2,
} from "lucide-react";
import ReactPlayer from "react-player";
import { Editor } from "primereact/editor";
import { ImageViewer } from "@/components/ui/imageViewer";
// import { PdfViewer } from "@/components/ui/pdfViewer";
// import { Skeleton } from "@/components/ui/skeleton";
import {
  pageUrl,
  ERROR_FETCHING_DATA,
  ORG_KEY,
  privilegeData,
  extractVideoIdFromSearch,
  MODULE_TYPE_ID,
} from "@/lib/constants";
import { Button } from "@/components/ui/button";
// import { useRouter } from "next/navigation";
import CheckPointProgress from "@/components/checkPointProgress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { AddCheckpointModal } from "./course-resource-add-checkpoint";
import { DataTable } from "@/components/ui/data-table/data-table";
import { getColumns } from "./columns";
import { useToast } from "@/components/ui/use-toast";
import Image from "next/image";
import moment from "moment";
// import FileViewer from 'react-file-viewer';
// import Viewer from "react-office-viewer"
import { DocumentViewer } from "react-documents";
import {
  convertGoogleDriveUrl,
  isGoogleDriveUrl,
  getViewerUrl,
} from "@/utils/googleDriveUtils";

// import { useRouter } from 'next/router';

import getPrivilegeList from "@/hooks/useCheckPrivilege";
import useExamDetails from "@/hooks/useExamDetails";
import { Card, CardContent } from "@/components/ui/card";
import { Spinner } from "@/components/ui/progressiveLoader";
import Link from "next/link";
import ImportResource from "./importResource";
import DeleteResource from "./deleteResource";
import AddFolder from "./addFolder";
import "primereact/resources/themes/bootstrap4-light-purple/theme.css";
import "primereact/resources/primereact.min.css";
import { Accordion, AccordionTab } from "primereact/accordion";
import { AddCheckpointPPTModal } from "./add-checkpoint-ppt";
import { getColumnsPpt } from "./pptcolumns";
import dynamic from "next/dynamic";
import ImportFolder from "./importFolder";
import { ResourceReorderModal } from "@/components/ui/resource-reorder/resource-reorder";
import { ArrowUpDown } from "lucide-react";
import { useTranslation } from "react-i18next";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
export interface SectionDetails {
  name: string;
  modules: SectionModule[];
  summary: string;
  course_id: string;
  resources: [];
  section_id: string;
  section_order: number;
  folders?: folderModule[];
  course_module_id?: string;
}
export interface folderModule {
  folder_id: string;
  resources: SectionModule[];
  section_id: string;
  folder_desc: string;
  folder_name: string;
  type: string;
}

export interface SectionModule {
  instance: string;
  progress: number;
  course_id: string;
  module_id: string;
  section_id: string;
  time_spent: string;
  module_name: string;
  module_type: string;
  module_source: string;
  marked_as_done: boolean;
  attempts_remaining: number;
  course_module_id: string;
  module_order: number;
}

interface ModuleDetailsProps {
  sectionId: string;
  expiry: boolean;
  courseIds: string;
  is_premium: string;
}

export default function ModuleDetails({
  sectionId,
  expiry,
  courseIds,
  is_premium,
}: ModuleDetailsProps): React.JSX.Element {
  const ReactGoogleSlides = dynamic(() => import("react-google-slides"), {
    ssr: false,
  });
  const {
    resourceView,
    viewResourcePage,
    addCheckPoint,
    addPPTCheckPoint,
    getCheckPointsList,
    getComments,
    orderFolder,
    orderResource,
  } = useCourse();
  const { getQuestions } = useExamDetails();
  const { t } = useTranslation();
  const columns = getColumns(t);
  const columnsppt = getColumnsPpt(t);
  const Expiry = expiry;
  const enableExpiry = Expiry;
  const customColumnWidths: Record<string, { width: number; align: string }> = {
    sequence_number: { width: 150, align: "center" },
    checkpoint_name: { width: 200, align: "left" },
    checkpoint_startTime: { width: 150, align: "left" },
    isMandatory: { width: 150, align: "left" },
  };
  const pptcustomColumnWidths: Record<
    string,
    { width: number; align: string }
  > = {
    sequence_number: { width: 150, align: "center" },
    checkpoint_name: { width: 200, align: "left" },
    checkpoint_slide: { width: 150, align: "left" },
    isMandatory: { width: 150, align: "left" },
  };

  const [courseResourceData, setCourseResourceData] =
    useState<SectionDetails>();
  const [selectedResource, setSelectedResource] = useState(0);
  const [selectedResourceData, setSelectedResourceData] =
    useState<ViewResourcePageType>();
  const [videoLength, setVideoLength] = useState<number>(0);
  //   const [modules, setModules] = useState<ResoureceModuleType | []>();
  const [comments, setComments] = useState<CommentResponse[]>([]);
  const [disableSubmitBtn, setDisableSubmitBtn] = useState<boolean>(false);
  const [disableBtn, setDisableBtn] = useState<boolean>(false);
  const [commentBtn, setCommentBtn] = useState<boolean>(false);
  const [checkPointBtn, setCheckPointBtn] = useState<boolean>(false);
  const [pptcheckPointBtn, setPPTCheckPointBtn] = useState<boolean>(false);
  const [videoFullLength, setVideoFullLength] = useState<string>("");
  const [courseId, setCourseId] = useState<string>("");
  const [examInfo, setExamInfo] = useState<ExamDetails>();
  const [isLoading, setIsLoading] = React.useState<boolean>(true);
  const [isFetching, setIsFetching] = React.useState<boolean>(true);
  const [openResource, setOpenResource] = useState<boolean>(false);
  const [openFolder, setOpenFolder] = useState<boolean>(false);
  const [openImportFolder, setOpenImportFolder] = useState<boolean>(false);
  const [importFromFolderId, setImportFromFolderId] = useState<string | null>(null);
  const [updateResource, setUpdateResource] = useState<boolean>(false);
  const [deleteResourceModal, setDeleteResourceModal] =
    useState<boolean>(false);
  const [courseModuleId, setCourseModuleId] = useState<string>("");
  const [coursePPTModuleId, setCoursePPTModuleId] = useState<
    string | undefined
  >("");
  const [keyInstance, setKeyInstance] = useState<string>("");
  const [resourceDelete, setDeleteResource] = useState<boolean>(false);
  const [reloadResource, setReloadResource] = useState<boolean>(false);
  const [reloadAfterRecImport, setReloadAfterRecImport] =
    useState<boolean>(false);
  const [reloadAfterFolderImport, setReloadAfterFolderImport] =
    useState<boolean>(false);
  const [moduleOrders, setModuleOrders] = useState<number[]>([]);

  // Reorder modal states
  const [isMainReorderOpen, setIsMainReorderOpen] = useState<boolean>(false);
  const [isFolderReorderOpen, setIsFolderReorderOpen] =
    useState<boolean>(false);
  const [selectedFolderId, setSelectedFolderId] = useState<string>("");
  const [isFolderListReorderOpen, setIsFolderListReorderOpen] =
    useState<boolean>(false);
  const [folderId, setFolderId] = useState<string>("");

  useEffect(() => {
    setCourseId(courseIds as string);
    setSelectedResource(0);
    const fetchData = async (): Promise<void> => {
      setIsFetching(false);
      try {
        const response = await resourceView(sectionId as string);
        if (typeof response === "object" && response !== null) {
          const result = response as SectionDetails;
          if (result.modules != null) {
            if (result.modules.length > 0) {
              setKeyInstance(result.modules[0].instance);
            }
          }
          setCourseResourceData(result);
          const moduleOrder: number[] = [];
          result?.folders?.forEach((folder) => {
            folder.resources.forEach((resource) => {
              moduleOrder.push(resource.module_order);
            });
          });
          result?.modules?.forEach((module) => {
            moduleOrder.push(module.module_order);
          });
          setModuleOrders(moduleOrder);
          setCourseId(result.course_id);
          void fetchFilesResources(
            result?.modules?.[0].module_type,
            result?.modules?.[0].instance,
            result?.modules?.[0].course_module_id,
          );
          // 503fcedd-4d9f-424c-9380-344bb5ce1754
          fetchComments(result?.modules?.[0].instance).catch((error) =>
            console.log(error),
          );
        }
      } catch (error) {
        setIsFetching(false);
      }
    };

    void fetchData();
    const canPerformAction = getPrivilegeList(
      "Course_Resource",
      privilegeData.Course_Resource.addResource,
    );
    setDisableBtn(canPerformAction);
    setCommentBtn(
      getPrivilegeList(
        "Resource_Bank",
        privilegeData.Resource_Bank.addCourseResource,
      ),
    );
    setCheckPointBtn(
      getPrivilegeList("Checkpoint", privilegeData.Checkpoint.createCheckpoint),
    );
  }, [
    updateResource,
    reloadResource,
    reloadAfterRecImport,
    reloadAfterFolderImport,
  ]);
  useEffect(() => {
    const iframe = document.querySelector("iframe");

    const contentDoc = iframe?.contentDocument;

    if (contentDoc) {
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (
            mutation.type === "attributes" &&
            mutation.attributeName === "class"
          ) {
            console.log("Class attribute changed:", mutation.target);
          }
        });
      });

      observer.observe(contentDoc, {
        attributes: true,
        childList: true,
        subtree: true,
      });

      return () => observer.disconnect();
    }
  }, []);

  const handleDuration = (duration: number): void => {
    setVideoLength(duration);
  };
  const fetchComments = async (instanceId: string): Promise<void> => {
    const org_id = localStorage.getItem(ORG_KEY);
    const params = {
      instance_id: instanceId,
      org_id: org_id ?? "",
    };
    try {
      if (instanceId !== null && instanceId !== undefined) {
        const response = await getComments(params);
        if (response != null) {
          setComments(response as CommentResponse[]);
        } else {
          setComments([] as CommentResponse[]);
        }
      } else {
        setComments([] as CommentResponse[]);
      }
    } catch (error) {
      console.error(ERROR_FETCHING_DATA, error);
    }
  };

  const [isDialogOpen, setIsDialogOpen] = React.useState(false);
  //   const [parentCurrentStep, setParentCurrentStep] = React.useState(0);
  //   const router = useRouter();
  const [typeFile, setTypeFile] = React.useState("");
  const [frameUrl, setFrameUrl] = React.useState("");
  const [filePath, setFilePath] = React.useState("");
  const [videoUrl, setVideoUrl] = React.useState("");
  const [content, setContent] = React.useState("");
  const [fileTypeCheck, setFileTypeCheck] = React.useState("");
  // const [filetype, setFiletype] = React.useState("");
  const [checkpointModalStatus, setCheckpointModalStatus] =
    React.useState(false);
  const [checkpointModalValue, setCheckpointModalValue] = React.useState(false);
  const [checkpointTime, setCheckpointTime] = useState<number>(0); // State to store the checkpoint time
  const [addedValues, setAddedValues] = useState<CheckPointForm[]>([]);
  const [addedPPTValues, setAddedPPTValues] = useState<PPTCheckPointForm[]>([]);
  const [existingCheckpoints, setExistingCheckpoints] = useState<
    CheckPointForm[]
  >([]);
  const [existingPPTCheckpoints, setExistingPPTCheckpoints] = useState<
    PPTCheckPointForm[]
  >([]);
  const [checkPointsList, setCheckPointsList] = useState<Milestone[]>([]);
  const [totalCheckpoint, setTotalCheckpoint] = useState<number>(0);
  const [existingCheckpointLength, setexistingCheckpointLength] =
    useState<number>(0);
  // const router = useRouter();
  const { toast } = useToast() as ToastType;
  //   console.log("fileTypeCheck", fileTypeCheck);

  const closeDialog = (value: boolean): void => {
    setIsDialogOpen(value);
  };
  const handleAddedValuesChange = (values: CheckPointForm[]): void => {
    setAddedValues((prevValues) => [...prevValues, ...values]);
  };
  const handleAddedPPTValuesChange = (values: PPTCheckPointForm[]): void => {
    // setExistingPPTCheckpoints(values);
    setAddedPPTValues((prevValues) => [...prevValues, ...values]);
    setPPTCheckPointBtn(true);
  };
  //   const handleStepInstance = (stepType: string, stepInstance: string): void => {
  //     console.log(stepType);
  //     void fetchFilesResources(stepType, stepInstance);
  //   };
  const isImageOrPdfUrl = (url: string, fileType: string): string => {
    const urlParts = url.split(".");

    const fileExtension = urlParts.pop()!.toLowerCase();
    // setFiletype(fileExtension);

    if (url.includes(".pdf?") || fileType === "pdf") {
      return "pdf";
    } else if (
      ["jpg", "jpeg", "png", "gif", "bmp"].includes(fileExtension) ||
      url.includes("images?")
    ) {
      return "image";
    } else if (["doc", "docx", "xls", "xlsx"].includes(fileExtension)) {
      return "document";
    } else if (["ppt", "pptx"].includes(fileExtension) || fileType === "ppt") {
      return "ppt";
    } else {
      return "unknown";
    }
  };

  // const router = useRouter();

  const fetchFilesResources = async (
    type: string,
    instanceId: string,
    course_module_id?: string,
  ): Promise<void> => {
    try {
      setExistingPPTCheckpoints([]);
      setTypeFile(type);
      setAddedValues([]);
      setExistingCheckpoints([]);
      setCheckPointsList([]);
      setComments([]);
      let response = {};
      if (type != "Quiz") {
        response = await viewResourcePage(type, instanceId, course_module_id);
      } else {
        setFrameUrl(`${pageUrl.examDetails}?type=${instanceId}`);
        // setFrameUrl(instanceId);
        // router.push(`${pageUrl.examDetails}?type=${instanceId}`);
        fetchExamData(instanceId).catch((error) => console.log(error));
        // console.log(parentCurrentStep);
        // if (parentCurrentStep !== 0) {
        // router.push(`${pageUrl.examDetails}?type=${instanceId}`); // Temporary fix
        // } else {
        //   response = await viewResourcePage(type, instanceId);
        // }
      }

      if (typeof response === "object" && response !== null) {
        const result = response as ViewResourcePageType;
        setSelectedResourceData(result as ViewResourcePageType);
        if (type == "Url") {
          setSelectedResourceData(result as ViewResourcePageType);
          const videoId = extractVideoIdFromSearch(result.external_url);
          if (videoId != null) {
            setVideoUrl(`https://www.youtube.com/watch?v=${videoId}`);
          } else {
            setVideoUrl(result.external_url);
          }

          setVideoFullLength(result.length);
          setTotalCheckpoint(result.num_of_checkpoints ?? 0);
          // fetch checkpoints if any exists to this resource
          if (result.is_checkpoint_enabled) {
            const checkPoints = (await getCheckPointsList(
              result.course_module_id,
            )) as CheckPointsResponse;
            const mileStones: Milestone[] = [];
            checkPoints.check_points.forEach((item) => {
              const time = item.start_time;
              const array = time.split(":");
              const time_seconds =
                parseInt(array[0], 10) * 60 * 60 +
                parseInt(array[1], 10) * 60 +
                parseInt(array[2], 10);

              mileStones.push({
                time: time_seconds,
                label: item.module_name,
              });
            });
            setCheckPointsList(mileStones);
            setexistingCheckpointLength(checkPoints.check_points.length);
            const formattedCheckpoints = checkPoints.check_points.map(
              (item) => ({
                sequence_number: item.sequence.toString(),
                checkpoint_name: item.checkpoint_name,
                checkpoint_startTime: {
                  HH: item.start_time.split(":")[0],
                  MM: item.start_time.split(":")[1],
                  SS: item.start_time.split(":")[2],
                },
                checkpoint_type: item.checkpoint_type,
                checkpoint_resid: item.instance_id,
                isMandatory: item.is_mandatory.toString(),
              }),
            );

            setExistingCheckpoints(formattedCheckpoints);
            //setAddedValues(checkPoints.check_points);
          }
          //
        } else if (type == "Quiz") {
          //setVideoUrl(result.external_url);
        } else if (type == "File") {
          console.log("file_type", result.file_type);
          const fileTypeCheck = isImageOrPdfUrl(result.url, result.file_type);
          setFileTypeCheck(fileTypeCheck);
          setFilePath(result.url);
          if (result.is_checkpoint_enabled) {
            const checkPoints = (await getCheckPointsList(
              result.course_module_id,
            )) as CheckPointsResponse;

            const formattedCheckpoints = checkPoints.check_points.map(
              (item) => ({
                sequence_number: item.sequence.toString(),
                checkpoint_name: item.checkpoint_name,
                checkpoint_slide: item.start_page,
                checkpoint_type: item.checkpoint_type,
                checkpoint_resid: item.instance_id,
                isMandatory: item.is_mandatory.toString(),
              }),
            );

            setExistingPPTCheckpoints(formattedCheckpoints);
            setPPTCheckPointBtn(true);
          }
        } else if (type == "Page") {
          setContent(result.content);
        }
      }
    } catch (error) {
      console.error(ERROR_FETCHING_DATA, error);
    }
    fetchComments(instanceId).catch((error) => console.log(error));
  };

  const fetchExamData = async (quizId: string): Promise<void> => {
    setIsLoading(true);
    try {
      const questionsData = await getQuestions(quizId);
      setIsLoading(false);
      setExamInfo(questionsData[0]);
    } catch (error: unknown) {
      setIsLoading(false);
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.error_fetching_data"),
      });
    }
  };
  const deleteResource = (item: string): void => {
    setCourseModuleId(item);
    setDeleteResourceModal(!deleteResourceModal);
  };
  // const handleAddResources = (): void => {
  //   const sectionId = searchParams?.get("sectionId");
  //   router.push(`${pageUrl.addResources}?sectionId=${sectionId}`);
  // };
  const handleCheckpointAdd = (time: number): void => {
    setCheckpointTime(time);
    handleAddCheckpoint();
  };
  const addCheckpointPPT = (course_module_id: string | null): void => {
    setCheckpointModalValue(true);
    setCoursePPTModuleId(course_module_id ?? "");
  };
  // const handleDelete = (data: CheckPointForm[]): void => {
  //   const updatedValues = addedValues.filter(
  //     (item) => JSON.stringify(item) !== JSON.stringify(data),
  //   );
  //   setAddedValues(updatedValues);
  // };
  const checkPointUpdate = async (e: BaseSyntheticEvent): Promise<void> => {
    console.log(e);
    setDisableSubmitBtn(true);
    const checkpointData = (
      addedValues: CheckPointForm[],
    ): CheckPointData[] => {
      return addedValues.map((item) => ({
        sequence: parseInt(item.sequence_number),
        is_mandatory: item?.isMandatory === "false" ? false : true,
        name: item.checkpoint_name,
        start_time: `${
          item.checkpoint_startTime?.HH !== undefined &&
          item.checkpoint_startTime.HH !== ""
            ? item.checkpoint_startTime.HH.length === 1
              ? `0${item.checkpoint_startTime.HH}`
              : item.checkpoint_startTime.HH
            : "00"
        }:${
          item.checkpoint_startTime?.MM !== undefined &&
          item.checkpoint_startTime.MM !== ""
            ? item.checkpoint_startTime.MM.length === 1
              ? `0${item.checkpoint_startTime.MM}`
              : item.checkpoint_startTime.MM
            : "00"
        }:${
          item.checkpoint_startTime?.SS !== undefined &&
          item.checkpoint_startTime.SS !== ""
            ? item.checkpoint_startTime.SS.length === 1
              ? `0${item.checkpoint_startTime.SS}`
              : item.checkpoint_startTime.SS
            : "00"
        }`,
        module_type_id: "dd33330a-7ab4-452b-b8bd-9e37192f0ffb",
        instance_id: item.checkpoint_resid,
        checkpoint_type: item.checkpoint_type,
      }));
    };

    const checkpoint_data = checkpointData(addedValues);
    const org_id = localStorage.getItem(ORG_KEY);
    const data: CheckPointRequest = {
      checkpoint_data: checkpoint_data,
      course_module_id: selectedResourceData?.course_module_id ?? "",
      org_id: org_id ?? "",
    };
    try {
      const result = await addCheckPoint([data]);
      if (result.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.toast_success_title"),
          description: t("successMessages.checkpoint_details"),
        });
        //To Do: need to update it to router push
        window.location.reload();
        //closeMilestoneDialog(false);
      } else if (result.status === "error") {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: t("errorMessages.checkpoint_fail"),
        });
        setDisableSubmitBtn(false);
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.details,
      });
      setDisableSubmitBtn(false);
    }
  };
  const checkPointPPTUpdate = async (e: BaseSyntheticEvent): Promise<void> => {
    console.log(e);
    setDisableSubmitBtn(true);
    const checkpointData = (
      addedPPTValues: PPTCheckPointForm[],
    ): PPTCheckPointData[] => {
      return addedPPTValues.map((item) => ({
        sequence: parseInt(item.sequence_number),
        is_mandatory: item?.isMandatory === "false" ? false : true,
        name: item.checkpoint_name,
        start_page: item.checkpoint_slide,
        module_type_id: MODULE_TYPE_ID,
        instance_id: item.checkpoint_resid,
        checkpoint_type: item.checkpoint_type,
      }));
    };

    const checkpoint_data = checkpointData(addedPPTValues);
    const org_id = localStorage.getItem(ORG_KEY);
    const data: PPTCheckPointRequest = {
      checkpoint_data: checkpoint_data,
      course_module_id: coursePPTModuleId ?? "",
      org_id: org_id ?? "",
    };
    try {
      const result = await addPPTCheckPoint([data]);
      if (result.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.toast_success_title"),
          description: t("successMessages.checkpoint_details"),
        });
        //To Do: need to update it to router push
        window.location.reload();
        //closeMilestoneDialog(false);
      } else if (result.status === "error") {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: t("errorMessages.checkpoint_fail"),
        });
        setDisableSubmitBtn(false);
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.details,
      });
      setDisableSubmitBtn(false);
    }
  };
  const handleAddCheckpoint = (): void => {
    if (selectedResourceData && selectedResourceData.is_checkpoint_enabled) {
      const total_checkpoints = existingCheckpoints.concat(addedValues);

      if (selectedResourceData.num_of_checkpoints > total_checkpoints.length) {
        setCheckpointModalStatus(!checkpointModalStatus);
      } else {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: t("errorMessages.exceeded_checkpoint"),
        });
      }
    } else {
      setCheckpointModalStatus(!checkpointModalStatus);
    }
  };
  const handleAddPPTCheckpoint = (): void => {
    setCheckpointModalValue(false);
  };

  // Reorder handlers
  const handleMainReorder = (): void => {
    setIsMainReorderOpen(true);
  };

  const handleFolderReorder = async (folderId: string): Promise<void> => {
    setFolderId(folderId);
    setSelectedFolderId(folderId);
    setIsFolderReorderOpen(true);
  };

  const handleMainReorderSave = async (
    reorderedResources: ResourceItem[],
  ): Promise<void> => {
    const params: OrderResourceRequest = {
      org_id: localStorage.getItem(ORG_KEY) ?? "",
      course_id: courseId ?? "",
      section_id: sectionId,
      folder_id: null,
      resources: reorderedResources.map((resource, index) => ({
        resource_id: resource.instance,
        resource_order: index + 1,
      })),
    };

    try {
      const response = await orderResource(params);
      if (response.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.toast_success_title"),
          description: t("successMessages.order_resource"),
        });
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
    }
    setReloadResource(!reloadResource);
  };

  const handleFolderReorderSave = async (
    reorderedResources: ResourceItem[],
  ): Promise<void> => {
    const folder = courseResourceData?.folders?.find(
      (f) => f.folder_id === folderId,
    );
    console.log(folder);
    if (!folder || folder.resources.length < 2) {
      toast({
        variant: "destructive",
        title: "Cannot Rearrange",
        description: `You need at least 2 resources to rearrange. This folder has ${
          folder?.resources.length ?? 0
        } resource${folder?.resources.length !== 1 ? "s" : ""}.`,
      });
      return;
    }

    const params: OrderResourceRequest = {
      org_id: localStorage.getItem(ORG_KEY) ?? "",
      course_id: courseId ?? "",
      section_id: sectionId,
      folder_id: folderId,
      resources: reorderedResources.map((resource, index) => ({
        resource_id: resource.instance,
        resource_order: index + 1,
      })),
    };

    try {
      const response = await orderResource(params);
      if (response.status === "success") {
        toast({
          variant: "success",
          title: "Success",
          description: SUCCESS_MESSAGES.order_resource,
        });
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: "destructive",
        title: "Error",
        description: err?.message,
      });
    }
    setReloadResource(!reloadResource);
  };

  // Folder list reorder handlers
  const handleFolderListReorder = (): void => {
    if (!courseResourceData?.folders || courseResourceData.folders.length < 2) {
      toast({
        variant: "destructive",
        title: "Cannot Rearrange",
        description: `You need at least 2 folders to rearrange. Currently there ${
          courseResourceData?.folders?.length === 1 ? "is" : "are"
        } ${courseResourceData?.folders?.length ?? 0} folder${
          courseResourceData?.folders?.length !== 1 ? "s" : ""
        }.`,
      });
      return;
    }
    setIsFolderListReorderOpen(true);
  };

  const handleFolderListReorderSave = async (
    reorderedFolders: ResourceItem[],
  ): Promise<void> => {
    const orgId = localStorage.getItem(ORG_KEY);
    const params: OrderFolderRequest = {
      org_id: orgId ?? "",
      course_id: courseId ?? "",
      section_id: sectionId,
      folders: reorderedFolders.map(
        (
          folder: {
            module_id: string;
            module_name: string;
            module_type: string;
            module_order: number;
            course_module_id: string;
            instance: string;
          },
          index,
        ) => ({
          folder_id: folder.module_id as string,
          folder_order: index + 1,
        }),
      ),
    };
    try {
      const response = await orderFolder(params);
      if (response.status === "success") {
        toast({
          variant: "success",
          title: "Success",
          description: SUCCESS_MESSAGES.order_folder,
        });
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: "destructive",
        title: "Error",
        description: err?.message,
      });
    }
    setReloadResource(!reloadResource);
  };

  const Icon = ({ name }: { name: string }): React.JSX.Element => {
    const LucideIcon = icons[name as keyof typeof icons];

    return <LucideIcon className="flex h-5 w-5 absolute justify-center" />;
  };

  const ICONS = {
    File: "File",
    Page: "Layout",
    Url: "PlaySquare",
    Quiz: "HelpCircle",
  };

  const GetIcons = ({
    type,
  }: {
    varient: "success" | "default";
    type: string;
  }): React.JSX.Element => {
    return <Icon name={ICONS[type as keyof typeof ICONS]} />;
  };

  const Step = ({
    item,
    index,
    totalCount,
    previousItem,
    nextItem,
    previousType,
    nextType,
    nextModuleId,
    previousModuleId,
    previousUrlId,
    nextUrlId,
    previousInstance,
    nextInstance,
  }: {
    item: ResoureceModuleType;
    index: number;
    totalCount: number;
    previousItem: string;
    nextItem: string;
    previousUrlId: string;
    nextUrlId: string;
    previousType: string;
    nextType: string;
    nextModuleId: string;
    previousModuleId: string;
    key: string;
    previousInstance: string;
    nextInstance: string;
    // nextType
  }): JSX.Element => {
    return (
      <>
        <div className="flex flex-row relative">
          <div
            className={` cursor-pointer w-6 h-6 flex items-center justify-center rounded-full p-4 after:top-[35px] after:border-l-[#2d65f5] after:border-l-[2.5px] after:h-[calc(100%-40px)] after:absolute ${
              item.marked_as_done === true && selectedResource !== index
                ? "bg-[#27bd87] border-[#2d65f5] border-2 text-white"
                : item.marked_as_done === true && selectedResource === index
                  ? "text-[#27bd87]"
                  : item.marked_as_done !== true && selectedResource !== index
                    ? "border-[#2d65f5] border-2 bg-white text-[#2d65f5]"
                    : " bg-white text-[#2d65f5]"
            }
              `}
          >
            {selectedResource === index && (
              <div
                className={`inline-block absolute h-8 w-8 p-4 animate-spin rounded-full border-2 border-solid ${
                  item.marked_as_done === true
                    ? "border-[#27bd87]"
                    : "border-[#2d65f5]"
                } border-l-transparent border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]`}
                role="status"
              />
            )}
            <GetIcons
              varient={item.marked_as_done ? "success" : "default"}
              type={item.module_type}
            />
            {/* <item.module_type /> */}
          </div>
          <div
            className={`relative border rounded-lg p-4 ml-4 mb-4 w-full ${
              selectedResource !== index && "bg-[#f2f2f2]"
            }`}
          >
            <div
              className="flex justify-between pr-4 align-middle cursor-pointer "
              onClick={() => {
                setSelectedResource(index);
                setTypeFile(item.module_type);
                setKeyInstance(item.instance);
                setKeyInstance(item.instance);
                void fetchFilesResources(
                  item.module_type,
                  item.instance,
                  item.course_module_id,
                );
              }}
            >
              <div className="font-semibold pl-2">{item.module_name}</div>
              <div className="flex flex-row justify-center ">
                {(item.is_premium ?? false) && (
                  <div title="Premium resource">
                    {" "}
                    <LockIcon
                      color="#000"
                      className="h-5 w-5 mt-1"
                      onClick={(e) => e.stopPropagation()}
                    ></LockIcon>
                  </div>
                )}
                <div title="Delete">
                  <Trash
                    color="#000"
                    className="h-5 w-5 mt-1 mr-1 ml-3"
                    onClick={(e) => {
                      (e.stopPropagation(),
                        deleteResource(item.course_module_id));
                    }}
                  ></Trash>
                </div>

                <div
                  className={`rounded-sm pl-3 pr-3 self-center pt-0.5 pb-[3px] ${
                    item.marked_as_done ? "text-green-600" : "text-orange-400"
                  }  font-normal text-sm `}
                >
                  {item.marked_as_done ? (
                    <div className="flex content-center">
                      <CheckIcon className="pr-1 w-6 text-green-500" />
                      {/* <span className="font-semibold text-base">Completed</span> */}
                    </div>
                  ) : (
                    <div className="flex items-center">
                      <HourglassIcon className="pr-1 w-6 text-green-700" />
                      {/* <span className="font-semibold text-base">Pending</span> */}
                    </div>
                  )}
                </div>
                <ChevronDown
                  color={"#000"}
                  className={`flex h-5 w-5 justify-center  ml-4  ${
                    selectedResource === index &&
                    keyInstance === item.instance &&
                    "rotate-180"
                  }`}
                />
              </div>
            </div>
            <div>
              {selectedResource === index &&
                typeFile != null &&
                keyInstance === item.instance && (
                  <div>
                    <div className="flex h-full">
                      <div className="w-1/2 md:row-span-2 col-span-1 lg:col-span-2 p-4 h-auto">
                        {typeFile === "Quiz" && (
                          // <iframe
                          //   src={frameUrl}
                          //   className="w-full h-full relative py-60 rounded-md overflow-hidden"
                          <>
                            {isLoading ? (
                              <Spinner />
                            ) : (
                              <div>
                                <Card className="w-full pt-4 bg-transparent">
                                  <CardContent>
                                    <div className="w-full text-xl font-semibold">
                                      <h1>{examInfo?.name}</h1>
                                    </div>
                                    <div className="md:grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 mt-4">
                                      <div className="md:col-span-2 lg:col-span-1 xl:col-span-1">
                                        <div className="font-semibold">
                                          {t("courses.courseModule.fromDate")}:
                                        </div>
                                        <div>
                                          {moment(examInfo?.start_time).format(
                                            "DD-MMM-YYYY hh:mm a",
                                          )}
                                        </div>
                                      </div>
                                      <div className="md:col-span-2 lg:col-span-1 xl:col-span-1">
                                        <div className="font-semibold">
                                          {t("courses.courseModule.toDate")}:
                                        </div>
                                        <div>
                                          {moment(examInfo?.end_time).format(
                                            "DD-MMM-YYYY hh:mm a",
                                          )}
                                        </div>
                                      </div>
                                      <div className="md:col-span-1 lg:col-span-1 xl:col-span-1">
                                        <div className="font-semibold">
                                          {t(
                                            "courses.courseModule.noOfQuestions",
                                          )}
                                        </div>
                                        <div>{examInfo?.num_of_questions}</div>
                                      </div>
                                      <div className="md:col-span-1 lg:col-span-1 xl:col-span-1">
                                        <div className="font-semibold">
                                          {t("courses.courseModule.totalMarks")}
                                        </div>
                                        <div>{examInfo?.total_mark}</div>
                                      </div>
                                      <div className="md:col-span-1 lg:col-span-1 xl:col-span-1">
                                        <div className="font-semibold">
                                          {t("courses.courseModule.passMarks")}
                                        </div>
                                        <div>{examInfo?.pass_mark}</div>
                                      </div>
                                    </div>
                                    <div className="w-full flex justify-end mt-4">
                                      <Link href={frameUrl}>
                                        <Button className="bg-[#9fc089] hover:bg-white hover:text-[#9fc089] border hover:border-[#9fc089]">
                                          {t("buttons.showMoreDetails")}
                                        </Button>
                                      </Link>
                                    </div>
                                  </CardContent>
                                </Card>
                              </div>
                            )}
                          </>
                        )}
                        {typeFile === "Url" ? (
                          <>
                            <div className="w-full h-0 relative py-60 rounded-md overflow-hidden">
                              <ReactPlayer
                                style={{
                                  position: "absolute",
                                  top: "0",
                                  left: "0",
                                }}
                                onDuration={handleDuration}
                                onPause={() => console.log("res")}
                                width="100%"
                                height="100%"
                                // url="https://www.youtube.com/watch?v=77Fo-q72ov8"
                                url={videoUrl}
                                controls={true}
                              />
                            </div>
                            {}
                            {checkPointBtn &&
                              !enableExpiry &&
                              (existingCheckpointLength < totalCheckpoint ||
                                totalCheckpoint === 0) && (
                                <CheckPointProgress
                                  videoLength={videoLength}
                                  className="mt-5"
                                  availableMilestones={checkPointsList}
                                  onCheckpointAdd={handleCheckpointAdd}
                                />
                              )}
                          </>
                        ) : (
                          <div className="overflow-x">
                            {typeFile == "Page" && (
                              // <PdfViewer url={filePath} />

                              <Editor
                                value={content}
                                readOnly
                                style={{ height: "320px" }}
                              />
                            )}
                            {typeFile === "File" && (
                              <>
                                {fileTypeCheck === "image" && (
                                  <ImageViewer url={filePath} />
                                )}
                                {fileTypeCheck === "pdf" && (
                                  <div style={{ height: "320px" }}>
                                    {isGoogleDriveUrl(filePath) ? (
                                      <iframe
                                        src={convertGoogleDriveUrl(filePath)}
                                        width="100%"
                                        height="100%"
                                        style={{ border: "none" }}
                                        title="PDF Viewer"
                                      />
                                    ) : (
                                      <DocumentViewer
                                        queryParams="hl=Nl"
                                        url={filePath}
                                        viewerUrl={filePath}
                                        overrideLocalhost="https://react-doc-viewer.firebaseapp.com/"
                                      />
                                    )}
                                  </div>
                                )}
                                {fileTypeCheck === "document" && (
                                  <div style={{ height: "320px" }}>
                                    {isGoogleDriveUrl(filePath) ? (
                                      <iframe
                                        src={getViewerUrl(filePath, "document")}
                                        width="100%"
                                        height="100%"
                                        style={{ border: "none" }}
                                        title="Document Viewer"
                                      />
                                    ) : (
                                      <DocumentViewer
                                        queryParams="hl=Nl"
                                        url={filePath}
                                        viewerUrl={filePath}
                                        overrideLocalhost="https://react-doc-viewer.firebaseapp.com/"
                                      />
                                    )}
                                  </div>
                                )}
                                {fileTypeCheck === "ppt" && (
                                  <>
                                    <div style={{ height: "320px" }}>
                                      <ReactGoogleSlides
                                        width={640}
                                        height={300}
                                        slidesLink={filePath}
                                        slideDuration={5}
                                        position={1}
                                        showControls
                                        loop
                                      />
                                    </div>
                                    <div className="">
                                      {" "}
                                      <Button
                                        type="submit"
                                        onClick={() =>
                                          addCheckpointPPT(
                                            item?.course_module_id ?? "",
                                          )
                                        }
                                      >
                                        {t("buttons.addCheckpoint")}
                                      </Button>
                                    </div>
                                  </>
                                )}
                                {fileTypeCheck === "unknown" && (
                                  <div>
                                    <div style={{ height: "320px" }}>
                                      <ReactGoogleSlides
                                        width={640}
                                        height={300}
                                        slidesLink={filePath}
                                        slideDuration={5}
                                        position={1}
                                        showControls
                                        loop
                                      />
                                    </div>
                                    {/* <div className="w-full flex "> */}
                                    <div className="">
                                      {" "}
                                      <Button
                                        type="submit"
                                        onClick={() =>
                                          addCheckpointPPT(
                                            item?.course_module_id ?? "",
                                          )
                                        }
                                      >
                                        {t("buttons.addCheckpoint")}
                                      </Button>
                                    </div>
                                    {/* </div> */}
                                  </div>
                                )}
                              </>
                            )}
                          </div>
                        )}
                      </div>
                      <div className="w-1/2 md:col-span-2 lg:col-span-1 h-full border m-4 rounded-md p-2">
                        <Tabs defaultValue="checkpoints" className="w-full">
                          <TabsList>
                            {checkPointBtn && typeFile === "Url" && (
                              <TabsTrigger
                                value="checkpoints"
                                className="bg-[#00afbb] text-white"
                              >
                                {t("courses.courseModule.checkpoints")}{" "}
                              </TabsTrigger>
                            )}
                            {commentBtn && (
                              <TabsTrigger
                                value="comments"
                                className="bg-[#00afbb] text-white"
                              >
                                {t("courses.courseModule.comments")}{" "}
                              </TabsTrigger>
                            )}
                          </TabsList>
                          {checkPointBtn && (
                            <TabsContent value="checkpoints">
                              {addedValues.length > 0 && (
                                <div className="text-red-500 mt-4">
                                  {t("courses.courseModule.warningText")}
                                </div>
                              )}
                              {(existingCheckpoints.length > 0 ||
                                addedValues.length > 0) && (
                                <div className="w-full">
                                  <div className="border rounded-md p-4 mt-4 w-full">
                                    <DataTable
                                      columns={columns}
                                      data={
                                        existingCheckpoints.concat(
                                          addedValues,
                                        ) as CheckPointList[]
                                      }
                                      FilterLabel={"Filter by name"}
                                      FilterBy={"checkpoint_name"}
                                      actions={
                                        [
                                          // {
                                          //   title: "Delete",
                                          //   icon: Trash2,
                                          //   varient: "icon",
                                          //   handleClick: (val): void =>
                                          //     handleDelete(val as CheckPointForm[]),
                                          // },
                                        ]
                                      }
                                      customColumnWidths={customColumnWidths}
                                    />
                                  </div>
                                  {addedValues.length > 0 && (
                                    <div className="float-right mt-4">
                                      <Button
                                        type="submit"
                                        disabled={disableSubmitBtn}
                                        onClick={(e: BaseSyntheticEvent) => {
                                          checkPointUpdate(e).catch((error) =>
                                            console.log(error),
                                          );
                                        }}
                                      >
                                        {t("buttons.submit")}
                                      </Button>
                                    </div>
                                  )}
                                </div>
                              )}
                            </TabsContent>
                          )}
                          {pptcheckPointBtn && (
                            <TabsContent value="checkpoints">
                              {addedPPTValues.length > 0 && (
                                <div className="text-red-500 mt-4">
                                  {t("courses.courseModule.warningText")}
                                </div>
                              )}
                              {(existingPPTCheckpoints.length > 0 ||
                                addedPPTValues.length > 0) && (
                                <div className="w-full">
                                  <div className="border rounded-md p-4 mt-4 w-full">
                                    <DataTable
                                      columns={columnsppt}
                                      data={
                                        existingPPTCheckpoints.concat(
                                          addedPPTValues,
                                        ) as PPTCheckPointList[]
                                      }
                                      FilterLabel={"Filter by name"}
                                      FilterBy={"checkpoint_name"}
                                      actions={
                                        [
                                          // {
                                          //   title: "Delete",
                                          //   icon: Trash2,
                                          //   varient: "icon",
                                          //   handleClick: (val): void =>
                                          //     handleDelete(val as CheckPointForm[]),
                                          // },
                                        ]
                                      }
                                      customColumnWidths={pptcustomColumnWidths}
                                    />
                                  </div>
                                  {addedPPTValues.length > 0 && (
                                    <div className="float-right mt-4">
                                      <Button
                                        type="submit"
                                        disabled={disableSubmitBtn}
                                        onClick={(e: BaseSyntheticEvent) => {
                                          checkPointPPTUpdate(e).catch(
                                            (error) => console.log(error),
                                          );
                                        }}
                                      >
                                        {t("buttons.submit")}
                                      </Button>
                                    </div>
                                  )}
                                </div>
                              )}
                            </TabsContent>
                          )}
                          <TabsContent value="comments">
                            {comments?.length > 0 ? (
                              <div className="p-2 max-h-64 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                                <span className="font-bold text-lg">
                                  {comments?.length} Comments
                                </span>
                                <div className="flex flex-col mt-2 mb-2">
                                  {comments?.map((comment) => (
                                    <div
                                      key={comment.id}
                                      className="flex items-center p-4 border rounded-lg shadow-md mb-2"
                                    >
                                      <div className="flex-shrink-0">
                                        <Image
                                          className="w-10 h-10 rounded-full"
                                          src={
                                            comment?.avatar_url != null
                                              ? comment.avatar_url
                                              : "/../../../../assets/default-profile.png"
                                          }
                                          alt="Profile Image"
                                          width={60}
                                          height={60}
                                        />
                                      </div>
                                      <div className="ml-4 flex-grow max-w-md">
                                        <div className="font-medium">
                                          {comment.name}
                                        </div>
                                        <div className="text-gray-600  break-all">
                                          {comment.message}
                                        </div>
                                      </div>
                                      <div className="ml-4 flex-grow">
                                        <div className="text-gray-600 text-right">
                                          {moment(comment.created_at).format(
                                            "DD MMMM YYYY",
                                          )}
                                        </div>
                                        <div className="text-xs font-semibold text-black py-1 px-2 rounded text-right">
                                          <span className="border border-slate-500 rounded-lg pr-3 pl-3 pt-1 pb-1">
                                            {comment.type}
                                          </span>
                                        </div>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            ) : (
                              <div className="p-2">
                                {/* <span className="font-bold text-lg">
                                  20 Comments
                                </span> */}
                                {/* <div className="w-full space-y-4 mt-6">
                                  <div className="flex items-center space-x-4 ">
                                    <Skeleton className="h-12 w-12 rounded-full" />
                                    <div className="space-y-2">
                                      <Skeleton className="h-4 w-[350px]" />
                                      <Skeleton className="h-4 w-[200px]" />
                                    </div>
                                  </div>
                                  <div className="flex items-center space-x-4  ">
                                    <Skeleton className="h-12 w-12 rounded-full" />
                                    <div className="space-y-2">
                                      <Skeleton className="h-8 w-[250px]" />
                                      <Skeleton className="h-4 w-[200px]" />
                                    </div>
                                  </div>
                                  <div className="flex items-center space-x-4 ">
                                    <Skeleton className="h-12 w-12 rounded-full" />
                                    <div className="space-y-2">
                                      <Skeleton className="h-6 w-[270px]" />
                                      <Skeleton className="h-4 w-[200px]" />
                                    </div>
                                  </div>

                                  <div className="flex items-center space-x-4 ">
                                    <Skeleton className="h-12 w-12 rounded-full" />
                                    <div className="space-y-2">
                                      <Skeleton className="h-6 w-[100%]" />
                                      <Skeleton className="h-4 w-[200px]" />
                                    </div>
                                  </div>

                                  <div className="flex items-center space-x-4 ">
                                    <Skeleton className="h-12 w-12 rounded-full" />
                                    <div className="space-y-2">
                                      <Skeleton className="h-6 w-[270px]" />
                                      <Skeleton className="h-4 w-[200px]" />
                                    </div>
                                  </div>
                                  <div className="flex items-center space-x-4 mt-4 ">
                                    <Skeleton className="h-12 w-[90%]" />
                                    <Skeleton className="h-12 w-12 rounded-full" />
                                  </div>
                                </div> */}
                              </div>
                            )}
                          </TabsContent>
                        </Tabs>
                      </div>
                    </div>
                    <div className="flex justify-between p-4">
                      <div>
                        {index !== 0 && (
                          <div
                            className="flex font-semibold items-center cursor-pointer rounded-md p-1 pr-3 text-white text-base border-0 bg-[#00afbb]"
                            onClick={() => {
                              setSelectedResource(selectedResource - 1);
                              setTypeFile(previousType);
                              setKeyInstance(previousInstance);
                              void fetchFilesResources(
                                previousType,
                                previousUrlId,
                                previousModuleId,
                              );
                            }}
                          >
                            <ChevronLeftIcon className="flex h-8 w-[22px] justify-center " />
                            <span className="pl-1 relative -top-0.5">
                              {previousItem}
                            </span>
                          </div>
                        )}
                      </div>
                      <div>
                        {index !== totalCount - 1 && (
                          <div
                            className="flex font-semibold items-center  cursor-pointer   rounded-md  p-1 pl-3 text-white text-base border-0 bg-[#00afbb]"
                            onClick={() => {
                              setSelectedResource(selectedResource + 1);
                              setTypeFile(nextType);
                              setKeyInstance(nextInstance);
                              void fetchFilesResources(
                                nextType,
                                nextUrlId,
                                nextModuleId,
                              );
                            }}
                          >
                            <span className="pr-1 relative -top-0.5">
                              {nextItem}
                            </span>
                            <ChevronRightIcon className="flex h-8 w-[22px] justify-center  " />
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}
            </div>
          </div>
        </div>
        {totalCount - 1 === index && (
          <div className="flex flex-row relative ml-1">
            <div
              className={` cursor-pointer w-4 h-4 flex items-center justify-center rounded-full p-3 border-[#353e4e] border-2 bg-white after:w-3 after:h-3 after:bg-[#353e4e] after:absolute after:rounded-full `}
            ></div>
          </div>
        )}
      </>
    );
  };
  // const backToDetails = (): void => {
  //   router.push(
  //     `${pageUrl.courseDetails}?courseId=${courseId}&&expiry=${Expiry}&&is_premium=${is_premium}`,
  //   );
  // };

  const handleImportResource = (folderId?: string): void => {
    setOpenResource(!openResource);
    setOpenImportFolder(false);
    setImportFromFolderId(folderId ?? null);
  };

  const handleImportFolder = (): void => {
    setOpenFolder(!openFolder);
    setOpenImportFolder(false);
  };
  const handleImport = (): void => {
    setOpenImportFolder(false);
  };
  const importFolderToCourse = (): void => {
    setOpenImportFolder(!openImportFolder);
  };
  const handleDeleteResource = (): void => {
    setDeleteResourceModal(!deleteResourceModal);
    setDeleteResource(!resourceDelete);
  };
  const handleDeleteReload = (): void => {
    setDeleteResourceModal(!deleteResourceModal);
    setDeleteResource(!resourceDelete);
    setReloadResource(!reloadResource);
  };
  const handleSaveResource = (): void => {
    setOpenImportFolder(false);
    setOpenResource(false);
    setUpdateResource(false);
    setReloadAfterRecImport(!reloadAfterRecImport);
  };
  const handleSaveAfterImport = (): void => {
    setOpenImportFolder(false);
    setReloadAfterFolderImport(!reloadAfterFolderImport);
  };
  const handleSaveFolder = (): void => {
    setOpenFolder(!openFolder);
    setOpenImportFolder(!openImportFolder);
    setUpdateResource(!updateResource);
  };

  return (
    <div>
      {isFetching ? (
        <Spinner />
      ) : (
        <div>
          <div>
            {/* <h1 className="text-2xl font-semibold tracking-tight">
            {courseResourceData?.name} Module
          </h1> */}
          </div>
          <div className="border rounded-md p-4 mt-4 bg-[#fff]">
            {/* {disableBtn && (
            <Button
              className="float-right bg-[#fb8500] hover:bg-[#fb5c00] "
              onClick={handleAddResources}
            >
              <PlusIcon className="h-5 w-5" /> Add Resource
            </Button>
          )} */}
            <div>
              {!enableExpiry && disableBtn && (
                <Button
                  className="float-right bg-[#fb8500] hover:bg-[#fb5c00] me-4"
                  onClick={() => handleImportResource()}
                >
                  {t("buttons.importResource")}
                </Button>
              )}
            </div>
            <div>
              {!enableExpiry && disableBtn && (
                <Button
                  className="float-right bg-[#fb8500] hover:bg-[#fb5c00] me-4"
                  onClick={importFolderToCourse}
                >
                  {t("buttons.importFolder")}
                </Button>
              )}
            </div>
            {disableBtn && !enableExpiry && (
              <div>
                <Button
                  className="float-right bg-[#fb8500] hover:bg-[#fb5c00] me-4"
                  onClick={handleImportFolder}
                >
                  {t("buttons.addFolder")}
                </Button>
              </div>
            )}

            {/* Rearrange Resources Button for Main Section */}
            {/* {disableBtn && !enableExpiry && courseResourceData?.modules && (
              <div>
                <Button
                  className="float-right bg-[#9fc089] hover:bg-[#8fb079] me-4"
                  onClick={handleMainReorder}
                  disabled={courseResourceData.modules.length < 2}
                >
                  <ArrowUpDown className="h-4 w-4 mr-2" />
                  Rearrange Resources
                  {courseResourceData.modules.length < 2 && (
                    <span className="ml-1 text-xs opacity-75">
                      ({courseResourceData.modules.length} resource
                      {courseResourceData.modules.length !== 1 ? "s" : ""})
                    </span>
                  )}
                </Button>
              </div>
            )} */}

            {/* Rearrange Resources Button for Main Section */}
            {disableBtn && !enableExpiry && courseResourceData?.modules && (
              <div>
                <Button
                  className="float-right bg-[#9fc089] hover:bg-[#8fb079] me-4"
                  onClick={handleMainReorder}
                  disabled={courseResourceData.modules.length < 2}
                >
                  <ArrowUpDown className="h-4 w-4 mr-2" />
                  Rearrange Resources
                  {courseResourceData.modules.length < 2 && (
                    <span className="ml-1 text-xs opacity-75">
                      ({courseResourceData.modules.length} resource
                      {courseResourceData.modules.length !== 1 ? "s" : ""})
                    </span>
                  )}
                </Button>
              </div>
            )}

            <div className="p-4 mt-8">
              {/* <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"> */}
              <div>
                <div className="text-left">
                  {courseResourceData?.modules?.map((indv, indx: number) => (
                    <Step
                      item={indv}
                      key={indv.instance}
                      index={indx}
                      previousItem={
                        indx !== 0
                          ? courseResourceData?.modules?.[indx - 1]?.module_name
                          : ""
                      }
                      previousType={
                        indx !== 0
                          ? courseResourceData?.modules?.[indx - 1]?.module_type
                          : ""
                      }
                      nextType={
                        indx !== courseResourceData?.modules?.length
                          ? courseResourceData?.modules?.[indx + 1]?.module_type
                          : ""
                      }
                      previousUrlId={
                        indx !== 0
                          ? courseResourceData?.modules?.[indx - 1]?.instance
                          : ""
                      }
                      nextUrlId={
                        indx !== courseResourceData?.modules?.length
                          ? courseResourceData?.modules?.[indx + 1]?.instance
                          : ""
                      }
                      previousModuleId={
                        indx !== 0
                          ? courseResourceData?.modules?.[indx - 1]
                              ?.course_module_id
                          : ""
                      }
                      nextModuleId={
                        indx !== courseResourceData?.modules?.length
                          ? courseResourceData?.modules?.[indx + 1]
                              ?.course_module_id
                          : ""
                      }
                      nextItem={
                        indx !== courseResourceData?.modules?.length
                          ? courseResourceData?.modules?.[indx + 1]?.module_name
                          : ""
                      }
                      previousInstance={
                        indx !== 0
                          ? courseResourceData?.modules?.[indx - 1]?.instance
                          : ""
                      }
                      nextInstance={
                        courseResourceData?.modules?.[indx + 1]?.instance
                      }
                      totalCount={courseResourceData?.modules?.length}
                    />
                  ))}
                  {/* )} */}
                </div>
                {/* </div> */}
              </div>
            </div>

            {/* Rearrange Folders Button */}
            {disableBtn &&
              !enableExpiry &&
              courseResourceData?.folders &&
              courseResourceData.folders.length > 0 && (
                <div className="mb-4 flex justify-end pr-16">
                  <Button
                    className="bg-[#9fc089] hover:bg-[#8fb079] text-white"
                    onClick={handleFolderListReorder}
                    disabled={courseResourceData.folders.length < 2}
                  >
                    <ArrowUpDown className="h-4 w-4 mr-2" />
                    Rearrange Folders
                    {courseResourceData.folders.length < 2 && (
                      <span className="ml-1 text-xs opacity-75">
                        ({courseResourceData.folders.length} folder
                        {courseResourceData.folders.length !== 1 ? "s" : ""})
                      </span>
                    )}
                  </Button>
                </div>
              )}

            <Accordion multiple className="rounded-lg bg-blue pl-16">
              {courseResourceData?.folders?.map((folder) => (
                <AccordionTab
                  key={folder.folder_id}
                  header={
                    <div className="flex justify-between items-center w-full pr-4">
                      <span>{folder.folder_name}</span>

                      <div className="flex items-center gap-2">
                        {disableBtn &&
                          !enableExpiry &&
                          folder.resources.length > 1 && (
                            <Button
                              className="bg-[#9fc089] hover:bg-[#8fb079] text-white text-xs px-2 py-1 h-7 flex items-center"
                              onClick={(e) => {
                                e.stopPropagation();
                                void handleFolderReorder(folder.folder_id);
                              }}
                            >
                              <ArrowUpDown className="h-3 w-3 mr-1" />
                              {/* Rearrange */}
                            </Button>
                          )}
                        <Import   onClick={() => handleImportResource(folder.folder_id)}>
                          {t("buttons.importResource")}
                        </Import>
                      </div>
                    </div>
                  }
                  className="border border-gray-200 bg-gray-100 rounded mb-5"
                >
                  <div className="p-2  shadow-md rounded-lg mb-2">
                    {folder?.resources?.length > 0 ? (
                      folder.resources.map((indv, indx) => {
                        const stepIndex = indx;
                        return (
                          <Step
                            item={indv}
                            key={indv.instance}
                            index={stepIndex}
                            previousItem={
                              indx !== 0
                                ? folder.resources[indx - 1]?.module_name
                                : ""
                            }
                            previousType={
                              indx !== 0
                                ? folder.resources[indx - 1]?.module_type
                                : ""
                            }
                            previousInstance={
                              indx !== 0
                                ? folder.resources[indx - 1]?.instance
                                : ""
                            }
                            nextInstance={folder.resources[indx + 1]?.instance}
                            nextType={
                              indx !== folder.resources.length - 1
                                ? folder.resources[indx + 1]?.module_type
                                : ""
                            }
                            previousUrlId={
                              indx !== 0
                                ? folder.resources[indx - 1]?.instance
                                : ""
                            }
                            nextUrlId={
                              indx !== folder.resources.length - 1
                                ? folder.resources[indx + 1]?.instance
                                : ""
                            }
                            previousModuleId={
                              indx !== 0
                                ? folder.resources[indx - 1]?.course_module_id
                                : ""
                            }
                            nextModuleId={
                              indx !== folder.resources.length - 1
                                ? folder.resources[indx + 1]?.course_module_id
                                : ""
                            }
                            nextItem={
                              indx !== folder.resources.length - 1
                                ? folder.resources[indx + 1]?.module_name
                                : ""
                            }
                            totalCount={folder.resources.length}
                          />
                        );
                      })
                    ) : (
                      <p className="text-gray-500">No resources found</p>
                    )}
                  </div>
                </AccordionTab>
              ))}
            </Accordion>
          </div>
          {/* <div className="flex items-center justify-end pt-5">
          <Button
            type="button"
            className="bg-[#33363F]"
            onClick={backToDetails}
          >
            Cancel
          </Button>
        </div> */}
        </div>
      )}

      {isDialogOpen && (
        <Modal
          title={t("courses.courseModule.addResource")}
          header=""
          openDialog={isDialogOpen}
          closeDialog={(value: boolean) => closeDialog(value)}
          type="max-w-5xl"
        >
          <CourseResourceAddForm
            closeDialog={(value: boolean) => closeDialog(value)}
          />
        </Modal>
      )}
      {checkpointModalStatus && !enableExpiry && (
        <Modal
          title={t("courses.courseModule.checkpointDetails")}
          header=""
          openDialog={checkpointModalStatus}
          closeDialog={handleAddCheckpoint}
          type="max-w-3xl"
        >
          <AddCheckpointModal
            closeDialog={handleAddCheckpoint}
            checkPointData={selectedResourceData as ViewResourcePageType}
            checkpointTime={checkpointTime}
            onAddedValuesChange={handleAddedValuesChange}
            allCheckPointData={addedValues}
            videoLength={videoFullLength}
            existingCheckPoints={existingCheckpoints}
          />
        </Modal>
      )}
      {openResource && (
        <Modal
          title={t("courses.courseModule.importResource")}
          header=""
          openDialog={openResource}
          closeDialog={handleImportResource}
          type="max-w-7xl"
        >
          <ImportResource
            onSave={handleSaveResource}
            onCancel={handleImportResource}
            isModal={true}
            courseId={courseId}
            sectionsId={sectionId as string}
            is_premium={is_premium as string}
            moduleOrders={moduleOrders}
            preselectedFolderId={importFromFolderId}
          />
        </Modal>
      )}
      {deleteResourceModal && (
        <Modal
          title={t("courses.courseModule.deleteResource")}
          header=""
          openDialog={deleteResourceModal}
          closeDialog={handleDeleteResource}
          type="max-w-xl"
        >
          <DeleteResource
            onSave={handleDeleteReload}
            onCancel={handleDeleteResource}
            isModal={true}
            courseModuleId={courseModuleId}
            courseId={courseId}
          />
        </Modal>
      )}
      {openFolder && (
        <Modal
          title={t("courses.courseModule.addFolder")}
          header=""
          openDialog={openFolder}
          closeDialog={handleImportFolder}
          type="max-w-3xl"
        >
          <AddFolder
            onSave={handleSaveFolder}
            onCancel={handleImportFolder}
            courseId={courseId}
            sectionsId={sectionId as string}
          />
        </Modal>
      )}
      {openImportFolder && (
        <Modal
          title={t("courses.courseModule.importFolder")}
          header=""
          openDialog={openImportFolder}
          closeDialog={handleImport}
          type="max-w-7xl"
        >
          <ImportFolder
            onSave={handleSaveAfterImport}
            onCancel={handleImport}
            courseId={courseId}
            sectionsId={sectionId as string}
            is_premium={is_premium as string}
            moduleOrders={moduleOrders}
          />
        </Modal>
      )}
      {checkpointModalValue && (
        <Modal
          title={t("courses.courseModule.checkpointDetails")}
          header=""
          openDialog={checkpointModalValue}
          closeDialog={handleAddPPTCheckpoint}
          type="max-w-3xl"
        >
          <AddCheckpointPPTModal
            closeDialog={handleAddPPTCheckpoint}
            checkPointData={selectedResourceData as ViewResourcePageType}
            checkpointTime={checkpointTime}
            onAddedValuesChange={handleAddedPPTValuesChange}
            allCheckPointData={addedPPTValues}
            coursePPTModuleId={coursePPTModuleId}
            existingCheckPoints={existingPPTCheckpoints}
          />
        </Modal>
      )}

      {/* Main Section Resource Reorder Modal */}
      {isMainReorderOpen && courseResourceData?.modules && (
        <ResourceReorderModal
          isOpen={isMainReorderOpen}
          onClose={() => setIsMainReorderOpen(false)}
          resources={courseResourceData.modules.map((module) => ({
            module_id: module.module_id,
            module_name: module.module_name,
            module_type: module.module_type,
            module_order: module.module_order,
            course_module_id: module.course_module_id,
            instance: module.instance,
          }))}
          sectionTitle={
            courseResourceData.name.length > 0
              ? courseResourceData.name
              : "Main Section"
          }
          onSave={handleMainReorderSave}
        />
      )}

      {/* Folder Resource Reorder Modal */}
      {isFolderReorderOpen && courseResourceData?.folders && (
        <ResourceReorderModal
          isOpen={isFolderReorderOpen}
          onClose={() => setIsFolderReorderOpen(false)}
          resources={
            courseResourceData.folders
              .find((folder) => folder.folder_id === selectedFolderId)
              ?.resources.map((resource) => ({
                module_id: resource.module_id,
                module_name: resource.module_name,
                module_type: resource.module_type,
                module_order: resource.module_order,
                course_module_id: resource.course_module_id,
                instance: resource.instance,
              })) ?? []
          }
          sectionTitle={
            courseResourceData.folders.find(
              (folder) => folder.folder_id === selectedFolderId,
            )?.folder_name ?? "Folder"
          }
          onSave={handleFolderReorderSave}
        />
      )}

      {/* Folder List Reorder Modal */}
      {isFolderListReorderOpen && courseResourceData?.folders && (
        <ResourceReorderModal
          isOpen={isFolderListReorderOpen}
          onClose={() => setIsFolderListReorderOpen(false)}
          resources={courseResourceData.folders.map((folder, index) => ({
            module_id: folder.folder_id,
            module_name: folder.folder_name,
            module_type: "Folder",
            module_order: index + 1,
            course_module_id: folder.folder_id,
            instance: folder.folder_id,
          }))}
          sectionTitle="Course Folders"
          onSave={handleFolderListReorderSave}
        />
      )}
    </div>
  );
}
