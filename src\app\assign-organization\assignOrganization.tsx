"use client";
import React, { useEffect, useState } from "react";
import { useToast } from "@/components/ui/use-toast";
import type {
  AssignOrganizationRequest,
  AssignUserList,
  ComboData,
  ErrorCatch,
  LogUserActivityRequest,
  ToastType,
} from "@/types";
import { Button } from "@/components/ui/button";
import { Combobox } from "@/components/ui/combobox";
import useAssignOrganization from "@/hooks/useAssignOrganization";
import useUsers from "@/hooks/useUsers";
import { Label } from "@/components/ui/label";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import { useTranslation } from "react-i18next";

export default function AssignOrganization({
  onSave,
  onCancel,
  selectedData,
  setIsLoading,
}: {
  onSave: () => void;
  onCancel: () => void;
  setIsLoading: (status: boolean) => void;
  selectedData: AssignUserList[];
}): React.JSX.Element {
  const { t } = useTranslation();
  const [comboData, setComboData] = useState<ComboData[]>([]);
  const [rolesComboData, setRolesComboData] = useState<ComboData[]>([]);
  const [selectedRole, setSelectedRole] = useState<string>("");
  const [selectedOrg, setSelectedOrg] = useState<string>("");
  const { getOrganizationList } = useAssignOrganization();
  const { getRoles, updateUser } = useUsers();
  const { updateUserActivity } = useLogUserActivity();
  const { toast } = useToast() as ToastType;

  useEffect(() => {
    const fetchAssignedUsers = async (): Promise<void> => {
      try {
        setIsLoading(false);
        const usersList = await getOrganizationList();
        const comboData: ComboData[] = usersList.map((item) => ({
          value: item.org_id,
          label: item.org_name,
        }));

        setComboData(comboData);
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
      }
    };

    fetchAssignedUsers().catch((error) => console.log(error));
  }, []);

  const handleSaveClick = async (): Promise<void> => {
    const ids: string[] = selectedData.map((item) => item.id);
    setIsLoading(true);
    if (selectedOrg !== "" && selectedRole != "") {
      const data: AssignOrganizationRequest = {
        org_id: selectedOrg,
        role_id: selectedRole,
        user_ids: ids,
      };
      try {
        const result = await updateUser(data);

        if (result.status === "success") {
          onSave();
          toast({
            variant: SUCCESS_MESSAGES.toast_variant_default,
            title: t("successMessages.toast_success_title"),
            description: t("successMessages.assign_organization"),
          });

          onCancel();
          const params = {
            activity_type: "Organization",
            screen_name: "Assign Organization",
            action_details: "User assigned to organization ",
            target_id: selectedRole as string,
            log_result: "SUCCESS",
          };
          void updateLogUserActivity(params).catch((error) => {
            console.error(error);
          });
        } else if (result.status === "error") {
          setIsLoading(false);
          toast({
            variant: ERROR_MESSAGES.toast_variant_destructive,
            title: t("errorMessages.toast_error_title"),
            description: result.status,
          });
          console.log("API Error:", result.status);
          const params = {
            activity_type: "Organization",
            screen_name: "Assign Organization",
            action_details: "Assign user to organization failed",
            target_id: selectedRole as string,
            log_result: "ERROR",
          };
          void updateLogUserActivity(params).catch((error) => {
            console.error(error);
          });
        }
      } catch (error) {
        setIsLoading(false);
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.details,
        });
        console.error("An unexpected error occurred:", error);
        const params = {
          activity_type: "Organization",
          screen_name: "Assign Organization",
          action_details: "Assign user to organization failed ",
          target_id: selectedRole as string,
          log_result: "ERROR",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      }
    } else {
      setIsLoading(false);
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.bothFilesMandatory"),
      });
    }
  };

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  const getOrgRoleList = async (res: string): Promise<void> => {
    try {
      const roles = await getRoles(res);
      if (roles !== null && roles.length > 0) {
        const filteredRoles = roles.map((role) => ({
          value: role.id,
          label: role.display_name ?? role.name,
        }));
        setRolesComboData(filteredRoles);
      } else {
        setRolesComboData([]);
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
    }
  };
  const comboSelectedValue = (res: string): void => {
    if (res !== "") {
      setSelectedOrg(res);
      getOrgRoleList(res).catch((error) => console.log(error));
    }
  };
  const comboSelectedRole = (res: string): void => {
    setSelectedRole(res);
  };

  return (
    <>
      <div className="mb-2 mr-4">
        <Label className="block mb-2">
          {t("assignOrganization.selectOrganization")}
          <span className="text-red-700">*</span>
        </Label>
        <Combobox data={comboData} onSelectChange={comboSelectedValue} />
      </div>
      <div className="mb-2 mr-4">
        <Label className="block mb-2">
          {t("assignOrganization.selectRole")}
          <span className="text-red-700">*</span>
        </Label>
        <Combobox
          data={rolesComboData}
          onSelectChange={comboSelectedRole}
          isDisabled={selectedOrg == ""}
        />
      </div>

      <div className="flex topic-icons pr-4">
        <div className="flex items-center justify-center float-right gap-3">
          <Button type="button" className="bg-[#33363F]" onClick={onCancel}>
            {t("buttons.cancel")}
          </Button>

          <Button
            type="submit"
            className="bg-[#9FC089]"
            onClick={() => {
              handleSaveClick().catch((error) => console.log(error));
            }}
          >
            {t("buttons.submit")}
          </Button>
        </div>
      </div>
    </>
  );
}
