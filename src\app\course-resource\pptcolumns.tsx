"use client";

import type { ColumnDef, Row } from "@tanstack/react-table";
import React from "react";
import type { PPTCheckPointList } from "@/types";

interface RowDefinition {
  row: Row<PPTCheckPointList>;
}

export const getColumnsPpt = (
  t: (key: string) => string,
): ColumnDef<PPTCheckPointList>[] => [
  {
    accessorKey: "sequence_number",
    header: t("courses.courseModule.seqNo"),
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div className="text-center">{row.original.sequence_number}</div>
    ),
  },
  {
    accessorKey: "checkpoint_name",
    header: t("courses.courseModule.name"),
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div>{row.original.checkpoint_name}</div>
    ),
  },
  {
    accessorKey: "checkpoint_slide",
    header: t("courses.courseModule.slideNo"),
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div>{row.original.checkpoint_slide}</div>
    ),
  },

  {
    accessorKey: "isMandatory",
    header: t("courses.courseModule.mandatory"),
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div>{row.original.isMandatory}</div>
    ),
  },
  // {
  //   accessorKey: "checkpoint_reslabel",
  //   header: "Exam Name",
  //   cell: ({ row }: RowDefinition): React.JSX.Element => (
  //     <div>{row.original.checkpoint_reslabel}</div>
  //   ),
  // },
];
