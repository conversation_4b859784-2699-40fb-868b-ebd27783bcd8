import React from "react";
import type { ErrorCatch, ToastType, UsersDataType } from "@/types";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import useUsers from "@/hooks/useUsers";
import { useTranslation } from "react-i18next";
export default function DeactivateUser({
  userData,
  onSave,
  onCancel,
}: {
  onSave: () => void;
  onCancel: () => void;
  userData: UsersDataType;
  isModal?: boolean;
}): React.JSX.Element {
  const { t } = useTranslation();
  const { toast } = useToast() as ToastType;
  const { deactivateUsers } = useUsers();
  const handleApproveClick = (): void => {
    void handleToastPublish();
    onCancel();
  };
  const handleToastPublish = async (): Promise<void> => {
    const DeactivateUserData = {
      user_id: userData.id,
      org_id: userData.org_id,
      block: userData.status === "Active" ? true : false,
    };
    try {
      const result = await deactivateUsers(DeactivateUserData);
      if (result.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title:
            userData.status === "Active"
              ? t("successMessages.deactivate_user")
              : t("successMessages.activate_user"),
          description:
            userData.status === "Active"
              ? t("successMessages.deactivate_user_successfully")
              : t("successMessages.activate_user_successfully"),
        });
        onSave();
        onCancel();
      } else if (result.status === "error") {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: t("errorMessages.deactivate_fail"),
        });
        console.log("API Error:", result.status);
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
      console.error("An unexpected error occurred:", error);
    }
  };

  return (
    <>
      <div className="mb-2 mr-4">
        {userData.status === "Active" ? (
          <p className="ml-0 ">
            Do you want to <strong>Deactivate</strong> the user ?
          </p>
        ) : (
          <p className="ml-0 ">
            Do you want to <strong>Activate</strong> this user ?
          </p>
        )}
      </div>
      <div className="flex topic-icons pr-4">
        <div className="flex items-center justify-center float-right gap-3">
          <Button type="button" className="bg-[#33363F]" onClick={onCancel}>
            Cancel
          </Button>
          <Button
            type="submit"
            className="bg-[#9FC089]"
            onClick={handleApproveClick}
          >
            Submit
          </Button>
        </div>
      </div>
    </>
  );
}
