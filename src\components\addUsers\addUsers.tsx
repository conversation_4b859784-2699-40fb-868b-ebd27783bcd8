"use client";

import React, { useState, useEffect, type FC } from "react";
import "../../styles/auth.css";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useForm } from "react-hook-form";
import type {
  AddUserForm,
  ToastType,
  ErrorCatch,
  AddUserProps,
  UpdateUserParams,
  UserUpdate,
  ComboData,
  // InviteUserRequest,
  LogUserActivityRequest,
} from "@/types";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  AddUserSchema,
  // InviteUserSchema,
} from "@/schema/schema";
import useUsers from "@/hooks/useUsers";
import { useToast } from "@/components/ui/use-toast";
import { Combobox } from "@/components/ui/combobox";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import { API_RESPONSE_SUCCESS, COUNTRY_CODES, ORG_KEY } from "@/lib/constants";
import {
  User,
  //  UserPlus
} from "lucide-react";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { t } from "i18next";

const UserAdd: FC<AddUserProps> = ({
  closeDialog,
  usersList,
  allUsers,
}): React.JSX.Element => {
  const {
    getRoles,
    updateUser,
    signUpUser,
    getReportingToResource,
    // inviteNewUser,
  } = useUsers();
  const { toast } = useToast() as ToastType;
  const form = useForm<AddUserForm>({
    resolver: zodResolver(AddUserSchema),
  });
  const { handleSubmit } = form;
  const { updateUserActivity } = useLogUserActivity();

  const [roleData, setRoles] = useState<{ value: string; label: string }[]>([]);
  const [userData, setUserData] = useState<{ value: string; label: string }[]>(
    [],
  );
  const [selectedRole, setSelectedRole] = React.useState<string>("");
  const [selectedUser, setSelectedUser] = React.useState<string>("");
  const [selectedCode, setselectedCode] = React.useState<string>("+91");
  const [roleName, setRoleName] = React.useState<string>("");
  const [selectedTab, setSelectedTab] = useState("User Details");

  const navigationTabs = [
    { id: "User Details", icon: User },
    // { id: "Invite User", icon: UserPlus },
  ];

  // const inviteForm = useForm<InviteUserRequest>({
  //   resolver: zodResolver(InviteUserSchema),
  // });

  useEffect(() => {
    allUsers = allUsers.map((item) => {
      item.define_heirarchy = true;
      return item;
    });

    const fetchRoles = async (): Promise<void> => {
      try {
        const org_id = localStorage.getItem(ORG_KEY) as string;
        const roles = await getRoles(org_id);
        if (roles !== null && roles.length > 0) {
          const filteredRoles = roles.map((role) => ({
            value: role.id,
            label: role.display_name ?? role.name,
          }));
          setRoles(filteredRoles);
        } else {
          setRoles([]);
        }
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
      }
    };

    fetchRoles().catch((error) => console.log(error));
  }, []);
  const fetchReporters = async (selectedOption: string): Promise<void> => {
    try {
      const org_id = localStorage.getItem(ORG_KEY) as string;
      const params = {
        org_id: org_id,
        role_id: selectedOption,
      };
      const reporters = await getReportingToResource(params);
      if (reporters != null) {
        const filteredUsers = reporters.map((user) => ({
          value: user.id,
          label: `${user.first_name} ${user.last_name}`,
        }));
        setUserData(filteredUsers);
      } else {
        setUserData([]);
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
    }
  };

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  async function onSubmit(): Promise<void> {
    const formData = form.getValues();
    const { first_name, last_name, email, password, phonenumber1 } = formData;
    // Perform null check on form values
    if (
      first_name == null ||
      last_name == null ||
      email == null ||
      password == null ||
      phonenumber1 == null
    ) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.enterAllDetails"),
      });
      return;
    }

    if (selectedRole === null || selectedRole === "") {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.select_role_msg"),
      });
      return;
    }
    if (roleName !== "Admin") {
      if (selectedUser === null || selectedUser === "") {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: t("errorMessages.select_user_msg"),
        });
        return;
      }
    }
    const params = {
      email: formData.email,
      password: formData.password,
      first_name: formData.first_name,
      last_name: formData.last_name,
      phonenumber1: selectedCode + " " + formData.phonenumber1,
      role_id: selectedRole,
      reporting_to: selectedUser,
    };
    try {
      const userData = await signUpUser(params);
      if (userData?.user?.id !== null) {
        const userUpdateData: UpdateUserParams = {
          role_id: selectedRole,
          user_id: userData.user.id,
        };
        const params = {
          activity_type: "SignUp",
          screen_name: "User",
          action_details: "User signed up successfully",
          target_id: userData?.user?.id as string,
          log_result: "SUCCESS",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
        await updateUserList(userUpdateData);
        closeDialog(true);
        usersList(true);
      } else {
        throw new Error(ERROR_MESSAGES.user_not_found_msg);
        const params = {
          activity_type: "SignUp",
          screen_name: "User",
          action_details: "Failed to user sign up",
          target_id: userData?.user?.id as string,
          log_result: "ERROR",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
    }
  }

  async function updateUserList(params: UpdateUserParams): Promise<void> {
    const role_id = params.role_id;
    const user_id = params.user_id;
    const org_id = localStorage.getItem(ORG_KEY);
    const requestBody: UserUpdate = {
      org_id: org_id ?? "",
      role_id: role_id ?? "",
      user_ids: [user_id],
    };
    try {
      const result = await updateUser(requestBody);
      if (result.status === API_RESPONSE_SUCCESS) {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.toast_success_title"),
          description: SUCCESS_MESSAGES.update_user_msg,
        });
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
    }
  }

  const handleCancel = (): void => {
    closeDialog(true);
  };

  const handleRoleChange = (selectedOption: string): void => {
    setSelectedRole(selectedOption);
    const roleName = getNameById(selectedOption);
    setRoleName(roleName);
    void fetchReporters(selectedOption);
  };
  const handleUserChange = (selectedOption: string): void => {
    setSelectedUser(selectedOption);
  };
  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    const value = e.target.value;

    // Sanitize the value by removing leading spaces and invalid characters
    const sanitizedValue = value
      .replace(/^\s+/, "") // Remove leading spaces
      .replace(/[^a-zA-Z0-9@._-]/g, ""); // Allow only valid email characters

    // Manually set the form value to ensure it updates
    form.setValue("email", sanitizedValue, {
      shouldValidate: true,
      shouldDirty: true,
    });
  };

  const getNameById = (id: string): string => {
    const role = roleData.find((role: ComboData) => role.value === id);
    return role ? role.label : ""; // Returns the name if found, otherwise null
  };

  /* async function onInviteSubmit(): Promise<void> {
    const inviteFormData = inviteForm.getValues();
    const org_id = localStorage.getItem(ORG_KEY);
    const { recipient } = inviteFormData;
    if (recipient == null) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: ERROR_MESSAGES.toast_error_title,
        description: "Please enter email",
      });
      return;
    } else {
      const params = {
        recipient: inviteFormData.recipient,
      };
      console.log("params", params);

      try {
        const response = await inviteNewUser(params);
        if (response.status === "success") {
          toast({
            variant: "success",
            title: "Success",
            description: response.message,
          });
          const params = {
            activity_type: "SignUp",
            screen_name: "User",
            action_details: "User signed up successfully",
            target_id: org_id as string,
            log_result: "SUCCESS",
          };
          void updateLogUserActivity(params).catch((error) => {
            console.error(error);
          });
          closeDialog(true);
        } else {
          toast({
            variant: "destructive",
            title: "Error",
            description: ERROR_MESSAGES.invite_user,
          });
          const params = {
            activity_type: "SignUp",
            screen_name: "User",
            action_details: "Failed to user sign up",
            target_id: org_id as string,
            log_result: "ERROR",
          };
          void updateLogUserActivity(params).catch((error) => {
            console.error(error);
          });
        }
      } catch (error) {
        const err = error as ErrorCatch;
        console.error("Error in invite user:", err);
        toast({
          variant: "destructive",
          title: "Error",
          description: ERROR_MESSAGES.invite_user,
        });
      }
    }
  }*/

  return (
    <>
      <div className="flex">
        <main className="flex-1 pl-2">
          <div className="bg-white border-b border-slate-200">
            <div className="flex items-center h-14">
              {navigationTabs.map((tab) => (
                <button
                  key={tab.id}
                  className={`flex items-center gap-2 px-4 py-2 -mb-px h-14
                  ${
                    selectedTab === tab.id
                      ? "text-[#218faa] border-b-2 border-[#218faa] font-medium"
                      : "text-slate-600 hover:text-slate-900"
                  }`}
                  onClick={() => setSelectedTab(tab.id)}
                >
                  <tab.icon className="w-4 h-4" />
                  {tab.id}
                </button>
              ))}
            </div>
          </div>

          <div className="pt-6">
            {selectedTab === "User Details" && (
              <div>
                <div className="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
                  <div className="mx-auto flex w-full flex-col justify-center space-y-6 ">
                    <Form {...form}>
                      <form
                        onSubmit={(event) => void handleSubmit(onSubmit)(event)}
                        className="grid gap-4"
                      >
                        <div className="grid gap-2">
                          <FormField
                            name="first_name"
                            control={form.control}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>
                                  First Name{" "}
                                  <span className="next-button">*</span>
                                </FormLabel>
                                <FormControl>
                                  <Input
                                    type="text"
                                    maxLength={35}
                                    autoComplete="off"
                                    value={field.value}
                                    onChange={(e) => {
                                      const value = e.target.value;
                                      const sanitizedValue = value.replace(
                                        /[^a-zA-Z]/g,
                                        "",
                                      );
                                      form.setValue(
                                        "first_name",
                                        sanitizedValue,
                                      );
                                    }}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                        <div className="grid gap-2">
                          <FormField
                            name="last_name"
                            control={form.control}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>
                                  Last Name{" "}
                                  <span className="next-button">*</span>
                                </FormLabel>
                                <FormControl>
                                  <Input
                                    type="text"
                                    maxLength={35}
                                    autoComplete="off"
                                    value={field.value}
                                    onChange={(e) => {
                                      const value = e.target.value;
                                      const sanitizedValue = value.replace(
                                        /[^a-zA-Z]/g,
                                        "",
                                      );
                                      form.setValue(
                                        "last_name",
                                        sanitizedValue,
                                      );
                                    }}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                        <div className="grid gap-2">
                          <FormField
                            name="email"
                            control={form.control}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>
                                  Email <span className="next-button">*</span>
                                </FormLabel>
                                <FormControl>
                                  <Input
                                    type="email"
                                    autoComplete="off"
                                    {...field}
                                    onChange={(e) => {
                                      handleEmailChange(e);
                                      field.onChange(e); // Ensure field's onChange is also called
                                    }}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                        <div className="grid gap-2">
                          <FormField
                            name="password"
                            control={form.control}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>
                                  Password{" "}
                                  <span className="next-button">*</span>
                                </FormLabel>
                                <FormControl>
                                  <Input
                                    type="password"
                                    autoComplete="off"
                                    {...field}
                                    value={field.value}
                                    onChange={(e) => {
                                      const value = e.target.value;
                                      // Remove white spaces from the input value
                                      const sanitizedValue = value.replace(
                                        /\s/g,
                                        "",
                                      );
                                      // Update the form field value with the sanitized value
                                      form.setValue("password", sanitizedValue);
                                    }}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        <div className="grid gap-2">
                          <FormField
                            name="phonenumber1"
                            control={form.control}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>
                                  Phone number{" "}
                                  <span className="next-button">*</span>
                                </FormLabel>
                                <FormControl>
                                  <div style={{ display: "flex" }}>
                                    <select
                                      style={{
                                        width: "150px",
                                        marginRight: "10px",
                                        border: "1px solid #ccc",
                                        borderRadius: "5px",
                                      }}
                                      defaultValue="+91"
                                      onChange={(event) => {
                                        const selectedCountryCode =
                                          event.target.value;
                                        setselectedCode(selectedCountryCode);
                                      }}
                                    >
                                      {COUNTRY_CODES.map((country, index) => (
                                        <option
                                          key={index}
                                          value={country.code}
                                        >
                                          {country.name} ({country.code})
                                        </option>
                                      ))}
                                    </select>
                                    <Input
                                      type="text"
                                      maxLength={10}
                                      autoComplete="off"
                                      {...field}
                                      onChange={(e) => {
                                        const onlyDigits =
                                          e.target.value.replace(/\D/g, "");
                                        if (onlyDigits.length <= 10) {
                                          field.onChange(onlyDigits);
                                        }
                                      }}
                                    />
                                  </div>
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        <div className="grid gap-2">
                          <FormItem>
                            <FormLabel>
                              Select Role <span className="next-button">*</span>{" "}
                            </FormLabel>
                            <FormControl>
                              <Combobox
                                data={roleData}
                                onSelectChange={handleRoleChange}
                              />
                            </FormControl>
                            {/* {(!selectedRole || selectedRole === "") && (
                      <FormMessage>Please select a category</FormMessage>
                    )} */}
                          </FormItem>
                        </div>
                        {selectedRole.length > 0 && (
                          <div className="grid gap-2">
                            <FormItem>
                              <FormLabel>
                                Reporting To{" "}
                                {roleName !== "Admin" && (
                                  <span className="next-button">*</span>
                                )}{" "}
                              </FormLabel>
                              <FormControl>
                                <Combobox
                                  data={userData}
                                  onSelectChange={handleUserChange}
                                />
                              </FormControl>
                              {/* {(!selectedRole || selectedRole === "") && (
                      <FormMessage>Please select a category</FormMessage>
                    )} */}
                            </FormItem>
                          </div>
                        )}
                        <div className="flex flex-wrap justify-end mt-6 space-x-3">
                          <Button
                            className="w-full sm:w-auto bg-[#33363F]"
                            onClick={handleCancel}
                          >
                            Cancel
                          </Button>
                          <Button type="submit" className="bg-[#9FC089]">
                            Submit
                          </Button>
                        </div>
                      </form>
                    </Form>
                  </div>
                </div>
              </div>
            )}

            {/* {selectedTab === "Invite User" && (
              <div className="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
                <Form {...inviteForm}>
                  <form
                    // eslint-disable-next-line @typescript-eslint/no-misused-promises
                    onSubmit={inviteForm.handleSubmit(onInviteSubmit)}
                    className="space-y-8"
                  >
                    <div className="mt-4">
                      <FormField
                        name="recipient"
                        control={inviteForm.control}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Enter the user&apos;s email address to send
                              invitation mail{" "}
                              <span className="text-red-700">*</span>
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Enter email"
                                type="text"
                                maxLength={50}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="w-full flex justify-end mt-4 gap-3">
                      <Button type="submit" onClick={handleCancel}>
                        Cancel
                      </Button>
                      <Button type="submit" className="bg-[#9FC089]">
                        Invite
                      </Button>
                    </div>
                  </form>
                </Form>
              </div>
            )} */}
          </div>
        </main>
      </div>
    </>
  );
};

export default UserAdd;
