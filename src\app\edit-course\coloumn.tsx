"use client";

import type { Column, ColumnDef } from "@tanstack/react-table";
import { ArrowUpDown } from "lucide-react";
import { Button } from "@/components/ui/button";

import type { CourseModule } from "@/types";
import React from "react";

const moduleColumns: ColumnDef<CourseModule>[] = [
  {
    accessorKey: "module_name",
    header({
      column,
    }: {
      column: Column<CourseModule, unknown>;
    }): React.JSX.Element {
      return (
        <Button
          className="px-0"
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
  },
];

export { moduleColumns };
