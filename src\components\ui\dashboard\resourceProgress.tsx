"use client";
import React from "react";
import { Button } from "@/components/ui/button";
import type { ResourceProgress } from "@/types";

export default function ResourceProgressList({
  onCancel,
  resources,
}: {
  onCancel: () => void;
  resources: ResourceProgress[];
}): JSX.Element {
  return (
    <div>
      <div>
        <h1 className="text-2xl font-semibold mb-4">{"Resource Progress"}</h1>
      </div>

      <div className="overflow-x-auto">
        {resources?.length > 0 ? (
          <table className="w-full border border-gray-200 rounded-md overflow-hidden">
            <thead className="bg-gray-100">
              <tr>
                <th className="text-left px-4 py-2 font-semibold">{"Sl no"}</th>
                <th className="text-left px-4 py-2 font-semibold">
                  {"Resource Name"}
                </th>
                <th className="px-4 py-2 text-right font-semibold">
                  {"Time Spent"}
                </th>
              </tr>
            </thead>
            <tbody>
              {resources?.map((activity, index) => (
                <tr
                  key={index}
                  className={index % 2 === 0 ? "bg-white" : "bg-gray-50"}
                >
                  {/* Serial Number */}
                  <td className="px-4 py-2 text-small text-left">
                    {index + 1}
                  </td>
                  {/* Course/Resource Name */}
                  <td className="px-4 py-2 text-small">
                    {activity?.resource_name}
                  </td>

                  {/* Value */}
                  <td className="px-4 py-2 text-right text-small">
                    {activity?.time_spent !== null &&
                    activity?.time_spent !== undefined &&
                    activity?.time_spent !== "" ? (
                      <span>{activity.time_spent}</span>
                    ) : (
                      <span>{"00:00:00"}</span>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          <div className="text-center text-gray-500 py-8">
            No data available!
          </div>
        )}
      </div>

      <div className="flex justify-end mt-4">
        <Button
          variant="outline"
          className="text-[white] bg-[#9FC089] hover:bg-[#9FC089] hover:text-[white]"
          onClick={onCancel}
        >
          Close
        </Button>
      </div>
    </div>
  );
}
