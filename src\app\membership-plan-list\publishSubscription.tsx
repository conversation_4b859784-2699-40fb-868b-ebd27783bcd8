import React from "react";
import type {
  ErrorCatch,
  LogUserActivityRequest,
  SubscriptionListResults,
  ToastType,
} from "@/types";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import useSubscription from "@/hooks/useSubscription";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";

export default function PublishSubscription({
  data,
  onSave,
  onCancel,
}: {
  onSave: () => void;
  onCancel: () => void;
  data: SubscriptionListResults;
  isModal?: boolean;
}): React.JSX.Element {
  const { t } = useTranslation();
  const { toast } = useToast() as ToastType;
  const { approveSubscription } = useSubscription();
  const handleApproveClick = (): void => {
    void handleToastPublish();
    onCancel();
  };
  const { updateUserActivity } = useLogUserActivity();
  const handleToastPublish = async (): Promise<void> => {
    const subscriptionApproveData = {
      status: data.subscription_status === "Published" ? "Draft" : "Published",
      org_id: data.org_id,
      plan_id: data.id,
    };
    try {
      const result = await approveSubscription(subscriptionApproveData);
      if (result.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.toast_success_title"),
          description: t("successMessages.subscription_status_changed", {
            status: subscriptionApproveData.status,
          }),
        });
        onSave();
        onCancel();
        const params = {
          activity_type: "Subscription",
          screen_name: "Subscription",
          action_details:
            "Subscription status changed to " +
            subscriptionApproveData.status +
            " ",
          target_id: data.id,
          log_result: "SUCCESS",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      } else if (result.status === "error") {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: t("errorMessages.approve_subscription_plan_message"),
        });
        console.log("API Error:", result.status);
        const params = {
          activity_type: "Subscription",
          screen_name: "Subscription",
          action_details: "Failed to change subscription status",
          target_id: data.id,
          log_result: "ERROR",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
      console.error("An unexpected error occurred:", error);
    }
  };
  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };
  return (
    <>
      <div className="mb-2 mr-4">
        {data.subscription_status === "Published" ? (
          <p className="ml-0 ">{t("subscriptionPlan.draftPrompt")}</p>
        ) : (
          <p className="ml-0 ">{t("subscriptionPlan.publishPrompt")}</p>
        )}
      </div>
      <div className="flex topic-icons pr-4">
        <div className="flex items-center justify-center float-right gap-3">
          <Button type="button" className="bg-[#33363F]" onClick={onCancel}>
            {t("buttons.cancel")}
          </Button>
          <Button
            type="submit"
            className="bg-[#9FC089]"
            onClick={handleApproveClick}
          >
            {t("buttons.submit")}
          </Button>
        </div>
      </div>
    </>
  );
}
