import React from "react";
import type { ErrorCatch, ToastType } from "@/types";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { ORG_KEY } from "@/lib/constants";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import useCurrentAffairs from "@/hooks/useCurrentAffairs";
import { useTranslation } from "react-i18next";

export default function UnPublishAffairs({
  onSave,
  onCancel,
  affairId,
}: {
  onSave: (value: boolean) => void;
  onCancel: () => void;
  affairId: string | undefined;
}): React.JSX.Element {
  const { t } = useTranslation();
  const { toast } = useToast() as ToastType;
  const { publishCurrentAffairs } = useCurrentAffairs();
  const handlePublishClick = (): void => {
    void handleToastSave();
    onCancel();
  };
  const handleToastSave = async (): Promise<void> => {
    const params = {
      bulletin_id: affairId,
      org_id: localStorage.getItem(ORG_KEY) ?? "",
      status: "Draft",
      publish_bulletin_date: new Date().toISOString(),
    };
    try {
      const result = await publishCurrentAffairs(params);
      if (result.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.eventUnpublishTitle"),
          description: t("successMessages.eventUnpublishDescription"),
        });
        onSave(true);
        // router.push("/currentAffairs");
      } else if (result.status === "error") {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: result?.status,
        });
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
      console.error("An unexpected error occurred:", error);
    }
  };
  return (
    <>
      <div className="mb-2 mr-4">
        <p className="ml-0 ">{t("currentAffairs.unpublishPrompt")}</p>
      </div>
      <div className="flex topic-icons pr-4">
        <div className="flex items-center justify-center float-right gap-3">
          <Button type="button" className="bg-[#33363F]" onClick={onCancel}>
            {t("buttons.cancel")}
          </Button>

          <Button
            type="submit"
            className="bg-[#9FC089]"
            onClick={handlePublishClick}
          >
            {t("buttons.draft")}
          </Button>
        </div>
      </div>
    </>
  );
}
