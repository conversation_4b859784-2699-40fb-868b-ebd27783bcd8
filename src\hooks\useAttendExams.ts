import { supabase } from "../lib/client";
import { views } from "../lib/apiConfig";
import type { AttendedExamsType, UsersDataType, ExamInfoType } from "@/types";

interface UseAttendExamsReturn {
  getAttendExams: (
    courseId?: string,
    userId?: string,
  ) => Promise<AttendedExamsType[]>;
  getUsers: (orgId: string) => Promise<UsersDataType[]>;
  getSubmittedAnswers: (quizAttemptId?: string) => Promise<ExamInfoType[]>;
}

const useAttendExams = (): UseAttendExamsReturn => {
  async function getAttendExams(
    courseId?: string,
    userId?: string,
  ): Promise<AttendedExamsType[]> {
    try {
      const attendExam = views?.attendExam;
      const org_id = localStorage.getItem("orgId");

      const exeQuery = supabase.from(attendExam).select().eq("org_id", org_id);

      if (courseId !== null && courseId !== "" && courseId !== undefined) {
        await exeQuery.eq("course_id", courseId);
      }
      if (userId !== null && userId !== "" && userId !== undefined) {
        await exeQuery.eq("user_id", userId);
      }
      const { data, error } = await exeQuery;
      if (error) {
        throw new Error(error.details);
      }
      return data as AttendedExamsType[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function getUsers(orgId: string): Promise<UsersDataType[]> {
    try {
      const usersView = views?.users ?? "";
      const org_id = orgId;
      const exeQuery = supabase
        .from(usersView)
        .select()
        .eq("org_id", org_id)
        .order("first_name", { ascending: true });
      const { data, error } = await exeQuery;
      if (error) {
        throw new Error(error.details);
      }
      return data as UsersDataType[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function getSubmittedAnswers(
    quizAttemptId?: string,
  ): Promise<ExamInfoType[]> {
    try {
      const answersView = views?.submittedAnswers ?? "";
      const quiz_attempt_id = quizAttemptId;
      const exeQuery = supabase
        .from(answersView)
        .select()
        .eq("quiz_attempt_id", quiz_attempt_id);
      const { data, error } = await exeQuery;
      if (error) {
        throw new Error(error.details);
      }
      return data as ExamInfoType[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  return {
    getAttendExams,
    getUsers,
    getSubmittedAnswers,
  };
};

export default useAttendExams;
