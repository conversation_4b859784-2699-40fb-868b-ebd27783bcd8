import type {
  ErrorType,
  ActivityLogRequest,
  ActivityLogResponse,
} from "@/types";
import { supabase } from "../lib/client";
import { rpc } from "@/lib/apiConfig";

interface useActivityLogReturn {
  getActivityLogList: (
    params: ActivityLogRequest,
  ) => Promise<ActivityLogResponse>;
}

const useActivityLog = (): useActivityLogReturn => {
  async function getActivityLogList(
    params: ActivityLogRequest,
  ): Promise<ActivityLogResponse> {
    try {
      const { data, error } = (await supabase.rpc<string, null>(
        rpc.activityLog,
        params,
      )) as { data: ActivityLogResponse; error: ErrorType | null };
      if (error) {
        throw new Error(error.details);
      }
      return data as ActivityLogResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  return {
    getActivityLogList,
  };
};
export default useActivityLog;
