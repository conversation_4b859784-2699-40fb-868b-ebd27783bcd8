import "mathlive";
import { useState, useRef, useEffect } from "react";
import { convertLatexToMathMl } from "mathlive";
import { Button } from "./ui/button";

// Define the type for your element
interface MathFieldElement extends HTMLElement {
  mathVirtualKeyboardPolicy: string;
  // setOptions: (options: { [key: string]: any }) => void;
  getValue: () => string;
  setValue: (value: string) => void;
}

function MathFormulaEditor({
  onCancel,
  onSave,
  data,
}: {
  onSave: (value: string, latex: string) => void;
  onCancel: () => void;
  data: string;
  isModal?: boolean;
}): React.ReactElement {
  const [value, setValue] = useState<string>("");
  const mf = useRef<MathFieldElement | null>(null);
  console.log(data);
  useEffect(() => {
    if (mf.current) {
      // mf.current.setOptions({
      //   smartMode: true, // Enable smartMode
      //   smartSuperscript: true, // Enable smartSuperscript
      // });
      mf.current.mathVirtualKeyboardPolicy = "manual";
      mf.current.addEventListener("focusin", () =>
        window.mathVirtualKeyboard.show(),
      );
      mf.current.addEventListener("focusout", () =>
        window.mathVirtualKeyboard.hide(),
      );
      mf.current.addEventListener("input", (evt: Event) => {
        const target = evt.target as MathFieldElement;
        console.log(target.getValue());
        setValue(target.getValue());
      });
      // mf.current.addEventListener("keydown", (evt) =>
      //   evt.preventDefault(), { capture: true });
    }
    if (data !== undefined && data !== "") {
      setValue(data);
    }
  }, []);

  function saveFormula(): void {
    const mathmlInput = convertLatexToMathMl(value);
    onSave(mathmlInput, value);
    onCancel();
  }

  function cancelFormula(): void {
    onCancel();
  }

  return (
    <div className="bg-white shadow-md rounded-lg p-6 max-w-xl mx-auto">
      <style>
        {`
        math-field {
          font-size: 40px;
          width: 100%;
          padding: 0.5rem;
          border: 2px solid #e0e0e0;
          border-radius: 0.5rem;
          outline: none;
          transition: border-color 0.3s ease;
        }
        math-field:focus {
          border-color: #3b82f6;
        }
      `}
      </style>
      <math-field
        ref={mf}
        default-mode="math"
        className="w-full rounded-lg focus:border-blue-500"
        onInput={(evt) => setValue((evt.target as MathFieldElement).getValue())}
      >
        {value}
      </math-field>
      <div className="mt-6 flex items-center justify-end space-x-4">
        <Button
          type="button"
          variant="outline"
          className="px-4 py-2 text-gray-700 hover:bg-gray-100 transition-colors"
          onClick={cancelFormula}
        >
          Cancel
        </Button>
        <Button
          type="button"
          className="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 transition-colors primary"
          onClick={saveFormula}
        >
          Save
        </Button>
      </div>
    </div>
  );
}

export default MathFormulaEditor;
