"use client";
import React, { useState, useEffect } from "react";
import { getColumns } from "./columns";
import { DataTable } from "../../components/ui/data-table/data-table";
import MainLayout from "../layout/mainlayout";

import type { AssignUserList, ErrorCatch, InnerItem } from "@/types";
import useAssignOrganization from "@/hooks/useAssignOrganization";

import { Modal } from "@/components/ui/modal";
import { useToast } from "@/components/ui/use-toast";
import type { ToastType } from "../../types";
import { Spinner } from "@/components/ui/progressiveLoader";
import { Button } from "@/components/ui/button";
import AssignOrganization from "./assignOrganization";
import { SchoolIcon } from "lucide-react";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumb";
import { useTranslation } from "react-i18next";
import { ERROR_MESSAGES } from "@/lib/messages";

export default function UsersListPage(): React.JSX.Element {
  const { t } = useTranslation();
  const columns = getColumns(t);
  const [usersList, setUsersList] = useState<AssignUserList[]>([]);
  const { getAssignUsers } = useAssignOrganization();
  const [isDialogOpen, setIsDialogOpen] = React.useState(false);
  const [isLoading, setIsLoading] = React.useState<boolean>(true);
  const [selectedData, setSelectedData] = useState<AssignUserList[]>([]);
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);

  const { toast } = useToast() as ToastType;

  const closeDialog = (): void => {
    setIsDialogOpen(!isDialogOpen);
  };

  useEffect(() => {
    setBreadcrumbItems(
      getBreadCrumbItems(t, t("breadcrumb.usersList"), { "": "" }),
    );
    setIsLoading(true);
    fetchAssignedUsers().catch((error) => console.log(error));
  }, [t]);

  const fetchAssignedUsers = async (): Promise<void> => {
    setIsLoading(true);
    try {
      const usersList = await getAssignUsers();
      setUsersList(usersList);
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
    }
  };
  const handleSelectedData = (selData: AssignUserList[]): void => {
    setSelectedData(selData);
  };
  const handleAssignOrgModal = (): void => {
    setIsDialogOpen(!isDialogOpen);
  };
  function listAssignUser(): void {
    fetchAssignedUsers().catch((error) => console.log(error));
  }

  return (
    <MainLayout>
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      <div className="flex items-center justify-between">
        <div className="text-2xl font-semibold tracking-tight">
          <h1> {t("assignOrganization.usersList")}</h1>
        </div>
        {selectedData.length > 0 && (
          <div>
            <Button
              onClick={handleAssignOrgModal}
              className="bg-[#27858a] hover:bg-[#1c4e50]"
            >
              <SchoolIcon className="h-5 w-5 text-red-200 mr-2" />
              {t("assignOrganization.assignOrganization")}
            </Button>
          </div>
        )}
      </div>
      <div className="border rounded-md p-4 pt-0 mt-4 bg-[#fff]">
        {isLoading ? (
          <Spinner />
        ) : (
          <div>
            <DataTable
              columns={columns}
              data={usersList}
              FilterLabel={t("assignOrganization.filterByFirstName")}
              FilterBy={"first_name"}
              actions={[]}
              onSelectedDataChange={(value: unknown) =>
                handleSelectedData(value as AssignUserList[])
              }
            />
          </div>
        )}
      </div>

      {isDialogOpen && (
        <Modal
          title={t("assignOrganization.assignOrganization")}
          header=""
          openDialog={isDialogOpen}
          closeDialog={closeDialog}
        >
          <AssignOrganization
            setIsLoading={setIsLoading}
            onSave={listAssignUser}
            onCancel={closeDialog}
            selectedData={selectedData}
          />
        </Modal>
      )}
    </MainLayout>
  );
}
