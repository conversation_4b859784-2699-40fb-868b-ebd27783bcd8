"use client";
import { useForm } from "react-hook-form";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import React, { useEffect, useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import type {
  AddResources,
  ToastType,
  ErrorCatch,
  ResourceLibraryData,
  ComboData,
  LoginUserData,
  UpdateResourceUrlRequest,
  LogUserActivityRequest,
} from "@/types";
import { AddResourceVideoschema, AddResourceschema } from "@/schema/schema";
import { zodResolver } from "@hookform/resolvers/zod";
import { pageUrl } from "@/lib/constants";
import Link from "next/link";
import { useToast } from "@/components/ui/use-toast";
import useResourceLibrary from "@/hooks/useResourceLibrary";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import { ORG_KEY, File, ResourceExtensions } from "@/lib/constants";
import { Combobox } from "@/components/ui/combobox";
import { supabase } from "@/lib/client";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";
interface ResourceAddProps {
  resourceType: string;
  onCancel: () => void;
  resourceData: ResourceLibraryData;
  uploadData: string | null;
  onSave: () => void;
}

export const UpdateUrl: React.FC<ResourceAddProps> = ({
  resourceType,
  onCancel,
  resourceData,
  uploadData,
  onSave,
}) => {
  const { t } = useTranslation();
  const form = useForm<AddResources>({
    resolver: zodResolver(
      resourceType === File ? AddResourceschema : AddResourceVideoschema,
    ),
  });
  const org_id = localStorage.getItem(ORG_KEY);
  const { editResourcesUrl } = useResourceLibrary();
  const { toast } = useToast() as ToastType;
  const [isButtonDisable, setButtonDisable] = useState<boolean>(false);
  const [extensions, setExtensions] = useState<ComboData[]>([]);
  const [selectedExtensions, setSelectedExtensions] = useState<string>("");
  const [defaultThumbnail, setDefaultThumbnail] = useState<string>("");
  const { updateUserActivity } = useLogUserActivity();
  useEffect(() => {
    resourceType === File
      ? setExtensions(ResourceExtensions.image)
      : setExtensions(ResourceExtensions.video);
  }, [resourceType]);

  useEffect(() => {
    console.log(uploadData);
    console.log(resourceData);

    if (resourceData !== null) {
      let url = "";
      if (resourceData.file_type === File) {
        url = resourceData.url;
      } else {
        url = resourceData.external_url;

        if (resourceData.length !== null && resourceData.length !== undefined) {
          const [hours, minutes, seconds] = resourceData.length.split(":");
          form.setValue("videolength.HH", hours);
          form.setValue("videolength.MM", minutes);
          form.setValue("videolength.SS", seconds);
        }
      }
      setSelectedExtensions(
        (resourceData?.extension ?? "").replace(/^\./, "") as string,
      );
      form.setValue("url", url);
      form.setValue("name", resourceData.name);
      form.setValue("description", resourceData.description);

      form.setValue(
        "extension",
        (resourceData?.extension ?? "").replace(/^\./, "") as string,
      );
      form.setValue("page_count", resourceData.page_count as number);
      form.setValue("thumbnail_url", resourceData.thumbnail_url as string);
      setDefaultThumbnail(resourceData.thumbnail_url as string);
    }
  }, []);

  async function onSubmit(): Promise<void> {
    setButtonDisable(true);
    const formData = form.getValues();
    console.log(formData);

    const videoLength = `${
      formData.videolength?.HH !== undefined && formData.videolength.HH !== ""
        ? formData.videolength.HH.length === 1
          ? `0${formData.videolength.HH}`
          : formData.videolength.HH
        : "00"
    }:${
      formData.videolength?.MM !== undefined && formData.videolength.MM !== ""
        ? formData.videolength.MM.length === 1
          ? `0${formData.videolength.MM}`
          : formData.videolength.MM
        : "00"
    }:${
      formData.videolength?.SS !== undefined && formData.videolength.SS !== ""
        ? formData.videolength.SS.length === 1
          ? `0${formData.videolength.SS}`
          : formData.videolength.SS
        : "00"
    }`;

    const fileParams: UpdateResourceUrlRequest = {
      org_id: org_id ?? "",
      module_type: resourceType.toLowerCase(),
      resource_url: formData.url.trim(),
      video_length: "00:00:00",
      thumbnail_url: formData.thumbnail_url ?? "",
      resource_id: resourceData.id,
      extension: selectedExtensions,
      page_count: formData.page_count != null ? Number(formData.page_count) : 0,
      page_content: null,
    };

    const urlParams: UpdateResourceUrlRequest = {
      org_id: org_id ?? "",
      module_type: "url",
      resource_id: resourceData.id,
      resource_url: formData.url.trim(),
      video_length: videoLength,
      thumbnail_url: formData.thumbnail_url ?? "",
      extension: formData.extension,
      page_count: 0,
      page_content: null,
    };

    const params = resourceType === File ? fileParams : urlParams;
    try {
      const result = await editResourcesUrl(params);
      if (result.status === SUCCESS_MESSAGES.api_status_success) {
        setButtonDisable(true);
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_success,
          title: t("successMessages.toast_success_title"),
          description: t("successMessages.resource_update_success"),
        });
        onSave();
        const param = {
          activity_type: "Resource_Library",
          screen_name: "Update Url",
          action_details: "Resource url updated ",
          target_id: resourceData.id,
          log_result: "SUCCESS",
        };
        void updateLogUserActivity(param).catch((error) => {
          console.error(error);
        });
      } else {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: result.status,
        });
        const param = {
          activity_type: "Resource_Library",
          screen_name: "Update Url",
          action_details: "Failed to update url  ",
          target_id: resourceData.id,
          log_result: "ERROR",
        };
        void updateLogUserActivity(param).catch((error) => {
          console.error(error);
        });
      }
    } catch (error) {
      setButtonDisable(false);
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
      const param = {
        activity_type: "Resource_Library",
        screen_name: "Update Url",
        action_details: "Failed to update url  ",
        target_id: resourceData.id,
        log_result: "ERROR",
      };
      void updateLogUserActivity(param).catch((error) => {
        console.error(error);
      });
    }
  }

  const handleCancel = (): void => {
    onCancel();
  };

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  const handleExtension = (selectedValue: string): void => {
    setSelectedExtensions(selectedValue);
    form.setValue("extension", selectedValue);
  };
  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ): Promise<string | null | undefined> => {
    const file = event.target.files?.[0];
    if (
      file &&
      (file.type === "image/png" ||
        file.type === "image/jpeg" ||
        file.type === "image/jpg")
    ) {
      if (file.size <= 1048576) {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        const fileExt = file.name.split(".").pop();
        const fileName = `${Math.random()}.${fileExt}`;
        const orgId = localStorage.getItem("orgId") as string;
        const userDetails = localStorage.getItem("userDetails");
        if (userDetails != null) {
          const users = JSON.parse(userDetails) as LoginUserData;
          const userId = users?.id;
          try {
            const result = await supabase.storage
              .from(`${orgId}`)
              .upload(`avatars/${userId}/${fileName}`, file, {
                cacheControl: "3600",
                upsert: true,
              });

            if (result.error) {
              console.error("Error uploading avatar:", result.error);
              return null;
            }
            const { data } = supabase.storage
              .from(`${orgId}`)
              .getPublicUrl(`avatars/${userId}/${fileName}`);

            console.log("Public URL:", data.publicUrl);
            setDefaultThumbnail(data.publicUrl);
            form.setValue("thumbnail_url", data.publicUrl);

            return data.publicUrl;
          } catch (error) {
            console.error("Upload error:", error);
            return null;
          }
        }
      } else {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: t("errorMessages.file_size_exceed"),
        });
        return null;
      }
    } else {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.select_valid_image"),
      });
      return null;
    }
  };

  return (
    <div className="border rounded-md p-4 mt-4">
      <Form {...form}>
        <form
          onSubmit={(event) => void form.handleSubmit(onSubmit)(event)}
          className="space-y-8"
        >
          <>
            <div className="flex flex-wrap sm:flex-nowrap w-full ">
              <div className="w-full sm:w-1/2 pr-4 ">
                <FormField
                  control={form.control}
                  name="url"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t("resourceLibrary.url")}{" "}
                        <span className="text-red-700">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          autoComplete="off"
                          placeholder={t("resourceLibrary.pasteUrl")}
                          // defaultValue={uploadData ?? ""}
                          {...field}
                          onChange={(e) => {
                            let trimmedValue = e.target.value.trim();
                            trimmedValue = trimmedValue.replace(/\s/g, "");
                            e.target.value = trimmedValue;
                            field.onChange(e);
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="w-full sm:w-1/2 pr-4 ">
                <FormField
                  // control={form.control}
                  name="extension"
                  render={() => (
                    <FormItem>
                      <FormLabel>
                        {t("resourceLibrary.extension")}{" "}
                        <span className="text-red-700">*</span>
                      </FormLabel>
                      <FormControl>
                        <Combobox
                          data={extensions}
                          onSelectChange={handleExtension}
                          defaultLabel={selectedExtensions}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {["pdf", "doc", "docx", "ppt"].includes(selectedExtensions) && (
              <div className="w-full sm:w-1/2 pr-4 ">
                <FormField
                  control={form.control}
                  name="page_count"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t(
                          selectedExtensions === "ppt"
                            ? "resourceLibrary.totalSlideCount"
                            : "resourceLibrary.pageCount",
                        )}
                        <span className="text-red-700">*</span>{" "}
                      </FormLabel>
                      <FormControl>
                        <Input
                          autoComplete="off"
                          type="number"
                          placeholder={t(
                            selectedExtensions === "ppt"
                              ? "resourceLibrary.totalSlideCount"
                              : "resourceLibrary.pageCount",
                          )}
                          min={0}
                          {...field}
                          onChange={(e) =>
                            form.setValue(
                              "page_count",
                              Number(e.target.value),
                              {},
                            )
                          }
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}

            {resourceType === "Video" && (
              <div className="w-full sm:w-1/2">
                <FormField
                  control={form.control}
                  name="videolength"
                  render={() => (
                    <FormControl>
                      <FormItem>
                        <FormLabel>
                          {t("resourceLibrary.videoLength")}{" "}
                          <span className="text-red-700">*</span>
                        </FormLabel>
                        <FormControl>
                          <div className="flex space-x-2">
                            <FormField
                              control={form.control}
                              name="videolength.HH"
                              render={() => (
                                <FormItem>
                                  <FormControl>
                                    <Input
                                      autoComplete="off"
                                      type="number"
                                      placeholder="Hours"
                                      min={0}
                                      {...form.register("videolength.HH")}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <span>:</span>
                            <FormField
                              control={form.control}
                              name="videolength.MM"
                              render={() => (
                                <FormItem>
                                  <FormControl>
                                    <Input
                                      autoComplete="off"
                                      type="number"
                                      placeholder="Minutes"
                                      min={0}
                                      {...form.register("videolength.MM")}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <span>:</span>
                            <FormField
                              control={form.control}
                              name="videolength.SS"
                              render={() => (
                                <FormItem>
                                  <FormControl>
                                    <Input
                                      autoComplete="off"
                                      type="number"
                                      placeholder="Seconds"
                                      min={0}
                                      {...form.register("videolength.SS")}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                        </FormControl>
                      </FormItem>
                    </FormControl>
                  )}
                />
              </div>
            )}

            <div className="flex items-center gap-4">
              <FormField
                control={form.control}
                name="thumbnail_url"
                render={() => (
                  <FormItem className="flex items-center gap-4">
                    <FormLabel className="whitespace-nowrap">
                      {t("resourceLibrary.thumbnail")}{" "}
                      {resourceType === "Video" && (
                        <span className="text-red-700">*</span>
                      )}
                    </FormLabel>
                    <FormControl>
                      <Input
                        className="file:mr-2 file:py-1 file:px-2 file:border file:rounded file:bg-gray-100 file:text-gray-700"
                        type="file"
                        accept="image/png, image/jpeg, image/jpg"
                        onChange={(e) => {
                          void handleFileChange(e).then(() => {
                            console.log("val", e.target.value);
                            // setDefaultThumbnail(e.target.value);
                          });
                        }}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              {/* Thumbnail Preview */}
              {defaultThumbnail?.length > 0 && (
                <div className="w-16 h-16 rounded-full overflow-hidden border border-gray-300 flex items-center justify-center">
                  <img
                    src={defaultThumbnail}
                    alt="Thumbnail"
                    className="w-full h-full object-cover"
                  />
                </div>
              )}
            </div>
          </>

          <div className="w-full flex justify-end mt-4 gap-3">
            <Link href={pageUrl.ResourceLibraryLink}>
              <Button
                className="w-full sm:w-auto bg-[#33363F]"
                onClick={handleCancel}
              >
                {t("buttons.close")}
              </Button>
            </Link>
            <Button disabled={isButtonDisable} className="bg-[#9FC089]">
              {t("buttons.submit")}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};
