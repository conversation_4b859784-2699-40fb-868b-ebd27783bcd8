import React from "react";
import type {
  ErrorCatch,
  ToastType,
  UserPlanListResult,
  subscriptionDeleteUserRequest,
} from "@/types";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import useSubscription from "@/hooks/useSubscription";
import { ORG_KEY } from "@/lib/constants";
import { useTranslation } from "react-i18next";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";

export default function DeleteSubscriptionUser({
  data,
  onSave,
  onCancel,
  planId,
}: {
  onSave: () => void;
  onCancel: () => void;
  data: UserPlanListResult;
  isModal?: boolean;
  planId: string;
}): React.JSX.Element {
  const { t } = useTranslation();
  const { toast } = useToast() as ToastType;
  const { getSubscriptionUserDelete } = useSubscription();
  const handleDeleteClick = (): void => {
    void handleToastSave();
    onCancel();
  };
  const handleToastSave = async (): Promise<void> => {
    const orgId = localStorage.getItem(ORG_KEY) ?? "";
    const userIds: string[] = []; // Assuming user_id is a string
    userIds.push(data.user_id);
    try {
      const reqParams = {
        org_id: orgId,
        plan_id: planId,
        user_ids: userIds,
      };
      const subscriptions = await getSubscriptionUserDelete(
        reqParams as subscriptionDeleteUserRequest,
      );
      if (subscriptions?.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.subscriptionUserRemoved"),
          description: t("successMessages.subscriptionUserDesc"),
        });
      }
      onSave();
      onCancel();
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.details,
      });
    }
  };
  return (
    <>
      <div className="mb-2 mr-4">
        <p className="ml-0 ">{t("subscriptionPlan.removeUserPrompt")}</p>
      </div>
      <div className="flex topic-icons pr-4">
        <div className="flex items-center justify-center float-right gap-3">
          <Button type="button" className="bg-[#33363F]" onClick={onCancel}>
            {t("buttons.cancel")}
          </Button>
          <Button
            type="submit"
            className="bg-[#9FC089]"
            onClick={handleDeleteClick}
          >
            {t("buttons.remove")}
          </Button>
        </div>
      </div>
    </>
  );
}
