"use client";
import React, { useEffect } from "react";
import "./../styles/global.css";
import { Toaster } from "@/components/ui/toaster";
import "../i18n";
import { I18nextProvider } from "react-i18next";
import i18n from "../i18n";
// import { Inter } from "next/font/google";

// const inter = Inter({ subsets: ["latin"] });

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}): React.JSX.Element {
  useEffect(() => {
    // Set initial RTL based on saved language
    const savedLanguage = localStorage.getItem("language") ?? "en";
    document.documentElement.dir = savedLanguage === "ar" ? "rtl" : "ltr";
  }, []);

  return (
    <html lang="en">
      <body>
        <I18nextProvider i18n={i18n}>{children}</I18nextProvider>
      </body>
      <Toaster />
    </html>
  );
}
