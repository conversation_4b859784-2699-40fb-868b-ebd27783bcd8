const courseData = [
  {
    category_id: "3d1ff1f9-e36f-46cd-a11d-6753cbbd97eb",
    category_name: "Science",
    course_id: "7a6d1ee9-a570-4791-a1f1-b9893c8cf7f2",
    duration: "71 days",
    end_date: "2023-11-29T11:37:00",
    full_name: "Earth sciences",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    short_name: "Earth sciences",
    start_date: "2023-09-19T11:37:00",
    status: "Published",
  },
  {
    category_id: "48d335fd-b70d-48be-9365-66c8427f9f8f",
    category_name: "Cryptography",
    course_id: "71144020-aa85-4049-98b8-4f9a872ac51b",
    duration: "101 days",
    end_date: "2023-11-30T15:58:00",
    full_name: "Post Quantum Encryption",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    short_name: "Post Quantum Encryption",
    start_date: "2023-08-21T15:58:00",
    status: "Draft",
  },
  {
    category_id: "a9358ea2-08b1-4e89-87c7-ea3d9a620867",
    category_name: "Programming in Angular",
    course_id: "c13dfa44-1d31-486f-a3d9-25be4861f482",
    duration: "91 days",
    end_date: "2023-10-31T00:00:00",
    full_name: "Angular Programming",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    short_name: "Angular",
    start_date: "2023-08-01T00:00:00",
    status: "Draft",
  },
  {
    category_id: "13fc6185-0b55-4ea6-81b5-6fca4d787de2",
    category_name: "APPIUM",
    course_id: "a579d0c4-142a-4c12-8902-d417cad6528c",
    duration: "00:00:00",
    end_date: "2023-08-07T17:13:00",
    full_name: "Appium Tool",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    short_name: "Appium",
    start_date: "2023-08-07T17:13:00",
    status: "Draft",
  },
  {
    category_id: "05fe6b55-e337-4f91-8635-a3d0a94195f1",
    category_name: "SBI PO",
    course_id: "33ad360a-d610-426d-a8f2-01a2aa0f615f",
    duration: "153 days 18:30:00",
    end_date: "2024-04-02T18:30:00",
    full_name: "Bank new course",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    short_name: "Bank course",
    start_date: "2023-11-01T00:00:00",
    status: "Published",
  },
  {
    category_id: "05fe6b55-e337-4f91-8635-a3d0a94195f1",
    category_name: "SBI PO",
    course_id: "77545d33-e8b1-4c36-9b08-0b6765011bdd",
    duration: "5 days",
    end_date: "2023-08-30T12:09:08",
    full_name: "Bank new course-Batch 2",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    short_name: "Bank course -Batch 2",
    start_date: "2023-08-25T12:09:08",
    status: "Draft",
  },
  {
    category_id: "05fe6b55-e337-4f91-8635-a3d0a94195f1",
    category_name: "SBI PO",
    course_id: "a2027f3a-9c25-4a67-9b33-57aedb3b763a",
    duration: "96 days 02:04:00",
    end_date: "2023-11-30T15:04:36",
    full_name: "Bank new course copy",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    short_name: "Bank course copy",
    start_date: "2023-08-26T13:00:36",
    status: "Draft",
  },
  {
    category_id: "05fe6b55-e337-4f91-8635-a3d0a94195f1",
    category_name: "SBI PO",
    course_id: "0d841392-e9f3-4c20-80e1-e68d7c887f4b",
    duration: "00:00:00",
    end_date: "2023-08-25T12:20:15",
    full_name: "Bank new course dt",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    short_name: "Bank course dt",
    start_date: "2023-08-25T12:20:15",
    status: "Draft",
  },
  {
    category_id: "868e809d-0a9d-47ee-aaac-afd7ee7964c5",
    category_name: "Plustwo Maths edt",
    course_id: "a22ee02c-1a24-4e62-ab75-fc48e45233b4",
    duration: "116 days 18:30:00",
    end_date: "2023-12-05T04:48:00",
    full_name: "Basic Hindi",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    short_name: "Basic Hindi",
    start_date: "2023-08-10T10:18:00",
    status: "Published",
  },
  {
    category_id: "868e809d-0a9d-47ee-aaac-afd7ee7964c5",
    category_name: "Plustwo Maths edt",
    course_id: "7af6b995-7533-4807-83a8-fb0a0f3fbcc0",
    duration: "172 days 13:16:00",
    end_date: "2024-01-30T04:30:00",
    full_name: "Introduction to Integration",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    short_name: "Basic Integration",
    start_date: "2023-08-10T15:14:00",
    status: "Published",
  },
];

export default courseData;
