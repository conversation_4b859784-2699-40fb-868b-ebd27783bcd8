"use client";
import React, { useState, useEffect } from "react";
import { getColumns } from "./columns";
import { DataTable } from "../../components/ui/data-table/data-table";
// import userData from "./userData";
import MainLayout from "../layout/mainlayout";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Combobox } from "../../components/ui/combobox";
import { Label } from "@/components/ui/label";
// import courseData from "./courseData";
import useCourse from "@/hooks/useCourse";
import useAddEnrollments from "@/hooks/useAddEnrollments";
import type {
  AddEnrollmentsResult,
  UserData,
  AddUserEnrollment,
  // ErrorCatch,
  ErrorType,
  ToastType,
  InnerItem,
  LogUserActivityRequest,
  ErrorCatch,
  insertMessageRequest,
} from "@/types";
import { pageUrl } from "@/lib/constants";
import type { BaseSyntheticEvent } from "react";
import { useRouter } from "next/navigation";
import { useToast } from "@/components/ui/use-toast";
import { Spinner } from "@/components/ui/progressiveLoader";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumb";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import useEnrollments from "@/hooks/useEnrollment";
import { useTranslation } from "react-i18next";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";

export default function EnrollmentAddPage(): React.JSX.Element {
  const router = useRouter();
  const { t } = useTranslation();
  const columns = getColumns(t);
  const { toast } = useToast() as ToastType;
  const { getCourseListForEnrollments } = useCourse();
  const { getNotEnrolledUsers, addNewEnrollment } = useAddEnrollments();
  const { updateUserActivity } = useLogUserActivity();

  const [courseData, setCourseData] = useState<
    { value?: string; label?: string }[]
  >([]);
  const [enrolledData, setEnrolledData] = useState<UserData[]>([]);
  const [selectCourse, setSelectCourse] = useState<string>("");
  const [selectedData, setSelectedData] = useState<UserData[]>([]);
  const [isLoading, setIsLoading] = React.useState<boolean>(true);
  const [isButtonDisabled, setIsButtonDisabled] = useState(true);
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);
  const { fetDeviceToken, pushNotification, insertMessage } = useEnrollments();
  useEffect(() => {
    setBreadcrumbItems(
      getBreadCrumbItems(t, t("breadcrumb.addEnrollments"), { "": "" }),
    );
  }, [t]);

  useEffect(() => {
    const fetchCourseData = async (): Promise<void> => {
      try {
        const courses = await getCourseListForEnrollments("");
        setIsLoading(false);
        if (
          courses !== null &&
          courses !== undefined &&
          Object.keys(courses).length > 0
        ) {
          const filteredCourses = courses
            .filter((course) => course.course_id != null && course.full_name)
            .map((course) => ({
              value: course.course_id,
              label: course.short_name,
            }));
          setCourseData(filteredCourses);
        } else {
          setCourseData([]);
        }
      } catch (error) {
        const err = error as ErrorType;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
      }
    };
    fetchCourseData().catch((error) => console.log(error));
  }, []);

  const handleCourseChange = (selectedOption: string): void => {
    setSelectCourse(selectedOption);
  };

  useEffect(() => {
    const fetchUserData = async (): Promise<void> => {
      setIsLoading(true);
      try {
        const orgId = localStorage.getItem("orgId");
        const requestBody = {
          course_id: selectCourse,
          org_id: orgId ?? "",
        };
        const enrollments = await getNotEnrolledUsers(requestBody);
        setIsLoading(false);
        if (
          enrollments !== null &&
          enrollments !== undefined &&
          enrollments.length > 0 &&
          selectCourse !== ""
        ) {
          setEnrolledData(enrollments);
        } else {
          setEnrolledData([]);
        }
      } catch (error) {
        setIsLoading(false);
        const err = error as ErrorType;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.details ?? t("errorMessages.something_went_wrong"),
        });
      }
    };
    if (courseData.length > 0 && selectCourse !== "") {
      fetchUserData().catch((error) => console.log(error));
    }
  }, [selectCourse]);

  const handleSelectedData = (selData: UserData[]): void => {
    setIsButtonDisabled(false);
    setSelectedData(selData);
  };

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  const handleImportUsers = async (e: BaseSyntheticEvent): Promise<void> => {
    console.log(e);
    const userIds = selectedData.map((user) => user.id);
    const orgId = localStorage.getItem("orgId");

    if (selectCourse === "" || userIds.length === 0) {
      // setIsButtonDisabled(true);
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.user_to_enroll"),
      });
    } else {
      setIsButtonDisabled(false);
      const requestBody = {
        course_id: selectCourse ?? "",
        org_id: orgId ?? "",
        users_ids: userIds,
      };
      try {
        const result = await addNewEnrollment(requestBody as AddUserEnrollment);
        if (result?.status === "success") {
          toast({
            variant: SUCCESS_MESSAGES.toast_variant_default,
            title: t("successMessages.toast_success_title"),
            description: t("successMessages.enrollment_added"),
          });
          const params = {
            activity_type: "Enrolment",
            screen_name: "Enrolments",
            action_details: "User enrolled successfully",
            target_id: selectCourse as string,
            log_result: "SUCCESS",
          };
          void updateLogUserActivity(params).catch((error) => {
            console.error(error);
          });
          for (const item of selectedData) {
            const userId = item.id;
            const tokenParams = {
              org_id: orgId as string,
              user_id: userId,
            };

            try {
              const msg =
                "You have been enrolled to " +
                courseData.find((course) => course.value === selectCourse)
                  ?.label;
              const resp = await fetDeviceToken(tokenParams);
              const token = resp.result[0];
              const reqParams = {
                userid: userId,
                message: msg,
                fcmtoken: token,
              };
              if (resp.result.length > 0) {
                const resp = await pushNotification(reqParams);
                if (resp.message === "Notification sent successfully") {
                  void insertMessageToTable(userId, token, msg);
                }
              }
            } catch (error) {
              const err = error as ErrorCatch;
              toast({
                variant: ERROR_MESSAGES.toast_variant_destructive,
                title: t("errorMessages.toast_error_title"),
                description: err.message,
              });
              console.log(err);
            }
          }
          router.push(`${pageUrl.enrollments}`);
        } else if (result.status === "error") {
          toast({
            variant: ERROR_MESSAGES.toast_variant_destructive,
            title: t("errorMessages.toast_error_title"),
            description: result.status,
          });
          const params = {
            activity_type: "Enrolment",
            screen_name: "Enrolments",
            action_details: "Failed to enroll user",
            target_id: selectCourse as string,
            log_result: "SUCCESS",
          };
          void updateLogUserActivity(params).catch((error) => {
            console.error(error);
          });
        }
      } catch (error) {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: t("errorMessages.select_course_and_user"),
        });
      }
    }
  };
  const insertMessageToTable = async (
    userId: string,
    token: string,
    msg: string,
  ): Promise<void> => {
    const orgId = localStorage.getItem("orgId");
    const reqParams: insertMessageRequest = {
      org_id: orgId as string,
      user_id: userId,
      notification_data: {
        target_id: null,
        device_token_id: token,
        message_text: msg,
      },
    };

    try {
      const resp = await insertMessage(reqParams);
      console.log("Message inserted:", resp);
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err.message,
      });
      console.log(err);
    }
  };
  return (
    <MainLayout>
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      <>
        <>
          <h1 className="text-2xl font-semibold tracking-tight">
            {t("enrollment.addEnrollments")}
          </h1>
        </>
        <div className="border rounded-md p-4 mt-4 bg-[#fff]">
          <div className="w-full flex flex-wrap justify-between space-x-4">
            <div className="w-full sm:w-1/2 md:w-1/3 lg:w-1/4 xl:w-1/5 2xl:w-1/6 flex">
              <div className="w-full">
                <Label className="block">
                  {t("enrollment.selectCourse")}{" "}
                  <span className="text-red-700">*</span>
                </Label>
                <div className="w-full course-width mt-2">
                  <Combobox
                    data={courseData}
                    onSelectChange={handleCourseChange}
                  />
                </div>
              </div>
            </div>
          </div>
          {isLoading ? (
            <Spinner />
          ) : (
            <div>
              {enrolledData?.length > 0 && (
                <DataTable
                  columns={columns}
                  data={enrolledData as AddEnrollmentsResult[]}
                  FilterLabel={t("enrollment.filterByFirstName")}
                  FilterBy={"first_name"}
                  actions={[]}
                  onSelectedDataChange={(value: unknown) =>
                    handleSelectedData(value as UserData[])
                  }
                />
              )}
            </div>
          )}
          {!isButtonDisabled && (
            <div className="flex flex-wrap justify-end mt-8 gap-x-3">
              <div className="">
                <Link href={pageUrl.enrollments}>
                  <Button className="bg-[#33363F]">
                    {t("buttons.cancel")}
                  </Button>
                </Link>
              </div>
              <div className="">
                <Button
                  className="bg-[#9FC089]"
                  onClick={(e: BaseSyntheticEvent) => {
                    handleImportUsers(e).catch((error) => console.log(error));
                  }}
                >
                  {t("buttons.submit")}
                </Button>
              </div>
            </div>
          )}
        </div>
      </>
    </MainLayout>
  );
}
