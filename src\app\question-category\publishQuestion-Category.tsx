import React from "react";
import type {
  ErrorCatch,
  LogUserActivityRequest,
  QuestionCategoryForm,
  ToastType,
} from "@/types";
import { Button } from "@/components/ui/button";
import useQuestionBank from "@/hooks/useQuestionBank";
import { useToast } from "@/components/ui/use-toast";
import { useRouter } from "next/navigation";
import { ORG_KEY } from "@/lib/constants";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";

export default function PublishQuestionCategory({
  data,
  onSave,
  onCancel,
}: {
  onSave: (value: boolean) => void;
  onCancel: () => void;
  data: QuestionCategoryForm;
  isModal?: boolean;
}): React.JSX.Element {
  const router = useRouter();
  const { t } = useTranslation();
  const { toast } = useToast() as ToastType;
  const { publishQuestionCategory } = useQuestionBank();
  const { updateUserActivity } = useLogUserActivity();

  const handlePublishClick = (): void => {
    void handleToastSave();
    onCancel();
  };

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  const handleToastSave = async (): Promise<void> => {
    const params = {
      org_id: localStorage.getItem(ORG_KEY) ?? "",
      ques_category_id: data.value,
      status: data.publish_status === "Published" ? "Draft" : "Published",
    };
    try {
      const result = await publishQuestionCategory(params);
      if (result.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.questionCategory"),
          description: t("successMessages.questionCatPublished"),
        });
        const params = {
          activity_type: "Question_Category",
          screen_name: "Question_Category",
          action_details: `${
            data.publish_status === "Published" ? "Draft" : "Published"
          } the exam successfully`,
          target_id: data.value as string,
          log_result: "SUCCESS",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
        onSave(true);
        router.push("/question-category");
      } else if (result.status === "error") {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: result?.status,
        });
        const params = {
          activity_type: "Question_Category",
          screen_name: "Question_Category",
          action_details: `Failed to ${
            data.publish_status === "Published" ? "Draft" : "Published"
          } the exam`,
          target_id: data.value as string,
          log_result: "ERROR",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
      console.error("An unexpected error occurred:", error);
    }
  };
  return (
    <>
      <div className="mb-2 mr-4">
        {data.publish_status === "Published" ? (
          <p className="ml-0 ">{String(t("questionCategory.draftPrompt"))}</p>
        ) : (
          <p className="ml-0 ">{String(t("questionCategory.publishPrompt"))}</p>
        )}
      </div>
      <div className="flex topic-icons pr-4">
        <div className="flex items-center justify-center float-right">
          <Button type="button" className="bg-[#33363F]" onClick={onCancel}>
            {String(t("buttons.cancel"))}
          </Button>
          &nbsp;
          <Button
            type="submit"
            className="bg-[#9FC089]"
            onClick={handlePublishClick}
          >
            {String(t("buttons.submit"))}
          </Button>
        </div>
      </div>
    </>
  );
}
