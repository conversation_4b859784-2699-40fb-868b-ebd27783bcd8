"use client";
import { Editor } from "primereact/editor";
import { ModalButton } from "../ui/modalButton";
import { useTranslation } from "react-i18next";

export default function CurrentAffairsContent({
  content,
  title,
  onCancel,
}: {
  onCancel: () => void;
  isModal?: boolean;
  content: string;
  title: string;
}): JSX.Element {
  const { t } = useTranslation();
  const handleCancelClick = (): void => {
    onCancel();
  };
  return (
    <div>
      <section>
        <h1 className="text-2xl font-semibold tracking-tight mb-2">{title}</h1>
        <Editor value={content} style={{ height: "300px" }} readOnly={true} />
        <div className="mt-4">
          <ModalButton
            closeDialog={handleCancelClick}
            closeLabel={t("buttons.cancel")}
          />
        </div>
      </section>
    </div>
  );
}
