"use client";

import type { ColumnDef, Row } from "@tanstack/react-table";
// import { ArrowUpDown } from "lucide-react";
// import { Button } from "@/components/ui/button";
import React from "react";

export interface Question {
  question_id: string;
  org_id: string;
  name: string;
  question_text: string;
  default_mark: number;
  penalty: number;
  question_category_id: string;
  status: string;
  question_category_name: string;
  answers?: [
    {
      id: string;
      slot: number;
      answer: string;
      org_id: string;
      fraction: number;
      ans_format: string;
      created_at: string;
      updated_at: string;
      answer_type: string;
      question_id: string;
    },
  ];
}

// interface ColumnDefinition {
//   column: Column<Question, unknown>;
// }
interface RowDefinition {
  row: Row<Question>;
}

export const getColumns = (
  t: (key: string) => string
): ColumnDef<Question>[] => [
  {
    accessorKey: "question_text",
    header: t("questionBank.question"),
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div className="text-left">{row.original.question_text}</div>
    ),
  },
  {
    accessorKey: "question_category_name",
    header: t("questionBank.questionCategory"),
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div className="text-left">{row.original.question_category_name}</div>
    ),
  },
  {
    accessorKey: "default_mark",
    header: t("questionBank.defaultMark"),
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div className="text-center">{row.original.default_mark}</div>
    ),
  },
  // {
  //   accessorKey: "penalty_applied",
  //   header: ({ column }: ColumnDefinition): React.JSX.Element => {
  //     return (
  //       <Button
  //         className="px-0"
  //         variant="ghost"
  //         onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
  //       >
  //         Penality Applied
  //       </Button>
  //     );
  //   },
  // },
  {
    accessorKey: "penalty",
    header: t("questionBank.penaltyScore"),
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div className="text-center">{row.original.penalty}</div>
    ),
  },
  {
    accessorKey: "status",
    header: t("questionBank.status"),
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div className="text-center">
        {row.original.question_id !== "" ? "Active" : ""}
      </div>
    ),
  },
];
