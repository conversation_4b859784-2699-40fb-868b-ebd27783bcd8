"use client";
import React, { useState, useEffect } from "react";
// import { columns } from "./columns";
import { getColumns } from "./columns";
import { DataTable } from "@/components/ui/data-table/data-table";
import useCurrentAffairs from "@/hooks/useCurrentAffairs";
import type { CurrentAffairsData } from "@/types";
import { Spinner } from "@/components/ui/progressiveLoader";
import { Eye } from "lucide-react";
import { Modal } from "@/components/ui/modal";
import CurrentAffairsContent from "./current-affairs-data";
import { useTranslation } from "react-i18next";

export default function CurrentAffairsData({
  isDashboard,
}: {
  isDashboard?: boolean;
}): React.JSX.Element {
  const { t } = useTranslation();
  const columns = getColumns(t);
  const { getCurrentAffairsList } = useCurrentAffairs();
  const [currentAffairs, setCurrentAffairs] = useState<CurrentAffairsData[]>(
    [],
  );
  const [isLoading, setIsLoading] = React.useState<boolean>(true);
  const [viewOpen, setViewOpen] = React.useState<boolean>(false);
  const [content, setContent] = React.useState<string>("");
  const [title, setTitle] = React.useState<string>("");

  useEffect(() => {
    const fetchData = async (): Promise<void> => {
      setIsLoading(true);
      try {
        const currentAffairsList = await getCurrentAffairsList();
        setIsLoading(false);
        if (currentAffairsList.length > 0) {
          currentAffairsList.map((item) => {
            if (item.content != null) {
              item.content = item.content.replace(
                /<pre\b[^>]*>(.*?)<\/pre>/s,
                "<p>$1</p>",
              );
            }
          });
          setCurrentAffairs(currentAffairsList);
        }
      } catch (error) {
        setIsLoading(false);
        console.error("Error fetching data");
      }
    };
    fetchData().catch((error) => console.log(error));
  }, []);

  const dialogOpen = (value: string, title: string): void => {
    setContent(value);
    setTitle(title);
    setViewOpen(true);
  };
  const closeDialog = (): void => {
    setViewOpen(false);
  };
  return (
    <div>
      <>
        {!(isDashboard ?? false) && (
          <h1>{t("dashboard.currentaffairs.title")}</h1>
        )}
      </>
      <div className="border rounded-md p-2">
        {isLoading ? (
          <Spinner />
        ) : (
          <div className="overflow-x-auto">
            <DataTable
              columns={columns}
              data={currentAffairs}
              FilterLabel={String(
                t("dashboard.currentaffairs.filterByEventTitle"),
              )}
              FilterBy={"title"}
              actions={[
                {
                  title: `${t("dashboard.currentaffairs.content")}`,
                  icon: Eye,
                  varient: "icon",
                  handleClick: (val: unknown) => {
                    dialogOpen(
                      (val as CurrentAffairsData).content,
                      (val as CurrentAffairsData).title,
                    );
                  },
                },
              ]}
            />
          </div>
        )}
      </div>
      {viewOpen && (
        <Modal
          title=""
          header=""
          openDialog={viewOpen}
          closeDialog={closeDialog}
          type="max-w-7xl"
        >
          <CurrentAffairsContent
            onCancel={closeDialog}
            content={content}
            title={title}
          ></CurrentAffairsContent>
        </Modal>
      )}
    </div>
  );
}
