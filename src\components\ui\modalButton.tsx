import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import type { modalButtonType } from "@/types";

export const ModalButton: React.FC<modalButtonType> = (
  props,
): React.JSX.Element => {
  return (
    <>
      <div className="flex items-center justify-center float-right space-x-2">
        <Button
          type="button"
          // variant="outline"
          className="bg-[#33363F]"
          onClick={() => props.closeDialog(false)}
        >
          {props.closeLabel}
        </Button>
        &nbsp;
        {props.submitLabel != null && (
          <Button type="submit" className="bg-[#9FC089]">
            {props.submitLabel}
          </Button>
        )}
      </div>
    </>
  );
};
