import React from "react";
import type {
  ErrorCatch,
  LogUserActivityRequest,
  SubscriptionListResults,
  ToastType,
} from "@/types";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import useSubscription from "@/hooks/useSubscription";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";

export default function DeleteSubscription({
  data,
  onSave,
  onCancel,
}: {
  onSave: () => void;
  onCancel: () => void;
  data: SubscriptionListResults;
  isModal?: boolean;
}): React.JSX.Element {
  const { t } = useTranslation();
  const { toast } = useToast() as ToastType;
  const { deleteSubscription } = useSubscription();
  const handleDeleteClick = (): void => {
    void handleToastSave();
    onCancel();
  };
  const { updateUserActivity } = useLogUserActivity();
  const handleToastSave = async (): Promise<void> => {
    const subscriptionDeleteData = {
      org_id: data.org_id,
      plan_id: data.id,
    };
    try {
      const result = await deleteSubscription(subscriptionDeleteData);
      if (result.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.subscriptionDelete"),
          description: t("successMessages.subdcriptionDelDesc"),
        });
        onSave();
        onCancel();
        const params = {
          activity_type: "Subscription",
          screen_name: "Subscription ",
          action_details: "Subscription deleted ",
          target_id: data.id,
          log_result: "SUCCESS",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      } else if (result.status === "error") {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: t("errorMessages.delete_subscription_plan_message"),
        });
        console.log("API Error:", result.status);
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
      console.error("An unexpected error occurred:", error);
      const params = {
        activity_type: "Subscription",
        screen_name: "Subscription ",
        action_details: "Failed to delete subscription ",
        target_id: data.id,
        log_result: "ERROR",
      };
      void updateLogUserActivity(params).catch((error) => {
        console.error(error);
      });
    }
  };
  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };
  return (
    <>
      <div className="mb-2 mr-4">
        <p className="ml-0 ">{t("subscriptionPlan.deletePrompt")}</p>
      </div>
      <div className="flex topic-icons pr-4">
        <div className="flex items-center justify-center float-right gap-3">
          <Button type="button" className="bg-[#33363F]" onClick={onCancel}>
            {t("buttons.cancel")}
          </Button>
          <Button
            type="submit"
            className="bg-[#9FC089]"
            onClick={handleDeleteClick}
          >
            {t("buttons.delete")}
          </Button>
        </div>
      </div>
    </>
  );
}
