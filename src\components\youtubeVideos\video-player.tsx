"use client";
import ReactPlayer from "react-player";
import { ModalButton } from "../ui/modalButton";

export default function VideoPlayer({
  url,
  onCancel,
}: {
  onCancel: () => void;
  isModal?: boolean;
  url?: string;
}): JSX.Element {
  const handleCancelClick = (): void => {
    onCancel();
  };
  return (
    <div>
      <div className="w-full h-0 relative py-60 rounded-md overflow-hidden">
        <ReactPlayer
          style={{
            position: "absolute",
            top: "0",
            left: "0",
          }}
          onPause={() => console.log("res")}
          width="100%"
          height="100%"
          url={url}
          controls={true}
        />
      </div>
      <div className="mt-4">
        <ModalButton closeDialog={handleCancelClick} closeLabel="Cancel" />
      </div>
    </div>
  );
}
