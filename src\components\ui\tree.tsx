"use client";

import React, { useEffect, useState } from "react";
import * as AccordionPrimitive from "@radix-ui/react-accordion";
import { cn } from "../../lib/utils";
import { ChevronRight, type LucideIcon } from "lucide-react";

import "../../styles/main.css";
import AddTopic from "../../app/topics/addTopic";
import { useToast } from "@/components/ui/use-toast";
import { Save, XCircle, Pencil, PlusCircle, Eye } from "lucide-react";
import useTopic from "@/hooks/useTopics";
import type { ErrorCatch, ToastType } from "@/types";
import { useRouter } from "next/navigation";
import getPrivilegeList from "@/hooks/useCheckPrivilege";
import { privilegeData } from "@/lib/constants";
import {
  Tooltip,
  TooltipArrow,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@radix-ui/react-tooltip";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import { useTranslation } from "react-i18next";
interface TreeDataItem {
  is_parent?: boolean | undefined;
  data: string | null;
  key?: string;
  value: string;
  label: string;
  description?: string;
  icon?: LucideIcon;
  children?: TreeDataItem[] | null;
  is_premium?: boolean;
}

type TreeProps = React.HTMLAttributes<HTMLDivElement> & {
  data: TreeDataItem[] | TreeDataItem;
  onSelectChange?: (item: TreeDataItem | undefined) => void;
  expandAll?: boolean;
  folderIcon?: LucideIcon;
  itemIcon?: LucideIcon;
};
type TreeItemProps = TreeProps & {
  selectedItemId?: string;
  handleSelectChange: (item: TreeDataItem | undefined) => void;
  expandedItemIds: string;
  FolderIcon?: LucideIcon;
  ItemIcon?: LucideIcon;
  handleItemClick?: (item: TreeDataItem) => void;
};

const TreeItem = React.forwardRef<HTMLDivElement, TreeItemProps>(
  (
    {
      className,
      data,
      selectedItemId,
      handleSelectChange,
      expandedItemIds,
      FolderIcon,
      ItemIcon,
      handleItemClick,
      ...props
    },
    ref,
  ) => {
    const [isEditing, setIsEditing] = React.useState<Record<number, boolean>>(
      {},
    );
    const [editedLabels, setEditedLabels] = React.useState<
      Record<number, string>
    >({});
    const [editedDescriptions, setEditedDescriptions] = React.useState<
      Record<number, string>
    >({});
    const [isAdding, setIsAdding] = React.useState(false);
    const { t } = useTranslation();
    const [selEditData, setEditData] = React.useState<TreeDataItem>();
    const [activeRow, setActiveRow] = useState<number | null>(null);
    const { editCategory } = useTopic();
    const { toast } = useToast() as ToastType;
    const router = useRouter();
    const [editBtn, setEditBtn] = useState<boolean>(false);
    const [addBtn, setAddBtn] = useState<boolean>(false);
    const [viewBtn, setViewBtn] = useState<boolean>(false);

    useEffect(() => {
      setEditBtn(getPrivilegeList("Topic", privilegeData.Topic.updateCategory));
      setAddBtn(getPrivilegeList("Topic", privilegeData.Topic.addSubCategory));
      setViewBtn(
        getPrivilegeList("Topic", privilegeData.Topic.getCategoryList),
      );
    }, []);

    const handleEditClick = (index: number, item: TreeDataItem): void => {
      setIsEditing((prevState) => ({
        ...prevState,
        [index]: true,
      }));

      setEditData(item);
      setEditedLabels((prevState) => ({
        ...prevState,
        [index]: item.label,
      }));
      setEditedDescriptions((prevState) => ({
        ...prevState,
        [index]: item.description ?? "",
      }));
    };
    const handleToastSave = (): void => {
      toast({
        variant: SUCCESS_MESSAGES.toast_variant_default,
        title: t("successMessages.categoryUpdated"),
        description: t("successMessages.categoryUpdateDesc"),
      });
    };
    const handleToastcancel = (): void => {
      toast({
        variant: SUCCESS_MESSAGES.toast_variant_default,
        title: t("successMessages.cancelCategory"),
        description: t("successMessages.cancelCategoryDesc"),
      });
    };
    const handleSaveClick = async (index: number): Promise<void> => {
      handleToastSave();

      const orgId = localStorage.getItem("orgId");
      const data = {
        org_id: orgId ?? "",
        category_data: {
          id: selEditData?.value,
          name: editedLabels[index].trimStart(),
          description: editedDescriptions[index].trimStart(),
        },
      };

      try {
        const result = await editCategory(data);

        if (result.status === "success") {
          router.push("/topics");
          // toast({
          //   variant: "success",
          //   title: "Category Added",
          //   description: "Successfully added a new category",
          // });
          //Tod need to check
        } else if (result.status === "error") {
          toast({
            variant: ERROR_MESSAGES.toast_variant_destructive,
            title: t("errorMessages.toast_error_title"),
            description: result.status,
          });
          console.log("API Error:", result.status);
        }
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err.message,
        });
        console.error("An unexpected error occurred:", error);
      }

      // if (Array.isArray(data)) {
      //   data[index].label = editedLabels[index];
      //   data[index].description = editedDescriptions[index];
      //   setIsEditing((prevState) => ({
      //     ...prevState,
      //     [index]: false,
      //   }));
      // }
    };

    const handleCancelClick = (index: number): void => {
      handleToastcancel();
      if (Array.isArray(data)) {
        setEditedLabels((prevState) => ({
          ...prevState,
          [index]: data[index].label,
        }));
        setEditedDescriptions((prevState) => ({
          ...prevState,
          [index]: data[index].description ?? "",
        }));
        setIsEditing((prevState) => ({
          ...prevState,
          [index]: false,
        }));
      }
    };

    const handleAddClick = (index: number): void => {
      setIsAdding(true);
      setActiveRow(index);
    };

    const handleSaveNewItem = (): void => {
      setIsAdding(false);
    };

    const handleCancelAdd = (): void => {
      setIsAdding(false);
    };

    const handleClickView = (data: TreeDataItem): void => {
      if (handleItemClick !== undefined) {
        handleItemClick(data);
      }
    };
    // const changeCheckEvent = (): void => {
    //   //to do
    // };

    return (
      <div ref={ref} role="tree" className={className} {...props}>
        <div className={`pr-2 pt-2 pl-2`}>
          <ul>
            {Array.isArray(data) &&
              data.map((item, index) => (
                <>
                  <li
                    key={item.value}
                    className="py-1"
                    style={{ listStyle: "none" }}
                  >
                    {/* {isAdding && activeRow !== null && activeRow === index ? (
                    
                    <AddTopic
                      onSave={handleSaveNewItem}
                      onCancel={handleCancelAdd}
                      newAddedItem={item}
                    />
                  ) : ( */}
                    <>
                      {item.children && item.value.length > 0 ? (
                        <AccordionPrimitive.Root type="multiple">
                          <AccordionPrimitive.Item value={item.value ?? ""}>
                            <AccordionTrigger
                              className={cn(
                                "px-2 before:absolute before:left-0 before:w-full before:opacity-0 before:bg-muted/50 before:h-[1.7rem] before:-z-10",
                                selectedItemId === item.value
                                  ? "before:opacity-100 bg-gray-200 text-gray-600 before:border-l-2 before:border-l-accent-foreground/50 dark:before:border-0"
                                  : "hover:before:opacity-100 hover:bg-gray-200 hover:text-gray-600",
                              )}
                              onClick={() => handleSelectChange(item)}
                            >
                              <div className="flex items-center">
                                {!item.icon && FolderIcon && (
                                  <FolderIcon
                                    className="h-4 w-4 shrink-0 mr-2 text-accent-foreground/50"
                                    aria-hidden="true"
                                  />
                                )}
                                {isEditing[index] ? (
                                  <>
                                    <div className="mb-2 mr-4">
                                      <input
                                        type="text"
                                        value={editedLabels[index]}
                                        onChange={(e) =>
                                          setEditedLabels((prevState) => ({
                                            ...prevState,
                                            [index]: e.target.value,
                                          }))
                                        }
                                        onClick={(e) => e.stopPropagation()}
                                        className="text-sm px-3 py-2 w-40  border border-gray-300 rounded-md shadow-sm focus:ring-accent focus:border-accent"
                                        placeholder="Label"
                                        maxLength={30}
                                      />
                                    </div>
                                    <div className="mb-2 mr-4">
                                      <input
                                        type="text"
                                        value={editedDescriptions[index]}
                                        onChange={(e) =>
                                          setEditedDescriptions(
                                            (prevState) => ({
                                              ...prevState,
                                              [index]: e.target.value,
                                            }),
                                          )
                                        }
                                        onClick={(e) => e.stopPropagation()}
                                        className="text-sm px-3 py-2 w-80 border border-gray-300 rounded-md shadow-sm focus:ring-accent focus:border-accent"
                                        placeholder="Description"
                                        maxLength={50}
                                      />
                                    </div>
                                    {/* <div className="mb-2 mr-4 flex items-center">
                                      <Checkbox
                                        checked={false}
                                        onCheckedChange={changeCheckEvent}
                                      />
                                      <span className="text-sm ml-2">
                                        Is Premium
                                      </span>
                                    </div> */}
                                  </>
                                ) : (
                                  <div className="flex">
                                    <div className="text-left">
                                      <span className="font-semibold">
                                        {item.label}
                                      </span>
                                    </div>
                                    {/* {item.description !== "" && (
                                      <div className="ml-4">
                                        <span className="text-gray-600">
                                          {item.description}
                                        </span>
                                      </div>
                                    )} */}
                                  </div>
                                )}
                              </div>
                              <div className="topic-icons pr-4">
                                {isEditing[index] ? (
                                  <>
                                    <div
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleSaveClick(index).catch((error) =>
                                          console.log(error),
                                        );
                                      }}
                                      className="text-accent-foreground/50 hover:text-accent cursor-pointer ml-4 custom-button"
                                    >
                                      <Save />
                                    </div>
                                    <div
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleCancelClick(index);
                                      }}
                                      className="text-accent-foreground/50 hover:text-accent cursor-pointer ml-4 custom-button"
                                    >
                                      <XCircle />
                                    </div>
                                  </>
                                ) : (
                                  <>
                                    <div
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleEditClick(index, item);
                                      }}
                                      className="text-accent-foreground/50 hover:text-accent cursor-pointer ml-4"
                                    >
                                      <TooltipProvider>
                                        <Tooltip>
                                          <TooltipTrigger asChild>
                                            {editBtn ? <Pencil /> : null}
                                          </TooltipTrigger>
                                          <TooltipContent
                                            className="data-[state=delayed-open]:data-[side=top]:animate-slideDownAndFade data-[state=delayed-open]:data-[side=right]:animate-slideLeftAndFade data-[state=delayed-open]:data-[side=left]:animate-slideRightAndFade data-[state=delayed-open]:data-[side=bottom]:animate-slideUpAndFade text-violet11 select-none rounded-[4px] bg-white px-[15px] py-[10px] text-[15px] leading-none shadow-[hsl(206_22%_7%_/_35%)_0px_10px_38px_-10px,_hsl(206_22%_7%_/_20%)_0px_10px_20px_-15px] will-change-[transform,opacity]"
                                            sideOffset={5}
                                          >
                                            <p className="text-black">Edit</p>
                                            <TooltipArrow className="fill-white" />
                                          </TooltipContent>
                                        </Tooltip>
                                      </TooltipProvider>
                                    </div>
                                    <div
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleAddClick(index);
                                      }}
                                      className="text-accent-foreground/50 hover:text-accent cursor-pointer ml-4"
                                    >
                                      <TooltipProvider>
                                        <Tooltip>
                                          <TooltipTrigger asChild>
                                            {addBtn && <PlusCircle />}
                                          </TooltipTrigger>
                                          <TooltipContent
                                            className="data-[state=delayed-open]:data-[side=top]:animate-slideDownAndFade data-[state=delayed-open]:data-[side=right]:animate-slideLeftAndFade data-[state=delayed-open]:data-[side=left]:animate-slideRightAndFade data-[state=delayed-open]:data-[side=bottom]:animate-slideUpAndFade text-violet11 select-none rounded-[4px] bg-white px-[15px] py-[10px] text-[15px] leading-none shadow-[hsl(206_22%_7%_/_35%)_0px_10px_38px_-10px,_hsl(206_22%_7%_/_20%)_0px_10px_20px_-15px] will-change-[transform,opacity]"
                                            sideOffset={5}
                                          >
                                            <p className="text-black">Add</p>
                                            <TooltipArrow className="fill-white" />
                                          </TooltipContent>
                                        </Tooltip>
                                      </TooltipProvider>
                                    </div>
                                    <div
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleClickView(item);
                                      }}
                                      className="text-accent-foreground/50 hover:text-accent cursor-pointer ml-4"
                                    >
                                      <TooltipProvider>
                                        <Tooltip>
                                          <TooltipTrigger asChild>
                                            {viewBtn && <Eye />}
                                          </TooltipTrigger>
                                          <TooltipContent
                                            className="data-[state=delayed-open]:data-[side=top]:animate-slideDownAndFade data-[state=delayed-open]:data-[side=right]:animate-slideLeftAndFade data-[state=delayed-open]:data-[side=left]:animate-slideRightAndFade data-[state=delayed-open]:data-[side=bottom]:animate-slideUpAndFade text-violet11 select-none rounded-[4px] bg-white px-[15px] py-[10px] text-[15px] leading-none shadow-[hsl(206_22%_7%_/_35%)_0px_10px_38px_-10px,_hsl(206_22%_7%_/_20%)_0px_10px_20px_-15px] will-change-[transform,opacity]"
                                            sideOffset={5}
                                          >
                                            <p className="text-black">View</p>
                                            <TooltipArrow className="fill-white" />
                                          </TooltipContent>
                                        </Tooltip>
                                      </TooltipProvider>
                                    </div>
                                  </>
                                )}
                              </div>
                            </AccordionTrigger>
                            <AccordionContent className="pl-6">
                              <TreeItem
                                data={item.children}
                                selectedItemId={selectedItemId}
                                handleSelectChange={handleSelectChange}
                                expandedItemIds={expandedItemIds}
                                FolderIcon={FolderIcon}
                                ItemIcon={ItemIcon}
                                handleItemClick={handleItemClick}
                              />
                            </AccordionContent>
                          </AccordionPrimitive.Item>
                        </AccordionPrimitive.Root>
                      ) : (
                        <div
                          className={cn(
                            "flex items-center py-2 pl-2 pr-2 cursor-pointer",
                            className,
                            selectedItemId === item.value
                              ? "before:opacity-100 bg-gray-200 text-accent-foreground before:border-l-2 before:border-l-accent-foreground/50 dark:before:border-0"
                              : "hover:before:opacity-100 hover:bg-gray-200 hover:text-gray-600",
                          )}
                          onClick={() => handleSelectChange(item)}
                        >
                          {FolderIcon && (
                            <FolderIcon
                              className="h-4 w-4 shrink-0 mr-2 text-accent-foreground/50"
                              aria-hidden="true"
                            />
                          )}
                          {isEditing[index] ? (
                            <>
                              <div className="mb-2 mr-4">
                                <input
                                  type="text"
                                  value={editedLabels[index]}
                                  onChange={(e) =>
                                    setEditedLabels((prevState) => ({
                                      ...prevState,
                                      [index]: e.target.value,
                                    }))
                                  }
                                  onClick={(e) => e.stopPropagation()}
                                  className="text-sm px-3 py-2 w-40  border border-gray-300 rounded-md shadow-sm focus:ring-accent focus:border-accent"
                                  placeholder="Label"
                                  maxLength={30}
                                />
                              </div>
                              <div className="mb-2 mr-4">
                                <input
                                  type="text"
                                  value={editedDescriptions[index]}
                                  onChange={(e) =>
                                    setEditedDescriptions((prevState) => ({
                                      ...prevState,
                                      [index]: e.target.value,
                                    }))
                                  }
                                  onClick={(e) => e.stopPropagation()}
                                  className="text-sm px-3 py-2 w-80 border border-gray-300 rounded-md shadow-sm focus:ring-accent focus:border-accent"
                                  placeholder="Description"
                                  maxLength={50}
                                />
                              </div>
                            </>
                          ) : (
                            <div className="flex">
                              <div className="text-left">
                                <span className="font-semibold">
                                  {item.label}
                                </span>
                              </div>
                              {/* {item.description !== "" && (
                                <div className="ml-4">
                                  <span className="text-gray-600">
                                    {item.description}
                                  </span>
                                </div>
                              )} */}
                            </div>
                          )}
                          <div className="topic-icons-inner flex">
                            {isEditing[index] ? (
                              <>
                                <div
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleSaveClick(index).catch((error) =>
                                      console.log(error),
                                    );
                                  }}
                                  className="text-accent-foreground/50 hover:text-accent cursor-pointer ml-4 custom-button"
                                >
                                  <Save />
                                </div>
                                <div
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleCancelClick(index);
                                  }}
                                  className="text-accent-foreground/50 hover:text-accent cursor-pointer ml-4 custom-button"
                                >
                                  <XCircle />
                                </div>
                              </>
                            ) : (
                              <>
                                <div
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleEditClick(index, item);
                                  }}
                                  className="text-accent-foreground/50 hover:text-accent cursor-pointer ml-4 custom-button"
                                >
                                  <TooltipProvider>
                                    <Tooltip>
                                      <TooltipTrigger asChild>
                                        {editBtn ? <Pencil /> : null}
                                      </TooltipTrigger>
                                      <TooltipContent
                                        className="data-[state=delayed-open]:data-[side=top]:animate-slideDownAndFade data-[state=delayed-open]:data-[side=right]:animate-slideLeftAndFade data-[state=delayed-open]:data-[side=left]:animate-slideRightAndFade data-[state=delayed-open]:data-[side=bottom]:animate-slideUpAndFade text-violet11 select-none rounded-[4px] bg-white px-[15px] py-[10px] text-[15px] leading-none shadow-[hsl(206_22%_7%_/_35%)_0px_10px_38px_-10px,_hsl(206_22%_7%_/_20%)_0px_10px_20px_-15px] will-change-[transform,opacity]"
                                        sideOffset={5}
                                      >
                                        <p className="text-black">Edit</p>
                                        <TooltipArrow className="fill-white" />
                                      </TooltipContent>
                                    </Tooltip>
                                  </TooltipProvider>
                                </div>
                                <div
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleAddClick(index);
                                  }}
                                  className="text-accent-foreground/50 hover:text-accent cursor-pointer ml-4 custom-button"
                                >
                                  <TooltipProvider>
                                    <Tooltip>
                                      <TooltipTrigger asChild>
                                        {addBtn && <PlusCircle />}
                                      </TooltipTrigger>
                                      <TooltipContent
                                        className="data-[state=delayed-open]:data-[side=top]:animate-slideDownAndFade data-[state=delayed-open]:data-[side=right]:animate-slideLeftAndFade data-[state=delayed-open]:data-[side=left]:animate-slideRightAndFade data-[state=delayed-open]:data-[side=bottom]:animate-slideUpAndFade text-violet11 select-none rounded-[4px] bg-white px-[15px] py-[10px] text-[15px] leading-none shadow-[hsl(206_22%_7%_/_35%)_0px_10px_38px_-10px,_hsl(206_22%_7%_/_20%)_0px_10px_20px_-15px] will-change-[transform,opacity]"
                                        sideOffset={5}
                                      >
                                        <p className="text-black">Add</p>
                                        <TooltipArrow className="fill-white" />
                                      </TooltipContent>
                                    </Tooltip>
                                  </TooltipProvider>
                                </div>
                                <div
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleClickView(item);
                                  }}
                                  className="text-accent-foreground/50 hover:text-accent cursor-pointer ml-4"
                                >
                                  <TooltipProvider>
                                    <Tooltip>
                                      <TooltipTrigger asChild>
                                        {viewBtn && <Eye />}
                                      </TooltipTrigger>
                                      <TooltipContent
                                        className="data-[state=delayed-open]:data-[side=top]:animate-slideDownAndFade data-[state=delayed-open]:data-[side=right]:animate-slideLeftAndFade data-[state=delayed-open]:data-[side=left]:animate-slideRightAndFade data-[state=delayed-open]:data-[side=bottom]:animate-slideUpAndFade text-violet11 select-none rounded-[4px] bg-white px-[15px] py-[10px] text-[15px] leading-none shadow-[hsl(206_22%_7%_/_35%)_0px_10px_38px_-10px,_hsl(206_22%_7%_/_20%)_0px_10px_20px_-15px] will-change-[transform,opacity]"
                                        sideOffset={5}
                                      >
                                        <p className="text-black">View</p>
                                        <TooltipArrow className="fill-white" />
                                      </TooltipContent>
                                    </Tooltip>
                                  </TooltipProvider>
                                </div>
                              </>
                            )}
                          </div>
                        </div>
                      )}
                    </>
                    {/* )} */}
                  </li>
                  {isAdding && activeRow !== null && activeRow === index && (
                    <AddTopic
                      onSave={handleSaveNewItem}
                      onCancel={handleCancelAdd}
                      newAddedItem={item}
                    />
                  )}
                </>
              ))}
          </ul>
        </div>
      </div>
    );
  },
);

TreeItem.displayName = "TreeItem";

const AccordionTrigger = React.forwardRef<
  React.ElementRef<typeof AccordionPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Trigger>
>(({ className, children, ...props }, ref) => (
  <AccordionPrimitive.Header>
    <AccordionPrimitive.Trigger
      ref={ref}
      className={cn(
        "px-1 pl-2 flex flex-1 w-full items-center py-2 transition-all last:[&[data-state=open]>svg]:rotate-90",
        className,
      )}
      {...props}
    >
      {children}
      <ChevronRight className="h-4 w-4 shrink-0 transition-transform duration-200 text-accent-foreground/50 ml-auto" />
    </AccordionPrimitive.Trigger>
  </AccordionPrimitive.Header>
));
AccordionTrigger.displayName = AccordionPrimitive.Trigger.displayName;

const AccordionContent = React.forwardRef<
  React.ElementRef<typeof AccordionPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Content>
>(({ className, children, ...props }, ref) => (
  <AccordionPrimitive.Content
    ref={ref}
    className={cn(
      "overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",
      className,
    )}
    {...props}
  >
    <div className="pb-1 pt-0">{children}</div>
  </AccordionPrimitive.Content>
));
AccordionContent.displayName = AccordionPrimitive.Content.displayName;

export { TreeItem, type TreeDataItem };
