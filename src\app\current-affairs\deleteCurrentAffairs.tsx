"use client";
import type { BaseSyntheticEvent } from "react";
import React, { useEffect } from "react";
import type { ErrorCatch, LogUserActivityRequest, ToastType } from "@/types";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import { ORG_KEY } from "@/lib/constants";
import useCurrentAffairs from "@/hooks/useCurrentAffairs";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";

export default function DeleteBulletinBoard({
  onCancel,
  bulletinId,
}: {
  onCancel: () => void;
  bulletinId?: string;
}): React.JSX.Element {
  const { t } = useTranslation();
  const { toast } = useToast() as ToastType;
  const { deleteBulletin } = useCurrentAffairs();
  const { updateUserActivity } = useLogUserActivity();
  useEffect(() => {}, []);
  const handleDeleteClick = async (e: BaseSyntheticEvent): Promise<void> => {
    console.log(e);
    console.log(bulletinId);

    try {
      const orgId = localStorage.getItem(ORG_KEY);
      const requestBody = {
        bulletin_id: bulletinId as string,
        org_id: orgId ?? "",
      };

      const result = await deleteBulletin(requestBody);
      if (result.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.deleteTitle"),
          description: t("successMessages.deleteDescription"),
        });
      }
      onCancel();
      const params = {
        activity_type: "Current_Affairs",
        screen_name: "Current Affairs",
        action_details: "Current affairs deleted ",
        target_id: bulletinId as string,
        log_result: "SUCCESS",
      };
      void updateLogUserActivity(params).catch((error) => {
        console.error(error);
      });
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.details,
      });
      const params = {
        activity_type: "Current_Affairs",
        screen_name: "Current Affairs",
        action_details: "Failed to delete current affairs ",
        target_id: bulletinId as string,
        log_result: "ERROR",
      };
      void updateLogUserActivity(params).catch((error) => {
        console.error(error);
      });
    }
  };
  const handleCancel = (): void => {
    onCancel();
  };
  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  return (
    <>
      <div className="mb-2 mr-4">
        <p className="ml-0 ">`${t(SUCCESS_MESSAGES.deleteEventMsg)}`</p>
      </div>
      <div className="flex topic-icons pr-4">
        <div className="flex items-center justify-center float-right gap-3">
          <Button type="button" className="bg-[#33363F]" onClick={handleCancel}>
            {t("buttons.cancel")}
          </Button>

          <Button
            type="submit"
            className="bg-[#9FC089]"
            onClick={(e: BaseSyntheticEvent) => {
              handleDeleteClick(e).catch((error) => console.log(error));
            }}
          >
            {t("buttons.delete")}
          </Button>
        </div>
      </div>
    </>
  );
}
