import { supabase } from "../lib/client";
import type {
  AccessPrivilegeRequest,
  PrivilegeResult,
  ErrorType,
  GetPrivilegeRequest,
  PrivilegeListForRole,
  GetPrivilegeResponse,
  LoginUserData,
} from "@/types";
import { rpc } from "@/lib/apiConfig";
interface usePrivilegesReturn {
  getListOfPrivilege: (
    params: GetPrivilegeRequest,
  ) => Promise<PrivilegeListForRole>;
  updatePrivileges: (
    params: AccessPrivilegeRequest,
  ) => Promise<PrivilegeResult>;
  getPrivilegesList: () => Promise<GetPrivilegeResponse>;
}

const usePrivileges = (): usePrivilegesReturn => {
  async function getListOfPrivilege(
    params: GetPrivilegeRequest,
  ): Promise<PrivilegeListForRole> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.getPrivilegeListForRole,
        params,
      )) as {
        data: PrivilegeListForRole;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as PrivilegeListForRole;
    } catch (error) {
      console.error("Error", error);
      throw error;
    }
  }

  async function updatePrivileges(
    params: AccessPrivilegeRequest,
  ): Promise<PrivilegeResult> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.updateRolePrivilege,
        params,
      )) as {
        data: PrivilegeResult;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as PrivilegeResult;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function getPrivilegesList(): Promise<GetPrivilegeResponse> {
    try {
      const userDetails = localStorage.getItem("userDetails") as string;
      const users = JSON.parse(userDetails) as LoginUserData;
      const user_id = users?.id;
      const orgId = localStorage.getItem("orgId");
      const params = {
        org_id: orgId,
        user_id: user_id,
      };
      const { data, error } = (await supabase.rpc(
        rpc.getUserRolePrivilege,
        params,
      )) as {
        data: GetPrivilegeResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as GetPrivilegeResponse;
    } catch (error) {
      console.error("Error", error);
      throw error;
    }
  }
  return { getListOfPrivilege, updatePrivileges, getPrivilegesList };
};

export default usePrivileges;
