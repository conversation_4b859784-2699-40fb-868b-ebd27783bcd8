import React from "react";
import type {
  DeleteCourseRequest,
  <PERSON>rrorCatch,
  LogUserActivityRequest,
  ToastType,
} from "@/types";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import useCourse from "@/hooks/useCourse";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";

export default function DeleteCourse({
  data,
  onSave,
  onCancel,
}: {
  onSave: () => void;
  onCancel: () => void;
  data: DeleteCourseRequest;
  isModal?: boolean;
}): React.JSX.Element {
  const {t} = useTranslation();
  const { toast } = useToast() as ToastType;
  const { deleteCourse } = useCourse();
  const { updateUserActivity } = useLogUserActivity();

  const handleDeleteClick = (): void => {
    void handleToastSave();
    onCancel();
  };

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  const handleToastSave = async (): Promise<void> => {
    const courseDeleteData = {
      org_id: data.org_id,
      course_id: data.course_id,
    };
    try {
      const result = await deleteCourse(courseDeleteData);
      if (result.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.course_deleted"),
          description: t("successMessages.course_delete_msg"),
        });

        const params = {
          activity_type: "Course",
          screen_name: "Course",
          action_details: "Course deleted successfully",
          target_id: data.course_id as string,
          log_result: "SUCCESS",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
        onSave();
        onCancel();
      } else if (result.status === "error") {
        toast({
           variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: t("errorMessages.delete_course_plan_message"),
        });
        const params = {
          activity_type: "Course",
          screen_name: "Course",
          action_details: "Failed to delete course",
          target_id: data.course_id as string,
          log_result: "ERROR",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
      console.error("An unexpected error occurred:", error);
    }
  };
  return (
    <>
      <div className="mb-2 mr-4">
        <p className="ml-0 ">{t("courses.deletePrompt")}</p>
      </div>
      <div className="flex topic-icons pr-4">
        <div className="flex items-center justify-center float-right">
          <Button
            type="button"
            className="primary bg-[#33363F] text-white"
            variant="outline"
            onClick={onCancel}
          >
            {t("buttons.cancel")}
          </Button>
          &nbsp;
          <Button
            type="submit"
            className="primary bg-[#9FC089]"
            onClick={handleDeleteClick}
          >
            {t("buttons.delete")}
          </Button>
        </div>
      </div>
    </>
  );
}
