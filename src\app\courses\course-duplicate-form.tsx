import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { DateTimePicker } from "@/components/ui/date-time-picker/date-time-picker";

import { parseZonedDateTime } from "@internationalized/date";
import type { DateValue, ZonedDateTime } from "@internationalized/date";

import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { courseTypes } from "@/lib/constants";
import { Input } from "@/components/ui/input";
import { ModalButton } from "@/components/ui/modalButton";
import type {
  CourseDuplicateType,
  CourseDuplicateFormType,
  DuplicateCourseValueType,
  ErrorCatch,
  ToastType,
  LogUserActivityRequest,
} from "@/types";
import { useEffect, useState } from "react";
import moment from "moment-timezone";
import { DuplicateCourseSchema } from "@/schema/schema";
import TreeSelectComponent from "@/components/ui/tree-select/tree-select";
import useCourse from "@/hooks/useCourse";
import { useToast } from "@/components/ui/use-toast";
import type { TreeDataItem } from "@/components/ui/tree";
import {
  DATE_FORMAT,
  DATE_FORMAT_WITHOUT_SECONDS,
  MINUTE,
  ORG_KEY,
} from "@/lib/constants";
import { SUCCESS_MESSAGES, ERROR_MESSAGES } from "@/lib/messages";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";

export const CourseDuplicateForm: React.FC<CourseDuplicateType> = (
  props,
): JSX.Element => {
  const { toast } = useToast() as ToastType;
  const { t } = useTranslation();
  // const [selectedCourseType, setSelectedCourseType] = useState<string>("");
  const [startDateTime, setStartDateTime] = useState<
    ZonedDateTime | DateValue
  >();

  const [timezone, setTimezone] = useState("");
  const { duplicateCourse } = useCourse();
  const { updateUserActivity } = useLogUserActivity();
  const [courseType, setCourseType] = useState(courseTypes);
  // const [isTypeDisable, setIsTypeDisable] = useState<boolean>(false);
  const form = useForm<CourseDuplicateFormType>({
    resolver: zodResolver(DuplicateCourseSchema),
  });
  const [defaultTreeSelect, setDefaultTreeSelect] = useState("");
  const handleChanage = (dateObject: DateValue): void => {
    const modifiedDateObject = { ...dateObject };

    modifiedDateObject.month = modifiedDateObject.month - 1;
    const setEndDateTime = parseZonedDateTime(
      moment.tz(modifiedDateObject, timezone).format(DATE_FORMAT) +
        `[${timezone}]`,
    );
    setStartDateTime(setEndDateTime);
  };
  useEffect(() => {
    form.setValue("courseFullName", props.data?.full_name ?? "");
    form.setValue("courseShortName", props.data?.short_name ?? "");
    form.setValue("courseCategory", props.data?.category_id ?? "");
    form.setValue(
      "courseVisibility",
      props.data?.visibility == true ? "yes" : "no",
    );
    form.setValue("course_type", props.data?.course_type ?? "");
    const key = findKeyByValue(props.topicList ?? [], props.data?.category_id);
    setDefaultTreeSelect(key as string);
  }, []);
  const handleCourseTypeChange = (selectedValue: string): void => {
    // setIsTypeDisable(false);

    form.setValue("course_type", selectedValue);
    // setSelectedCourseType(selectedValue);
  };

  const findKeyByValue = (
    treeData: TreeDataItem[],
    targetValue: unknown,
  ): string | null => {
    for (const node of treeData) {
      if (node.value === targetValue) {
        return String(node.key);
      }

      if (node.children && node.children.length > 0) {
        const keyInChildren = findKeyByValue(node.children, targetValue);
        if (keyInChildren != null) {
          return keyInChildren;
        }
      }
    }
    return null;
  };

  useEffect(() => {
    const currentTimezone = moment.tz.guess();
    setTimezone(currentTimezone);

    const originalDatetime = moment.tz(currentTimezone).format(DATE_FORMAT);

    const parsedDatetime = moment.tz(originalDatetime, currentTimezone);
    const newDatetime = parsedDatetime.add(60, MINUTE);
    const formattedDatetime =
      newDatetime.format(DATE_FORMAT) + `[${currentTimezone}]`;
    const dateTime = parseZonedDateTime(formattedDatetime);
    form.setValue("courseStartDate", dateTime);
    setStartDateTime(dateTime);
  }, []);

  async function onSubmit(data: CourseDuplicateFormType): Promise<void> {
    const dateStart = data.courseStartDate;

    const startDate = new Date(
      dateStart.year,
      dateStart.month - 1,
      dateStart.day,
      dateStart.hour,
      dateStart.minute,
    );

    const dateEnd = data.courseEndDate;

    const endDate = new Date(
      dateEnd.year,
      dateEnd.month - 1,
      dateEnd.day,
      dateEnd.hour,
      dateEnd.minute,
    );

    const momentStartDate = moment(startDate);
    const momentEndDate = moment(endDate);

    const formattedStartDate = momentStartDate.format(
      DATE_FORMAT_WITHOUT_SECONDS,
    );

    const formattedEndDate = momentEndDate.format(DATE_FORMAT_WITHOUT_SECONDS);

    const org_id = localStorage.getItem(ORG_KEY);
    console.log(data);
    const transformedData = {
      org_id: org_id,
      course_data: {
        course_id: props.data?.course_id,
        full_name: data.courseFullName,
        short_name: data.courseShortName,
        course_start_date: formattedStartDate,
        course_end_date: formattedEndDate,
        category_id: data.courseCategory,
        course_type: data.course_type,
        courseVisibility: data.courseVisibility,
      },
      update_section: true,
      update_exams: false,
      update_resources: false,
    };

    try {
      const result = await duplicateCourse(
        transformedData as DuplicateCourseValueType,
      );

      if (result.status == SUCCESS_MESSAGES.api_status_success) {
        toast({
          variant:SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.course_duplicate_title"),
          description: t("successMessages.course_duplicate_msg"),
        });
        const reqParams = {
          activity_type: "Course",
          screen_name: "Course",
          action_details: "Course duplicated successfully",
          target_id: props.data?.course_id as string,
          log_result: "SUCCESS",
        };
        void updateLogUserActivity(reqParams).catch((error) => {
          console.error(error);
        });
        props.onSave();
        props.closeDialog(true);
      } else {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: t("errorMessages.something_went_wrong"),
        });
        const reqParams = {
          activity_type: "Course",
          screen_name: "Course",
          action_details: "Failed to duplicate course",
          target_id: props.data?.course_id as string,
          log_result: "ERROR",
        };
        void updateLogUserActivity(reqParams).catch((error) => {
          console.error(error);
        });
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
    }
  }

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  return (
    <Form {...form}>
      <form
        onSubmit={(event) => void form.handleSubmit(onSubmit)(event)}
        className="space-y-4"
      >
        <div className="flex gap-4">
          <FormField
            control={form.control}
            name="courseFullName"
            render={({ field }) => (
              <FormItem className="flex-1">
                <FormLabel>
                  {String(t("courses.courseFullName"))}{" "}
                  <span className="text-red-700">*</span>
                </FormLabel>
                <FormControl>
                  <Input
                    autoComplete="off"
                    placeholder={String(t("courses.courseFullName"))}
                    {...field}
                    maxLength={50}
                    defaultValue={props.data?.full_name}
                  />
                </FormControl>
                <FormDescription></FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="courseShortName"
            render={({ field }) => (
              <FormItem className="flex-1">
                <FormLabel>
                  {String(t("courses.courseShortName"))}{" "}
                  <span className="text-red-700">*</span>
                </FormLabel>
                <FormControl>
                  <Input
                    autoComplete="off"
                    placeholder={String(t("courses.courseShortName"))}
                    maxLength={30}
                    {...field}
                    defaultValue={props.data?.short_name}
                  />
                </FormControl>
                <FormDescription></FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="courseCategory"
          render={() => (
            <FormItem>
              <FormLabel>
                {String(t("courses.courseCategory"))}
                <span className="text-red-700">*</span>
              </FormLabel>
              <FormControl>
                {/* <TreeSelectComponent
                  nodeData={props.nodeData}
                  selectLabel={"Select Category"}
                  onNodeSelect={(selectedValue) => {
                    form.setValue("courseCategory", selectedValue);
                  }}
                ></TreeSelectComponent> */}
                {defaultTreeSelect?.length > 0 && (
                  <div className="md:col-span-1">
                    <TreeSelectComponent
                      nodeData={props.topicList ?? []}
                      selectLabel={"Select Topic"}
                      onNodeSelect={(selectedValue: string | null) => {
                        if (selectedValue !== null) {
                          form.setValue("courseCategory", selectedValue);
                        }
                        setCourseType(courseTypes);
                      }}
                      defaultValueKey={defaultTreeSelect}
                    ></TreeSelectComponent>
                  </div>
                )}
              </FormControl>

              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex gap-4">
          <FormField
            control={form.control}
            name="courseStartDate"
            render={({ field }) => (
              <FormItem className="flex-1">
                <FormLabel>
                  {String(t("courses.courseStartDate"))}{" "}
                  <span className="text-red-700">*</span>
                </FormLabel>
                <FormControl>
                  <DateTimePicker
                    granularity="minute"
                    minValue={startDateTime}
                    value={field.value as DateValue}
                    onChange={(newDate) => {
                      handleChanage(newDate as DateValue);
                      field.onChange(newDate);
                    }}

                    //value={field.value}
                    //onChange={(newDate) => setDateTime(newDate)}
                  />
                </FormControl>
                <FormDescription></FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="courseEndDate"
            render={({ field }) => (
              <FormItem className="flex-1">
                <FormLabel>
                  {String(t("courses.courseEndDate"))}
                  <span className="text-red-700">*</span>
                </FormLabel>
                <FormControl>
                  <DateTimePicker
                    //defaultValue={defaultDate}
                    granularity="minute"
                    minValue={startDateTime}
                    value={field.value as DateValue}
                    onChange={(newDate) => {
                      // handleChanage(newDate);
                      field.onChange(newDate);
                    }}
                  />
                </FormControl>
                <FormDescription></FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <div className="flex gap-4">
          <FormField
            name="course_type"
            control={form.control}
            render={({ field }) => (
              <FormItem className="flex-1">
                <FormLabel>
                  {String(t("courses.selectCourseType"))}
                  <span className="text-red-700">*</span>
                </FormLabel>
                <FormControl>
                  <Select
                    onValueChange={handleCourseTypeChange}
                    defaultValue={props.data?.course_type ?? field.value}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                    <SelectContent>
                      {courseType.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="courseVisibility"
            render={({ field }) => (
              <FormItem className="flex-1">
                <FormLabel>
                  {" "}
                  {String(t("courses.courseVisibility"))}
                  <span className="text-red-700">*</span>
                </FormLabel>

                <Select
                  name="courseVisibility"
                  onValueChange={field.onChange}
                  defaultValue={props.data?.visibility == true ? "yes" : "no"}
                >
                  {" "}
                  <FormControl>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="true">Yes</SelectItem>
                    <SelectItem value="false">No</SelectItem>
                  </SelectContent>
                </Select>

                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <ModalButton
          closeDialog={() => props.closeDialog(false)}
          closeLabel={String(t("buttons.cancel"))}
          submitLabel={String(t("buttons.submit"))}
        />
      </form>
    </Form>
  );
};
