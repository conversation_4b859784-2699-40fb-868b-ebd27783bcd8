import React from "react";
import { Button } from "@/components/ui/button";
import { useTranslation } from "react-i18next";

interface ConfirmDeleteModalProps {
  title: string;
  message: string;
  onConfirm: () => void;
  onCancel: () => void;
  isLoading?: boolean;
  confirmButtonText?: string;
  cancelButtonText?: string;
}

export default function ConfirmDeleteModal({
  title,
  message,
  onConfirm,
  onCancel,
  isLoading = false,
}: ConfirmDeleteModalProps): React.JSX.Element {
  const { t } = useTranslation();

  const handleConfirmClick = (): void => {
    onConfirm();
  };

  const handleCancelClick = (): void => {
    onCancel();
  };

  return (
    <>
      <div className="mb-4">
        <h3 className="text-lg font-semibold mb-2">{title}</h3>
        <p className="text-gray-600">{message}</p>
      </div>
      <div className="flex justify-end space-x-2 pt-4">
        <Button
          type="button"
          variant="outline"
          onClick={handleCancelClick}
          disabled={isLoading}
          className="bg-gray-100 hover:bg-gray-200"
        >
          {t("buttons.cancel")}
        </Button>
        <Button
          type="button"
          onClick={handleConfirmClick}
          disabled={isLoading}
          className="bg-red-600 hover:bg-red-700 text-white"
        >
          {t("buttons.delete")}
        </Button>
      </div>
    </>
  );
}
