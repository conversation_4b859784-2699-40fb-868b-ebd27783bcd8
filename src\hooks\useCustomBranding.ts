import { supabase } from "@/lib/client";
import { rpc } from "@/lib/apiConfig";
import type { ErrorType, SuccessMessage, CustomBrandingDetails } from "@/types";

interface useCustomBrandingReturn {
  getCustomBrandingDetails: (orgId: string) => Promise<CustomBrandingDetails[]>;
  addCustomBranding: (params: CustomBrandingDetails) => Promise<SuccessMessage>;
}

const useCustomBranding = (): useCustomBrandingReturn => {
  async function addCustomBranding(
    params: CustomBrandingDetails,
  ): Promise<SuccessMessage> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.addCustomBranidng,
        params,
      )) as { data: SuccessMessage; error: ErrorType | null };
      if (error) {
        throw new Error(error.details);
      }
      return data as SuccessMessage;
    } catch (error) {
      console.error("Error", error);
      throw error;
    }
  }
  async function getCustomBrandingDetails(
    orgId: string,
  ): Promise<CustomBrandingDetails[]> {
    try {
      const params = { org_id: orgId };

      const { data, error } = (await supabase.rpc(
        rpc.getCustomBrandingDetails,
        params,
      )) as { data: CustomBrandingDetails[]; error: ErrorType | null };
      if (error) {
        throw new Error(error.details);
      }
      return data as CustomBrandingDetails[];
    } catch (error) {
      console.error("Error", error);
      throw error;
    }
  }
  return { addCustomBranding, getCustomBrandingDetails };
};

export default useCustomBranding;
