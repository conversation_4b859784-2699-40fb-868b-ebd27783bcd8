import React, { useEffect, useState } from "react";
import type {
  ExtendPlanValidityRequest,
  LogUserActivityRequest,
  SubscriptionListResults,
  SubscriptionPlans,
  ToastType,
} from "@/types";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { DateTimePicker } from "@/components/ui/date-time-picker/date-time-picker";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { MembershipExtendValiditySchema } from "@/schema/schema";
import type { ZonedDateTime } from "@internationalized/date";
import { parseZonedDateTime, type DateValue } from "@internationalized/date";
import moment from "moment";
import { DATE_FORMAT, FORMATTED_DATE_FORMAT, ORG_KEY } from "@/lib/constants";
import useSubscription from "@/hooks/useSubscription";
import { useToast } from "@/components/ui/use-toast";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";

export default function ExtendSubscriptionModal({
  data,
  onSave,
  onCancel,
}: {
  onSave: () => void;
  onCancel: () => void;
  data: SubscriptionListResults;
}): React.JSX.Element {
  const { t } = useTranslation();
  const { toast } = useToast() as ToastType;
  const form = useForm<SubscriptionPlans>({
    resolver: zodResolver(MembershipExtendValiditySchema),
  });
  const { extendValidity } = useSubscription();
  const [endDateTime, setEndDateTime] = useState<ZonedDateTime | DateValue>();
  const { updateUserActivity } = useLogUserActivity();
  useEffect(() => {
    console.log("data", data);
    const currentTimezone = moment.tz.guess();
    const parsedDatetime = moment.tz(
      data.valid_to.split("+")[0],
      currentTimezone,
    );

    const formattedDatetime =
      parsedDatetime.format(DATE_FORMAT) + `[${currentTimezone}]`;
    const dateTime = parseZonedDateTime(formattedDatetime);
    setEndDateTime(endDateTime);
    form.setValue("valid_to", dateTime);
    form.setValue("name", data.name);
  }, []);
  async function onSubmit(): Promise<void> {
    const formData = form.getValues();
    const dateEnd = formData.valid_to;
    const endDate = new Date(
      dateEnd.year,
      dateEnd.month - 1,
      dateEnd.day,
      dateEnd.hour,
      dateEnd.minute,
    );
    const momentEndDate = moment(endDate);
    const formattedValidTo = momentEndDate.format(FORMATTED_DATE_FORMAT);
    const org_id = localStorage.getItem(ORG_KEY) ?? "";
    const subscriptionPlanData: ExtendPlanValidityRequest = {
      plan_id: data.id,
      validity_upto_date: formattedValidTo,
      org_id: org_id,
    };
    try {
      await extendValidity(subscriptionPlanData);
      onSave();
      toast({
        variant: SUCCESS_MESSAGES.toast_variant_default,
        title: t("successMessages.toast_success_title"),
        description: t("successMessages.extend_validity"),
      });
      const params = {
        activity_type: "Subscription",
        screen_name: "Subscription",
        action_details: "Extend plan validity ",
        target_id: data.id,
        log_result: "SUCCESS",
      };
      void updateLogUserActivity(params).catch((error) => {
        console.error(error);
      });
    } catch (e) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.extend_validity"),
      });
      const params = {
        activity_type: "Subscription",
        screen_name: "Subscription",
        action_details: "Failed to extend plan validity ",
        target_id: data.id,
        log_result: "ERROR",
      };
      void updateLogUserActivity(params).catch((error) => {
        console.error(error);
      });
    }
  }
  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };
  return (
    <div className="w-full">
      <>
        <h1 className="text-2xl font-semibold tracking-tight"></h1>
      </>
      <div className="border rounded-md p-4 mt-4">
        <Form {...form}>
          <form onSubmit={(event) => void form.handleSubmit(onSubmit)(event)}>
            <div>
              <FormField
                name="name"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel autoCorrect="off">
                      {t("subscriptionPlan.membershipName")}{" "}
                      <span className="text-red-700">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        autoComplete="off"
                        type="text"
                        {...field}
                        disabled
                        maxLength={30}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="row-span-2 mt-4">
              <FormField
                name="valid_to"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {t("subscriptionPlan.validTo")}{" "}
                      <span className="text-red-700">*</span>
                    </FormLabel>
                    <FormControl>
                      <DateTimePicker
                        granularity="minute"
                        // minValue={endDateTime}
                        defaultValue={endDateTime}
                        hideTimeZone={true}
                        value={field.value as DateValue}
                        onChange={(newDate) => {
                          // handleValidToChange(newDate);
                          field.onChange(newDate);
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="w-full flex justify-end mt-4">
              <div className="px-4">
                {/* <Link href={pageUrl.membershipPlanList}> */}
                <Button
                  className="w-full sm:w-auto bg-[#33363F]"
                  type="button"
                  onClick={onCancel}
                >
                  {t("buttons.cancel")}
                </Button>
                {/* </Link> */}
              </div>
              <div>
                <Button type="submit" className="bg-[#9FC089]">
                  {t("buttons.submit")}
                </Button>
              </div>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
}
