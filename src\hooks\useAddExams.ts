import { supabase } from "../lib/client";
import type {
  CourseDeatilsType,
  AddExamsResultType,
  AddNewExamSubmitType,
  CourseDetailsRequest,
  ErrorType,
  updateExamSubmitType,
  LinkExamToCourse,
} from "../types";
import { rpc } from "@/lib/apiConfig";
interface UseAddExamsReturn {
  getSections: (
    reqParams: CourseDetailsRequest,
  ) => Promise<CourseDeatilsType[]>;
  addNewExam: (formData: AddNewExamSubmitType) => Promise<AddExamsResultType>;
  updateExams: (formData: updateExamSubmitType) => Promise<AddExamsResultType>;
  addQuiz:(params:LinkExamToCourse)=> Promise<AddExamsResultType>;
}

const useAddExams = (): UseAddExamsReturn => {
  async function getSections(
    reqParams: CourseDetailsRequest,
  ): Promise<CourseDeatilsType[]> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.getCourseDetails,
        reqParams,
      )) as {
        data: CourseDeatilsType[];
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as CourseDeatilsType[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function addNewExam(
    formData: AddNewExamSubmitType,
  ): Promise<AddExamsResultType> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.insertQuiz,
        formData,
      )) as {
        data: AddExamsResultType;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as AddExamsResultType;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function addQuiz(
    params: LinkExamToCourse,
  ): Promise<AddExamsResultType> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.addQuiz,
        params,
      )) as {
        data: AddExamsResultType;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as AddExamsResultType;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  const updateExams = async (
    formData: updateExamSubmitType,
  ): Promise<AddExamsResultType> => {
    try {
      const { data, error } = (await supabase.rpc<string, null>(
        rpc.updateQuiz,
        formData,
      )) as {
        data: AddExamsResultType;
        error: ErrorType | null;
      };

      if (error) {
        throw new Error(error.details);
      }

      return data as AddExamsResultType;
    } catch (err) {
      console.error("Error:", err);
      throw err;
    }
  };

  return {
    getSections,
    addNewExam,
    updateExams,
    addQuiz,
  };
};

export default useAddExams;
