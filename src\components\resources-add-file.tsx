"use client";
import { useForm } from "react-hook-form";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

import React, { useEffect, useState } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@radix-ui/react-label";
// import { Milestone } from "./milestone";
import { Button } from "@/components/ui/button";
import type {
  AddResources,
  ToastType,
  AddResourceFile,
  AddResourceUrl,
  ErrorCatch,
  ResourceLibraryData,
  EditResourceFile,
  EditResourceUrl,
  ComboData,
  LoginUserData,
  LogUserActivityRequest,
  YouTubeVideoDetails,
} from "@/types";
import { AddResourceVideoschema, AddResourceschema } from "@/schema/schema";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  DEFAULT_FOLDER_ID,
  // YoutubeUrl,
  pageUrl,
  YOUTUBE_KEY,
} from "@/lib/constants";
import Link from "next/link";
import { useToast } from "@/components/ui/use-toast";
import useResourceLibrary from "@/hooks/useResourceLibrary";
import { Textarea } from "./ui/textarea";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import {
  ORG_KEY,
  File,
  ResourceExtensions,
  //  Video
} from "@/lib/constants";
import { Combobox } from "@/components/ui/combobox";
import { supabase } from "@/lib/client";
import useCourse from "@/hooks/useCourse";
import { FolderPlus } from "lucide-react";
import { Modal } from "./ui/modal";
import AddFolders from "@/app/resources-library/add-folders";
import UploadPdfUrl from "./uploadPdfUrl";
import useLogUserActivity from "@/hooks/useLogUserActivity";
// Removed invalid import for NEXT_PUBLIC_DEFAULT_THUMBNAIL_URL
import { useTranslation } from "react-i18next";
interface ResourceAddProps {
  resourceType: string;
  onCancel: () => void;
  resourceData: ResourceLibraryData;
  uploadData: string | null;
  onSave: () => void;
}

export const CourseResourcesAddfile: React.FC<ResourceAddProps> = ({
  resourceType,
  onCancel,
  resourceData,
  uploadData,
  onSave,

  // folderId
}) => {
  const { t } = useTranslation();
  const form = useForm<AddResources>({
    resolver: zodResolver(
      resourceType === File ? AddResourceschema : AddResourceVideoschema,
    ),
  });

  const org_id = localStorage.getItem(ORG_KEY);
  const { addResources, editResources } = useResourceLibrary();
  const { toast } = useToast() as ToastType;
  const [comboData, setComboData] = useState<ComboData[]>([]);
  const [isButtonDisable, setButtonDisable] = useState<boolean>(false);
  const [extensions, setExtensions] = useState<ComboData[]>([]);
  const [selectedExtensions, setSelectedExtensions] = useState<string>("");
  const [folderId, setFolderId] = useState<string>("");
  const [defaultLabel, setDefaultLabel] = useState<string>("");
  const [defaultThumbnail, setDefaultThumbnail] = useState<string>("");
  const [openFolder, setOpenFolder] = useState<boolean>(false);
  const [reloadList, setReloadList] = useState<boolean>(false);
  const [openPdfDialog, setIsOpenPdfDialog] = useState<boolean>(false);
  const [uploadedPdfUrl, setUploadedPdfUrl] = useState<string>("");
  const [isUrlFromUpload, setIsUrlFromUpload] = useState<boolean>(false);
  const { updateUserActivity } = useLogUserActivity();
  const { listFolderFromLibrary } = useCourse();
  useEffect(() => {
    resourceType === File
      ? setExtensions(ResourceExtensions.image)
      : setExtensions(ResourceExtensions.video);
  }, [resourceType]);

  useEffect(() => {
    // Set default thumbnail from environment variable
    const defaultThumbnailUrl =
      process.env.NEXT_PUBLIC_DEFAULT_THUMBNAIL_URL ?? "";
    console.log("defaultThumbnailUrl", defaultThumbnailUrl);

    if (resourceData !== null) {
      let url = "";
      if (resourceData.file_type === File) {
        url = resourceData.url;
      } else {
        url = resourceData.external_url;

        if (resourceData.length !== null && resourceData.length !== undefined) {
          const [hours, minutes, seconds] = resourceData.length.split(":");
          form.setValue("videolength.HH", hours);
          form.setValue("videolength.MM", minutes);
          form.setValue("videolength.SS", seconds);
        }
      }
      setSelectedExtensions(
        (resourceData?.extension ?? "").replace(/^\./, "") as string,
      );
      form.setValue("url", url);
      form.setValue("name", resourceData.name);
      form.setValue("description", resourceData.description);

      form.setValue(
        "extension",
        (resourceData?.extension ?? "").replace(/^\./, "") as string,
      );
      form.setValue("page_count", resourceData.page_count as number);
      form.setValue("thumbnail_url", resourceData.thumbnail_url as string);
      setDefaultThumbnail(resourceData.thumbnail_url as string);
      setFolderId(resourceData.folder_id);
    } else {
      // Set default thumbnail for new resources
      if (defaultThumbnailUrl !== "") {
        form.setValue("thumbnail_url", defaultThumbnailUrl);
        setDefaultThumbnail(defaultThumbnailUrl);

        console.log(
          "🖼️ Default thumbnail set from environment:",
          defaultThumbnailUrl,
        );
      }
    }
  }, [form, resourceData, uploadData]);

  useEffect(() => {
    const fetchData = async (): Promise<void> => {
      try {
        const orgId = localStorage.getItem("orgId");
        const folderList = await listFolderFromLibrary(orgId as string);
        const comboData: ComboData[] = folderList?.map((cat) => ({
          value: cat.folder_id,
          label: cat.folder_name,
        }));
        const defaultFolder = comboData.find(
          (folder) => folder.value === resourceData.folder_id,
        );
        setDefaultLabel(defaultFolder ? (defaultFolder.label as string) : "");

        setComboData(comboData);
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
        console.error("Error fetching data:");
      }
    };
    fetchData().catch((error) => console.log(error));
  }, [reloadList]);
  const onHandleFolderChange = (selectedValue: string): void => {
    console.log(selectedValue);
    setFolderId(selectedValue);
  };

  const handleImportFolder = (): void => {
    setOpenFolder(!openFolder);
  };
  async function onSubmit(): Promise<void> {
    if (
      typeof folderId !== "string" ||
      folderId.trim().length === 0 ||
      folderId === DEFAULT_FOLDER_ID
    ) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.select_folder"),
      });
    } else {
      setButtonDisable(true);
      const formData = form.getValues();
      console.log(formData);

      if (formData.name !== null && /^[0-9,-]*$/.test(formData.name)) {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: t("errorMessages.valid_name"),
        });
        return;
      }

      if (
        // formData.description !== null &&
        // /^[0-9\s\W_]*$/.test(formData.description)
        formData.description === null ||
        formData.description.trim() === ""
      ) {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: t("errorMessages.valid_description"),
        });
        return;
      }

      // if (resourceType === Video) {
      //   const validationResult = validateUrl(formData.url, [YoutubeUrl]);
      //   if (validationResult === false) {
      //     toast({
      //       variant: ERROR_MESSAGES.toast_variant_destructive,
      //       title: ERROR_MESSAGES.toast_error_title,
      //       description: ERROR_MESSAGES.valid_url,
      //     });
      //   }
      //   return;
      // }

      const videoLength = `${
        formData.videolength?.HH !== undefined && formData.videolength.HH !== ""
          ? formData.videolength.HH.length === 1
            ? `0${formData.videolength.HH}`
            : formData.videolength.HH
          : "00"
      }:${
        formData.videolength?.MM !== undefined && formData.videolength.MM !== ""
          ? formData.videolength.MM.length === 1
            ? `0${formData.videolength.MM}`
            : formData.videolength.MM
          : "00"
      }:${
        formData.videolength?.SS !== undefined && formData.videolength.SS !== ""
          ? formData.videolength.SS.length === 1
            ? `0${formData.videolength.SS}`
            : formData.videolength.SS
          : "00"
      }`;

      if (Object.keys(resourceData).length === 0) {
        const fileParams: AddResourceFile = {
          file_data: {
            url: formData.url.trim(),
            name: formData.name.trim(),
            description: formData.description,
            extension: selectedExtensions,
            module_source: resourceType,
            page_count: formData.page_count,
            thumbnail_url: formData.thumbnail_url ?? "",
            ...(folderId.length > 0 ? { folder_id: folderId } : {}),
          },
          org_id: org_id ?? "",
        };

        const urlParams: AddResourceUrl = {
          url_data: {
            name: formData.name.trim(),
            description: formData.description,
            external_url: formData.url.trim(),
            module_source: resourceType,
            length: videoLength,
            extension: selectedExtensions,
            page_count: formData.page_count,
            thumbnail_url: formData.thumbnail_url ?? "",
            ...(folderId.length > 0 ? { folder_id: folderId } : {}),
          },
          org_id: org_id ?? "",
        };

        const params = resourceType === File ? fileParams : urlParams;
        try {
          const result = await addResources(params, resourceType);
          if (result.status === SUCCESS_MESSAGES.api_status_success) {
            setButtonDisable(true);
            toast({
              variant: SUCCESS_MESSAGES.toast_variant_success,
              title: t("successMessages.toast_success_title"),
              description: t("successMessages.resource_add_success"),
            });
            onSave();
            const params = {
              activity_type: "Resource_Library",
              screen_name: "Resource Library",
              action_details: "Resource added to library ",
              target_id: result.instance_id as string,
              log_result: "SUCCESS",
            };
            void updateLogUserActivity(params).catch((error) => {
              console.error(error);
            });
          } else {
            toast({
              variant: ERROR_MESSAGES.toast_variant_destructive,
              title: t("errorMessages.toast_error_title"),
              description: result.status,
            });
            const params = {
              activity_type: "Resource_Library",
              screen_name: "Resource Library",
              action_details: "Failed to add resource to library ",
              target_id: DEFAULT_FOLDER_ID,
              log_result: "ERROR",
            };
            void updateLogUserActivity(params).catch((error) => {
              console.error(error);
            });
          }
        } catch (error) {
          setButtonDisable(false);
          const err = error as ErrorCatch;
          toast({
            variant: ERROR_MESSAGES.toast_variant_destructive,
            title: t("errorMessages.toast_error_title"),
            description: err?.message,
          });
          const params = {
            activity_type: "Resource_Library",
            screen_name: "Resource Library",
            action_details: "Failed to add resource to library ",
            target_id: DEFAULT_FOLDER_ID,
            log_result: "ERROR",
          };
          void updateLogUserActivity(params).catch((error) => {
            console.error(error);
          });
        }
      } else {
        const fileParams: EditResourceFile = {
          resource_data: {
            url: formData.url,
            name: formData.name,
            description: formData.description,
            module_source: resourceType,
            extension: selectedExtensions,
            page_count: formData.page_count,
            thumbnail_url: formData.thumbnail_url ?? "",
            ...(folderId.length > 0 ? { folder_id: folderId } : {}),
          },
          org_id: org_id ?? "",
          instance: resourceData.id,
        };

        const urlParams: EditResourceUrl = {
          resource_data: {
            name: formData.name,
            description: formData.description,
            external_url: formData.url,
            module_source: resourceType,
            length: videoLength,
            extension: selectedExtensions,
            page_count: formData.page_count,
            thumbnail_url: formData.thumbnail_url as string,
            ...(folderId.length > 0 ? { folder_id: folderId } : {}),
          },
          org_id: org_id ?? "",
          instance: resourceData.id,
        };

        const params = resourceType === File ? fileParams : urlParams;
        try {
          const result = await editResources(params, resourceType);

          if (result.status === SUCCESS_MESSAGES.api_status_success) {
            setButtonDisable(true);
            toast({
              variant: SUCCESS_MESSAGES.toast_variant_success,
              title: t("successMessages.toast_success_title"),
              description: t("successMessages.resource_update_success"),
            });
            onSave();
            const params = {
              activity_type: "Resource_Library",
              screen_name: "Resource Library",
              action_details: "Resource edited ",
              target_id: resourceData.id as string,
              log_result: "SUCCESS",
            };
            void updateLogUserActivity(params).catch((error) => {
              console.error(error);
            });
          } else {
            toast({
              variant: ERROR_MESSAGES.toast_variant_destructive,
              title: t("errorMessages.toast_error_title"),
              description: result.status,
            });
            const params = {
              activity_type: "Resource_Library",
              screen_name: "Resource Library",
              action_details: "Failed to edit resource ",
              target_id: resourceData.id as string,
              log_result: "ERROR",
            };
            void updateLogUserActivity(params).catch((error) => {
              console.error(error);
            });
          }
        } catch (error) {
          setButtonDisable(false);
          const err = error as ErrorCatch;
          toast({
            variant: ERROR_MESSAGES.toast_variant_destructive,
            title: t("errorMessages.toast_error_title"),
            description: err?.message,
          });
          const params = {
            activity_type: "Resource_Library",
            screen_name: "Resource Library",
            action_details: "Failed to edit resource ",
            target_id: resourceData.id as string,
            log_result: "ERROR",
          };
          void updateLogUserActivity(params).catch((error) => {
            console.error(error);
          });
        }
      }
    }
  }

  // const validateUrl = (inputUrl: string, acceptOnly: string[]): boolean => {
  //   const validate = inputUrl.match(
  //     /(http(s)?:\/\/.)?(www\.)?[-a-zA-Z0-9@:%._+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_+.~#?&//=]*)/g,
  //   );
  //   const accepted = acceptOnly.some((el) => inputUrl.startsWith(el));
  //   const res = validate !== null && accepted;
  //   if (res) return true;
  //   return false;
  // };

  const handleCancel = (): void => {
    onCancel();
  };

  const handleNameInputChange = (
    e: React.ChangeEvent<HTMLInputElement>,
  ): void => {
    const value = e.target.value;
    const sanitizedValue = value
      .trimStart()
      .replace(/\s{2,}/g, " ")
      .replace(/\s+$/, (match) =>
        match.length > 1 ? match.slice(0, -1) : match,
      );

    form.setValue("name", sanitizedValue);
  };

  const handleDescriptionChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>,
  ): void => {
    const value = e.target.value;
    const sanitizedValue = value
      .trimStart()
      .replace(/^\p{P}+/u, "")
      .replace(/[^\p{L}\p{M}\p{N}\s\-!@#$%^&*()_+={}[\]:;"'<>,.?/\\|~`]/gu, "")
      .replace(/\s{2,}/g, " ")
      .replace(
        /^[^\p{L}\p{M}\p{N}]|[^\p{L}\p{M}\p{N}\s\-!@#$%^&*()_+={}[\]:;"'<>,.?/\\|~`]/gu,
        "",
      )
      .replace(/\s+$/, (match) =>
        match.length > 1 ? match.slice(0, -1) : match,
      );
    form.setValue("description", sanitizedValue);
  };

  const handleExtension = (selectedValue: string): void => {
    setSelectedExtensions(selectedValue);
    form.setValue("extension", selectedValue);
    if (resourceType === "File" && selectedValue === "pdf") {
      setIsOpenPdfDialog(true);
    }
  };

  const handleClosePdfDialog = (): void => {
    setIsOpenPdfDialog(false);
  };

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ): Promise<string | null | undefined> => {
    const file = event.target.files?.[0];
    if (
      file &&
      (file.type === "image/png" ||
        file.type === "image/jpeg" ||
        file.type === "image/jpg")
    ) {
      if (file.size <= 1048576) {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        const fileExt = file.name.split(".").pop();
        const fileName = `${Math.random()}.${fileExt}`;
        const orgId = localStorage.getItem("orgId") as string;
        const userDetails = localStorage.getItem("userDetails");
        if (userDetails != null) {
          const users = JSON.parse(userDetails) as LoginUserData;
          const userId = users?.id;
          try {
            const result = await supabase.storage
              .from(`${orgId}`)
              .upload(`avatars/${userId}/${fileName}`, file, {
                cacheControl: "3600",
                upsert: true,
              });

            if (result.error) {
              console.error("Error uploading avatar:", result.error);
              return null;
            }
            const { data } = supabase.storage
              .from(`${orgId}`)
              .getPublicUrl(`avatars/${userId}/${fileName}`);

            console.log("Public URL:", data.publicUrl);
            setDefaultThumbnail(data.publicUrl);
            form.setValue("thumbnail_url", data.publicUrl);

            return data.publicUrl;
          } catch (error) {
            console.error("Upload error:", error);
            return null;
          }
        }
      } else {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: t("errorMessages.file_size_exceed"),
        });
        return null;
      }
    } else {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.select_valid_image"),
      });
      return null;
    }
  };
  function handleSaveFolder(): void {
    setOpenFolder(false);
    setReloadList(true);
  }

  const handleUploadComplete = (url: string): void => {
    setUploadedPdfUrl(url);
    setIsUrlFromUpload(true);
    form.setValue("url", url);
    console.log(uploadedPdfUrl);
  };
  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  // Function to extract file extension from URL
  const extractExtensionFromUrl = (url: string): string => {
    try {
      const urlObj = new URL(url);
      const pathname = urlObj.pathname;

      // Check for video platforms first
      if (url.includes("youtube.com") || url.includes("youtu.be")) {
        return "mp4"; // YouTube videos are typically mp4
      }
      if (url.includes("vimeo.com")) {
        return "mp4"; // Vimeo videos are typically mp4
      }
      if (url.includes("dailymotion.com")) {
        return "mp4"; // Dailymotion videos are typically mp4
      }

      const extension = pathname.split(".").pop()?.toLowerCase() ?? "";

      // Map common extensions
      const extensionMap: Record<string, string> = {
        mp4: "mp4",
        avi: "avi",
        mov: "mov",
        wmv: "wmv",
        flv: "flv",
        webm: "webm",
        mkv: "mkv",
        pdf: "pdf",
        doc: "doc",
        docx: "docx",
        ppt: "ppt",
        pptx: "ppt",
        jpg: "jpg",
        jpeg: "jpg",
        png: "png",
        gif: "gif",
        svg: "svg",
      };

      return extensionMap[extension] ?? extension;
    } catch (error) {
      console.error("Error extracting extension:", error);
      return "";
    }
  };

  // Function to handle URL change and auto-set extension
  const handleUrlChangeWithExtensionDetection = (url: string): void => {
    console.log("🔗 URL entered:", url);

    // Extract and set extension
    const extension = extractExtensionFromUrl(url);
    console.log("📁 Detected extension:", extension);

    if (extension !== "" && extensions.some((ext) => ext.value === extension)) {
      setSelectedExtensions(extension);
      form.setValue("extension", extension);
      console.log("✅ Extension set successfully:", extension);
    } else {
      console.log("❌ Extension not found or not supported:", extension);
    }

    console.log("🏁 URL processing completed");
  };
  const fetchVideoDuration = async (videoId: string): Promise<void> => {
    const apiKey = YOUTUBE_KEY;
    const url = `https://www.googleapis.com/youtube/v3/videos?id=${videoId}&part=contentDetails&key=${apiKey}`;
    const res = await fetch(url);
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    const data: YouTubeVideoDetails = await res.json();
    const duration = data?.items?.[0]?.contentDetails?.duration;
    if (duration.length === 0 || duration.length === 0) return;
    const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
    if (match === null) return;
    const HH = parseInt(match[1] ?? "0");
    const MM = parseInt(match[2] ?? "0");
    const SS = parseInt(match[3] ?? "0");
    form.setValue("videolength.HH", String(HH));
    form.setValue("videolength.MM", String(MM));
    form.setValue("videolength.SS", String(SS));
  };

  return (
    <div className="border rounded-md p-4 mt-4">
      <div className="w-full mb-4 mt-4 ">
        {/* {Object.keys(resourceData).length === 0 ? (
          <h4 className="text-2xl tracking-tight">
            Add - {resourceType === "File" ? "File" : "Video"}
          </h4>
        ) : (
          <h4 className="text-2xl tracking-tight">
            Edit - {resourceData.file_type === "File" ? "File" : "Video"}
          </h4>
        )} */}
        {Object.keys(resourceData).length === 0 ? (
          <h4 className="text-2xl tracking-tight">
            {t("resourceLibrary.addResourceType", {
              type: t(
                resourceType === "File"
                  ? "resourceLibrary.file"
                  : "resourceLibrary.video",
              ),
            })}
          </h4>
        ) : (
          <h4 className="text-2xl tracking-tight">
            {t("resourceLibrary.editResourceType", {
              type: t(
                resourceData.file_type === "File"
                  ? "resourceLibrary.file"
                  : "resourceLibrary.video",
              ),
            })}
          </h4>
        )}
      </div>
      <Form {...form}>
        <form
          onSubmit={(event) => void form.handleSubmit(onSubmit)(event)}
          className="space-y-8"
        >
          <>
            <div className="flex flex-wrap sm:flex-nowrap w-full ">
              <div className="w-full sm:w-1/2 pr-4 ">
                <Label
                  htmlFor="firstname"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 pb-4"
                >
                  {t("resourceLibrary.selectFolder")}
                  <span className="text-red-700 ml-2">*</span>
                </Label>

                <div className="pt-2 flex items-center gap-2">
                  <Combobox
                    data={comboData}
                    onSelectChange={onHandleFolderChange}
                    defaultLabel={defaultLabel}
                  />
                  <button
                    type="button"
                    className="p-2 rounded-full hover:bg-gray-200"
                  >
                    <span title={t("resourceLibrary.addFolder")}>
                      {" "}
                      <FolderPlus
                        className="w-5 h-5 text-blue-600"
                        onClick={handleImportFolder}
                      />
                    </span>
                  </button>
                </div>
              </div>
              <div className="w-full sm:w-1/2 pr-4 ">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t("resourceLibrary.name")}{" "}
                        <span className="text-red-700">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          autoComplete="off"
                          placeholder={t("resourceLibrary.name")}
                          {...field}
                          onChange={handleNameInputChange}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
            <div className="flex flex-wrap sm:flex-nowrap w-full ">
              <div className="w-full sm:w-1/2 pr-4 ">
                <FormField
                  // control={form.control}
                  name="extension"
                  render={() => (
                    <FormItem>
                      <FormLabel>
                        {t("resourceLibrary.extension")}{" "}
                        <span className="text-red-700">*</span>
                      </FormLabel>
                      <FormControl>
                        <Combobox
                          data={extensions}
                          onSelectChange={handleExtension}
                          defaultLabel={selectedExtensions}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="w-full sm:w-1/2 pr-4 ">
                <FormField
                  control={form.control}
                  name="url"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t("resourceLibrary.url")}{" "}
                        <span className="text-red-700">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          autoComplete="off"
                          placeholder={t("resourceLibrary.pasteUrl")}
                          {...field}
                          readOnly={isUrlFromUpload}
                          value={field.value ?? ""}
                          onChange={(e) => {
                            void (async () => {
                              const trimmedValue = e.target.value
                                .trim()
                                .replace(/\s/g, "");
                              field.onChange(trimmedValue);

                              if (trimmedValue.length === 0) {
                                setDefaultThumbnail("");
                                form.setValue("thumbnail_url", "", {
                                  shouldValidate: true,
                                });
                                return;
                              }

                              handleUrlChangeWithExtensionDetection(
                                trimmedValue,
                              );

                              const getYouTubeVideoId = (
                                url: string,
                              ): string | null => {
                                const regex =
                                  /(?:youtube\.com.*(?:\/|v=)|youtu\.be\/)([a-zA-Z0-9_-]{11})/;
                                const match = url.match(regex);
                                return match ? match[1] : null;
                              };

                              const getYouTubeThumbnailUrl = (
                                videoUrl: string,
                              ): string | null => {
                                const videoId = getYouTubeVideoId(videoUrl);
                                return videoId != null
                                  ? `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`
                                  : null;
                              };
                              const videoId = getYouTubeVideoId(trimmedValue);
                              await fetchVideoDuration(videoId as string);
                              const isYouTube =
                                trimmedValue.includes("youtube.com") ||
                                trimmedValue.includes("youtu.be");
                              const thumbUrl = isYouTube
                                ? getYouTubeThumbnailUrl(trimmedValue)
                                : null;

                              try {
                                const response = await fetch(
                                  thumbUrl ?? trimmedValue,
                                );
                                if (!response.ok) return;

                                const blob = await response.blob();
                                const fileExt =
                                  thumbUrl != null
                                    ? "jpg"
                                    : (trimmedValue.split(".").pop() ?? "jpg");
                                const fileName = `${Math.random()}.${fileExt}`;
                                const orgId =
                                  localStorage.getItem("orgId") ?? "";
                                const userDetails =
                                  localStorage.getItem("userDetails");

                                if (userDetails == null) return;
                                const parsedUserDetails = JSON.parse(
                                  userDetails,
                                ) as LoginUserData;
                                const userId = parsedUserDetails.id;
                                const result = await supabase.storage
                                  .from(orgId)
                                  .upload(
                                    `avatars/${userId}/${fileName}`,
                                    blob,
                                    {
                                      cacheControl: "3600",
                                      upsert: true,
                                    },
                                  );

                                if (!result.error) {
                                  const { data } = supabase.storage
                                    .from(orgId)
                                    .getPublicUrl(
                                      `avatars/${userId}/${fileName}`,
                                    );
                                  setDefaultThumbnail(data.publicUrl);
                                  form.setValue(
                                    "thumbnail_url",
                                    data.publicUrl,
                                    { shouldValidate: true },
                                  );
                                }
                              } catch (err) {
                                console.error("Upload error:", err);
                              }
                            })();
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {["pdf", "doc", "docx", "ppt"].includes(selectedExtensions) && (
              <div className="w-full sm:w-1/2 pr-4 ">
                <FormField
                  control={form.control}
                  name="page_count"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t(
                          selectedExtensions === "ppt"
                            ? "resourceLibrary.totalSlideCount"
                            : "resourceLibrary.pageCount",
                        )}
                        <span className="text-red-700">*</span>{" "}
                      </FormLabel>
                      <FormControl>
                        <Input
                          autoComplete="off"
                          type="number"
                          placeholder={t(
                            selectedExtensions === "ppt"
                              ? "resourceLibrary.totalSlideCount"
                              : "resourceLibrary.pageCount",
                          )}
                          min={0}
                          {...field}
                          onChange={(e) =>
                            form.setValue(
                              "page_count",
                              Number(e.target.value),
                              {},
                            )
                          }
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}

            {resourceType === "Video" && (
              <div className="w-full sm:w-1/2">
                <FormField
                  control={form.control}
                  name="videolength"
                  render={() => (
                    <FormControl>
                      <FormItem>
                        <FormLabel>
                          {t("resourceLibrary.videoLength")}{" "}
                          <span className="text-red-700">*</span>
                        </FormLabel>
                        <FormControl>
                          <div className="flex space-x-2">
                            <FormField
                              control={form.control}
                              name="videolength.HH"
                              render={() => (
                                <FormItem>
                                  <FormControl>
                                    <Input
                                      autoComplete="off"
                                      type="number"
                                      placeholder={t("resourceLibrary.hours")}
                                      min={0}
                                      {...form.register("videolength.HH")}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <span>:</span>
                            <FormField
                              control={form.control}
                              name="videolength.MM"
                              render={() => (
                                <FormItem>
                                  <FormControl>
                                    <Input
                                      autoComplete="off"
                                      type="number"
                                      placeholder={t("resourceLibrary.minutes")}
                                      min={0}
                                      {...form.register("videolength.MM")}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <span>:</span>
                            <FormField
                              control={form.control}
                              name="videolength.SS"
                              render={() => (
                                <FormItem>
                                  <FormControl>
                                    <Input
                                      autoComplete="off"
                                      type="number"
                                      placeholder={t("resourceLibrary.seconds")}
                                      min={0}
                                      {...form.register("videolength.SS")}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                        </FormControl>
                      </FormItem>
                    </FormControl>
                  )}
                />
              </div>
            )}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {t("resourceLibrary.description")}{" "}
                    <span className="text-red-700">*</span>
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder={t("resourceLibrary.description")}
                      autoComplete="off"
                      maxLength={100}
                      {...field}
                      onChange={handleDescriptionChange}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex items-center gap-4">
              <FormField
                control={form.control}
                name="thumbnail_url"
                render={() => (
                  <FormItem className="flex items-center gap-4">
                    <FormLabel className="whitespace-nowrap">
                      {t("resourceLibrary.thumbnail")}
                      {resourceType === "Video" && (
                        <span className="text-red-700">*</span>
                      )}
                    </FormLabel>
                    <FormControl>
                      <Input
                        className="file:mr-2 file:py-1 file:px-2 file:border file:rounded file:bg-gray-100 file:text-gray-700"
                        type="file"
                        accept="image/png, image/jpeg, image/jpg"
                        onChange={(e) => {
                          void handleFileChange(e).then(() => {
                            console.log("val", e.target.value);
                            // setDefaultThumbnail(e.target.value);
                          });
                        }}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              {/* Thumbnail Preview */}
              {defaultThumbnail !== "" && defaultThumbnail?.length > 0 && (
                <div className="w-16 h-16 rounded-full overflow-hidden border border-gray-300 flex items-center justify-center">
                  <img
                    src={defaultThumbnail}
                    alt="Thumbnail"
                    onError={(e) => {
                      e.currentTarget.src = "/fallback-thumbnail.png"; // or any placeholder
                    }}
                    className="w-full h-full object-cover"
                  />
                </div>
              )}
            </div>

            {/* <div className="w-full md:w-1/3">
              <FormItem>
                <FormControl>
                  <>
                    <Checkbox id="premium" />
                    <FormLabel className="px-4">Premium content</FormLabel>
                  </>
                </FormControl>
                <FormMessage />
              </FormItem>
            </div> */}
          </>

          <div className="w-full flex justify-end mt-4 gap-3">
            <Link href={pageUrl.ResourceLibraryLink}>
              <Button
                className="w-full sm:w-auto bg-[#33363F]"
                onClick={handleCancel}
              >
                {t("buttons.close")}
              </Button>
            </Link>
            <Button disabled={isButtonDisable} className="bg-[#9FC089]">
              {t("buttons.submit")}
            </Button>
          </div>
        </form>
      </Form>
      {openFolder && (
        <Modal
          title={t("resourceLibrary.addFolder")}
          header=""
          openDialog={openFolder}
          closeDialog={handleImportFolder}
          type="max-w-3xl"
        >
          <AddFolders onSave={handleSaveFolder} onCancel={handleImportFolder} />
        </Modal>
      )}
      {openPdfDialog && (
        <Modal
          title={t("resourceLibrary.pdfUpload")}
          header=""
          openDialog={openPdfDialog}
          closeDialog={handleClosePdfDialog}
          type="max-w-3xl"
        >
          <UploadPdfUrl
            onCancel={handleClosePdfDialog}
            onUploadComplete={handleUploadComplete}
          />
        </Modal>
      )}
    </div>
  );
};
