import { supabase } from "../lib/client";
import { views, rpc } from "../lib/apiConfig";
import type {
  AssignOrganizationList,
  AssignUserList,
  LoginUserData,
  RoleType,
} from "@/types";
interface UseUserseReturn {
  getRoles: () => Promise<RoleType[]>;
  getAssignUsers: () => Promise<AssignUserList[]>;
  getOrganizationList: () => Promise<AssignOrganizationList[]>;
}

const useAssignOrganization = (): UseUserseReturn => {
  async function getRoles(): Promise<RoleType[]> {
    const orgId = localStorage.getItem("orgId");
    try {
      const requestBody = {
        org_id: orgId,
      };
      const { data, error } = await supabase.rpc<string, null>(
        rpc.getRolesOfOrg,
        requestBody,
      );
      if (error) {
        throw new Error(error.details);
      }
      return data as RoleType[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function getAssignUsers(): Promise<AssignUserList[]> {
    try {
      const usersList = views?.assignUsersList ?? "";

      const exeQuery = supabase
        .from(usersList)
        .select()
        .order("first_name", { ascending: true });
      const { data, error } = await exeQuery;
      if (error) {
        throw new Error(error.details);
      }
      return data as AssignUserList[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function getOrganizationList(): Promise<AssignOrganizationList[]> {
    try {
      const orgList = views?.userOrganizationList ?? "";
      const userDetails = localStorage.getItem("userDetails");
      let user_id = "";

      if (userDetails != null && userDetails != undefined) {
        const users = JSON.parse(userDetails) as LoginUserData;
        user_id = users?.id;
        console.log(user_id);
      }
      const exeQuery = supabase
        .from(orgList)
        .select()
        .eq("user_id", user_id)
        .order("org_name", { ascending: true });
      const { data, error } = await exeQuery;
      if (error) {
        throw new Error(error.details);
      }
      return data as AssignOrganizationList[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  return {
    getRoles,
    getAssignUsers,
    getOrganizationList,
  };
};

export default useAssignOrganization;
