"use client";
import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import MainLayout from "../layout/mainlayout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useForm } from "react-hook-form";
import { Combobox } from "@/components/ui/combobox";
import type {
  ComboData,
  ErrorCatch,
  LogUserActivityRequest,
  ToastType,
  TopicDataType,
  addCourseFormType,
} from "@/types";
import { DateTimePicker } from "@/components/ui/date-time-picker/date-time-picker";

import { parseZonedDateTime } from "@internationalized/date";
import type { DateValue, ZonedDateTime } from "@internationalized/date";
import { useEffect, useState } from "react";
import moment from "moment-timezone";
import { AddCourseSchema } from "@/schema/schema";
import type { AddCourseSchemaType, richTextType } from "@/types";
import TreeSelectComponent from "@/components/ui/tree-select/tree-select";
import useCourse from "@/hooks/useCourse";
import useTopics from "@/hooks/useTopics";
import { Editor } from "primereact/editor";
import { useRouter } from "next/navigation";
import { pageUrl } from "@/lib/constants";
import { useToast } from "@/components/ui/use-toast";
import type { TreeDataItem } from "@/components/ui/tree";
import { courseTypes } from "@/lib/constants";
// import { PlusIcon } from "@radix-ui/react-icons";
// import { Modal } from "@/components/ui/modal";
// import AddCategories from "./addCategories";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";

const comboData: ComboData[] = [
  {
    value: "Topics_Format",
    label: "Topic Format",
  },
  {
    value: "Weekly_Format",
    label: "Weekly Format",
  },
];

export default function AddCourse(): JSX.Element {
  const { getCategoryHierarchy } = useTopics();
  const { updateUserActivity } = useLogUserActivity();
  const { t } = useTranslation();
  const { toast } = useToast() as ToastType;
  const router = useRouter();
  const [courseType, setCourseType] = useState(courseTypes);
  const [selectedCourseType, setSelectedCourseType] = useState<string>("");

  const comboSelectedValue = (res: string): void => {
    form.setValue("courseFormat", res);
  };

  const form = useForm<AddCourseSchemaType>({
    resolver: zodResolver(AddCourseSchema),
  });

  const handleCourseTypeChange = (selectedValue: string): void => {
    setIsTypeDisable(false);

    form.setValue("type", selectedValue);
    setSelectedCourseType(selectedValue);
  };

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  async function onSubmit(data: addCourseFormType): Promise<void> {
    const org_id = localStorage.getItem("orgId") as string;
    const dateStart = data.courseStartDate;

    const startDate = new Date(
      dateStart.year,
      dateStart.month - 1,
      dateStart.day,
      dateStart.hour,
      dateStart.minute,
    );

    const dateEnd = data.courseEndDate;

    const endDate = new Date(
      dateEnd.year,
      dateEnd.month - 1,
      dateEnd.day,
      dateEnd.hour,
      dateEnd.minute,
    );

    const momentStartDate = moment(startDate);
    const momentEndDate = moment(endDate);

    const formattedStartDate = momentStartDate.format("YYYY-MM-DD HH:mm");

    const formattedEndDate = momentEndDate.format("YYYY-MM-DD HH:mm");
    const fromDateObj = new Date(formattedStartDate);
    const toDateObj = new Date(formattedEndDate);
    if (fromDateObj >= toDateObj) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.course_date"),
      });
    } else {
      const formData = data;

      if (richTextValues?.htmlValue !== undefined) {
        formData.courseDescription = richTextValues.htmlValue;
      }

      const transformedData = {
        category_id: formData.courseCategory,
        end_date: formattedEndDate,
        format: formData.courseFormat,
        full_name: formData.courseFullName,
        org_id: org_id,
        short_name: formData.courseShortName,
        start_date: formattedStartDate,
        status: "Draft",
        summary: formData.courseDescription,
        type: selectedCourseType,
        num_sections: formData.numberOfSections,
        visibility: formData.courseVisibility === "yes" ? true : false,
      };

      try {
        const result = await addCourse(transformedData);

        if (result.status == "success") {
          toast({
            variant: SUCCESS_MESSAGES.toast_variant_default,
            title: t("successMessages.course_add_title"),
            description: t("successMessages.course_add_msg"),
          });
          const params = {
            activity_type: "Course",
            screen_name: "Course",
            action_details: "Course added successfully",
            target_id: result.course_id,
            log_result: "SUCCESS",
          };
          void updateLogUserActivity(params).catch((error) => {
            console.error(error);
          });
          router.push(pageUrl.courseList);
        } else {
          toast({
            variant: ERROR_MESSAGES.toast_variant_destructive,
            title: t("errorMessages.toast_error_title"),
            description: t("errorMessages.something_went_wrong"),
          });
          const params = {
            activity_type: "Course",
            screen_name: "Course",
            action_details: "Failed to add course",
            target_id: result.course_id,
            log_result: "ERROR",
          };
          void updateLogUserActivity(params).catch((error) => {
            console.error(error);
          });
        }
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
        console.error("An unexpected error occurred:", error);
      }
    }
  }

  const [defaultDate, setDefaultDate] = useState<ZonedDateTime | DateValue>();

  const [startDateTime, setStartDateTime] = useState<
    ZonedDateTime | DateValue
  >();
  const [endDateTime, setEndDateTime] = useState<ZonedDateTime | DateValue>();
  const [timezone, setTimezone] = useState("");

  const [richTextValues, setRichTextValues] = useState<
    richTextType | undefined
  >(undefined);

  const [nodeData, setNodeData] = useState<TreeDataItem[]>([]);
  // const [isCategoryOpen, setIsCategoryOpen] = useState<boolean>(false);
  const [isTypeDisable, setIsTypeDisable] = useState<boolean>(false);

  const { addCourse, convertDataToTreeNode } = useCourse();

  useEffect(() => {
    const currentTimezone = moment.tz.guess();

    const originalDatetime = moment
      .tz(currentTimezone)
      .format("YYYY-MM-DDTHH:mm");

    const parsedDatetime = moment.tz(originalDatetime, currentTimezone);

    // Add 1 minute
    const newDatetime = parsedDatetime.add(60, "minute");

    // Format the new datetime as desired
    const formattedDatetime =
      newDatetime.format("YYYY-MM-DDTHH:mm") + `[${currentTimezone}]`;

    const dateTime = parseZonedDateTime(formattedDatetime);
    setTimezone(currentTimezone);
    setDefaultDate(dateTime);
    form.setValue("courseStartDate", dateTime);
    setStartDateTime(dateTime);
    const endDatetime = parsedDatetime.clone().add(1, "day");
    const formattedEndDatetime =
      endDatetime.format("YYYY-MM-DDTHH:mm") + `[${currentTimezone}]`;

    const endDateTime = parseZonedDateTime(formattedEndDatetime);
    form.setValue("courseEndDate", endDateTime);
    setEndDateTime(endDateTime);
  }, []);

  const handleChanage = (dateObject: DateValue): void => {
    const modifiedDateObject = { ...dateObject };

    modifiedDateObject.month = modifiedDateObject.month - 1;
    const setEndDateTime = parseZonedDateTime(
      moment.tz(modifiedDateObject, timezone).format("YYYY-MM-DDTHH:mm") +
        `[${timezone}]`,
    );
    setStartDateTime(setEndDateTime);
  };

  const setRichTextValue = (richTextValue: richTextType | undefined): void => {
    if (richTextValue && richTextValue.htmlValue == null) {
      richTextValue = undefined;
    }
    form.setValue("courseDescription", richTextValue as { htmlValue: string });

    setRichTextValues(richTextValue);
  };

  useEffect(() => {
    topicList();
  }, []);
  const addCourseCancel = (): void => {
    router.push(pageUrl.courseList);
  };
  const topicList = (): void => {
    const fetchData = async (): Promise<void> => {
      const org_id = localStorage.getItem("orgId") as string;
      const params = {
        org_id: org_id,
        filter_data: 1,
      };
      try {
        const topics = await getCategoryHierarchy(params);
        if (topics.length > 0) {
          const treeData: TreeDataItem[] = convertDataToTreeNode(
            topics as TopicDataType[],
          );
          // const tempTree: TreeNode[] = [];
          // treeData.map((indv, indx) => {
          //   tempTree.push({
          //     data: indv.value,
          //     key: String(indx),
          //     label: indv.label,
          //   });
          // });
          setNodeData(treeData);
          // setTopicData(topics);
        }
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
        console.error("Error fetching data:");
      }
    };
    fetchData().catch((error) => console.error(error));
  };

  // const handleDialogOpen = (): void => {
  //   setIsCategoryOpen(true);
  // };

  // const handleDialogClose = (): void => {
  //   setIsCategoryOpen(false);
  // };
  // const handleDialogSave = (): void => {
  //   topicList();
  // };

  const handleInputChange =
    (fieldName: "courseFullName" | "courseShortName") =>
    (e: React.ChangeEvent<HTMLInputElement>): void => {
      const value = e.target.value;
      const sanitizedValue = value
        .trimStart()
        .replace(/^\p{P}+/u, "")
        .replace(/[^\p{L}\p{M}\p{N}\s&-]/gu, "")
        .replace(/\s{2,}/g, " ")
        .replace(/^[^\p{L}\p{M}\p{N}]|[^\p{L}\p{M}\p{N}\s&-]/gu, "")
        .replace(/\s+$/, (match) =>
          match.length > 1 ? match.slice(0, -1) : match,
        );
      form.setValue(fieldName, sanitizedValue);
    };

  return (
    <MainLayout>
      <h1 className="text-2xl font-semibold tracking-tight">{t("courses.addCourse")}</h1>

      <div className="border rounded-md p-4 mt-4 bg-[#fff]">
        <Form {...form}>
          <form
            onSubmit={(event) => void form.handleSubmit(onSubmit)(event)}
            className="space-y-8"
          >
            <div className="grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
              <div className="sm:col-span-3">
                <div className="mt-2">
                  <FormField
                    control={form.control}
                    name="courseFullName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {String(t("courses.courseFullName"))}
                          <span className="text-red-700">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            autoComplete="off"
                            onChange={handleInputChange("courseFullName")}
                            maxLength={50}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <div className="sm:col-span-3">
                <div className="mt-2">
                  <FormField
                    control={form.control}
                    name="courseShortName"
                    render={({ field }) => (
                      <FormItem>
                        {" "}
                        <FormLabel>
                          {" "}
                          {String(t("courses.courseShortName"))}
                          <span className="text-red-700">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            autoComplete="off"
                            maxLength={50}
                            onChange={handleInputChange("courseShortName")}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
              <div className="sm:col-span-3 flex items-center w-full">
                <div className="w-full">
                  <FormField
                    control={form.control}
                    name="courseCategory"
                    render={() => (
                      <FormItem>
                        <FormLabel>
                          {String(t("courses.courseCategory"))}
                          <span className="text-red-700">*</span>
                        </FormLabel>
                        <FormControl>
                          <TreeSelectComponent
                            nodeData={nodeData}
                            selectLabel={"Select Category"}
                            onNodeSelect={(selectedValue: string | null) => {
                              if (selectedValue !== null) {
                                form.setValue("courseCategory", selectedValue);
                                const item = nodeData.find(
                                  (topic) => topic.value === selectedValue,
                                );
                                if (item?.is_premium === true) {
                                  handleCourseTypeChange("Paid");
                                  setIsTypeDisable(true);
                                } else {
                                  setSelectedCourseType("");
                                  setCourseType(courseTypes);
                                  setIsTypeDisable(false);
                                }
                              }
                            }}
                          ></TreeSelectComponent>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                {/* <div className="ms-2 mt-6">
                  <Button
                    className="bg-[#fb8500] hover:bg-[#fb5c00]"
                    onClick={handleDialogOpen}
                    type="button"
                  >
                    <PlusIcon className="h-4 w-5" />
                  </Button>
                </div> */}
              </div>

              <div className="sm:col-span-3">
                <div>
                  <FormField
                    control={form.control}
                    name="courseVisibility"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {" "}
                          {String(t("courses.courseVisibility"))}
                          <span className="text-red-700">*</span>
                        </FormLabel>

                        <Select
                          name="courseVisibility"
                          onValueChange={field.onChange}
                          defaultValue={"yes"}
                        >
                          {" "}
                          <FormControl>
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="yes">Yes</SelectItem>
                            <SelectItem value="no">No</SelectItem>
                          </SelectContent>
                        </Select>

                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
              <div className="sm:col-span-3">
                <div className="mt-2">
                  <FormField
                    control={form.control}
                    name="courseStartDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {" "}
                          {String(t("courses.courseStartDate"))}
                          <span className="text-red-700">*</span>
                        </FormLabel>
                        <FormControl>
                          <DateTimePicker
                            granularity={"minute"}
                            minValue={defaultDate}
                            hideTimeZone={true}
                            value={field.value as DateValue}
                            defaultValue={startDateTime}
                            onChange={(newDate) => {
                              handleChanage(newDate as DateValue);

                              field.onChange(newDate);
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <div className="sm:col-span-3">
                <div className="mt-2">
                  <FormField
                    control={form.control}
                    name="courseEndDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {String(t("courses.courseEndDate"))}
                          <span className="text-red-700">*</span>
                        </FormLabel>
                        <FormControl>
                          <DateTimePicker
                            granularity="minute"
                            minValue={defaultDate}
                            value={field.value as DateValue}
                            hideTimeZone={true}
                            defaultValue={endDateTime}
                            onChange={(newDate) => {
                              //handleChanage(newDate);
                              field.onChange(newDate);
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
              <div className="sm:col-span-3">
                <div>
                  <FormField
                    control={form.control}
                    name="courseFormat"
                    render={() => (
                      <FormItem>
                        <FormLabel>
                          {String(t("courses.format"))}
                          <span className="text-red-700">*</span>
                        </FormLabel>
                        <FormControl>
                          <Combobox
                            data={comboData}
                            onSelectChange={comboSelectedValue}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <div className="sm:col-span-3">
                <div className="">
                  <FormField
                    control={form.control}
                    name="numberOfSections"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {String(t("courses.noOfSections"))}
                          <span className="text-red-700">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            autoComplete="off"
                            type="number"
                            {...field}
                            defaultValue="1"
                            min={1}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
              <div className="col-span-full">
                <div className="mt-2">
                  <FormField
                    control={form.control}
                    name="courseDescription"
                    render={() => (
                      <FormItem>
                        <FormLabel>
                          {String(t("courses.description"))}
                          <span className="text-red-700">*</span>
                        </FormLabel>
                        <FormControl>
                          <Editor
                            value=""
                            onTextChange={(event) => {
                              const htmlValue = event.htmlValue;
                              const richTextValue = {
                                htmlValue: htmlValue,
                              };
                              setRichTextValue(richTextValue as richTextType);
                            }}
                            style={{ height: "320px" }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </div>
            <div className="mt-4 w-1/2">
              <FormField
                name="type"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {String(t("courses.selectCourseType"))}{" "}
                      <span className="text-red-700">*</span>
                    </FormLabel>
                    <FormControl>
                      <Select
                        onValueChange={handleCourseTypeChange}
                        defaultValue={field.value}
                        disabled={isTypeDisable}
                        value={selectedCourseType}
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Select" />
                        </SelectTrigger>
                        <SelectContent>
                          {courseType.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="mt-6 flex items-center justify-end gap-x-3">
              <Button
                type="button"
                className="bg-[#33363F]"
                onClick={addCourseCancel}
              >
                {String(t("buttons.cancel"))}
              </Button>
              <Button type="submit" className="bg-[#9FC089]">
                {String(t("buttons.submit"))}
              </Button>
            </div>
          </form>
        </Form>
      </div>
      {/* {isCategoryOpen && (
        <Modal
          title="Add Category"
          header=""
          openDialog={isCategoryOpen}
          closeDialog={handleDialogClose}
        >
          <AddCategories
            onCancel={handleDialogClose}
            onSave={handleDialogSave}
          />
        </Modal>
      )} */}
    </MainLayout>
  );
}
