"use client";
import React, { useState, useEffect, type KeyboardEvent } from "react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useForm } from "react-hook-form";
import type {
  EditNewExamFormType,
  richTextType,
  ErrorCatch,
  ToastType,
  ExamDetails,
  LogUserActivityRequest,
} from "@/types";
import { EditExamSchema } from "../../schema/schema";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import useAddExams from "@/hooks/useAddExams";
import moment from "moment";
import { Editor } from "primereact/editor";
import { useToast } from "@/components/ui/use-toast";
import { useRouter } from "next/navigation";
import { marksTypes, pageUrl } from "@/lib/constants";
import { DateTimePicker } from "@/components/ui/date-time-picker/date-time-picker";
import { parseZonedDateTime } from "@internationalized/date";
import type { DateValue } from "@internationalized/date";
import "moment-timezone";
import { useSearchParams } from "next/navigation";
import {
  TIME_ZONE,
  ORG_KEY,
  FORMATTED_DATE_FORMAT,
  DATE_FORMAT,
  DAY,
  DAYS,
  MINUTE,
} from "@/lib/constants";
import { examTypes } from "@/lib/constants";
import useCourse from "@/hooks/useCourse";
import useExamDetails from "@/hooks/useExamDetails";
import { Spinner } from "../ui/progressiveLoader";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";

export default function ExamEditPage(): React.JSX.Element {
  const { t } = useTranslation();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { updateExams } = useAddExams();
  const { getCourseList } = useCourse();
  const { toast } = useToast() as ToastType;
  const form = useForm<EditNewExamFormType>({
    resolver: zodResolver(EditExamSchema),
  });
  const { getQuestions } = useExamDetails();
  const { handleSubmit, register } = form;
  const { updateUserActivity } = useLogUserActivity();

  const [startDate, setStartDate] = React.useState<DateValue | undefined>();
  const [endDate, setEndDate] = React.useState<DateValue | undefined>();
  const [minEndDate, setMinEndDate] = React.useState<DateValue | undefined>();
  const [richTextValues, setRichTextValues] = React.useState<
    richTextType | undefined
  >(undefined);
  const [selectedExamType, setSelectedExamType] = useState<string>("");
  const [selectedMarksType, setSelectedMarksType] = useState<string>("");
  const [isExamDisabled, setIsExamDisabled] = useState(false);
  const examId = searchParams.get("type") ?? null;
  const courseId = searchParams.get("course") ?? null;
  const [examInfo, setExamInfo] = useState<ExamDetails[]>([]);
  const [isLoading, setIsLoading] = React.useState<boolean>(false);
  const [selectedTopic, setSelectedTopic] = React.useState<string>("");
  const [number_of_questions, setNumberOfQuestions] = useState<number>(0);
  const [penaltyMode, setPenaltyMode] = useState<string>("Fixed");
  const [penaltyType, setPenaltyType] = useState<string>("Exam");
  const [wrongAnswers, setWrongAnswers] = useState<number>();
  const [wrongAnswerWeightage, setWrongAnswerWeightage] = useState<number>();
  const [penaltyApplicable, setPenaltyApplicable] = useState(false);
  useEffect(() => {
    const fetchCourseData = async (): Promise<void> => {
      try {
        const fetchData = async (): Promise<void> => {
          const quiz_id = examId;
          try {
            setIsLoading(true);
            const questionsData = await getQuestions(quiz_id);

            questionsData.map((item) => {
              if (item.description != null) {
                item.description = item.description.replace(
                  /<pre\b[^>]*>(.*?)<\/pre>/s,
                  "<p>$1</p>",
                );
              }
            });
            setExamInfo(questionsData);

            console.log(questionsData);
            console.log(examInfo);

            if (questionsData !== null && questionsData !== undefined) {
              form.setValue("duration", questionsData[0].duration);
              form.setValue("description", questionsData[0].description);
              // if ( questionsData[0]?.description !== "") {
              const richTextValue: richTextType = {
                htmlValue: questionsData[0]?.description,
              };
              setRichTextValues(richTextValue);
              // }
              form.setValue("pass_mark", questionsData[0].pass_mark);
              form.setValue(
                "allowed_attempts",
                questionsData[0].allowed_attempts,
              );
              form.setValue("name", questionsData[0].name);
              form.setValue(
                "is_weightage",
                questionsData[0].is_equal_weightage,
              );
              form.setValue("total_mark", questionsData[0].total_mark);
              setIsExamDisabled(questionsData[0].is_equal_weightage);
              if (questionsData[0].is_equal_weightage) {
                form.setValue(
                  "marks",
                  questionsData[0].eq_weightage_marks.toString(),
                );
                const marks = questionsData[0].eq_weightage_marks.toString();
                setSelectedMarksType(marks);
              }

              form.setValue(
                "allowed_attempts",
                questionsData[0].allowed_attempts,
              );
              setNumberOfQuestions(questionsData[0].num_of_questions);
              form.setValue("quiz_type", questionsData[0].quiz_type);
              form.setValue("penalty_mode", questionsData[0].calculation_type);
              setWrongAnswers(questionsData[0].no_wrong_answers);
              setWrongAnswerWeightage(questionsData[0].minus_mark_applicable);
              form.setValue(
                "penalty_available",
                questionsData[0].penalty_available,
              );
              setPenaltyMode(questionsData[0].calculation_type);
              form.setValue("penalty_type", penaltyType);
              setPenaltyType(questionsData[0].penalty_type);
              if (questionsData[0].penalty_available === true) {
                setPenaltyApplicable(true);
              } else {
                setPenaltyApplicable(false);
              }
              setSelectedExamType(questionsData[0].quiz_type);
              form.setValue(
                "num_of_questions",
                questionsData[0].num_of_questions,
              );
              setSelectedTopic(questionsData[0].category_id);

              const currentTimezone = moment.tz.guess();
              const parsedDatetime = moment.tz(
                questionsData[0].start_time,
                currentTimezone,
              );
              const formattedStartDatetime =
                parsedDatetime.format("YYYY-MM-DDTHH:mm") +
                `[${currentTimezone}]`;

              const startDateTime = parseZonedDateTime(formattedStartDatetime);
              form.setValue("start_time", startDateTime);
              const parsedEndDatetime = moment.tz(
                questionsData[0].end_time,
                currentTimezone,
              );
              const formattedEndDatetime =
                parsedEndDatetime.format("YYYY-MM-DDTHH:mm") +
                `[${currentTimezone}]`;

              const endDateTime = parseZonedDateTime(formattedEndDatetime);
              form.setValue("end_time", endDateTime);
              setIsLoading(false);
            }
          } catch (error: unknown) {
            setIsLoading(false);
            console.log(error);
          }
        };
        console.log("testtttt");
        fetchData().catch((error) => console.log(error));
        const courses = await getCourseList(selectedTopic);
        if (courses !== null && courses !== undefined) {
          const selectedCourseData = courses.find(
            (course) => course.course_id === courseId,
          );

          const start_date = selectedCourseData?.start_date;
          const end_date = selectedCourseData?.end_date;

          const currentTimezone = moment.tz.guess();
          const originalDatetime = moment().tz(currentTimezone);

          const newDatetime = originalDatetime.clone().add(10, MINUTE);
          const formattedDatetime =
            newDatetime.format(DATE_FORMAT) + `[${currentTimezone}]`;
          const currentDateTime = parseZonedDateTime(formattedDatetime);

          if (end_date !== null && end_date !== undefined) {
            const formattedEndDatetime = moment
              .tz(end_date, TIME_ZONE)
              .subtract(1, DAYS)
              .format(DATE_FORMAT);
            if (originalDatetime.isAfter(formattedEndDatetime)) {
              toast({
                variant: ERROR_MESSAGES.toast_variant_destructive,
                title: t("errorMessages.toast_error_title"),
                description: t("errorMessages.course_expired"),
              });
              return;
            }
            const parsedEndDatetime = parseZonedDateTime(
              `${formattedEndDatetime}[${currentTimezone}]`,
            );
            form.setValue("end_time", parsedEndDatetime);
            setEndDate(parsedEndDatetime);
          } else {
            setEndDate(undefined);
          }

          if (start_date !== null && start_date !== undefined) {
            const formattedStartDatetime = moment.tz(start_date, TIME_ZONE);

            let displayDate;
            if (formattedStartDatetime.isBefore(originalDatetime, DAY)) {
              displayDate = currentDateTime;

              console.log("start date: " + displayDate);

              form.setValue("start_time", displayDate);
              setStartDate(displayDate);
            } else {
              const formattedNextDayStart = formattedStartDatetime
                .clone()
                // .add(1, DAYS)
                .format(DATE_FORMAT);
              displayDate = parseZonedDateTime(
                `${formattedNextDayStart}[${currentTimezone}]`,
              );
              form.setValue("start_time", displayDate);
              setStartDate(displayDate);
            }
          } else {
            setStartDate(undefined);
          }
        }
      } catch (error) {
        console.error(error);
      }
    };
    fetchCourseData().catch((error) => console.log("Error", error));
  }, []);

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  const setRichTextValue = (richTextValue: richTextType | undefined): void => {
    if (richTextValue && richTextValue.htmlValue == null) {
      richTextValue = undefined;
    }
    form.setValue("description", richTextValue?.htmlValue ?? "");
    setRichTextValues(richTextValue);
  };

  useEffect(() => {
    form.setValue("allowed_attempts", 1);
  }, [form]);

  const handleExamTypeChange = (selectedValue: string): void => {
    form.setValue("quiz_type", selectedValue);
    setSelectedExamType(selectedValue);
  };
  const handleMarksTypeChange = (selectedValue: string): void => {
    form.setValue("marks", selectedValue);
    setSelectedMarksType(selectedValue);
  };
  async function onSubmit(): Promise<void> {
    const formData = form.getValues();
    if (richTextValues?.htmlValue !== undefined) {
      formData.description = richTextValues?.htmlValue;
    }
    const passMark = formData.pass_mark;
    const totalMark = formData.total_mark;
    const noOfQuestions = formData.num_of_questions;
    if (passMark > totalMark) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_validation_error"),
        description: t("errorMessages.pasmark_greaterthan_totalmark"),
      });
      return;
    }
    if (isExamDisabled) {
      if (totalMark / noOfQuestions != parseInt(selectedMarksType)) {
        const mark = totalMark / noOfQuestions;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_validation_error"),
          description: t("errorMessages.mark_for_all_questions", { mark }),
        });
        return;
      }
    }
    const dateStart = formData.start_time;
    const startDate = new Date(
      dateStart.year,
      dateStart.month - 1,
      dateStart.day,
      dateStart.hour,
      dateStart.minute,
    );
    const dateEnd = formData.end_time;
    const endDate = new Date(
      dateEnd.year,
      dateEnd.month - 1,
      dateEnd.day,
      dateEnd.hour,
      dateEnd.minute,
    );
    const momentStartDate = moment(startDate).utc();
    const momentEndDate = moment(endDate).utc();

    const formattedValidFrom = momentStartDate.format(FORMATTED_DATE_FORMAT);

    const formattedValidTo = momentEndDate.format(FORMATTED_DATE_FORMAT);

    const orgId = localStorage.getItem(ORG_KEY);
    const examUpdateData = {
      org_id: orgId ?? "",
      quiz_data: {
        id: examId,
        name: formData.name,
        description: formData.description,
        main_topic: selectedTopic ?? "",
        num_of_questions: formData.num_of_questions,
        total_mark: formData.total_mark,
        pass_mark: formData.pass_mark,
        start_time: formattedValidFrom,
        end_time: formattedValidTo,
        duration: formData.duration,
        allowed_attempts: formData.allowed_attempts,
        quiz_type: selectedExamType,
        penalty_available: formData.penalty_available ?? false,
        is_premium: formData.is_premium ?? false,
        is_equal_weightage: formData.is_weightage ?? false,
        eq_weightage_marks: parseInt(selectedMarksType),
        penalty_type: formData.penalty_type as string,
        calculation_type: formData.penalty_mode as string,
        minus_mark_applicable: wrongAnswerWeightage ?? 0,
        no_wrong_answers: wrongAnswers ?? 0,
      },
    };
    try {
      const result = await updateExams(examUpdateData);
      if (result.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.toast_success_title"),
          description: t("successMessages.exam_updated"),
        });
        const params = {
          activity_type: "Exam",
          screen_name: "Exam",
          action_details: "Exam updated successfully",
          target_id: examId as string,
          log_result: "SUCCESS",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
        router.push(`${pageUrl.exams}`);
      } else if (result.status === "error") {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: result.status,
        });
        const params = {
          activity_type: "Exam",
          screen_name: "Exam",
          action_details: "Failed to update exam",
          target_id: examId as string,
          log_result: "ERROR",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
    }
  }

  const handleValidFromChanage = (dateObject: DateValue): void => {
    setMinEndDate(dateObject);
    const modifiedTime = moment.utc(dateObject);
    const startTime = moment.utc(startDate);

    if (modifiedTime.isBefore(startTime)) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.exam_time_before_course_time"),
      });
      return;
    }
  };

  const handleValidToChanage = (dateObject: DateValue): void => {
    const modifiedTime = moment.utc(dateObject);
    const endTime = moment.utc(endDate);

    if (modifiedTime.isAfter(endTime)) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.exam_time_after_course_time"),
      });
    }
  };

  const handleCancel = (): void => {
    router.push(`${pageUrl.exams}`);
  };

  const handleKeyDown = (event: KeyboardEvent<HTMLInputElement>): void => {
    const forbiddenKeys = ["e", "E", "-", "+"];
    if (forbiddenKeys.includes(event.key)) {
      event.preventDefault();
    }
  };

  const handleKeyDownDuration = (
    event: React.KeyboardEvent<HTMLInputElement>,
  ): void => {
    const forbiddenKeys = ["e", "E", "-", "+"];
    const { value } = event.currentTarget;
    const isDeleteKey = event.key === "Backspace" || event.key === "Delete";
    if (
      forbiddenKeys.includes(event.key) || // Prevent typing forbidden keys
      (value.length >= 3 && !isDeleteKey) // Limit to 3 digits
    ) {
      event.preventDefault();
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    const value = e.target.value;
    const sanitizedValue = value
      .trimStart()
      .replace(/^\p{P}+/u, "")
      .replace(/[^\p{L}\p{M}\p{N}\s-]/gu, "")
      .replace(/\s{2,}/g, " ")
      .replace(/^[^\p{L}\p{M}\p{N}]|[^\p{L}\p{M}\p{N}\s-]/gu, "")
      .replace(/\s+$/, (match) =>
        match.length > 1 ? match.slice(0, -1) : match,
      );
    form.setValue("name", sanitizedValue);
  };

  return (
    <div>
      <div className="w-full mb-4">
        <h1 className="text-2xl font-semibold tracking-tight">Edit Exams</h1>
      </div>
      {isLoading ? (
        <Spinner />
      ) : (
        <div className="w-full border rounded-md ps-4 pb-4 pe-4 mb-2 bg-[#fff]">
          <div>
            <Form {...form}>
              <form
                onSubmit={(event) => void handleSubmit(onSubmit)(event)}
                className="space-y-8 flex flex-wrap"
              >
                <div className="w-full md:w-1/3 mt-8 pe-4">
                  <FormField
                    name="quiz_type"
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t("exams.selectExamType")}
                          <span className="text-red-700">*</span>
                        </FormLabel>
                        <FormControl>
                          <Select
                            onValueChange={handleExamTypeChange}
                            defaultValue={selectedExamType ?? field.value}
                          >
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select" />
                            </SelectTrigger>
                            <SelectContent>
                              {examTypes.map((option) => (
                                <SelectItem
                                  key={option.value}
                                  value={option.value}
                                >
                                  {option.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="w-full md:w-1/3 pe-4">
                  <FormField
                    name="name"
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t("exams.selectExamType")}
                          <span className="text-red-700">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            autoComplete="off"
                            type="text"
                            maxLength={100}
                            {...field}
                            onChange={handleInputChange}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="w-full md:w-1/3">
                  <FormField
                    name="num_of_questions"
                    // control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t("exams.noOfQuestions")}
                          <span className="text-red-700">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            autoComplete="off"
                            type="number"
                            min={0}
                            defaultValue={number_of_questions ?? field.value}
                            onKeyDown={handleKeyDownDuration}
                            {...field}
                            {...register("num_of_questions", {
                              validate: (value) => {
                                return !isNaN(value);
                              },
                              valueAsNumber: true,
                            })}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="w-full md:w-1/3 pe-4">
                  <FormField
                    name="total_mark"
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t("exams.totalMarks")}{" "}
                          <span className="text-red-700">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            autoComplete="off"
                            type="number"
                            min={0}
                            onKeyDown={handleKeyDown}
                            {...field}
                            {...register("total_mark", { valueAsNumber: true })}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="w-full md:w-1/3 pe-4">
                  <FormField
                    name="pass_mark"
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t("exams.passMark")}{" "}
                          <span className="text-red-700">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            autoComplete="off"
                            type="number"
                            min={0}
                            onKeyDown={handleKeyDown}
                            {...field}
                            {...register("pass_mark", { valueAsNumber: true })}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="w-full md:w-1/3">
                  <FormField
                    name="duration"
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t("exams.duration")}
                          <span className="text-red-700">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            autoComplete="off"
                            type="number"
                            min={0}
                            onKeyDown={handleKeyDownDuration}
                            {...field}
                            // {...register("duration", { valueAsNumber: true })}
                            {...register("duration", {
                              validate: (value) => {
                                return (
                                  value <= 180 ||
                                  "Duration must be up to 180 minutes"
                                );
                              },
                              valueAsNumber: true,
                            })}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="w-full md:w-1/3 pe-4">
                  <FormField
                    name="allowed_attempts"
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t("exams.allowedAttempts")}
                          <span className="text-red-700">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            autoComplete="off"
                            type="number"
                            min={0}
                            onKeyDown={handleKeyDown}
                            {...field}
                            {...register("allowed_attempts", {
                              value: 1,
                              valueAsNumber: true,
                            })}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="w-full md:w-1/3 pe-4">
                  <FormField
                    control={form.control}
                    name="start_time"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t("exams.examValidFrom")}
                          <span className="text-red-700">*</span>
                        </FormLabel>
                        <FormControl>
                          <DateTimePicker
                            granularity="minute"
                            minValue={startDate}
                            value={field.value as DateValue}
                            hideTimeZone={true}
                            onChange={(newDate) => {
                              handleValidFromChanage(newDate as DateValue);
                              field.onChange(newDate);
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="w-full md:w-1/3">
                  <FormField
                    control={form.control}
                    name="end_time"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t("exams.examValidTo")}{" "}
                          <span className="text-red-700">*</span>
                        </FormLabel>
                        <FormControl>
                          <DateTimePicker
                            granularity="minute"
                            minValue={minEndDate}
                            maxValue={endDate}
                            hideTimeZone={true}
                            value={field.value as DateValue}
                            // onChange={(newDate) => handleEndDateChange(newDate)}
                            onChange={(newDate) => {
                              handleValidToChanage(newDate as DateValue);
                              field.onChange(newDate);
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="w-full md:w-1/3">
                  <FormField
                    name="is_weightage"
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center">
                          <FormControl>
                            <>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={(value: boolean) => {
                                  field.onChange(value);
                                  setIsExamDisabled(value);
                                  !value ? setSelectedMarksType("") : "";
                                }}
                              />
                              <FormLabel className="px-4">
                                {t("exams.isEqualWeightage")}
                              </FormLabel>
                            </>
                          </FormControl>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="w-full md:w-1/3">
                  <FormField
                    name="is_premium"
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center">
                          <FormControl>
                            <>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={(value) => {
                                  field.onChange(value);
                                }}
                              />
                              <FormLabel className="px-4">
                                {t("exams.premiumContent")}
                              </FormLabel>
                            </>
                          </FormControl>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="w-full md:w-1/3">
                  <FormField
                    name="penalty_available"
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center">
                          <FormControl>
                            <>
                              <Checkbox
                                checked={penaltyApplicable}
                                onCheckedChange={(value) => {
                                  field.onChange(value);
                                  if (value === true) {
                                    setPenaltyApplicable(true);
                                  } else {
                                    setPenaltyApplicable(false);
                                  }
                                }}
                              />
                              <FormLabel className="px-4">
                                {t("exams.penaltyApplicable")}
                              </FormLabel>
                            </>
                          </FormControl>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="w-full">
                  {penaltyApplicable && (
                    <div className="flex flex-wrap ">
                      <div className="md:w-1/4 w-full pe-4 mb-2">
                        <FormItem>
                          <FormLabel>{t("exams.penaltyType")}</FormLabel>
                          <FormControl>
                            <Select
                              onValueChange={(value) => {
                                form.setValue("penalty_type", value);
                                if (value === "Question") {
                                  form.setValue("num_of_questions", 1);
                                  setWrongAnswers(1);
                                } else {
                                  setWrongAnswers(undefined);
                                }
                              }}
                              defaultValue={penaltyType}
                            >
                              <SelectTrigger className="w-full">
                                <SelectValue
                                  placeholder={t("exams.selectPenaltyType")}
                                />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="Question">
                                  {t("exams.questionBased")}
                                </SelectItem>
                                <SelectItem value="Exam">
                                  {t("exams.examBased")}
                                </SelectItem>
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      </div>
                      <div className="md:w-1/4 w-full pe-4 mb-2">
                        <FormItem>
                          <FormLabel>{t("exams.penaltyMode")}</FormLabel>
                          <FormControl>
                            <Select
                              onValueChange={(value) => {
                                form.setValue("penalty_mode", value);
                              }}
                              defaultValue={penaltyMode}
                            >
                              <SelectTrigger className="w-full">
                                <SelectValue
                                  placeholder={t("exams.selectPenaltyMode")}
                                />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="Fixed">
                                  {t("exams.fixed")}
                                </SelectItem>
                                <SelectItem value="Percentage">
                                  {t("exams.percentageWise")}
                                </SelectItem>
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      </div>
                      <div className="md:w-1/4 w-full pe-4 mb-2">
                        <FormItem>
                          <FormLabel>{t("exams.noOfWrongAnswers")}</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min={1}
                              step="any"
                              autoComplete="off"
                              value={wrongAnswers ?? ""}
                              onChange={(e) =>
                                setWrongAnswers(Number(e.target.value))
                              }
                              onKeyDown={handleKeyDown}
                              disabled={
                                form.getValues("penalty_type") === "Question"
                              }
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      </div>
                      <div className="md:w-1/4 w-full pe-4 mb-2 mt-2">
                        <FormItem>
                          {form.watch("penalty_mode") === "Percentage"
                            ? t("exams.penalty%")
                            : t("exams.penaltyMarksDeducted")}
                          <FormControl>
                            <Input
                              type="number"
                              min={0}
                              step={
                                form.watch("penalty_mode") === "Percentage"
                                  ? "1"
                                  : "any"
                              }
                              pattern={
                                form.watch("penalty_mode") === "Percentage"
                                  ? "\\d*"
                                  : undefined
                              }
                              autoComplete="off"
                              value={wrongAnswerWeightage ?? ""}
                              onChange={(e) =>
                                setWrongAnswerWeightage(Number(e.target.value))
                              }
                              onKeyDown={handleKeyDown}
                              disabled={
                                form.getValues("penalty_type") === "Question"
                              }
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      </div>
                    </div>
                  )}
                </div>
                {isExamDisabled && (
                  <div className="w-full md:w-1/3 pe-4">
                    <FormField
                      name="marks"
                      control={form.control}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            {t("exams.marks")}{" "}
                            <span className="text-red-700">*</span>
                          </FormLabel>
                          <FormControl>
                            <Select
                              onValueChange={handleMarksTypeChange}
                              defaultValue={selectedMarksType ?? field.value}
                            >
                              <SelectTrigger className="w-full">
                                <SelectValue placeholder="Select Marks" />
                              </SelectTrigger>
                              <SelectContent>
                                {marksTypes.map((option) => (
                                  <SelectItem
                                    key={option.value}
                                    value={option.value}
                                  >
                                    {option.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                )}
                <div className="w-full">
                  <FormField
                    name="description"
                    control={form.control}
                    render={() => (
                      <FormItem>
                        <FormLabel>
                          {t("exams.examRules")}
                          <span className="text-red-700">*</span>
                        </FormLabel>
                        <FormControl>
                          <Editor
                            value={richTextValues?.htmlValue}
                            defaultValue={richTextValues?.htmlValue}
                            onTextChange={(event) => {
                              const htmlValue = event.htmlValue; // Convert the HTML value to your richTextType
                              const richTextValue = {
                                htmlValue: htmlValue,
                              };
                              setRichTextValue(richTextValue as richTextType);
                            }}
                            style={{ height: "320px" }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="w-full flex justify-end mt-4">
                  <div className="px-4">
                    <Button
                      onClick={handleCancel}
                      className="w-full sm:w-auto bg-[#33363F]"
                    >
                      {t("buttons.cancel")}
                    </Button>
                  </div>
                  <div>
                    <Button type="submit" className="bg-[#9FC089]">
                      {t("buttons.submit")}
                    </Button>
                  </div>
                </div>
              </form>
            </Form>
          </div>
        </div>
      )}
    </div>
  );
}
