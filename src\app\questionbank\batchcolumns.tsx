"use client";

import type { ColumnDef, Row } from "@tanstack/react-table";
// import { ArrowUpDown } from "lucide-react";
// import { Button } from "@/components/ui/button";
import React from "react";
import type { BatchQuestionData } from "@/types";

// interface ColumnDefinition {
//   column: Column<BatchQuestionData, unknown>;
// }
interface RowDefinition {
  row: Row<BatchQuestionData>;
}

export const  getBatchColumns = (
  t: (key: string) => string
): ColumnDef<BatchQuestionData>[] => [
  {
    id: "select",
  },
  {
    accessorKey: "question_text",
    header: t("questionBank.question"),
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div className="text-center">{row.original.question_text}</div>
    ),
  },
];
