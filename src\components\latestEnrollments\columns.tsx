"use client";
import type { ColumnDef } from "@tanstack/react-table";
// import { ArrowUpDown } from "lucide-react";
// import { Button } from "@/components/ui/button";
import type {
  // LatestEnrollmentColumnDefinition,
  LatestEnrollmentRowDefinition,
  LatestEnrollmentResponse,
} from "@/types";
import moment from "moment";
import { DATE_FORMAT_DMY_HM_AM_PM } from "@/lib/constants";

export const getColumns = (
  t: (key: string) => string,
): ColumnDef<LatestEnrollmentResponse>[] => [
  {
    accessorKey: "first_name",
    header: t("dashboard.recentEnrollments.name"),
    cell: ({ row }: LatestEnrollmentRowDefinition): React.JSX.Element => {
      const firstName = row.original.first_name ?? " ";
      const lastName = row.original.last_name ?? " ";
      return <span>{`${firstName} ${lastName}`}</span>;
    },
  },
  {
    accessorKey: "enrolled_date",
    header: t("dashboard.recentEnrollments.enrolledDate"),
    cell: ({ row }: LatestEnrollmentRowDefinition): React.JSX.Element => {
      const formattedDate = moment
        .utc(row.original.enrolled_date)
        .local()
        .format(DATE_FORMAT_DMY_HM_AM_PM);

      return <div>{formattedDate}</div>;
    },
  },
  {
    accessorKey: "course",
    header: t("dashboard.recentEnrollments.course"),
    cell: ({ row }: LatestEnrollmentRowDefinition): React.JSX.Element => (
      <div className="text-align word-break: break-all">
        {row.original.course_short_name}
      </div>
    ),
  },
  {
    accessorKey: "validity",
    header: t("dashboard.recentEnrollments.validUpTo"),
    cell: ({ row }: LatestEnrollmentRowDefinition): React.JSX.Element => {
      const formattedDate = moment
        .utc(row.original.course_end_date)
        .local()
        .format(DATE_FORMAT_DMY_HM_AM_PM);

      return <div>{formattedDate}</div>;
    },
  },
];
