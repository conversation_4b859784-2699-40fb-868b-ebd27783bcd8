"use client";
import React, { useEffect, useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Editor } from "primereact/editor";
import { useForm } from "react-hook-form";
import type {
  EmailRequest,
  ErrorCatch,
  LogUserActivityRequest,
  SentEmailRequest,
  ToastType,
} from "@/types";
import { zodResolver } from "@hookform/resolvers/zod";
import { EmailSchema } from "@/schema/schema";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import useEnrollments from "@/hooks/useEnrollment";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/use-toast";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import useDashboardStatsViews from "@/hooks/useDashboardStats";
import { ORG_NAME } from "@/lib/constants";
import { Loader2 } from "lucide-react";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";

export default function Email({
  onCancel,
  onSave,
  emailId,
  cerificateImage,
  isCertificate,
  recipient,
  course,
  courseId,
}: {
  onCancel: () => void;
  onSave: () => void;
  emailId: string;
  isCertificate: boolean;
  cerificateImage?: File | null;
  recipient: string;
  course: string;
  courseId?: string;
}): React.JSX.Element {
  const { sentEmailNotification } = useEnrollments();
  const { updateUserActivity } = useLogUserActivity();
  const { t } = useTranslation();
  const form = useForm<EmailRequest>({
    resolver: zodResolver(EmailSchema),
  });
  const { toast } = useToast() as ToastType;
  const [isLoading, setIsLoading] = useState(true);
  const [isSent, setIsSent] = useState(true);
  // Set default values
  useEffect(() => {
    const orgName = localStorage.getItem(ORG_NAME) as string;
    let msgBody = "";
    if (isCertificate) {
      msgBody = `
    <div style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
      <p>Dear <strong>${recipient ?? ""}</strong>,</p>
     <p> </p>
      <p>Congratulations on successfully completing <strong>${
        course ?? ""
      }</strong>!</p>

      <p>
        We are delighted to recognize your achievement, and as a token of your
        accomplishment, your certificate is included as an attachment to this email.
      </p>

      <p>Please feel free to reach out if you have any questions or need further assistance.</p>
      <p> </p>
      <p>Best regards,</p>
     
      <p><strong>${orgName ?? ""}</strong></p>
    
    </div>
    `;
    } else {
      msgBody = `
    <div style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
      <p>Dear <strong>${recipient ?? ""}</strong>,</p>
      <p> </p>
       <p>
        We wanted to inform you that your enrolled course,  <strong>${
          course ?? ""
        }</strong>, has not yet started.
      </p>

      Click on the link to start your course <a href="https://smartlearn-web-portal.vercel.app">SmartLearn Training Portal</a>
         <br>
      <p>Please feel free to reach out if you have any questions or need further assistance.</p>
      <p> </p>
      <p style="margin-bottom: 8px;">Best regards,</p>
      <p><strong>${orgName ?? ""}</strong></p>
    
    </div>
    `;
    }

    //   <Image
    //   className="w-10 h-10 rounded-full"
    //   src="/assets/citrus.png"

    //   alt="Profile Image"
    //   width={60}
    //   height={60}
    // />
    const subject = isCertificate
      ? "Certificate of Completion for Your Achievement"
      : "Course Access Update – Not Yet Started";

    // Set values for the form
    form.setValue("message", msgBody);
    form.setValue("subject", subject);
    setIsLoading(false);
  }, [recipient, course, form]);

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  const { sentEmail } = useDashboardStatsViews();
  const onSubmit = async (): Promise<void> => {
    setIsSent(false);
    const formData = form.getValues();
    if (!isCertificate) {
      const params: SentEmailRequest = {
        email: emailId,
        mail_content: formData.message,
        subject: formData.subject,
      };
      try {
        const resp = await sentEmailNotification(params);
        onSave();
        setIsSent(true);
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.toast_success_title"),
          description: t("successMessages.send_email"),
        });
        const reqParams = {
          activity_type: "Enrolment",
          screen_name: "Enrollments",
          action_details: "Mail sent successfully",
          target_id: courseId as string,
          log_result: "SUCCESS",
        };
        void updateLogUserActivity(reqParams).catch((error) => {
          console.error(error);
        });
        console.log("Notification sent:", resp);
      } catch (error) {
        const err = error as ErrorCatch;
        console.log(err);
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: t("errorMessages.send_email"),
        });
      }
    } else {
      try {
        const params: SentEmailRequest = {
          email: emailId,
          subject: formData.subject,
          file: cerificateImage,
          mail_content: formData.message,
        };

        const resp = await sentEmail(params);
        onSave();
        setIsSent(true);
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.toast_success_title"),
          description: t("successMessages.send_email"),
        });
        console.log("Notification sent:", resp);
      } catch (error) {
        const err = error as ErrorCatch;
        console.error("Error sending email:", err);
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: t("errorMessages.send_email"),
        });
      }
    }
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div>
      <div className="border rounded-md p-4 bg-[#fff]">
        <Form {...form}>
          <form
            // eslint-disable-next-line @typescript-eslint/no-misused-promises
            onSubmit={form.handleSubmit(onSubmit)}
            className="space-y-8"
          >
            <div className="mt-4">
              <FormField
                name="subject"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {String(t("enrollment.subject"))}{" "}
                      <span className="text-red-700">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        autoComplete="off"
                        {...field}
                        maxLength={30}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="mt-4">
              <FormField
                name="message"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {String(t("enrollment.messageBody"))}{" "}
                      <span className="text-red-700">*</span>
                    </FormLabel>
                    <FormControl>
                      <Editor
                        value={field.value} // Bind the Editor to the form value
                        onTextChange={(event) => {
                          field.onChange(event.textValue); // Update form state when text changes
                        }}
                        style={{ height: "320px" }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            {/* {isCertificate && (
              <p className="text-blue-500 test-small">
                The certificate is included as an attachment to this email
              </p>
            )} */}

            <div className="w-full flex justify-end mt-4 gap-3">
              <Button type="button" onClick={onCancel}>
                {String(t("buttons.cancel"))}
              </Button>
              <Button
                type="submit"
                className="bg-[#9FC089]"
                disabled={isLoading}
              >
                {String(t("buttons.send"))}
                {!isSent && <Loader2 className="animate-spin w-5 h-5" />}
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
}
