"use client";

import React, { useEffect, useState } from "react";
import "../../styles/auth.css";
import AuthLayout from "../layout/authlayout";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { ResetPasswordSchema } from "@/schema/schema";
import type { ErrorCatch, PasswordSchemaType, ToastType } from "@/types";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { useToast } from "@/components/ui/use-toast";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import useAuthorization from "@/hooks/useAuth";
import { supabase } from "@/lib/client";

export default function ResetPassword(): React.JSX.Element {
  const [userToken, setUserToken] = useState<string>("");
  const { ResetPassword } = useAuthorization();
  const { toast } = useToast() as ToastType;

  const form = useForm<PasswordSchemaType>({
    resolver: zodResolver(ResetPasswordSchema),
  });

  useEffect(() => {
    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === "PASSWORD_RECOVERY") {
          if (session?.access_token != null) {
            setUserToken(session.access_token);
          } else {
            console.log("Token not available in session.");
          }
        }
      },
    );

    return () => {
      authListener.subscription?.unsubscribe();
    };
  }, []);

  async function onSubmit(data: PasswordSchemaType): Promise<void> {
    const newPassword = data.newPassword;
    try {
      await ResetPassword(newPassword, userToken).then((pwdStatus) => {
        if (pwdStatus?.error === "") {
          toast({
            variant: "primary",
            title: "Success",
            description: "Password udpated successfully",
          });
        } else {
          toast({
            variant: "error",
            title: "Error",
            description: "Token expired",
          });
        }
      });
    } catch (error) {
      console.error("An unexpected error occurred:", error);

      const err = error as ErrorCatch;
      toast({
        variant: "error",
        title: "Error",
        description: err?.details,
      });
    }
  }

  return (
    <AuthLayout>
      <div className="lg:p-4 sm:p-4">
        <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px] md:w-[400px] lg:w-[500px] xl:w-[600px] 2xl:w-[800px]">
          <div className="flex flex-col space-y-2 text-center">
            <h1 className="text-2xl font-semibold tracking-tight">
              Reset Password
            </h1>
          </div>

          <Form {...form}>
            <form
              onSubmit={(event) => void form.handleSubmit(onSubmit)(event)}
              className="space-y-8"
            >
              <FormField
                name="newPassword"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>New password</FormLabel>
                    <FormControl>
                      <Input
                        autoComplete="off"
                        id="newPassword"
                        type="password"
                        placeholder="New password"
                        autoCorrect="off"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                name="confirmPassword"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Confirm password</FormLabel>
                    <FormControl>
                      <Input
                        autoComplete="off"
                        id="confirmPassword"
                        type="password"
                        placeholder="Confirm password"
                        autoCorrect="off"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="flex justify-center items-center">
                <Button
                  type="submit"
                  className="w-1/4 bg-primary text-primary-foreground rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 p-2"
                >
                  Submit
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </div>
    </AuthLayout>
  );
}
