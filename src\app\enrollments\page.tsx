"use client";

import React, { useEffect, useState } from "react";
import { getColumns } from "./columns";
import { DataTable } from "../../components/ui/data-table/data-table";
// import enrollmentData from "./enrollmentData";
import MainLayout from "../layout/mainlayout";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import "../../styles/main.css";
// import courseData from "./courseData";
import type {
  Enrollment,
  ErrorCatch,
  InnerItem,
  LogUserActivityRequest,
  ToastType,
} from "@/types";
import { Combobox } from "../../components/ui/combobox";
import useEnrollments from "@/hooks/useEnrollment";
import useCourse from "@/hooks/useCourse";
import { pageUrl, privilegeData } from "@/lib/constants";
import { Spinner } from "@/components/ui/progressiveLoader";
import { useToast } from "@/components/ui/use-toast";
import { Label } from "@/components/ui/label";
import { Mail<PERSON>con, PlusIcon, Trash2 } from "lucide-react";
import getPrivilegeList from "@/hooks/useCheckPrivilege";
import { Modal } from "@/components/ui/modal";
import DeleteUserEnrollment from "./deleteUserEnrollment";
import Notifications from "./notifications";
// import Email from "@/components/ui/sent-mail";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumb";
import "../../styles/main.css";
import Email from "@/components/ui/sent-mail";
import { Input } from "@/components/ui/input";
import * as XLSX from "xlsx";
import moment from "moment";
import { DATE_FORMAT_DMY_HM_AM_PM } from "@/lib/constants";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";
import { ERROR_MESSAGES } from "@/lib/messages";
interface ColInfo {
  wch: number;
}

export default function EnrollmentListPage(): React.JSX.Element {
  const { t } = useTranslation();
  const columns = getColumns(t);
  const [data, setData] = useState<Enrollment[]>([]);
  const [originalData, setOriginalData] = useState<Enrollment[]>([]);
  const [courseData, setCourseData] = useState<
    { value?: string; label?: string }[]
  >([]);
  const [selectCourse, setSelectCourse] = useState<string>("");
  const [defaultCourseLabel, setDefaultCourseLabel] = useState<string>("");
  const { toast } = useToast() as ToastType;
  const [isLoading, setIsLoading] = React.useState<boolean>(true);
  const { getEnrollments } = useEnrollments();
  const { getCourseList } = useCourse();
  const { updateUserActivity } = useLogUserActivity();
  const [disableBtn, setDisableBtn] = useState<boolean>(false);
  const [isOpenDeletDialog, setIsOpenDeleteDialog] = useState<boolean>(false);
  const [reloadResource, setReloadResource] = useState<boolean>(false);
  const [userId, setUserId] = useState<string>("");
  const [isOpenNotification, setIsOpenNotification] = useState<boolean>(false);
  const [isOpenMail, setIsOpenMail] = useState<boolean>(false);
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);
  const [emailFilter, setEmailFilter] = useState<string>("");
  const [recipient, setRecipient] = useState<string>("");
  const [emailId, setEmailId] = useState<string>("");

  useEffect(() => {
    setBreadcrumbItems(
      getBreadCrumbItems(t, t("breadcrumb.enrollments"), { "": "" }),
    );
  }, [t]);

  useEffect(() => {
    const fetchCourseData = async (): Promise<void> => {
      try {
        const courses = await getCourseList("");
        setIsLoading(false);
        if (
          courses !== null &&
          courses !== undefined &&
          Object.keys(courses).length > 0
        ) {
          const filteredCourses = courses
            .filter((course) => course.course_id != null && course.short_name)
            .map((course) => ({
              value: course.course_id,
              defaultCourseLabel,
              label: course.short_name,
            }));
          setCourseData(filteredCourses);
          if (filteredCourses?.length > 0) {
            setSelectCourse(filteredCourses[0].value ?? "");
            setDefaultCourseLabel(filteredCourses[0].label ?? "");
          }
        } else {
          setCourseData([]);
        }
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
      }
    };

    fetchCourseData().catch((error) => console.log(error));

    const canPerformAction = getPrivilegeList(
      "Enrollments",
      privilegeData.Enrollments.addEnrollment,
    );
    setDisableBtn(canPerformAction);
  }, [reloadResource]);

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  const handleEmailFilterChange = (
    e: React.ChangeEvent<HTMLInputElement>,
  ): void => {
    setEmailFilter(e.target.value);
    if (e.target.value === "") {
      setData(originalData);
    } else {
      const filteredData = data.filter((enrollment) =>
        enrollment.email.toLowerCase().includes(emailFilter.toLowerCase()),
      );
      setData(filteredData);
    }
  };
  const DeleteUser = (data: Enrollment): void => {
    console.log(data);
    setIsOpenDeleteDialog(true);
    setUserId(data.id);
  };

  const sentMail = (data: Enrollment): void => {
    setIsOpenMail(true);
    setEmailId(data.email);
    setRecipient(data.first_name + " " + data.last_name);
    setUserId(data.id);
  };
  // const sentNotification = (data: Enrollment): void => {
  //   setIsOpenNotification(true);
  //   setUserId(data.id);
  //   console.log(isOpenDeletDialog);
  // };

  const handleDeleteReload = (): void => {
    setReloadResource(!reloadResource);
  };

  useEffect(() => {
    const fetchEnrollmentData = async (): Promise<void> => {
      setIsLoading(true);
      try {
        let selectCid = "";
        if (selectCourse.length > 0) {
          selectCid = selectCourse;
        } else if (courseData.length > 0) {
          selectCid = courseData[0].value as string;
        } else {
          console.error("No valid courses available to select");
        }
        const enrollments = await getEnrollments(selectCid);
        setIsLoading(false);
        if (
          enrollments !== null &&
          enrollments !== undefined &&
          Object.keys(enrollments).length > 0
        ) {
          const updatedEnrollments = enrollments.map((enrollment) => ({
            ...enrollment,
            attended: String(enrollment.attended).toLowerCase() === "true",
          }));
          setData(updatedEnrollments);
          setOriginalData(updatedEnrollments);
        } else {
          setData([]);
          setOriginalData([]);
        }
      } catch (error) {
        setIsLoading(false);
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
      }
      setIsLoading(false);
    };
    if (courseData?.length > 0 && selectCourse !== "") {
      fetchEnrollmentData().catch((error) => console.log(error));
    }
  }, [selectCourse, courseData]);

  const handleCourseChange = (selectedOption: string): void => {
    setSelectCourse(selectedOption);
  };

  const handleExport = (): void => {
    try {
      const workbook = XLSX.utils.book_new();
      const modifiedData = data.map((row) => ({
        ID: row.id,
        "First name": row.first_name,
        "Last name": row.last_name,
        "Enrolled date": moment
          .utc(row.enrolled_date)
          .local()
          .format(DATE_FORMAT_DMY_HM_AM_PM),
      }));
      const worksheet = XLSX.utils.json_to_sheet(modifiedData);
      const columnWidths: (ColInfo | undefined)[] = [
        { wch: 40 },
        { wch: 30 },
        { wch: 30 },
        { wch: 25 },
      ];
      const filteredColumnWidths: ColInfo[] = columnWidths.filter(
        (width): width is ColInfo => width !== undefined,
      );
      worksheet["!cols"] = filteredColumnWidths;
      // worksheet["!rows"] = [{ hpx: 30 }];
      const numRows = modifiedData.length;
      const rowHeights = Array(numRows).fill({ hpx: 20 }); // Set the height to 25 pixels for all rows
      rowHeights[0] = { hpx: 30 }; // Set the height of the heading row to 30 pixels
      worksheet["!rows"] = rowHeights;
      XLSX.utils.book_append_sheet(workbook, worksheet, "MySheet1");
      XLSX.writeFile(workbook, "Enrollments.xlsx");
      const params = {
        activity_type: "Exam",
        screen_name: "Exam",
        action_details: "Successfully export enrollemnts to excel",
        target_id: selectCourse as string,
        log_result: "SUCCESS",
      };
      void updateLogUserActivity(params).catch((error) => {
        console.error(error);
      });
    } catch (error) {
      console.error("Error exporting to Excel:", error);
      const params = {
        activity_type: "Exam",
        screen_name: "Exam",
        action_details: "Failed export enrollemnts to excel",
        target_id: selectCourse as string,
        log_result: "ERROR",
      };
      void updateLogUserActivity(params).catch((error) => {
        console.error(error);
      });
    }
  };

  const closeDeleteDialog = (): void => {
    setIsOpenDeleteDialog(false);
  };
  const closeNotificationDialog = (): void => {
    setIsOpenNotification(false);
  };
  const closeEmailDialog = (): void => {
    setIsOpenMail(false);
  };
  return (
    <MainLayout>
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      <span>
        <h1 className="text-2xl font-semibold tracking-tight mb-4">
          {t("enrollment.title")}
          {disableBtn && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Link href={pageUrl.addEnrollments}>
                    <Button className="bg-ring bg-[#fb8500] hover:bg-[#fb5c00] py-2 md:py-3 px-4 md:px-6 whitespace-nowrap ml-2">
                      <PlusIcon className="h-4 w-4 md:h-5 md:w-5" />
                    </Button>
                  </Link>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{t("enrollment.newEnrollment")}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </h1>
      </span>
      <div className="border rounded-md p-4 pt-0 bg-[#fff]">
        {/* <h1 className="text-2xl font-semibold tracking-tight"></h1> */}
        <div className="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4 items-start md:items-center">
          <div className="text-left mt-4 min-w-[300px]">
            <Label>{t("enrollment.selectCourse")}</Label>
            <Combobox
              data={courseData}
              defaultLabel={defaultCourseLabel}
              onSelectChange={handleCourseChange}
              placeHolder={t("enrollment.selectCourse")}
            />
          </div>
          <div className="text-left pt-4 min-w-[300px]">
            <Label>{t("enrollment.filterByEmail")}</Label>
            <div className="w-full">
              <Input
                type="text"
                value={emailFilter}
                onChange={handleEmailFilterChange}
                placeholder={t("enrollment.filterByEmail")}
              />
            </div>
          </div>
        </div>

        <div className="p-4 mt-4 border rounded-md bg-white">
          {isLoading ? (
            <Spinner />
          ) : (
            <div className="">
              <div className="flex flex-wrap justify-end">
                <Button className="w-full sm:w-auto" onClick={handleExport}>
                  {t("enrollment.exportToExcel")}
                </Button>
              </div>
              <DataTable
                columns={columns}
                data={data}
                FilterLabel={t("enrollment.filterByName")}
                FilterBy={"first_name"}
                disableEmail={"attended"}
                actions={[
                  {
                    title: t("enrollment.remove"),
                    icon: Trash2,
                    color: "#ff0000",
                    varient: "icon",
                    isEnable: true,
                    handleClick: (val: unknown) =>
                      DeleteUser(val as Enrollment),
                  },
                  {
                    title: t("enrollment.sendEmail"),
                    icon: MailIcon,
                    color: "#2e86c1 ",
                    varient: "icon",
                    // isEnable: true,
                    handleClick: (val: unknown) => sentMail(val as Enrollment),
                  },
                  // {
                  //   title: "Compose Notification",
                  //   icon: BellPlus,
                  //   color: "#117a65  ",
                  //   varient: "icon",
                  //   isEnable: true,
                  //   handleClick: (val: unknown) =>
                  //     sentNotification(val as Enrollment),
                  // },
                ]}
              />
            </div>
          )}
        </div>
        {isOpenDeletDialog && (
          <Modal
            title={t("enrollment.removeUser")}
            header=""
            openDialog={isOpenDeletDialog}
            closeDialog={closeDeleteDialog}
          >
            <DeleteUserEnrollment
              onSave={handleDeleteReload}
              onCancel={closeDeleteDialog}
              userId={userId}
              selectCourse={selectCourse}
            />
          </Modal>
        )}
        {isOpenNotification && (
          <Modal
            title=""
            header=""
            openDialog={isOpenNotification}
            closeDialog={closeNotificationDialog}
            type="max-w-5xl"
          >
            <Notifications
              onSave={closeNotificationDialog}
              onCancel={closeNotificationDialog}
              userId={userId}
            />
          </Modal>
        )}
        {isOpenMail && (
          <Modal
            title=""
            header=""
            openDialog={isOpenMail}
            closeDialog={closeEmailDialog}
            type="max-w-5xl"
          >
            <Email
              onSave={closeEmailDialog}
              onCancel={closeEmailDialog}
              emailId={emailId}
              isCertificate={false}
              recipient={recipient}
              course={defaultCourseLabel}
              courseId={selectCourse}
            />
          </Modal>
        )}
      </div>
    </MainLayout>
  );
}
