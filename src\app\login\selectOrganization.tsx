import React, { useEffect, useState } from "react";
import { Combobox } from "@/components/ui/combobox";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { useRouter } from "next/navigation";
import { ORG_KEY, ORG_NAME } from "@/lib/constants";
import type { ComboData, LogUserActivityRequest, OrgSelection } from "@/types";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";

export const SelectOrganization: React.FC<OrgSelection> = (
  props,
): JSX.Element => {
  const {t}= useTranslation();
  const [selectedOrg, setSelectedOrg] = useState<string>("");
  const [buttonDisable, setButtonDisable] = useState<boolean>(true);
  const router = useRouter();
  const [comboData, setComboData] = useState<ComboData[]>([]);
  const { updateUserActivity } = useLogUserActivity();

  useEffect(() => {
    const sortedData = props.orgList.sort((data, item) => {
      return (data.label ?? "").localeCompare(item.label ?? "");
    });
    setComboData(sortedData);
  });

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  const handleSaveClick = async (): Promise<void> => {
    if (props.orgList.length > 1) {
      if (selectedOrg !== "") {
        setButtonDisable(false);

        const selectedLabel = (
          props.orgList.find(
            (orgDetails: ComboData) => orgDetails.value === selectedOrg,
          ) as ComboData
        )?.label as string;
        const params = {
          activity_type: "Organization",
          screen_name: "Organization",
          action_details: `Selected the organization ${selectedLabel}`,
          target_id: selectedOrg,
          log_result: "SUCCESS",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
        localStorage.setItem(ORG_KEY, selectedOrg);
        localStorage.setItem(ORG_NAME, selectedLabel);
        router.push("/dashboard");
      } else {
        const params = {
          activity_type: "Organization",
          screen_name: "Organization",
          action_details: "Organization not selected",
          target_id: selectedOrg,
          log_result: "ERROR",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
        setButtonDisable(true);
      }
    }
  };
  const comboSelectedValue = (res: string): void => {
    setSelectedOrg(res);
    setButtonDisable(false);
  };

  return (
    <>
      <div className="mb-2 mr-4">
        <Label className="block mb-2">
        {t("combobox.selectOrganization")}<span className="text-red-700">*</span>
        </Label>
        <Combobox
          data={comboData}
          onSelectChange={comboSelectedValue}
          placeHolder={t("combobox.selectOrganization")}
        />
      </div>

      <div className="flex topic-icons pr-4 justify-end">
        <div className="flex items-center">
          <Button
            type="button"
            variant="outline"
            className="primary mr-2"
            onClick={() => props.closeDialog()}
          >
            {t("buttons.cancel")}
          </Button>
          &nbsp;
          <Button
            type="submit"
            className="primary"
            disabled={buttonDisable}
            onClick={() => {
              handleSaveClick().catch((error) => console.log(error));
            }}
          >
            {t("buttons.submit")}
          </Button>
        </div>
      </div>
    </>
  );
};
