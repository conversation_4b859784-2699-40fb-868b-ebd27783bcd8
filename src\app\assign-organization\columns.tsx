"use client";

import type { ColumnDef } from "@tanstack/react-table";
// import { ArrowUpDown } from "lucide-react";
// import { Button } from "@/components/ui/button";
// import Image from "next/image";
import type {
  AssignUserList,
  // AssignUserListColumnDefinition
} from "@/types";

export const getColumns = (
  t: (key: string) => string,
): ColumnDef<AssignUserList>[] => [
  {
    id: "select",
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "first_name",
    header: t("assignOrganization.firstName"),
    // header: ({ column }: AssignUserListColumnDefinition): React.JSX.Element => {
    //   return (
    //     <Button
    //       className="px-0"
    //       variant="ghost"
    //       onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
    //     >
    //       First name
    //       <ArrowUpDown className="ml-2 h-4 w-4" />
    //     </Button>
    //   );
    // },
    cell: ({
      row,
    }: {
      row: { original: AssignUserList };
    }): React.JSX.Element => {
      const firstName = row.original.first_name ?? " ";
      return <div className="text-align">{firstName}</div>;
    },
  },
  {
    accessorKey: "last_name",
    header: t("assignOrganization.lastName"),
    cell: ({
      row,
    }: {
      row: { original: AssignUserList };
    }): React.JSX.Element => {
      const lastName = row.original.last_name ?? " ";
      return <div className="text-align">{lastName}</div>;
    },
  },
  {
    accessorKey: "email",
    header: t("assignOrganization.email"),
  },
  // {
  //   accessorKey: "avatar_url",
  //   header: "Profile",
  //   cell: ({ row }) => (
  //     <div className="h-10 w-10">
  //       <Image
  //         src={row.original.avatar_url}
  //         alt=""
  //         width={45}
  //         height={45}
  //         objectFit="cover"
  //         className={`rounded-full ${
  //           row.original.avatar_url !== "" ? "" : "hidden"
  //         }`}
  //       />
  //     </div>
  //   ),
  // },
];
