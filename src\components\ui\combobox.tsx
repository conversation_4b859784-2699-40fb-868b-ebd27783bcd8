import React, { useEffect, useState } from "react";
import { CheckIcon, ChevronDownIcon } from "@radix-ui/react-icons";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import type { ComboData } from "@/types";
import { ShieldAlert } from "lucide-react";

interface ComboDataValueType {
  data: ComboData[];
  onSelectChange: (value: string) => void;
  isDisabled?: boolean;
  placeHolder?: string;
  defaultLabel?: string;
}
import { useTranslation } from "react-i18next";

export const Combobox: React.FC<ComboDataValueType> = ({
  data,
  onSelectChange,
  isDisabled = false,
  placeHolder = "Select Item",
  defaultLabel,
}): React.JSX.Element => {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);
  const [selectedValue, setSelectedValue] = useState<string | null>(null);
  const translatedPlaceholder = placeHolder ?? t("combobox.selectItem");

  useEffect(() => {
    const initialValue =
      defaultLabel !== undefined && defaultLabel !== null
        ? data.find((item) => item.label === defaultLabel)?.value ?? null
        : null;

    // Set selectedValue conditionally if initialValue exists
    // if (initialValue !== null) {
    //   setSelectedValue(initialValue);
    // }
    setSelectedValue(initialValue);
  }, [data, defaultLabel]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={`w-full justify-between text-left font-normal ${
            translatedPlaceholder === t("combobox.selectOrganization")
              ? ""
              : "bg-[#fff]"
          }`}
          disabled={isDisabled}
          onClick={() => setOpen(!open)}
        >
          {selectedValue !== null
            ? data.find((item) => item.value === selectedValue)?.label ??
              translatedPlaceholder
            : translatedPlaceholder}
          <ChevronDownIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[--radix-popover-trigger-width] break-all">
        <Command>
          <CommandInput
            placeholder="Search..."
            className="h-9"
            onKeyDown={(e) => {
              if (e.key === " " && e.currentTarget.selectionStart === 0) {
                e.preventDefault();
              }
            }}
            onInput={(e) => {
              const target = e.currentTarget as HTMLInputElement;
              target.value = target.value.replace(/\s+/g, " ");
            }}
          />
          <CommandGroup
            className="overflow-y-auto max-h-80"
            onWheel={(e) => {
              e.preventDefault();
              e.stopPropagation();
              const delta = e.deltaY;
              const element = e.currentTarget;
              element.scrollTop += delta;
            }}
          >
            {data.length > 0 ? (
              data?.map((item) => (
                <CommandItem
                  key={item.value}
                  value={item.label}
                  onSelect={() => {
                    if (!(item.isDisabled ?? false)) {
                      // const selectedValue = item.value ?? "";
                      setSelectedValue(item.value ?? null);
                      setOpen(false);
                      onSelectChange(item.value ?? "");
                    }
                  }}
                >
                  {item.label}
                  <CheckIcon
                    className={`ml-auto h-4 w-4 ${
                      selectedValue === item.value ? "opacity-100" : "opacity-0"
                    }`}
                  />
                  {(item.isDisabled ?? false) && <ShieldAlert />}
                </CommandItem>
              ))
            ) : (
              <CommandItem className="flex justify-center">
                No data found.
              </CommandItem>
            )}
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
};
