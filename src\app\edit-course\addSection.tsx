"use client";

import React, { useState } from "react";
import { Label } from "@radix-ui/react-label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import useCourse from "@/hooks/useCourse";
import type { AddSectionRequest, ErrorCatch, ToastType } from "@/types";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import { Trash } from "lucide-react";
import { useTranslation } from "react-i18next";

export default function AddSection({
  onCancel,
  courseId,
  currentSectionsCount = 0,
}: {
  onCancel: () => void;
  courseId?: string;
  currentSectionsCount?: number;
}): React.JSX.Element {
  const { t } = useTranslation();
  const { toast } = useToast() as ToastType;
  const { addSection } = useCourse();

  const [name, setName] = useState("");
  // Initialize section order to be next after existing sections
  // Example: if there are 3 existing sections, start with order 4
  const [sectionOrder, setSectionOrder] = useState<number>(
    currentSectionsCount + 1,
  );
  const [sections, setSections] = useState<
    { name: string; section_order: number }[]
  >([]);

  const handleAddToTable = (): void => {
    if (name.trim() === "") {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.sectionRequired"),
      });
      return;
    }

    setSections([...sections, { name, section_order: sectionOrder }]);
    setName("");

    // Calculate next order considering both existing sections and newly added sections
    const usedOrders = [
      ...Array.from({ length: currentSectionsCount }, (_, i) => i + 1), // Existing sections: 1, 2, 3, ...
      ...sections.map((s) => s.section_order), // Already added sections
      sectionOrder, // Current section being added
    ];

    let nextOrder = currentSectionsCount + 1;
    while (usedOrders.includes(nextOrder)) {
      nextOrder++;
    }
    setSectionOrder(nextOrder);
  };

  const handleRemove = (index: number): void => {
    const updated = sections.filter((_, i) => i !== index);
    setSections(updated);
  };

  const handleSubmit = async (e: React.FormEvent): Promise<void> => {
    e.preventDefault();

    if (sections.length === 0) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.addOneSection"),
      });
      return;
    }

    const orgId = localStorage.getItem("orgId");
    const params: AddSectionRequest = {
      org_id: orgId ?? "",
      course_id: courseId ?? "",
      section_details: sections,
    };

    console.log("Submitting sections:", params);

    try {
      const result = await addSection(params);
      if (result.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_success_title,
          title: t("successMessages.add_folder_title"),
          description: t("successMessages.add_folder_msg"),
        });
        onCancel();
      } else {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: result.status ?? "Unknown error",
        });
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err.message,
      });
    }
  };

  return (
    <form onSubmit={(e) => void handleSubmit(e)} className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2">
        <div>
          <Label htmlFor="section-name">
            {t("courses.section.addSection")}
          </Label>
          <Input
            id="section-name"
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder={t("courses.section.addSection")}
          />
        </div>
        <div>
          <Label htmlFor="section-order">{t("courses.section.order")}</Label>
          <Input
            id="section-order"
            type="number"
            value={sectionOrder}
            onChange={(e) => setSectionOrder(Number(e.target.value))}
            placeholder={t("courses.section.order")}
          />
        </div>
      </div>

      <div className="flex justify-end">
        <Button type="button" onClick={handleAddToTable}>
          {t("courses.section.addSection")}
        </Button>
      </div>

      {/* Table of added sections */}
      {sections.length > 0 && (
        <div className="mt-6">
          <table className="w-full table-auto border border-gray-300">
            <thead className="bg-gray-100">
              <tr>
                <th className="border p-2 text-left">
                  {t("courses.section.name")}
                </th>
                <th className="border p-2 text-left">
                  {t("courses.section.order")}
                </th>
                <th className="border p-2 text-left">
                  {t("courses.section.remove")}
                </th>
              </tr>
            </thead>
            <tbody>
              {[...sections]
                .sort((a, b) => a.section_order - b.section_order)
                .map((section, index) => (
                  <tr key={index}>
                    <td className="border p-2">{section.name}</td>
                    <td className="border p-2">{section.section_order}</td>
                    <td className="border p-2">
                      <Button
                        type="button"
                        onClick={() => handleRemove(index)}
                        className="bg-transparent"
                      >
                        <Trash className="text-red-500" />
                      </Button>
                    </td>
                  </tr>
                ))}
            </tbody>
          </table>
        </div>
      )}

      <div className="mt-6 flex justify-end gap-4">
        <Button
          type="button"
          onClick={onCancel}
          className="bg-gray-600 text-white"
        >
          {t("buttons.cancel")}
        </Button>
        <Button type="submit" className="bg-[#9FC089] text-white">
          {t("courses.section.submitAll")}
        </Button>
      </div>
    </form>
  );
}
