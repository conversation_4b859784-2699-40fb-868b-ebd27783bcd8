import { useState } from "react";

interface StarRatingProps {
  rating: number;
  onRatingChange: (newRating: number) => void;
}

const StarRating: React.FC<StarRatingProps> = ({ rating, onRatingChange }) => {
  const [hoveredRating, setHoveredRating] = useState<number | null>(null);

  const handleMouseEnter = (hoveredValue: number): void => {
    setHoveredRating(hoveredValue);
  };

  const handleMouseLeave = (): void => {
    setHoveredRating(null);
  };

  const handleStarClick = (selectedValue: number): void => {
    onRatingChange(selectedValue);
  };

  const renderStars = (): React.JSX.Element => {
    const maxRating = 5;
    const stars = [];

    for (let i = 1; i <= maxRating; i++) {
      stars.push(
        <span
          key={i}
          className={`cursor-pointer text-2xl ${
            (hoveredRating ?? rating) >= i ? "text-black" : "text-gray-300"
          }`}
          onMouseEnter={() => handleMouseEnter(i)}
          onMouseLeave={handleMouseLeave}
          onClick={() => handleStarClick(i)}
        >
          ★
        </span>,
      );
    }

    return <div>{stars}</div>;
  };

  return (
    <div className="flex items-center">
      {renderStars()}
      {/* <span className="ml-2">{rating || hoveredRating} / 5</span> */}
    </div>
  );
};

export default StarRating;
