import React, { useState, useEffect } from "react";
import { getColumns } from "./userColumn";
import { DataTable } from "../../components/ui/data-table/data-table";
import useUsers from "@/hooks/useUsers";
import type {
  AddUserToSubscriptionRequest,
  ErrorCatch,
  LogUserActivityRequest,
  ToastType,
  UserData,
  UsersDataType,
} from "@/types";
import { useToast } from "../../components/ui/use-toast";
import { Spinner } from "@/components/ui/progressiveLoader";
import Link from "next/link";
import { pageUrl } from "@/lib/constants";
import { Button } from "@/components/ui/button";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import moment from "moment-timezone";
import useSubscription from "@/hooks/useSubscription";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";

interface UsersListProps {
  data?: string;
  onCancel: () => void;
}

const AddUsers: React.FC<UsersListProps> = ({ data, onCancel }) => {
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [filteredUsers, setFilteredUsers] = useState<UsersDataType[]>([]);
  const { getUsers } = useUsers();
  const { addToSubscription } = useSubscription();
  const { toast } = useToast() as ToastType;
  const [selectedData, setSelectedData] = useState<UserData[]>([]);
  const { updateUserActivity } = useLogUserActivity();
  const { t } = useTranslation();
  const columns = getColumns(t);

  useEffect(() => {
    void getUsersList().catch((error) => console.log(error));
  }, []);

  const getUsersList = async (): Promise<void> => {
    setIsLoading(true);
    try {
      const org_id = localStorage.getItem("orgId") as string;
      const fetchedUsers = await getUsers(org_id);
      setIsLoading(false);
      if (fetchedUsers.length > 0) {
        setFilteredUsers(fetchedUsers);
      } else {
        setFilteredUsers([]);
      }
    } catch (error) {
      setIsLoading(false);
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
    }
  };

  const handleSelectedData = (selData: UsersDataType[]): void => {
    setSelectedData(
      selData.map((user) => ({
        id: user.id,
        first_name: user.first_name,
        last_name: user.last_name,
      })),
    );
  };

  const handleAddUsers = async (): Promise<void> => {
    const orgId = localStorage.getItem("orgId");
    const planId = data;
    const idList = selectedData.map((user) => user.id);
    const today = moment();
    const formattedToday =
      today.utc().format("YYYY-MM-DD HH:mm:ss.SSSSSS") + "+00";
    const passData: AddUserToSubscriptionRequest = {
      org_id: orgId as string,
      plan_id: planId,
      user_ids: idList,
      subscription_plan_for_user_data: {
        purchase_date: formattedToday,
      },
    };
    try {
      const response = await addToSubscription(passData);
      if (response.status === "success") {
        onCancel();
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.add_user_to_plan_title"),
          description: t("successMessages.add_user_to_plan_msg"),
        });
        const params = {
          activity_type: "Subscription",
          screen_name: "Subscription",
          action_details: "Add users to subscription ",
          target_id: planId as string,
          log_result: "SUCCESS",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      }
    } catch (error) {
      setIsLoading(false);
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
      const params = {
        activity_type: "Subscription",
        screen_name: "Subscription",
        action_details: "Failed to add user to subscription ",
        target_id: planId as string,
        log_result: "ERROR",
      };
      void updateLogUserActivity(params).catch((error) => {
        console.error(error);
      });
    }
  };

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  return (
    <div>
      <h1 className="text-2xl font-semibold tracking-tight">
        {String(t("membershipPlan.addUsers.title"))}
      </h1>
      <div className="border rounded-md p-4 mt-4">
        {isLoading ? (
          <Spinner />
        ) : (
          <div style={{ maxHeight: "700px" }} className="overflow-x-auto">
            <DataTable
              columns={columns}
              data={filteredUsers}
              FilterLabel={t("subscriptionPlan.filterByFirstName")}
              FilterBy={"first_name"}
              actions={[]}
              onSelectedDataChange={handleSelectedData}
            />
          </div>
        )}
      </div>
      <div className="flex flex-wrap justify-end mt-8 gap-x-3">
        <div className="">
          <Link href={pageUrl.membershipPlanList}>
            <Button className="bg-[#33363F]" onClick={onCancel}>
              {String(t("buttons.cancel"))}
            </Button>
          </Link>
        </div>
        <div className="">
          <Button
            className="bg-[#9FC089]"
            disabled={selectedData.length < 1}
            onClick={() => {
              void handleAddUsers();
            }}
          >
            {String(t("buttons.submit"))}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AddUsers;
