"use client";
import type { ColumnDef } from "@tanstack/react-table";
// import { ArrowUpDown } from "lucide-react";
// import { Button } from "@/components/ui/button";
import type {
  // ResourceColumnDefinition,
  // ResourceForm,
  // coursePlanRowDefinition,
  CourseDatas,
  // coursePlanColumnDefinition,
  coursePlanRowDefinition,
} from "@/types";

export const getCourseColumns = (
  t: (key: string) => string,
): ColumnDef<CourseDatas>[] => [
  {
    accessorKey: "full_name",
    header: t("subscriptionPlan.courseName"),
    cell: ({ row }: coursePlanRowDefinition): React.JSX.Element => (
      <div className="text-align">
        {row.original?.full_name !== undefined && row.original.full_name !== ""
          ? row.original.full_name
          : row.original?.name}
      </div>
    ),
  },
];
export const getResourceColumns = (
  t: (key: string) => string,
): ColumnDef<CourseDatas>[] => [
  {
    accessorKey: "full_name",
    header: t("subscriptionPlan.resourceName"),
    cell: ({ row }: coursePlanRowDefinition): React.JSX.Element => (
      <div className="text-align">{row.original?.full_name}</div>
    ),
  },
];
