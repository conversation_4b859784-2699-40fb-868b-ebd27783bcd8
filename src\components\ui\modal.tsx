import React from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

import type { ModalProps } from "@/types";

export function Modal({
  title,
  header,
  openDialog,
  closeDialog,
  type,
  children,
}: ModalProps): React.JSX.Element {
  return (
    <div>
      <Dialog open={openDialog} onOpenChange={() => closeDialog(false)}>
        <DialogContent
          className={` ${type} mx-auto p-4 sm:p-8 overflow-y-auto max-h-full`}
          onInteractOutside={(e) => {
            e.preventDefault();
          }}
        >
          <DialogHeader>
            <DialogTitle className="text-xl sm:text-2xl text-left">
              {title}
            </DialogTitle>
            <DialogDescription className="text-sm sm:text-base">
              {header}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4">{children}</div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
