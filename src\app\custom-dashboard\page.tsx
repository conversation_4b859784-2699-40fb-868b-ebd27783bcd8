"use client";

import React, { useState, useEffect } from "react";
import MainLayout from "../layout/mainlayout";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import type {
  ErrorCatch,
  UpdateCustomResource,
  CustomResource,
  ToastType,
  // moduleList,
  // ComboData,
} from "@/types";
import { useToast } from "@/components/ui/use-toast";
import { Card, CardContent } from "@/components/ui/card";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { Pencil, Trash, Video, File } from "lucide-react";
import { Modal } from "@/components/ui/modal";
import EditResources from "./editResource";
import useCustomDashboard from "@/hooks/useCustomDashboard";
import { ORG_KEY } from "@/lib/constants";
import DeleteCustomResources from "./deleteResource";
import { But<PERSON> } from "@/components/ui/button";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useTranslation } from "react-i18next";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";

export default function CustomDashboard(): React.JSX.Element {
  const { t } = useTranslation();
  const { toast } = useToast() as ToastType;
  const [currentAffairsItem, setCurrentAffairsItem] = useState<number>();
  const [generalResourcesItem, setGeneralResourcesItem] = useState<number>();
  const [assignmentsItem, setAssignmentsItem] = useState<number>();
  const [subjectsItem, setSubjectsItem] = useState<number>();
  const [haveExam, setHaveExam] = useState<number>(0);
  const { updateResources, getCustomDashboard } = useCustomDashboard();
  const [resourceLibrary, setResourceLibrary] = useState<CustomResource[]>([]);
  const [visibleResources, setVisibleResources] = useState<CustomResource[]>(
    [],
  );
  const [updatedResourceArray, setUpdatedResourceArray] = useState<
    CustomResource[]
  >([]);
  const [isOpenEditDialog, setIsOpenEditDialog] = useState<boolean>(false);
  const [isOpenAddDialog, setIsOpenAddDialog] = useState<boolean>(false);
  const [isOpenDeleteDialog, setIsOpenDeleteDialog] = useState<boolean>(false);
  const [deleteId, setDeleteId] = useState<string>("");
  const [editIndex, setEditIndex] = useState<number | null>(null);
  const [updateDashboardData, setUpdateDashboardData] = useState(false);

  // const [resourceType, setResourceType] = useState<ComboData[]>([]);
  // const [resourceLabel, setResourceLabel] = useState<string>("File");
  // const [selectedType, setSelectedType] = useState<string>("");

  useEffect(() => {
    const getDashboardData = async (): Promise<void> => {
      try {
        const requestBody = {
          org_id: localStorage.getItem("orgId") ?? "",
          course_id: null,
        };
        const result = await getCustomDashboard(requestBody);

        if (result.status === "success") {
          setCurrentAffairsItem(Number(result.current_affairs.no_of_items));
          setGeneralResourcesItem(result.general_resources.no_of_items);
          setAssignmentsItem(result.assignments.no_of_items);
          setSubjectsItem(result.subjects.no_of_items);

          setAssignmentsItem(result.assignments.no_of_items);
          setResourceLibrary(result.general_resources.resources);
          setVisibleResources(result.general_resources.resources);
        } else {
          setResourceLibrary([]);
          setVisibleResources([]);
        }
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
      }
    };
    getDashboardData().catch((error) => console.log(error));
  }, [updateDashboardData]);

  const handleCurentAffairsNo = (
    e: React.ChangeEvent<HTMLInputElement>,
  ): void => {
    const value = e.target.value;
    const numericValue = value === "" ? undefined : Number(value);

    if (numericValue === undefined || /^\d+$/.test(value)) {
      setCurrentAffairsItem(numericValue);
    }
  };
  const handleGeneralResourceNo = (
    e: React.ChangeEvent<HTMLInputElement>,
  ): void => {
    const value = e.target.value;
    const numericValue = value === "" ? undefined : Number(value);

    if (numericValue === undefined || /^\d+$/.test(value)) {
      setGeneralResourcesItem(numericValue);
    }
  };

  const handleAssignmentsNo = (
    e: React.ChangeEvent<HTMLInputElement>,
  ): void => {
    const value = e.target.value;
    const numericValue = value === "" ? undefined : Number(value);

    if (numericValue === undefined || /^\d+$/.test(value)) {
      setAssignmentsItem(numericValue);
    }
  };

  const handleSubjectsNo = (e: React.ChangeEvent<HTMLInputElement>): void => {
    const value = e.target.value;
    const numericValue = value === "" ? undefined : Number(value);

    if (numericValue === undefined || /^\d+$/.test(value)) {
      setSubjectsItem(numericValue);
    }
  };

  const handleEditDialog = (resource: CustomResource, index: number): void => {
    setEditIndex(index);
    setIsOpenEditDialog(true);
  };

  const handleDeleteDialog = (resource: CustomResource): void => {
    setDeleteId(resource.resource_id);

    setIsOpenDeleteDialog(true);
  };

  const closeEditDialog = (): void => {
    setIsOpenEditDialog(false);
    setIsOpenAddDialog(false);
  };

  const closeDeleteDialog = (): void => {
    setIsOpenDeleteDialog(false);
  };

  const handleDeleteSuccess = (deletedId: string): void => {
    const updatedLibrary = resourceLibrary.filter(
      (item) => item.resource_id !== deletedId,
    );
    setResourceLibrary(updatedLibrary);

    const updatedVisible: (CustomResource | null)[] = visibleResources.map(
      (item) => (item?.resource_id === deletedId ? null : item),
    );
    setVisibleResources(
      updatedVisible.filter((item): item is CustomResource => item !== null),
    );
  };

  const handleResourceUpdate = async (
    updatedResources: CustomResource[],
  ): Promise<void> => {
    const generalResouresArr = updatedResources.map((item, index) => ({
      resource_id: item?.resource_id,
      resource_name: item?.resource_name,
      thumbnail_url: item?.thumbnail_url as string,
      resource_type: item?.resource_type as string,
      resource_order: index + 1, // Pass the index as resource_order
    }));
    setUpdatedResourceArray(generalResouresArr);
  };

  const handleSubmit = async (): Promise<void> => {
    const orgId = localStorage.getItem(ORG_KEY);

    const params = {
      org_id: orgId ?? "",
      course_id: null,
      assignments: {
        Assignments: [],
        no_of_items: assignmentsItem ?? 0,
        component_type: "assignments",
      },
      general_resources: {
        resources: updatedResourceArray,
        no_of_items: generalResourcesItem ?? updatedResourceArray.length,
        component_type: "general_resources",
      },
      current_affairs: {
        no_of_items: currentAffairsItem ?? 0,
        component_type: "current_affairs",
      },
      subjects: {
        subjects: [],
        no_of_items: subjectsItem ?? 0,
        component_type: "subjects",
      },
      exams: {
        show_exam: haveExam,
      },
    };

    try {
      const result = await updateResources(params as UpdateCustomResource);
      if (result.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.toast_success_title"),
          description: t("successMessages.dashboardUpdated"),
        });
      }
      setUpdateDashboardData(true);
      return Promise.resolve();
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
      return Promise.reject(error);
    }
  };

  const onAddResource = (): void => {
    setEditIndex(null);
    setIsOpenAddDialog(true);
  };

  return (
    <MainLayout>
      <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <h1 className="text-2xl font-semibold tracking-tight">
          {String(t("customDashboard.dashboardCustomization"))}
        </h1>
        <div className="border rounded-md mt-4 p-4 pt-0 bg-[#fff]">
          <h2 className="text-lg mt-4 font-semibold tracking-tight">
            {String(t("customDashboard.currentAffairs"))}
          </h2>
          <div className="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4 items-start md:items-center">
            <div className="text-left pt-4 min-w-[300px]">
              <Label> {String(t("customDashboard.noOfItemsToDisplay"))}</Label>
              <div className="w-full">
                <Input
                  type="number"
                  value={currentAffairsItem ?? ""}
                  onChange={handleCurentAffairsNo}
                  onKeyDown={(e) => {
                    if (e.key === "." || e.key === "e" || e.key === "-") {
                      e.preventDefault();
                    }
                  }}
                />
              </div>
            </div>
          </div>
          <div className="flex justify-end">
            <Button
              type="submit"
              className="bg-[#9FC089]"
              onClick={() => {
                handleSubmit().catch((error) => {
                  console.error("Error submitting resources:", error);
                });
              }}
            >
              {String(t("buttons.submit"))}
            </Button>
          </div>
        </div>
        <div className="mt-4 border rounded-md p-4 pt-0 bg-[#fff]">
          <h2 className="text-lg mt-4 font-semibold tracking-tight">
            {String(t("customDashboard.generalResources"))}
          </h2>
          <>
            <div className="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4 items-start md:items-center">
              <div className="text-left pt-4 min-w-[300px]">
                <Label>{String(t("customDashboard.noOfItemsToDisplay"))}</Label>
                <div className="w-full">
                  <Input
                    type="number"
                    value={generalResourcesItem}
                    onChange={handleGeneralResourceNo}
                    onKeyDown={(e) => {
                      if (e.key === "." || e.key === "e" || e.key === "-") {
                        e.preventDefault();
                      }
                    }}
                  />
                </div>
              </div>
            </div>
            <div className="mt-4 px-12">
              <Carousel className="w-full">
                <CarouselContent>
                  {visibleResources?.map((resource, index) => (
                    <CarouselItem
                      key={resource.resource_id}
                      className="pl-1 basis-1/4 md:basis-1/4"
                    >
                      <div className="p-1">
                        <Card>
                          <CardContent className="flex flex-col w-full h-[280px] p-0 overflow-hidden">
                            <div className="flex justify-between items-center px-2 py-1">
                              {/* Left: file vs. video icon */}
                              {resource?.resource_type.toLowerCase() ===
                              "url" ? (
                                <Video size={16} className="text-gray-600" />
                              ) : (
                                <File size={16} className="text-gray-600" />
                              )}
                              <div className="flex items-center gap-2">
                                <button
                                  className="p-1 rounded hover:bg-gray-200"
                                  onClick={() =>
                                    handleEditDialog(resource, index)
                                  }
                                >
                                  <Pencil
                                    size={16}
                                    className="text-[#fb8500]"
                                  />
                                </button>
                                <button
                                  className="p-1 rounded hover:bg-gray-200"
                                  onClick={() => handleDeleteDialog(resource)}
                                >
                                  <Trash size={16} className="text-red-500" />
                                </button>
                              </div>
                            </div>

                            <div
                              className="flex-3 flex-grow bg-cover bg-center"
                              style={{
                                backgroundImage: `url(${resource.thumbnail_url})`,
                              }}
                            />
                            <div className="bg-white flex items-center justify-center text-center px-2 py-6">
                              <span className="text-sm font-medium text-black truncate">
                                {resource.resource_name}
                              </span>
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                    </CarouselItem>
                  ))}
                  <CarouselItem className="pl-1 basis-1/4 md:basis-1/4">
                    <div className="p-1">
                      <Card>
                        <CardContent className="flex flex-col w-full h-[280px] p-0">
                          <div className="flex flex-col justify-center items-center h-full">
                            <button
                              onClick={() => onAddResource()}
                              className="text-white bg-[#9FC089] px-4 py-2 rounded-md hover:bg-[#8AAD76]"
                            >
                              {String(t("buttons.addResource"))}
                            </button>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </CarouselItem>
                </CarouselContent>
                <CarouselPrevious />
                <CarouselNext />
              </Carousel>
            </div>
          </>
          <div className=" mt-2 flex justify-end">
            <Button
              type="submit"
              className="bg-[#9FC089]"
              onClick={() => {
                handleSubmit().catch((error) => {
                  console.error("Error submitting resources:", error);
                });
              }}
            >
              {String(t("buttons.submit"))}
            </Button>
          </div>
        </div>
        <div className="border rounded-md mt-4 p-4 pt-0 bg-[#fff]">
          <h2 className="text-lg mt-4 font-semibold tracking-tight">
            {String(t("customDashboard.assignments"))}
          </h2>
          <div className="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4 items-start md:items-center">
            <div className="text-left pt-4 min-w-[300px]">
              <Label>{String(t("customDashboard.noOfItemsToDisplay"))}</Label>
              <div className="w-full">
                <Input
                  type="number"
                  value={assignmentsItem}
                  onChange={handleAssignmentsNo}
                  onKeyDown={(e) => {
                    if (e.key === "." || e.key === "e" || e.key === "-") {
                      e.preventDefault();
                    }
                  }}
                />
              </div>
            </div>
          </div>
          <div className="flex justify-end">
            <Button
              type="submit"
              className="bg-[#9FC089]"
              onClick={() => {
                handleSubmit().catch((error) => {
                  console.error("Error submitting resources:", error);
                });
              }}
            >
              {String(t("buttons.submit"))}
            </Button>
          </div>
        </div>
        <div className="mt-4 border rounded-md p-4 pt-0 bg-[#fff]">
          <h2 className="text-lg mt-4 font-semibold tracking-tight">
            {String(t("customDashboard.subjects"))}
          </h2>
          <div className="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4 items-start md:items-center">
            <div className="text-left pt-4 min-w-[300px]">
              <Label>{String(t("customDashboard.noOfItemsToDisplay"))}</Label>
              <div className="w-full">
                <Input
                  type="number"
                  value={subjectsItem}
                  onChange={handleSubjectsNo}
                  onKeyDown={(e) => {
                    if (e.key === "." || e.key === "e" || e.key === "-") {
                      e.preventDefault();
                    }
                  }}
                />
              </div>
            </div>
          </div>
          <div className="flex justify-end">
            <Button
              type="submit"
              className="bg-[#9FC089]"
              onClick={() => {
                handleSubmit().catch((error) => {
                  console.error("Error submitting resources:", error);
                });
              }}
            >
              {String(t("buttons.submit"))}
            </Button>
          </div>
        </div>
        <div className="mt-4 border rounded-md p-4 pt-0 bg-[#fff]">
          <h2 className="text-lg mt-4 font-semibold tracking-tight">
            {" "}
            {String(t("customDashboard.exams"))}
          </h2>
          <div className="mt-4 flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4 items-start md:items-center">
            <RadioGroup
              defaultValue="false"
              value={haveExam === 1 ? "true" : "false"}
              onValueChange={(val) => setHaveExam(val === "true" ? 1 : 0)}
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="true" id="r1" />
                <Label htmlFor="r1"> {String(t("customDashboard.true"))}</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="false" id="r2" />
                <Label htmlFor="r2">
                  {" "}
                  {String(t("customDashboard.false"))}
                </Label>
              </div>
            </RadioGroup>
          </div>
          <div className="flex justify-end">
            <Button
              type="submit"
              className="bg-[#9FC089]"
              onClick={() => {
                handleSubmit().catch((error) => {
                  console.error("Error submitting resources:", error);
                });
              }}
            >
              {String(t("buttons.submit"))}
            </Button>
          </div>
        </div>
        {(isOpenEditDialog || isOpenAddDialog) && (
          <Modal
            title={` ${
              isOpenAddDialog
                ? t("customDashboard.addResource")
                : t("customDashboard.editResource")
            }`}
            header=""
            openDialog={isOpenEditDialog || isOpenAddDialog}
            closeDialog={closeEditDialog}
            type="max-w-6xl"
          >
            <EditResources
              onCancel={closeEditDialog}
              setVisibleResources={setVisibleResources}
              setResourceLibrary={setResourceLibrary}
              visibleResources={visibleResources}
              editIndex={editIndex}
              onSubmit={handleResourceUpdate}
            />
          </Modal>
        )}
        {isOpenDeleteDialog && (
          <Modal
            title={String(t("customDashboard.deleteResource"))}
            header=""
            openDialog={isOpenDeleteDialog}
            closeDialog={closeDeleteDialog}
            type="max-w-5xl"
          >
            <DeleteCustomResources
              onCancel={closeDeleteDialog}
              resourceId={deleteId}
              onDeleteSuccess={handleDeleteSuccess}
              totalResources={visibleResources?.length}
              noOfgeneralResourcesItems={generalResourcesItem as number}
            />
          </Modal>
        )}
      </div>
    </MainLayout>
  );
}
