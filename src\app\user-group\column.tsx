"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type {
  UserGroup,
  UserGroupRowDefinition,
} from "@/types";

export const getColumns = (
  t: (key: string) => string,
): ColumnDef<UserGroup>[] => [
  {
    accessorKey: "first_name",
    header:t("groups.userGroup.user"),
    cell: ({ row }: UserGroupRowDefinition): React.JSX.Element => {
      const firstName = row.original.first_name ?? " ";
      const lastName = row.original.last_name ?? " ";
      return <div className="text-align">{`${firstName} ${lastName}`}</div>;
    },
  },
  {
    accessorKey: "email",
    header:t("groups.userGroup.columns.email"),
    cell: ({ row }: UserGroupRowDefinition): React.JSX.Element => (
      <div className="text-align">{row.original.email}</div>
    ),
  },
];
