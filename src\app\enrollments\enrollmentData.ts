const enrollmentData = [
  {
    enrolled_date: "2023-09-13T09:41:34.745738",
    first_name: "<PERSON><PERSON><PERSON>",
    id: "9140d1fb-7f65-44ea-a1ca-1c5c31713dae",
    last_name: "<PERSON><PERSON>",
  },
  {
    enrolled_date: "2023-09-21T12:34:03.000541",
    first_name: "<PERSON><PERSON>",
    id: "e8ee0bc8-ed01-415b-b09e-167c58c8c36e",
    last_name: "<PERSON>",
  },
  {
    enrolled_date: "2023-09-20T04:53:27.014759",
    first_name: "<PERSON>",
    id: "42e3b175-5342-4cdd-a47b-ffdf98b65bf0",
    last_name: "<PERSON> <PERSON>",
  },
  {
    enrolled_date: "2023-09-20T04:53:27.014759",
    first_name: "<PERSON>",
    id: "294fa5f0-d3c1-407e-8736-fbe3<PERSON><PERSON><PERSON><PERSON>",
    last_name: "<PERSON>",
  },
  {
    enrolled_date: "2023-09-22T05:49:36.773805",
    first_name: "<PERSON><PERSON><PERSON>",
    id: "eec8ac3f-5275-4988-91b6-20da3055<PERSON><PERSON>",
    last_name: "<PERSON>ai",
  },
  {
    enrolled_date: "2023-09-01T11:28:22.457281",
    first_name: "Meera",
    id: "9fed1801-6b2b-423d-95a8-9be2dcbae1c8",
    last_name: "Catherine",
  },
  {
    enrolled_date: "2023-09-01T11:35:56.363739",
    first_name: "Priya",
    id: "2dc53231-5712-4039-8532-2553b96e7659",
    last_name: "Anoop ",
  },
  {
    enrolled_date: "2023-09-19T12:22:05.485457",
    first_name: "Riya",
    id: "c61f2f8e-7370-41c3-9844-e1297a5593b6",
    last_name: "Sandra Admin",
  },
  {
    enrolled_date: "2023-09-19T12:22:05.485457",
    first_name: "Riya sandra",
    id: "338a3019-aa95-4d22-8c79-9550c1c4d1e4",
    last_name: "Sandra riya",
  },
  {
    enrolled_date: "2023-09-13T09:42:39.198798",
    first_name: "Samantha",
    id: "de8f3d5d-d232-4822-b69e-461b989bf9e5",
    last_name: "S",
  },
  {
    enrolled_date: "2023-09-08T11:08:49.802216",
    first_name: "Sandra",
    id: "31c78a14-4c4d-4a93-a9a3-e7c05468b307",
    last_name: "Test",
  },
  {
    enrolled_date: "2023-09-08T11:08:49.802216",
    first_name: "Sandra",
    id: "0d39fc8a-acb1-4989-8e45-d13695384124",
    last_name: "Sebastian",
  },
  {
    enrolled_date: "2023-09-08T11:08:49.802216",
    first_name: "Sandra",
    id: "2da586b8-65a9-4edf-887e-ddb74989209b",
    last_name: "Sebastian",
  },
  {
    enrolled_date: "2023-09-08T11:08:49.802216",
    first_name: "Sandra",
    id: "c8741acd-27e0-43b1-aa16-df525bd55c70",
    last_name: "S",
  },
  {
    enrolled_date: "2023-09-08T11:08:49.802216",
    first_name: "Sandra",
    id: "40b7d2d2-1b69-4ce6-b640-28a0a4a7e806",
    last_name: "Jino",
  },
  {
    enrolled_date: "2023-09-08T11:08:49.802216",
    first_name: "Sandra Maria",
    id: "84ecccc0-e425-431c-b2f1-91795d0bfdac",
    last_name: "sebastian",
  },
  {
    enrolled_date: "2023-08-31T08:59:16.274483",
    first_name: "Sandra S",
    id: "84221af2-9875-4af2-b309-52b4a0eaa33f",
    last_name: "Sebastian",
  },
  {
    enrolled_date: "2023-09-19T09:40:46.653516",
    first_name: "Sanjana Mathew",
    id: "8bd46a7f-f093-4add-b3ad-8be9b7017ad0",
    last_name: "s",
  },
  {
    enrolled_date: "2023-09-06T12:09:40.332242",
    first_name: "Surya",
    id: "a46c7ae8-7661-43f1-bcc3-1b00cb145932",
    last_name: "Leksh",
  },
  {
    enrolled_date: "2023-09-04T05:49:20.272693",
    first_name: "Vineetha",
    id: "7b0951f9-633a-4673-8246-ba5ac1142d8f",
    last_name: "K",
  },

  {
    enrolled_date: "2023-09-13T04:45:46.7218",
    first_name: "Winny",
    id: "2c1e31c8-dcf7-4773-a67b-c39ab4bc29bd",
    last_name: "Baby",
  },
];

export default enrollmentData;
