"use client";

import type { ColumnDef } from "@tanstack/react-table";
import { ArrowUpDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import type {
  PrivilegeGroup,
  PrivilegeGroupColumnDefinition,
  PrivilegeGroupRowDefinition,
} from "@/types";

export const getColumns = (
  t: (key: string) => string
): ColumnDef<PrivilegeGroup>[] => [
  {
    accessorKey: "name",
    header: ({ column }: PrivilegeGroupColumnDefinition): React.JSX.Element => {
      return (
        <Button
          className="px-0"
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          {t("groups.groupPrivileges.columns.name")}
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }: PrivilegeGroupRowDefinition): React.JSX.Element => (
      <div className="text-align">{row.original?.name}</div>
    ),
  },
  {
    accessorKey: "privilege_key",
    header: t("groups.groupPrivileges.columns.key"),
    cell: ({ row }: PrivilegeGroupRowDefinition): React.JSX.Element => {
      return <div className="text-align">{row.original?.privilege_key}</div>;
    },
  },
  {
    accessorKey: "privilege_description",
    header: t("groups.groupPrivileges.columns.description"),
    cell: ({ row }: PrivilegeGroupRowDefinition): React.JSX.Element => {
      return (
        <div className="text-align">{row.original?.privilege_description}</div>
      );
    },
  },
];
