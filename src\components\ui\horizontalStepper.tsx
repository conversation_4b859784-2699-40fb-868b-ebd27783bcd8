import React, { type ReactNode, useState, useEffect } from "react";
import "./../../styles/main.css";
import { useTranslation } from "react-i18next";

interface HorizontalStepperProps {
  stepperData: {
    stepNo: number;
    StepLabel: string;
    stepValue: {
      value: string;
      label: string;
    }[];
  };
  stepLabel: string[];
  component: ReactNode;
  onStepChange: (step: number) => void; // Define a prop for the callback function
  stepIcons?: React.JSX.Element[];
  stepInstance?: string[];
  onStepInstance?: (stepType: string, stepInstance: string) => void;
  stepType?: string[];
}

const HorizontalStepper: React.FC<HorizontalStepperProps> = ({
  stepperData,

  component,
  onStepChange,
  stepLabel,
  stepIcons,
  stepInstance,
  onStepInstance,
  stepType,
}) => {
  const {t}= useTranslation();
  const { stepNo } = stepperData;

  const [currentStep, setCurrentStep] = useState(0);
  const [stepsIndex, setStepsIndex] = useState<number>();

  useEffect(() => {
    handleStepClick(0);
  }, []);

  const handleStepClick = (stepIndex: number): void => {
    setStepsIndex(stepIndex as number);
    if (stepType && stepInstance) {
      onStepInstance?.(stepType?.[stepIndex], stepInstance?.[stepIndex]);
    }

    setCurrentStep(stepIndex);
    onStepChange(stepIndex); // Call the callback function
  };

  const handlePrevClick = (): void => {
    setCurrentStep(Math.max(currentStep - 1, 0));
    handleStepClick(Math.max(currentStep - 1, 0));
  };

  const handleNextClick = (): void => {
    setCurrentStep(Math.min(currentStep + 1, stepNo - 1));
    handleStepClick(Math.min(currentStep + 1, stepNo - 1));
  };

  return (
    <div>
      <div
        className={
          stepNo < 6
            ? "w-full max-w-lg overflow-x-auto p-4 space-y-4"
            : "w-full overflow-x-auto p-4 space-y-4"
        }
      >
        <div className="flex ">
          {Array.from({ length: stepNo }, (_, index) => (
            <>
              <div key={index} className="flex w-full items-center -mr-2">
                <div
                  onClick={() => handleStepClick(index)}
                  className={`step-number ${
                    currentStep === index
                      ? "items-center justify-center w-10 h-10 text-blue-600 dark:text-blue-500 bg-white border-2 border-blue-600 rounded-full lg:h-12 lg:w-12 dark:bg-blue-800 shrink-0 cursor-pointer"
                      : index < currentStep
                        ? "items-center justify-center w-10 h-10 text-white dark:text-white bg-blue-600 border-2 border-blue-600 rounded-full lg:h-12 lg:w-12 dark:bg-white shrink-0 cursor-pointer"
                        : "bg-gray-300 text-indigo-700"
                  }`}
                >
                  {index + 1}
                </div>
                {index < stepNo - 1 && (
                  <div className="flex-grow h-0.5 bg-blue-600 dark:bg-blue-600"></div>
                )}
                <div
                  onClick={() => handleStepClick(index)}
                  className="flex items-center  cursor-pointer"
                >
                  <div className="mr-2">{stepIcons?.[index]}</div>
                  <div>{stepLabel[index]}</div>
                </div>
              </div>
            </>
          ))}
        </div>

        <div className="flex justify-between">
          <button
            onClick={handlePrevClick}
            disabled={currentStep === 0}
            className={`px-4 py-2 ${
              currentStep === 0
                ? "button-disabled"
                : "hover:button-active bg-blue-400 text-white"
            } rounded`}
          >
            {t("exams.prev")}
          </button>
          <button
            onClick={handleNextClick}
            disabled={currentStep === stepNo - 1}
            className={`px-4 py-2 ${
              currentStep === stepNo - 1
                ? "button-disabled"
                : " hover:button-active bg-blue-400 text-white"
            } rounded`}
          >
            {t("exams.next")}
          </button>
        </div>
        <div className="flex"></div>
      </div>
      <div className={`${currentStep === stepsIndex ? "show" : "hidden"}`}>
        {component}
      </div>
    </div>
  );
};

export default HorizontalStepper;
