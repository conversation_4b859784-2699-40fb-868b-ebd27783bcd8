"use client";

import type { ColumnDef, Row } from "@tanstack/react-table";
import React from "react";
import type { CheckPointList } from "@/types";

interface RowDefinition {
  row: Row<CheckPointList>;
}

export const getColumns = (
  t: (key: string) => string,
): ColumnDef<CheckPointList>[] => [
  {
    accessorKey: "sequence_number",
    header: t("courses.courseModule.seqNo"),
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div className="text-center">{row.original.sequence_number}</div>
    ),
  },
  {
    accessorKey: "checkpoint_name",
    header: t("courses.courseModule.name"),
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div>{row.original.checkpoint_name}</div>
    ),
  },
  {
    accessorKey: "checkpoint_startTime",
    header: t("courses.courseModule.startTime"),
    cell: ({ row }: RowDefinition): React.JSX.Element => {
      const checkPointTime = `${
        row.original.checkpoint_startTime?.HH !== undefined &&
        row.original.checkpoint_startTime.HH !== ""
          ? row.original.checkpoint_startTime.HH.length === 1
            ? `0${row.original.checkpoint_startTime.HH}`
            : row.original.checkpoint_startTime.HH
          : "00"
      }:${
        row.original.checkpoint_startTime?.MM !== undefined &&
        row.original.checkpoint_startTime.MM !== ""
          ? row.original.checkpoint_startTime.MM.length === 1
            ? `0${row.original.checkpoint_startTime.MM}`
            : row.original.checkpoint_startTime.MM
          : "00"
      }:${
        row.original.checkpoint_startTime?.SS !== undefined &&
        row.original.checkpoint_startTime.SS !== ""
          ? row.original.checkpoint_startTime.SS.length === 1
            ? `0${row.original.checkpoint_startTime.SS}`
            : row.original.checkpoint_startTime.SS
          : "00"
      }`;

      return <div>{checkPointTime}</div>;
    },
  },

  {
    accessorKey: "isMandatory",
    header: t("courses.courseModule.mandatory"),
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div>{row.original.isMandatory}</div>
    ),
  },
  // {
  //   accessorKey: "checkpoint_reslabel",
  //   header: "Exam Name",
  //   cell: ({ row }: RowDefinition): React.JSX.Element => (
  //     <div>{row.original.checkpoint_reslabel}</div>
  //   ),
  // },
];
