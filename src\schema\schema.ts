import { z } from "zod";
import moment from "moment";
import {
  ADD_CATEGORY_IN_COURSE_MSG_CATEGORY_TYPE,
  ADD_CATEGORY_IN_COURSE_MSG_DESCRIPTION,
  ADD_CATEGORY_IN_COURSE_MSG_NAME,
  ADD_CATEGORY_IN_COURSE_MSG_TOPIC,
  ADD_EXAM_SCHEMA_DATE_FORMAT,
  ADD_EXAM_SCHEMA_ERROR_MSG_ALLOWED_ATTEMPTS,
  ADD_EXAM_SCHEMA_ERROR_MSG_DESCRIPTION,
  ADD_EXAM_SCHEMA_ERROR_MSG_DURATION,
  ADD_EXAM_SCHEMA_ERROR_MSG_END_TIME,
  ADD_EXAM_SCHEMA_ERROR_MSG_NAME,
  ADD_EXAM_SCHEMA_ERROR_MSG_NUMBER,
  ADD_EXAM_SCHEMA_ERROR_MSG_NUM_OF_QUESTIONS,
  ADD_EXAM_SCHEMA_ERROR_MSG_PASS_MARK,
  ADD_EXAM_SCHEMA_ERROR_MSG_QUIZ_TYPE,
  ADD_EXAM_SCHEMA_ERROR_MSG_START_TIME,
  ADD_EXAM_SCHEMA_ERROR_MSG_TOTAL_MARK,
  ADD_EXAM_SCHEMA_MSG_ONE_END_TIME,
  ADD_EXAM_SCHEMA_MSG_ONE_START_TIME,
  ADD_EXAM_SCHEMA_MSG_TWO_END_TIME,
  ADD_EXAM_SCHEMA_MSG_TWO_START_TIME,
  ADD_EXAM_SCHEMA_TIME_FORMAT,
} from "@/lib/constants";

export const ResetPasswordSchema = z
  .object({
    newPassword: z
      .string({
        required_error: "Please enter your new password",
      })
      .min(8, "Password must be atleast 8 characters long")
      .regex(
        /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[@$!%*?&#+])[A-Za-z\d@$!%*?&#+]{8,}$/,
        "Password must include at least one uppercase letter, one lowercase letter, one number, and one special character.",
      ),
    confirmPassword: z
      .string({
        required_error: "Please enter your password for confirmation",
      })
      .min(8, "Password must be atleast 8 characters long")
      .regex(
        /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[@$!%*?&#+])[A-Za-z\d@$!%*?&#+]{8,}$/,
        "Password must include at least one uppercase letter, one lowercase letter, one number, and one special character.",
      ),
  })
  .refine(
    (data) => {
      return data.newPassword === data.confirmPassword;
    },
    {
      message: "Oops! Passwords doesn't match",
      path: ["confirmPassword"],
    },
  );

export const FormSchema = z.object({
  dateOnly: z
    .object({
      calendar: z.object({ identifier: z.string() }),
      era: z.string(),
      year: z.number(),
      month: z.number(),
      day: z.number(),
      hour: z.number().optional(),
      minute: z.number().optional(),
    })
    .refine(
      (endDate) => {
        const d = new Date(
          endDate.year,
          endDate.month - 1,
          endDate.day,
          endDate.hour,
          endDate.minute,
        );

        const selectedDate = moment(d).format("DD/MM/YYYY");
        const currentDate = moment(new Date()).format("DD/MM/YYYY");

        return selectedDate >= currentDate;
      },
      {
        message: "Start date must be greater than or equal the current date",
      },
    )
    .refine(
      (endDate) => {
        const d = new Date(
          endDate.year,
          endDate.month - 1,
          endDate.day,
          endDate.hour,
          endDate.minute,
        );

        const selectedDate = moment(d).format("DD/MM/YYYY");
        const currentDate = moment(new Date()).format("DD/MM/YYYY");

        const selectedTime = moment(d).format("HH:mm");
        const currentTime = moment(new Date()).format("HH:mm");

        return selectedDate == currentDate
          ? selectedTime > currentTime
          : selectedDate >= currentDate;
      },
      {
        message: "Please provide a valid time. You have entered a past time",
      },
    ),
});

export const LoginFormSchema = z.object({
  email: z
    .string({
      required_error: "Please enter your email id",
    })
    .min(8),
  password: z
    .string({
      required_error: "Please enter your password",
    })
    .min(4),
});

export const AddNewEnrollmentSchema = z.object({
  course_id: z.string({
    required_error: "Please select a course",
  }),
});

export const AddCategoryInCourseSchema = z.object({
  name: z
    .string({
      required_error: ADD_CATEGORY_IN_COURSE_MSG_NAME,
    })
    .trim()
    .min(1),
  description: z
    .string({
      required_error: ADD_CATEGORY_IN_COURSE_MSG_DESCRIPTION,
    })
    .trim()
    .min(1),
  is_premium: z.boolean().default(false),
  topic: z.string({
    required_error: ADD_CATEGORY_IN_COURSE_MSG_TOPIC,
  }),
  category_type: z.string({
    required_error: ADD_CATEGORY_IN_COURSE_MSG_CATEGORY_TYPE,
  }),
});

export const ExamAddResourceSchema = z.object({
  main_topic: z.string({ required_error: "Please select category" }),
  folder_id: z.string({ required_error: "Please select folder" }),
  quiz_type: z.string({
    required_error: ADD_EXAM_SCHEMA_ERROR_MSG_QUIZ_TYPE,
  }),
  name: z
    .string({
      required_error: ADD_EXAM_SCHEMA_ERROR_MSG_NAME,
    })
    .min(8)
    .max(100, "Name must be at most 150 characters long"),
  num_of_questions: z.preprocess(
    (value) => {
      if (typeof value === "string" && value.trim() !== "") {
        return Number(value);
      }
      return value;
    },
    z
      .number({
        required_error: ADD_EXAM_SCHEMA_ERROR_MSG_NUM_OF_QUESTIONS,
      })
      .min(1, { message: "Number of questions must be at least 1" }),
  ),
  total_mark: z.preprocess(
    (value) => {
      if (typeof value === "string" && value.trim() !== "") {
        return Number(value);
      }
      return value;
    },
    z
      .number({
        required_error: ADD_EXAM_SCHEMA_ERROR_MSG_TOTAL_MARK,
      })
      .min(1),
  ),
  pass_mark: z.preprocess(
    (value) => {
      if (typeof value === "string" && value.trim() !== "") {
        return Number(value);
      }
      return value;
    },
    z
      .number({
        required_error: ADD_EXAM_SCHEMA_ERROR_MSG_PASS_MARK,
      })
      .min(1)
      .refine((value) => value >= 0, {
        message: ADD_EXAM_SCHEMA_ERROR_MSG_NUMBER,
      }),
  ),
  duration: z.preprocess(
    (value) => {
      if (typeof value === "string" && value.trim() !== "") {
        return Number(value);
      }
      return value;
    },
    z
      .number({
        required_error: ADD_EXAM_SCHEMA_ERROR_MSG_DURATION,
      })
      .min(1)
      .refine((value) => value >= 0, {
        message: ADD_EXAM_SCHEMA_ERROR_MSG_NUMBER,
      }),
  ),
  allowed_attempts: z.coerce
    .number({
      required_error: ADD_EXAM_SCHEMA_ERROR_MSG_ALLOWED_ATTEMPTS,
    })
    .min(1)
    .default(1)
    .refine((value) => value >= 0, {
      message: ADD_EXAM_SCHEMA_ERROR_MSG_NUMBER,
    }),
  description: z
    .string({
      required_error: ADD_EXAM_SCHEMA_ERROR_MSG_DESCRIPTION,
    })
    .trim()
    .min(1),
});

export const AddExamSchema = z.object({
  main_topic: z.string({ required_error: "Please select category" }),
  course_id: z.string({ required_error: "Please select course" }),
  section_id: z.string({ required_error: "Please select section" }),
  quiz_type: z.string({
    required_error: ADD_EXAM_SCHEMA_ERROR_MSG_QUIZ_TYPE,
  }),
  name: z
    .string({
      required_error: ADD_EXAM_SCHEMA_ERROR_MSG_NAME,
    })
    .min(8)
    .max(100, "Name must be at most 150 characters long"),
  num_of_questions: z.preprocess(
    (value) => {
      if (typeof value === "string" && value.trim() !== "") {
        return Number(value);
      }
      return value;
    },
    z
      .number({
        required_error: ADD_EXAM_SCHEMA_ERROR_MSG_NUM_OF_QUESTIONS,
      })
      .min(1, { message: "Number of questions must be at least 1" }),
  ),
  total_mark: z.preprocess(
    (value) => {
      if (typeof value === "string" && value.trim() !== "") {
        return Number(value);
      }
      return value;
    },
    z
      .number({
        required_error: ADD_EXAM_SCHEMA_ERROR_MSG_TOTAL_MARK,
      })
      .min(1),
  ),
  pass_mark: z.preprocess(
    (value) => {
      if (typeof value === "string" && value.trim() !== "") {
        return Number(value);
      }
      return value;
    },
    z
      .number({
        required_error: ADD_EXAM_SCHEMA_ERROR_MSG_PASS_MARK,
      })
      .min(1)
      .refine((value) => value >= 0, {
        message: ADD_EXAM_SCHEMA_ERROR_MSG_NUMBER,
      }),
  ),
  duration: z.preprocess(
    (value) => {
      if (typeof value === "string" && value.trim() !== "") {
        return Number(value);
      }
      return value;
    },
    z
      .number({
        required_error: ADD_EXAM_SCHEMA_ERROR_MSG_DURATION,
      })
      .min(1)
      .refine((value) => value >= 0, {
        message: ADD_EXAM_SCHEMA_ERROR_MSG_NUMBER,
      }),
  ),
  allowed_attempts: z.coerce
    .number({
      required_error: ADD_EXAM_SCHEMA_ERROR_MSG_ALLOWED_ATTEMPTS,
    })
    .min(1)
    .default(1)
    .refine((value) => value >= 0, {
      message: ADD_EXAM_SCHEMA_ERROR_MSG_NUMBER,
    }),
  start_time: z
    .object(
      {
        calendar: z.object({ identifier: z.string() }),
        era: z.string(),
        year: z.number(),
        month: z.number(),
        day: z.number(),
        hour: z.number().optional(),
        minute: z.number().optional(),
      },
      {
        required_error: ADD_EXAM_SCHEMA_ERROR_MSG_START_TIME,
      },
    )
    .refine(
      (endDate) => {
        const d = new Date(
          endDate.year,
          endDate.month - 1,
          endDate.day,
          endDate.hour,
          endDate.minute,
        );

        const selectedDate = moment(d).format(ADD_EXAM_SCHEMA_DATE_FORMAT);
        const currentDate = moment(new Date()).format(
          ADD_EXAM_SCHEMA_DATE_FORMAT,
        );

        const selectedDateAsDate = moment(
          selectedDate,
          ADD_EXAM_SCHEMA_DATE_FORMAT,
        ).toDate();
        const currentDateAsDate = moment(
          currentDate,
          ADD_EXAM_SCHEMA_DATE_FORMAT,
        ).toDate();

        return selectedDateAsDate >= currentDateAsDate;
      },
      {
        message: ADD_EXAM_SCHEMA_MSG_ONE_START_TIME,
      },
    )
    .refine(
      (endDate) => {
        const d = new Date(
          endDate.year,
          endDate.month - 1,
          endDate.day,
          endDate.hour,
          endDate.minute,
        );

        const selectedDate = moment(d).format(ADD_EXAM_SCHEMA_DATE_FORMAT);
        const currentDate = moment(new Date()).format(
          ADD_EXAM_SCHEMA_DATE_FORMAT,
        );

        const selectedTime = moment(d).format(ADD_EXAM_SCHEMA_TIME_FORMAT);
        const currentTime = moment(new Date()).format(
          ADD_EXAM_SCHEMA_TIME_FORMAT,
        );

        const selectedDateAsDate = moment(
          selectedDate,
          ADD_EXAM_SCHEMA_DATE_FORMAT,
        ).toDate();
        const currentDateAsDate = moment(
          currentDate,
          ADD_EXAM_SCHEMA_DATE_FORMAT,
        ).toDate();

        if (selectedDateAsDate < currentDateAsDate) {
          return false;
        } else if (selectedDateAsDate > currentDateAsDate) {
          return true;
        } else {
          return selectedTime > currentTime;
        }
      },
      {
        message: ADD_EXAM_SCHEMA_MSG_TWO_START_TIME,
      },
    ),
  end_time: z
    .object(
      {
        calendar: z.object({ identifier: z.string() }),
        era: z.string(),
        year: z.number(),
        month: z.number(),
        day: z.number(),
        hour: z.number().optional(),
        minute: z.number().optional(),
      },
      {
        required_error: ADD_EXAM_SCHEMA_ERROR_MSG_END_TIME,
      },
    )
    .refine(
      (endDate) => {
        const d = new Date(
          endDate.year,
          endDate.month - 1,
          endDate.day,
          endDate.hour,
          endDate.minute,
        );

        const selectedDate = moment(d).format(ADD_EXAM_SCHEMA_DATE_FORMAT);
        const currentDate = moment(new Date()).format(
          ADD_EXAM_SCHEMA_DATE_FORMAT,
        );

        const selectedDateAsDate = moment(
          selectedDate,
          ADD_EXAM_SCHEMA_DATE_FORMAT,
        ).toDate();
        const currentDateAsDate = moment(
          currentDate,
          ADD_EXAM_SCHEMA_DATE_FORMAT,
        ).toDate();

        return selectedDateAsDate >= currentDateAsDate;
        // const selectedDate = moment(d);
        // const currentDate = moment();

        // return selectedDate.isAfter(currentDate);
      },
      {
        message: ADD_EXAM_SCHEMA_MSG_ONE_END_TIME,
      },
    )
    .refine(
      (endDate) => {
        const d = new Date(
          endDate.year,
          endDate.month - 1,
          endDate.day,
          endDate.hour,
          endDate.minute,
        );

        const selectedDate = moment(d).format(ADD_EXAM_SCHEMA_DATE_FORMAT);
        const currentDate = moment(new Date()).format(
          ADD_EXAM_SCHEMA_DATE_FORMAT,
        );

        const selectedTime = moment(d).format(ADD_EXAM_SCHEMA_TIME_FORMAT);
        const currentTime = moment(new Date()).format(
          ADD_EXAM_SCHEMA_TIME_FORMAT,
        );

        const selectedDateAsDate = moment(
          selectedDate,
          ADD_EXAM_SCHEMA_DATE_FORMAT,
        ).toDate();
        const currentDateAsDate = moment(
          currentDate,
          ADD_EXAM_SCHEMA_DATE_FORMAT,
        ).toDate();

        if (selectedDateAsDate < currentDateAsDate) {
          return false;
        } else if (selectedDateAsDate > currentDateAsDate) {
          return true;
        } else {
          return selectedTime > currentTime;
        }
      },
      {
        message: ADD_EXAM_SCHEMA_MSG_TWO_END_TIME,
      },
    ),
  description: z
    .string({
      required_error: ADD_EXAM_SCHEMA_ERROR_MSG_DESCRIPTION,
    })
    .trim()
    .min(1),
});
export const EditExamSchema = z.object({
  quiz_type: z.string({
    required_error: ADD_EXAM_SCHEMA_ERROR_MSG_QUIZ_TYPE,
  }),
  name: z
    .string({
      required_error: ADD_EXAM_SCHEMA_ERROR_MSG_NAME,
    })
    .min(8)
    .max(100, "Name must be at most 150 characters long"),
  num_of_questions: z
    .number({
      required_error: ADD_EXAM_SCHEMA_ERROR_MSG_NUM_OF_QUESTIONS,
    })
    .min(1),
  total_mark: z.coerce.number({
    required_error: ADD_EXAM_SCHEMA_ERROR_MSG_TOTAL_MARK,
  }),
  // .refine((value) => value >= 0 && value <= 180, {
  //   message: ADD_EXAM_SCHEMA_ERROR_MSG_NUMBER,
  // }),
  pass_mark: z.coerce
    .number({
      required_error: ADD_EXAM_SCHEMA_ERROR_MSG_PASS_MARK,
    })
    .refine((value) => value >= 0, {
      message: ADD_EXAM_SCHEMA_ERROR_MSG_NUMBER,
    }),
  duration: z.coerce
    .number({
      required_error: ADD_EXAM_SCHEMA_ERROR_MSG_DURATION,
    })
    .refine((value) => value >= 0, {
      message: ADD_EXAM_SCHEMA_ERROR_MSG_NUMBER,
    }),
  allowed_attempts: z.coerce
    .number({
      required_error: ADD_EXAM_SCHEMA_ERROR_MSG_ALLOWED_ATTEMPTS,
    })
    .default(1)
    .refine((value) => value >= 0, {
      message: ADD_EXAM_SCHEMA_ERROR_MSG_NUMBER,
    }),
  start_time: z
    .object(
      {
        calendar: z.object({ identifier: z.string() }),
        era: z.string(),
        year: z.number(),
        month: z.number(),
        day: z.number(),
        hour: z.number().optional(),
        minute: z.number().optional(),
      },
      {
        required_error: ADD_EXAM_SCHEMA_ERROR_MSG_START_TIME,
      },
    )
    .refine(
      (endDate) => {
        const d = new Date(
          endDate.year,
          endDate.month - 1,
          endDate.day,
          endDate.hour,
          endDate.minute,
        );

        const selectedDate = moment(d).format(ADD_EXAM_SCHEMA_DATE_FORMAT);
        const currentDate = moment(new Date()).format(
          ADD_EXAM_SCHEMA_DATE_FORMAT,
        );

        const selectedDateAsDate = moment(
          selectedDate,
          ADD_EXAM_SCHEMA_DATE_FORMAT,
        ).toDate();
        const currentDateAsDate = moment(
          currentDate,
          ADD_EXAM_SCHEMA_DATE_FORMAT,
        ).toDate();

        return selectedDateAsDate >= currentDateAsDate;
      },
      {
        message: ADD_EXAM_SCHEMA_MSG_ONE_START_TIME,
      },
    )
    .refine(
      (endDate) => {
        const d = new Date(
          endDate.year,
          endDate.month - 1,
          endDate.day,
          endDate.hour,
          endDate.minute,
        );

        const selectedDate = moment(d).format(ADD_EXAM_SCHEMA_DATE_FORMAT);
        const currentDate = moment(new Date()).format(
          ADD_EXAM_SCHEMA_DATE_FORMAT,
        );

        const selectedTime = moment(d).format(ADD_EXAM_SCHEMA_TIME_FORMAT);
        const currentTime = moment(new Date()).format(
          ADD_EXAM_SCHEMA_TIME_FORMAT,
        );

        const selectedDateAsDate = moment(
          selectedDate,
          ADD_EXAM_SCHEMA_DATE_FORMAT,
        ).toDate();
        const currentDateAsDate = moment(
          currentDate,
          ADD_EXAM_SCHEMA_DATE_FORMAT,
        ).toDate();

        if (selectedDateAsDate < currentDateAsDate) {
          return false;
        } else if (selectedDateAsDate > currentDateAsDate) {
          return true;
        } else {
          return selectedTime > currentTime;
        }
      },
      {
        message: ADD_EXAM_SCHEMA_MSG_TWO_START_TIME,
      },
    ),
  end_time: z
    .object(
      {
        calendar: z.object({ identifier: z.string() }),
        era: z.string(),
        year: z.number(),
        month: z.number(),
        day: z.number(),
        hour: z.number().optional(),
        minute: z.number().optional(),
      },
      {
        required_error: ADD_EXAM_SCHEMA_ERROR_MSG_END_TIME,
      },
    )
    .refine(
      (endDate) => {
        const d = new Date(
          endDate.year,
          endDate.month - 1,
          endDate.day,
          endDate.hour,
          endDate.minute,
        );

        const selectedDate = moment(d).format(ADD_EXAM_SCHEMA_DATE_FORMAT);
        const currentDate = moment(new Date()).format(
          ADD_EXAM_SCHEMA_DATE_FORMAT,
        );

        const selectedDateAsDate = moment(
          selectedDate,
          ADD_EXAM_SCHEMA_DATE_FORMAT,
        ).toDate();
        const currentDateAsDate = moment(
          currentDate,
          ADD_EXAM_SCHEMA_DATE_FORMAT,
        ).toDate();

        return selectedDateAsDate >= currentDateAsDate;
        // const selectedDate = moment(d);
        // const currentDate = moment();

        // return selectedDate.isAfter(currentDate);
      },
      {
        message: ADD_EXAM_SCHEMA_MSG_ONE_END_TIME,
      },
    )
    .refine(
      (endDate) => {
        const d = new Date(
          endDate.year,
          endDate.month - 1,
          endDate.day,
          endDate.hour,
          endDate.minute,
        );

        const selectedDate = moment(d).format(ADD_EXAM_SCHEMA_DATE_FORMAT);
        const currentDate = moment(new Date()).format(
          ADD_EXAM_SCHEMA_DATE_FORMAT,
        );

        const selectedTime = moment(d).format(ADD_EXAM_SCHEMA_TIME_FORMAT);
        const currentTime = moment(new Date()).format(
          ADD_EXAM_SCHEMA_TIME_FORMAT,
        );

        const selectedDateAsDate = moment(
          selectedDate,
          ADD_EXAM_SCHEMA_DATE_FORMAT,
        ).toDate();
        const currentDateAsDate = moment(
          currentDate,
          ADD_EXAM_SCHEMA_DATE_FORMAT,
        ).toDate();

        if (selectedDateAsDate < currentDateAsDate) {
          return false;
        } else if (selectedDateAsDate > currentDateAsDate) {
          return true;
        } else {
          return selectedTime > currentTime;
        }
      },
      {
        message: ADD_EXAM_SCHEMA_MSG_TWO_END_TIME,
      },
    ),
  description: z
    .string({
      required_error: ADD_EXAM_SCHEMA_ERROR_MSG_DESCRIPTION,
    })
    .trim()
    .min(1),
});

export const AddquestionFormSchema = z.object({
  contenttype: z
    .string({
      required_error: "Please select type",
    })
    .optional(),
  markassigned: z.coerce
    .number({
      required_error: "Please enter value",
    })
    .min(1),
  // answers: z.array(
  //   z.object({
  //     text: z.string().min(1),
  //     type: z.string().min(1),
  //     isAnswer: z.boolean(),
  //   }),
  // ),
  category: z.string({
    required_error: "Please select category",
  }),
  question: z
    .string({
      required_error: "Please enter question",
    })
    .trim()
    .min(1),
});
export const AddCourseSchema = z.object({
  courseStartDate: z
    .object(
      {
        calendar: z.object({ identifier: z.string() }),
        era: z.string(),
        year: z.number(),
        month: z.number(),
        day: z.number(),
        hour: z.number().optional(),
        minute: z.number().optional(),
      },
      {
        required_error: "Please enter course start date",
      },
    )
    .refine(
      (endDate) => {
        const d = new Date(
          endDate.year,
          endDate.month - 1,
          endDate.day,
          endDate.hour,
          endDate.minute,
        );

        const selectedDate = moment(d).format("DD/MM/YYYY");
        // alert(selectedDate)
        const currentDate = moment(new Date()).format("DD/MM/YYYY");
        // alert(currentDate)

        const selectedDateAsDate = moment(selectedDate, "DD/MM/YYYY").toDate();
        const currentDateAsDate = moment(currentDate, "DD/MM/YYYY").toDate();

        return selectedDateAsDate >= currentDateAsDate;
      },
      {
        message: "Start date must be greater than or equal the current date",
      },
    )
    .refine(
      (endDate) => {
        const d = new Date(
          endDate.year,
          endDate.month - 1,
          endDate.day,
          endDate.hour,
          endDate.minute,
        );

        const selectedDate = moment(d).format("DD/MM/YYYY");
        const currentDate = moment(new Date()).format("DD/MM/YYYY");

        const selectedTime = moment(d).format("HH:mm");
        const currentTime = moment(new Date()).format("HH:mm");

        const selectedDateAsDate = moment(selectedDate, "DD/MM/YYYY").toDate();
        const currentDateAsDate = moment(currentDate, "DD/MM/YYYY").toDate();

        if (selectedDateAsDate < currentDateAsDate) {
          return false;
        } else if (selectedDateAsDate > currentDateAsDate) {
          return true;
        } else {
          return selectedTime > currentTime;
        }
      },
      {
        message: "Please provide a valid time. You have entered a past time",
      },
    ),
  courseEndDate: z
    .object(
      {
        calendar: z.object({ identifier: z.string() }),
        era: z.string(),
        year: z.number(),
        month: z.number(),
        day: z.number(),
        hour: z.number().optional(),
        minute: z.number().optional(),
      },
      {
        required_error: "Please enter course end date",
      },
    )
    .refine(
      (endDate) => {
        const d = new Date(
          endDate.year,
          endDate.month - 1,
          endDate.day,
          endDate.hour,
          endDate.minute,
        );

        const selectedDate = moment(d).format("DD/MM/YYYY");
        const currentDate = moment(new Date()).format("DD/MM/YYYY");

        const selectedDateAsDate = moment(selectedDate, "DD/MM/YYYY").toDate();
        const currentDateAsDate = moment(currentDate, "DD/MM/YYYY").toDate();

        return selectedDateAsDate >= currentDateAsDate;
      },
      {
        message: "Start date must be greater than or equal the current date",
      },
    )
    .refine(
      (endDate) => {
        const d = new Date(
          endDate.year,
          endDate.month - 1,
          endDate.day,
          endDate.hour,
          endDate.minute,
        );

        const selectedDate = moment(d).format("DD/MM/YYYY");
        const currentDate = moment(new Date()).format("DD/MM/YYYY");

        const selectedTime = moment(d).format("HH:mm");
        const currentTime = moment(new Date()).format("HH:mm");

        const selectedDateAsDate = moment(selectedDate, "DD/MM/YYYY").toDate();
        const currentDateAsDate = moment(currentDate, "DD/MM/YYYY").toDate();

        if (selectedDateAsDate < currentDateAsDate) {
          return false;
        } else if (selectedDateAsDate > currentDateAsDate) {
          return true;
        } else {
          return selectedTime > currentTime;
        }
      },
      {
        message: "Please provide a valid time. You have entered a past time",
      },
    ),

  courseFullName: z
    .string({
      required_error: "Please enter course full name",
    })
    .trim()
    .min(1)
    .max(50),

  courseShortName: z
    .string({
      required_error: "Please enter course short name",
    })
    .trim()
    .min(1)
    .max(30),
  courseFormat: z.string({
    required_error: "Please select course format",
  }),
  courseCategory: z.string({
    required_error: "Please select course category",
  }),
  courseVisibility: z
    .string({
      required_error: "Please select course visibility",
    })
    .default("yes"),
  courseDescription: z.object(
    {
      htmlValue: z.string(),
    },
    {
      required_error: "Please fill course description",
    },
  ),

  numberOfSections: z
    .union([z.number(), z.string()])
    .refine(
      (value) => {
        return value !== undefined && value !== null && value !== "";
      },
      {
        message: "Please fill number of sections",
      },
    )
    .refine((n) => (typeof n === "number" ? n >= 0 : /^[0-9]+$/.test(n)), {
      message: "The value must be a non-negative number.",
    })
    .transform((value) => {
      if (typeof value === "string" && /^[0-9]+$/.test(value)) {
        return Number(value);
      }
      return value;
    })
    .default(1),
  type: z.string({
    required_error: "Please select a course type",
  }),
});

// Duplicate course schema
export const DuplicateCourseSchema = z.object({
  courseStartDate: z
    .object(
      {
        calendar: z.object({ identifier: z.string() }),
        era: z.string(),
        year: z.number(),
        month: z.number(),
        day: z.number(),
        hour: z.number().optional(),
        minute: z.number().optional(),
      },
      {
        required_error: "Please enter course start date",
      },
    )
    .refine(
      (endDate) => {
        const d = new Date(
          endDate.year,
          endDate.month - 1,
          endDate.day,
          endDate.hour,
          endDate.minute,
        );

        const selectedDate = moment(d).format("DD/MM/YYYY");
        // alert(selectedDate)
        const currentDate = moment(new Date()).format("DD/MM/YYYY");
        // alert(currentDate)

        const selectedDateAsDate = moment(selectedDate, "DD/MM/YYYY").toDate();
        const currentDateAsDate = moment(currentDate, "DD/MM/YYYY").toDate();

        return selectedDateAsDate >= currentDateAsDate;
      },
      {
        message: "Start date must be greater than or equal the current date",
      },
    )
    .refine(
      (endDate) => {
        const d = new Date(
          endDate.year,
          endDate.month - 1,
          endDate.day,
          endDate.hour,
          endDate.minute,
        );

        const selectedDate = moment(d).format("DD/MM/YYYY");
        const currentDate = moment(new Date()).format("DD/MM/YYYY");

        const selectedTime = moment(d).format("HH:mm");
        const currentTime = moment(new Date()).format("HH:mm");

        const selectedDateAsDate = moment(selectedDate, "DD/MM/YYYY").toDate();
        const currentDateAsDate = moment(currentDate, "DD/MM/YYYY").toDate();

        if (selectedDateAsDate < currentDateAsDate) {
          return false;
        } else if (selectedDateAsDate > currentDateAsDate) {
          return true;
        } else {
          return selectedTime > currentTime;
        }
      },
      {
        message: "Please provide a valid time. You have entered a past time",
      },
    ),
  courseEndDate: z
    .object(
      {
        calendar: z.object({ identifier: z.string() }),
        era: z.string(),
        year: z.number(),
        month: z.number(),
        day: z.number(),
        hour: z.number().optional(),
        minute: z.number().optional(),
      },
      {
        required_error: "Please enter course end date",
      },
    )
    .refine(
      (endDate) => {
        const d = new Date(
          endDate.year,
          endDate.month - 1,
          endDate.day,
          endDate.hour,
          endDate.minute,
        );

        const selectedDate = moment(d).format("DD/MM/YYYY");
        const currentDate = moment(new Date()).format("DD/MM/YYYY");

        const selectedDateAsDate = moment(selectedDate, "DD/MM/YYYY").toDate();
        const currentDateAsDate = moment(currentDate, "DD/MM/YYYY").toDate();

        return selectedDateAsDate >= currentDateAsDate;
      },
      {
        message: "Start date must be greater than or equal the current date",
      },
    )
    .refine(
      (endDate) => {
        const d = new Date(
          endDate.year,
          endDate.month - 1,
          endDate.day,
          endDate.hour,
          endDate.minute,
        );

        const selectedDate = moment(d).format("DD/MM/YYYY");
        const currentDate = moment(new Date()).format("DD/MM/YYYY");

        const selectedTime = moment(d).format("HH:mm");
        const currentTime = moment(new Date()).format("HH:mm");

        const selectedDateAsDate = moment(selectedDate, "DD/MM/YYYY").toDate();
        const currentDateAsDate = moment(currentDate, "DD/MM/YYYY").toDate();

        if (selectedDateAsDate < currentDateAsDate) {
          return false;
        } else if (selectedDateAsDate > currentDateAsDate) {
          return true;
        } else {
          return selectedTime > currentTime;
        }
      },
      {
        message: "Please provide a valid time. You have entered a past time",
      },
    ),

  courseFullName: z
    .string({
      required_error: "Please enter course full name",
    })
    .trim()
    .min(1),
  courseShortName: z
    .string({
      required_error: "Please enter course short name",
    })
    .trim()
    .min(1),

  courseCategory: z.string({
    required_error: "Please select course category",
  }),
  course_type: z.string({
    required_error: "Please select course type",
  }),
  courseVisibility: z.string({
    required_error: "Please select visiility",
  }),
});

export const AddResourceschema = z.object({
  description: z
    .string({
      required_error: "Please enter description",
    })
    .trim()
    .min(1),
  page_count: z.number().optional(),
  name: z
    .string({
      required_error: "Please enter name",
    })
    .trim()
    .min(1),
  url: z
    .string({
      required_error: "Please enter URL",
    })
    .trim()
    .min(1)
    .refine(
      (value) => {
        const isGoogleDriveUrl = value.includes("drive.google.com");
        const isGoogleDocsUrl = value.includes("docs.google.com");
        if (isGoogleDriveUrl || isGoogleDocsUrl) {
          // Bypass validation for Google Drive and Google Docs URLs
          return true;
        }
        // Regex to match valid file extensions for other URLs
        const urlRegex = /\.(jpg|jpeg|png|pptx|ppt|pdf|xls|xlsx|doc|docx)$/i;
        return urlRegex.test(value);
      },
      {
        message:
          "URL must be a valid file type (e.g., jpg, jpeg, png, pptx, pdf, etc.) or a valid Google Drive/Docs URL",
      },
    ),
  extension: z.string(),
  type: z.string().optional(),
  randomenabled: z.boolean().default(false),
  showcheckpoint: z.boolean().default(false),
  is_premium: z.boolean().default(false),
  checkpointnumber: z.coerce
    .number({
      required_error: "Please enter value",
    })
    .optional(),

  videolength: z
    .object({
      HH: z
        .string()
        .refine((value) => Number(value) <= 12, {
          message: "Invalid hour value",
        })
        .default("00"),
      MM: z
        .string()
        .refine((value) => Number(value) <= 59, {
          message: "Invalid minute value",
        })
        .default("00"),
      SS: z
        .string()
        .refine((value) => Number(value) <= 59, {
          message: "Invalid second value",
        })
        .default("00"),
    })
    .optional(),
  thumbnail_url: z.string().nullable().optional(),
});
export const AddResourceVideoschema = z.object({
  description: z
    .string({
      required_error: "Please enter description",
    })
    .trim()
    .min(1),
  name: z
    .string({
      required_error: "Please enter name",
    })
    .trim()
    .min(1),
  // thumbnail_url: z
  //   .string({
  //     required_error: "Please enter thumbnail",
  //   })
  //   .trim()
  //   .min(1)
  //   .refine(
  //     (value) => {
  //       const urlRegex = /^(ftp|http|https):\/\/[^ "]+$/;
  //       return urlRegex.test(value);
  //     },
  //     {
  //       message: "Please enter a valid URL",
  //     },
  //   ),
  thumbnail_url: z.string().nullable().optional(),
  url: z
    .string({
      required_error: "Please enter URL",
    })
    .trim()
    .min(1)
    .refine(
      (value) => {
        const urlRegex = /^(ftp|http|https):\/\/[^ "]+$/;
        return urlRegex.test(value);
      },
      {
        message: "Please enter a valid URL",
      },
    ),
  type: z.string().optional(),
  randomenabled: z.boolean().default(false),
  showcheckpoint: z.boolean().default(false),
  is_premium: z.boolean().default(false),
  checkpointnumber: z.coerce
    .number({
      required_error: "Please enter value",
    })
    .optional(),
  extension: z.string(),
  // page_count: z.number(),
  videolength: z
    .object({
      HH: z.string().refine(
        (value) => {
          return value !== undefined && value !== null && Number(value) <= 25;
        },
        {
          message: "Invalid hour value",
        },
      ),
      MM: z.string().refine(
        (value) => {
          return value !== undefined && value !== null && Number(value) <= 59;
        },
        {
          message: "Invalid minute value",
        },
      ),
      SS: z.string().refine(
        (value) => {
          return value !== undefined && value !== null && Number(value) <= 59;
        },
        {
          message: "Invalid second value",
        },
      ),
    })
    .refine(
      (value) => {
        // Custom refinement for the entire videolength object
        return (
          (value.HH !== undefined && value.HH !== null && value.HH !== "") ||
          (value.MM !== undefined && value.MM !== null && value.MM !== "") ||
          (value.SS !== undefined && value.SS !== null && value.SS !== "")
        );
      },
      {
        message: "Either HH, MM, or SS must have a value",
      },
    ),
});
export const CheckPointFormShema = z.object({
  sequence_number: z
    .string({
      required_error: "Please enter sequence number",
    })
    .min(1),
  checkpoint_name: z
    .string({
      required_error: "Please enter checkpoint name",
    })
    .min(1),
  checkpoint_startTime: z
    .object({
      HH: z.string().refine(
        (value) => {
          return value !== undefined && value !== null && Number(value) <= 12;
        },
        {
          message: "Invalid hour value",
        },
      ),
      MM: z.string().refine(
        (value) => {
          return value !== undefined && value !== null && Number(value) <= 59;
        },
        {
          message: "Invalid minute value",
        },
      ),
      SS: z.string().refine(
        (value) => {
          return value !== undefined && value !== null && Number(value) <= 59;
        },
        {
          message: "Invalid second value",
        },
      ),
    })
    .refine(
      (value) => {
        // Custom refinement for the entire videolength object
        return (
          (value.HH !== undefined && value.HH !== null && value.HH !== "") ||
          (value.MM !== undefined && value.MM !== null && value.MM !== "") ||
          (value.SS !== undefined && value.SS !== null && value.SS !== "")
        );
      },
      {
        message: "Either HH, MM, or SS must have a value",
      },
    ),
  checkpoint_type: z.string().optional(),
  checkpoint_resname: z.string().optional(),
  checkpoint_resid: z.string({
    required_error: "Please enter resource",
  }),
  isMandatory: z.string().optional(),
});
export const PPTCheckPointFormShema = z.object({
  sequence_number: z
    .string({
      required_error: "Please enter sequence number",
    })
    .min(1),
  checkpoint_name: z
    .string({
      required_error: "Please enter checkpoint name",
    })
    .min(1),
  checkpoint_slide: z
    .string({
      required_error: "Please enter checkpoint slide",
    })
    .min(1),
  checkpoint_type: z.string().optional(),
  checkpoint_resname: z.string().optional(),
  checkpoint_resid: z.string({
    required_error: "Please enter resource",
  }),
  isMandatory: z.string().optional(),
});
export const AddPageContentSchema = z.object({
  pageTitle: z
    .string({
      required_error: "Please enter page title",
    })
    .min(8),
  section: z.string().optional(),
  pageContent: z.object(
    {
      htmlValue: z.string(),
    },
    {
      required_error: "Please fill course description",
    },
  ),
  is_premium: z.boolean().default(false),
  course: z.string().optional(),
});

export const EditCourseSchema = z.object({
  courseEndDate: z
    .object(
      {
        calendar: z.object({ identifier: z.string() }),
        era: z.string(),
        year: z.number(),
        month: z.number(),
        day: z.number(),
        hour: z.number().optional(),
        minute: z.number().optional(),
      },
      {
        required_error: "Please enter course end date",
      },
    )
    .refine(
      (endDate) => {
        const d = new Date(
          endDate.year,
          endDate.month - 1,
          endDate.day,
          endDate.hour,
          endDate.minute,
        );

        const selectedDate = moment(d).format("DD/MM/YYYY");
        const currentDate = moment(new Date()).format("DD/MM/YYYY");

        const selectedDateAsDate = moment(selectedDate, "DD/MM/YYYY").toDate();
        const currentDateAsDate = moment(currentDate, "DD/MM/YYYY").toDate();

        return selectedDateAsDate >= currentDateAsDate;
      },
      {
        message: "Start date must be greater than or equal the current date",
      },
    )
    .refine(
      (endDate) => {
        const d = new Date(
          endDate.year,
          endDate.month - 1,
          endDate.day,
          endDate.hour,
          endDate.minute,
        );

        const selectedDate = moment(d).format("DD/MM/YYYY");
        const currentDate = moment(new Date()).format("DD/MM/YYYY");

        const selectedTime = moment(d).format("HH:mm");
        const currentTime = moment(new Date()).format("HH:mm");

        const selectedDateAsDate = moment(selectedDate, "DD/MM/YYYY").toDate();
        const currentDateAsDate = moment(currentDate, "DD/MM/YYYY").toDate();

        if (selectedDateAsDate < currentDateAsDate) {
          return false;
        } else if (selectedDateAsDate > currentDateAsDate) {
          return true;
        } else {
          return selectedTime > currentTime;
        }
      },
      {
        message: "Please provide a valid time. You have entered a past time",
      },
    ),

  courseFullName: z
    .string({
      required_error: "Please enter course full name",
    })
    .trim()
    .min(1),
  courseShortName: z
    .string({
      required_error: "Please enter course short name",
    })
    .trim()
    .min(1),
  courseCategory: z.string({
    required_error: "Please select course category",
  }),
  courseDescription: z
    .string({
      required_error: "Please enter description",
    })
    .trim()
    .min(1),
});

export const AddCurrentAffairsSchema = z.object({
  title: z.string({
    required_error: "Please enter the event title",
  }),
  month: z.string({
    required_error: "Please enter the month",
  }),
  content: z.string({
    required_error: "Please enter the event title",
  }),
  publish_date: z.object(
    {
      calendar: z.object({ identifier: z.string() }),
      era: z.string(),
      year: z.number(),
      month: z.number(),
      day: z.number(),
      hour: z.number().optional(),
      minute: z.number().optional(),
    },
    {
      required_error: "Please select date",
    },
  ),
});

export const AddCourseVideoschema = z.object({
  name: z
    .string({
      required_error: "Please enter Name",
    })
    .trim()
    .min(1),
  video_url: z
    .string({
      required_error: "Please enter URL",
    })
    .trim()
    .min(1)
    .refine(
      (value) => {
        const urlRegex = /^(ftp|http|https):\/\/[^ "]+$/;
        return urlRegex.test(value);
      },
      {
        message: "Please enter a valid URL",
      },
    ),
});

export const InviteUserSchema = z.object({
  recipient: z
    .string({
      required_error: "Please enter email",
    })
    .refine(
      (value) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(value);
      },
      {
        message: "Please enter a valid email address",
      },
    ),
});

export const AddUserSchema = z.object({
  first_name: z
    .string({
      required_error: "Please enter first name",
    })
    .min(1),
  last_name: z
    .string({
      required_error: "Please enter last name",
    })
    .min(1),
  email: z
    .string({
      required_error: "Please enter email",
    })
    .refine(
      (value) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(value);
      },
      {
        message: "Please enter a valid email address",
      },
    ),
  password: z
    .string({
      required_error: "Please enter password",
    })
    .min(6),
  phonenumber1: z
    .string({
      required_error: "Please enter phone number",
    })
    .max(10)
    .regex(/^[0-9]{10}$/, {
      message: "Phone number must contain exactly 10 digits",
    }),
});
export const AddQuestionCategoryschema = z.object({
  categoryName: z
    .string({
      required_error: "Please enter category name",
    })
    .trim()
    .min(1),
  description: z
    .string({
      required_error: "Please enter description",
    })
    .trim()
    .min(1),
});
export const UpdateVideoCheckPointSchema = z.object({
  randomenabled: z.boolean().default(false),
  showcheckpoint: z.boolean().default(false),
  checkpointnumber: z.coerce
    .number({
      required_error: "Please enter value",
    })
    .min(1),
});
export const UpdatePPTCheckPointSchema = z.object({
  randomenabled: z.boolean().default(false),
  showcheckpoint: z.boolean().default(false),
  checkpointnumber: z.coerce
    .number({
      required_error: "Please enter value",
    })
    .min(1),
});
export const UpdateGroupschema = z.object({
  name: z
    .string({
      required_error: "Please enter Group name",
    })
    .trim()
    .min(1)
    .max(50),
  description: z
    .string({
      required_error: "Please enter description",
    })
    .trim()
    .min(1),
});
export const UpdateTopicschema = z.object({
  name: z
    .string({
      required_error: "Please enter Category name",
    })
    .trim()
    .min(1),
  description: z
    .string({
      required_error: "Please enter Category description",
    })
    .trim()
    .min(1),
  is_premium: z.boolean().default(false),
});
export const MembershipPlanSchema = z.object({
  name: z
    .string({
      required_error: "Please enter membership name",
    })
    .trim()
    .min(1)
    .max(50),
  description: z
    .string({
      required_error: "Please enter description",
    })
    .trim()
    .min(1),
  subscription_type: z.enum(["course_based", "resource_based", "time_based"], {
    required_error: "You need to select course or resource.",
  }),
  subscription_frequency_type: z.enum(["annual", "custom", "monthly"], {
    required_error: "You need to select a plan frequency.",
  }),
  currency: z.enum(["INR"], {
    required_error: "You need to select currency.",
  }),
  valid_from: z
    .object(
      {
        calendar: z.object({ identifier: z.string() }),
        era: z.string(),
        year: z.number(),
        month: z.number(),
        day: z.number(),
        hour: z.number().optional(),
        minute: z.number().optional(),
      },
      {
        required_error: "Please select from date",
      },
    )
    .refine(
      (endDate) => {
        const d = new Date(
          endDate.year,
          endDate.month - 1,
          endDate.day,
          endDate.hour,
          endDate.minute,
        );

        const selectedDate = moment(d).format("DD/MM/YYYY");
        const currentDate = moment(new Date()).format("DD/MM/YYYY");
        const selectedDateAsDate = moment(selectedDate, "DD/MM/YYYY").toDate();
        const currentDateAsDate = moment(currentDate, "DD/MM/YYYY").toDate();
        return selectedDateAsDate >= currentDateAsDate;
      },
      {
        message: "Start date must be greater than or equal the current date",
      },
    )
    .refine(
      (endDate) => {
        const d = new Date(
          endDate.year,
          endDate.month - 1,
          endDate.day,
          endDate.hour,
          endDate.minute,
        );

        const selectedDate = moment(d).format("DD/MM/YYYY");
        const currentDate = moment(new Date()).format("DD/MM/YYYY");

        const selectedTime = moment(d).format("HH:mm");
        const currentTime = moment(new Date()).format("HH:mm");

        const selectedDateAsDate = moment(selectedDate, "DD/MM/YYYY").toDate();
        const currentDateAsDate = moment(currentDate, "DD/MM/YYYY").toDate();

        if (selectedDateAsDate < currentDateAsDate) {
          return false;
        } else if (selectedDateAsDate > currentDateAsDate) {
          return true;
        } else {
          return selectedTime > currentTime;
        }
      },
      {
        message: "Please provide a valid time. You have entered a past time",
      },
    )
    .nullable()
    .refine((value) => value !== null, {
      message: "Please select a valid from date.",
    }),
  valid_to: z
    .object(
      {
        calendar: z.object({ identifier: z.string() }),
        era: z.string(),
        year: z.number(),
        month: z.number(),
        day: z.number(),
        hour: z.number().optional(),
        minute: z.number().optional(),
      },
      {
        required_error: "Please select to date",
      },
    )
    .refine(
      (endDate) => {
        const d = new Date(
          endDate.year,
          endDate.month - 1,
          endDate.day,
          endDate.hour,
          endDate.minute,
        );

        const selectedDate = moment(d).format("DD/MM/YYYY");
        const currentDate = moment(new Date()).format("DD/MM/YYYY");
        const selectedDateAsDate = moment(selectedDate, "DD/MM/YYYY").toDate();
        const currentDateAsDate = moment(currentDate, "DD/MM/YYYY").toDate();
        return selectedDateAsDate >= currentDateAsDate;
      },
      {
        message: "End date must be greater than or equal the current date",
      },
    )
    .refine(
      (endDate) => {
        const d = new Date(
          endDate.year,
          endDate.month - 1,
          endDate.day,
          endDate.hour,
          endDate.minute,
        );

        const selectedDate = moment(d).format("DD/MM/YYYY");
        const currentDate = moment(new Date()).format("DD/MM/YYYY");

        const selectedTime = moment(d).format("HH:mm");
        const currentTime = moment(new Date()).format("HH:mm");

        const selectedDateAsDate = moment(selectedDate, "DD/MM/YYYY").toDate();
        const currentDateAsDate = moment(currentDate, "DD/MM/YYYY").toDate();

        if (selectedDateAsDate < currentDateAsDate) {
          return false;
        } else if (selectedDateAsDate > currentDateAsDate) {
          return true;
        } else {
          return selectedTime > currentTime;
        }
      },
      {
        message: "Please provide a valid time. You have entered a past time",
      },
    )
    .nullable()
    .refine((value) => value !== null, {
      message: "Please select a valid to date.",
    }),
  price: z.string({ required_error: "Please enter amount" }).refine(
    (n) => {
      if (typeof n !== "number" && !/^\d+$/.test(n)) {
        return false; // If not a number or string of digits, return false
      }
      const num = Number(n); // Convert to number if it's a string of digits
      return num > 0; // Return true only if the number is greater than 0
    },
    {
      message: "The value must be a non-zero, non-negative number.",
    },
  ),
});

export const MembershipPlanEditSchema = z.object({
  name: z
    .string({
      required_error: "Please enter membership name",
    })
    .trim()
    .min(1)
    .max(50),
  description: z
    .string({
      required_error: "Please enter description",
    })
    .trim()
    .min(1),
  subscription_type: z.enum(["course_based", "resource_based", "time_based"], {
    required_error: "You need to select course or resource.",
  }),
  subscription_frequency_type: z.enum(["annual", "custom", "monthly"], {
    required_error: "You need to select a plan frequency.",
  }),
  currency: z.enum(["INR"], {
    required_error: "You need to select currency.",
  }),
  valid_from: z
    .object(
      {
        calendar: z.object({ identifier: z.string() }),
        era: z.string(),
        year: z.number(),
        month: z.number(),
        day: z.number(),
        hour: z.number().optional(),
        minute: z.number().optional(),
      },
      {
        required_error: "Please select from date",
      },
    )
    .nullable()
    .refine((value) => value !== null, {
      message: "Please select a valid from date.",
    }),
  valid_to: z
    .object(
      {
        calendar: z.object({ identifier: z.string() }),
        era: z.string(),
        year: z.number(),
        month: z.number(),
        day: z.number(),
        hour: z.number().optional(),
        minute: z.number().optional(),
      },
      {
        required_error: "Please select from date",
      },
    )
    .nullable()
    .refine((value) => value !== null, {
      message: "Please select a valid to date.",
    }),
  price: z.string({ required_error: "Please enter amount" }).refine(
    (n) => {
      if (typeof n !== "number" && !/^\d+$/.test(n)) {
        return false; // If not a number or string of digits, return false
      }
      const num = Number(n); // Convert to number if it's a string of digits
      return num > 0; // Return true only if the number is greater than 0
    },
    {
      message: "The value must be a non-zero, non-negative number.",
    },
  ),
});
export const MembershipPlanEditModalSchema = z.object({
  subscription_plan_status: z.enum(["active", "inactive"], {
    required_error: "You need to select a status.",
  }),

  // valid_from: z.object(
  //   {
  //     calendar: z.object({ identifier: z.string() }),
  //     era: z.string(),
  //     year: z.number(),
  //     month: z.number(),
  //     day: z.number(),
  //     hour: z.number().optional(),
  //     minute: z.number().optional(),
  //   },
  //   {
  //     required_error: "Please select from date",
  //   },
  // ),
  // valid_to: z.object(
  //   {
  //     calendar: z.object({ identifier: z.string() }),
  //     era: z.string(),
  //     year: z.number(),
  //     month: z.number(),
  //     day: z.number(),
  //     hour: z.number().optional(),
  //     minute: z.number().optional(),
  //   },
  //   {
  //     required_error: "Please select from date",
  //   },
  // ),
});
export const AddPageResourceSchema = z.object({
  pageTitle: z
    .string({
      required_error: "Please enter page title",
    })
    .min(8),
  section: z.string().optional(),
  pageContent: z
    .string({
      required_error: "Please enter description",
    })
    .trim()
    .min(1),
  is_premium: z.boolean().default(false),
  course: z.string().optional(),
  extension: z.string(),
});
export const UpdateFolderSchema = z.object({
  name: z
    .string({
      required_error: "Please enter Folder name",
    })
    .trim()
    .min(1)
    .max(50),
  description: z
    .string({
      required_error: "Please enter description",
    })
    .trim()
    .min(1),
});

export const NotificationSchema = z.object({
  message: z
    .string({
      required_error: "Please enter message",
    })
    .trim()
    .min(1)
    .max(100),
  // email: z
  //   .string({
  //     required_error: "Please enter your email id",
  //   })
  //   .min(8),
  // phonenumber: z
  //   .string({
  //     required_error: "Please enter phone number",
  //   })
  //   .max(10)
  //   .regex(/^[0-9]{10}$/, {
  //     message: "Phone number must contain exactly 10 digits",
  //   }),
});
export const EmailSchema = z.object({
  message: z
    .string({
      required_error: "Please enter message",
    })
    .trim()
    .min(1)
    .max(3000),
  subject: z
    .string({
      required_error: "Please enter subject",
    })
    .trim()
    .min(1)
    .max(50),
});
export const fileAttachmantSchema = z.object({
  message: z
    .string({
      required_error: "Please enter message",
    })
    .trim()
    .min(1)
    .max(1000),
  file: z.instanceof(File, {
    message: "A valid file is required",
  }),
});
export const addFoldersSchema = z.object({
  name: z
    .string({
      required_error: "Please enter Folder name",
    })
    .trim()
    .min(1)
    .max(50),
  description: z
    .string({
      required_error: "Please enter description",
    })
    .trim()
    .min(1),
  valid_from: z
    .object(
      {
        calendar: z.object({ identifier: z.string() }),
        era: z.string(),
        year: z.number(),
        month: z.number(),
        day: z.number(),
        hour: z.number().optional(),
        minute: z.number().optional(),
      },
      {
        required_error: "Please select from date",
      },
    )
    .refine(
      (endDate) => {
        const d = new Date(
          endDate.year,
          endDate.month - 1,
          endDate.day,
          endDate.hour,
          endDate.minute,
        );

        const selectedDate = moment(d).format("DD/MM/YYYY");
        const currentDate = moment(new Date()).format("DD/MM/YYYY");
        const selectedDateAsDate = moment(selectedDate, "DD/MM/YYYY").toDate();
        const currentDateAsDate = moment(currentDate, "DD/MM/YYYY").toDate();
        return selectedDateAsDate >= currentDateAsDate;
      },
      {
        message: "Start date must be greater than or equal the current date",
      },
    )
    .refine(
      (endDate) => {
        const d = new Date(
          endDate.year,
          endDate.month - 1,
          endDate.day,
          endDate.hour,
          endDate.minute,
        );

        const selectedDate = moment(d).format("DD/MM/YYYY");
        const currentDate = moment(new Date()).format("DD/MM/YYYY");

        const selectedTime = moment(d).format("HH:mm");
        const currentTime = moment(new Date()).format("HH:mm");

        const selectedDateAsDate = moment(selectedDate, "DD/MM/YYYY").toDate();
        const currentDateAsDate = moment(currentDate, "DD/MM/YYYY").toDate();

        if (selectedDateAsDate < currentDateAsDate) {
          return false;
        } else if (selectedDateAsDate > currentDateAsDate) {
          return true;
        } else {
          return selectedTime > currentTime;
        }
      },
      {
        message: "Please provide a valid time. You have entered a past time",
      },
    )
    .nullable()
    .refine((value) => value !== null, {
      message: "Please select a valid from date.",
    }),
  valid_to: z
    .object(
      {
        calendar: z.object({ identifier: z.string() }),
        era: z.string(),
        year: z.number(),
        month: z.number(),
        day: z.number(),
        hour: z.number().optional(),
        minute: z.number().optional(),
      },
      {
        required_error: "Please select to date",
      },
    )
    .refine(
      (endDate) => {
        const d = new Date(
          endDate.year,
          endDate.month - 1,
          endDate.day,
          endDate.hour,
          endDate.minute,
        );

        const selectedDate = moment(d).format("DD/MM/YYYY");
        const currentDate = moment(new Date()).format("DD/MM/YYYY");
        const selectedDateAsDate = moment(selectedDate, "DD/MM/YYYY").toDate();
        const currentDateAsDate = moment(currentDate, "DD/MM/YYYY").toDate();
        return selectedDateAsDate >= currentDateAsDate;
      },
      {
        message: "End date must be greater than or equal the current date",
      },
    )
    .refine(
      (endDate) => {
        const d = new Date(
          endDate.year,
          endDate.month - 1,
          endDate.day,
          endDate.hour,
          endDate.minute,
        );

        const selectedDate = moment(d).format("DD/MM/YYYY");
        const currentDate = moment(new Date()).format("DD/MM/YYYY");

        const selectedTime = moment(d).format("HH:mm");
        const currentTime = moment(new Date()).format("HH:mm");

        const selectedDateAsDate = moment(selectedDate, "DD/MM/YYYY").toDate();
        const currentDateAsDate = moment(currentDate, "DD/MM/YYYY").toDate();

        if (selectedDateAsDate < currentDateAsDate) {
          return false;
        } else if (selectedDateAsDate > currentDateAsDate) {
          return true;
        } else {
          return selectedTime > currentTime;
        }
      },
      {
        message: "Please provide a valid time. You have entered a past time",
      },
    )
    .nullable()
    .refine((value) => value !== null, {
      message: "Please select a valid to date.",
    }),
});
export const MembershipExtendValiditySchema = z.object({
  valid_to: z
    .object(
      {
        calendar: z.object({ identifier: z.string() }),
        era: z.string(),
        year: z.number(),
        month: z.number(),
        day: z.number(),
        hour: z.number().optional(),
        minute: z.number().optional(),
      },
      {
        required_error: "Please select to date",
      },
    )
    .refine(
      (endDate) => {
        const d = new Date(
          endDate.year,
          endDate.month - 1,
          endDate.day,
          endDate.hour,
          endDate.minute,
        );

        const selectedDate = moment(d).format("DD/MM/YYYY");
        const currentDate = moment(new Date()).format("DD/MM/YYYY");
        const selectedDateAsDate = moment(selectedDate, "DD/MM/YYYY").toDate();
        const currentDateAsDate = moment(currentDate, "DD/MM/YYYY").toDate();
        return selectedDateAsDate >= currentDateAsDate;
      },
      {
        message: "End date must be greater than or equal to the current date",
      },
    )
    .refine(
      (endDate) => {
        const d = new Date(
          endDate.year,
          endDate.month - 1,
          endDate.day,
          endDate.hour,
          endDate.minute,
        );

        const selectedDate = moment(d).format("DD/MM/YYYY");
        const currentDate = moment(new Date()).format("DD/MM/YYYY");

        const selectedTime = moment(d).format("HH:mm");
        const currentTime = moment(new Date()).format("HH:mm");

        const selectedDateAsDate = moment(selectedDate, "DD/MM/YYYY").toDate();
        const currentDateAsDate = moment(currentDate, "DD/MM/YYYY").toDate();

        if (selectedDateAsDate < currentDateAsDate) {
          return false;
        } else if (selectedDateAsDate > currentDateAsDate) {
          return true;
        } else {
          return selectedTime > currentTime;
        }
      },
      {
        message: "Please provide a valid time. You have entered a past time",
      },
    )
    .nullable()
    .refine((value) => value !== null, {
      message: "Please select a valid to date.",
    }),
});
export const LiveClassSchema = z.object({
  meeting_id: z
    .string({
      required_error: "Please enter the meeting ID",
    })
    .trim()
    .min(1, "Meeting ID cannot be empty")
    .or(z.literal("").optional()),
  passcode: z
    .string({
      required_error: "Please enter the Passcode",
    })
    .trim()
    .min(1, "Passcode cannot be empty"),
  meeting_platform: z
    .string({
      required_error: "Please select the meeting platform",
    })
    .trim()
    .min(1, "Meeting platform cannot be empty"),
  meeting_url: z
    .string({
      required_error: "Please enter the meeting URL",
    })
    .trim()
    .url("Please enter a valid URL")
    .or(z.literal("").optional())
    .optional(),
  start_date: z
    .object(
      {
        calendar: z.object({ identifier: z.string() }),
        era: z.string(),
        year: z.number(),
        month: z.number(),
        day: z.number(),
        hour: z.number().optional(),
        minute: z.number().optional(),
      },
      {
        required_error: "Please enter the start date",
      },
    )
    .refine(
      (date) => {
        const d = new Date(
          date.year,
          date.month - 1,
          date.day,
          date.hour,
          date.minute,
        );
        const now = new Date();
        return (
          d > now ||
          (d.toDateString() === now.toDateString() &&
            d.getTime() > now.getTime())
        );
      },
      {
        message: "Start date must be in the future",
      },
    ),
  course_id: z
    .string({
      required_error: "Please select a course",
    })
    .optional(),
  end_date: z
    .object(
      {
        calendar: z.object({ identifier: z.string() }),
        era: z.string(),
        year: z.number(),
        month: z.number(),
        day: z.number(),
        hour: z.number().optional(),
        minute: z.number().optional(),
      },
      {
        required_error: "Please enter the end date",
      },
    )
    .refine(
      (endDate) => {
        const d = new Date(
          endDate.year,
          endDate.month - 1,
          endDate.day,
          endDate.hour,
          endDate.minute,
        );

        const selectedDate = moment(d).format("DD/MM/YYYY");
        const currentDate = moment(new Date()).format("DD/MM/YYYY");

        const selectedTime = moment(d).format("HH:mm");
        const currentTime = moment(new Date()).format("HH:mm");

        const selectedDateAsDate = moment(selectedDate, "DD/MM/YYYY").toDate();
        const currentDateAsDate = moment(currentDate, "DD/MM/YYYY").toDate();

        if (selectedDateAsDate < currentDateAsDate) {
          return false;
        } else if (selectedDateAsDate > currentDateAsDate) {
          return true;
        } else {
          return selectedTime > currentTime;
        }
      },
      {
        message: "Please provide a valid time. You have entered a past time",
      },
    ),
});
