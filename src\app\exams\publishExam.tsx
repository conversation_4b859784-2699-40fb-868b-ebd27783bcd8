import React from "react";
import type { ErrorCatch, LogUserActivityRequest, ToastType } from "@/types";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import useExams from "@/hooks/useExams";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";

export default function PublishExam({
  onCancel,
  onSave,
  examId,
  status,
}: {
  onCancel: () => void;
  onSave: (value: boolean) => void;
  examId: string;
  status: string;
}): React.JSX.Element {
  const { t } = useTranslation();
  const { toast } = useToast() as ToastType;
  const { publishExam } = useExams();
  const { updateUserActivity } = useLogUserActivity();

  const handlePublishClick = (): void => {
    void handleToastPublish();
    onCancel();
  };

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  const handleToastPublish = async (): Promise<void> => {
    const params = {
      quiz_id: examId,
      status: status === "Published" ? "Draft" : "Published",
    };
    try {
      const result = await publishExam(params);
      if (result.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_success,
          title:
            status === "Published"
              ? t("successMessages.deaftExamTitle")
              : t("successMessages.pulishExamTitle"),
          description:
            status === "Published"
              ? t("successMessages.deaftExamMsg")
              : t("successMessages.pulishExamMsg"),
        });
        onSave(true);
        const params = {
          activity_type: "Exam",
          screen_name: "Exam",
          action_details: `${status === "Published" ? "Draft" : "Published"} the exam successfully`,
          target_id: examId as string,
          log_result: "SUCCESS",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
        onCancel();
      } else {
        const params = {
          activity_type: "Exam",
          screen_name: "Exam",
          action_details: `Failed to ${status === "Published" ? "Draft" : "Published"} the exam`,
          target_id: examId as string,
          log_result: "ERROR",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("successMessages.toast_error_title"),
        description: err?.message,
      });
    }
    // onCancel();
  };

  const handleCancel = (): void => {
    onCancel();
  };

  return (
    <>
      <div className="mb-2 mr-4">
        {status === "Published" ? (
          <p className="ml-0 ">
            {t("exams.draftPrompt")}
          </p>
        ) : (
          <p className="ml-0 ">
            {t("exams.publishPrompt")}
          </p>
        )}
      </div>
      <div className="flex topic-icons pr-4">
        <div className="flex items-center justify-center float-right gap-3">
          <Button type="button" className="bg-[#33363F]" onClick={handleCancel}>
          {t("buttons.cancel")}
          </Button>

          <Button
            type="submit"
            className="bg-[#9FC089]"
            onClick={handlePublishClick}
          >
            {t("buttons.submit")}
          </Button>
        </div>
      </div>
    </>
  );
}
