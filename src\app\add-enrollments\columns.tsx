"use client";

import type {  ColumnDef } from "@tanstack/react-table";

export interface Enrollment {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
}

export const getColumns = (
  t: (key: string) => string
): ColumnDef<Enrollment>[] => [
  {
    id: "select",
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "first_name",
    header: t("enrollment.firstName"),
    cell: ({ row }: { row: { original: Enrollment } }): React.JSX.Element => {
      const firstName = row.original.first_name ?? " ";
      return <div className="text-align">{firstName}</div>;
    },
  },
  {
    accessorKey: "last_name",
    header: t("enrollment.lastName"),
    cell: ({ row }: { row: { original: Enrollment } }): React.JSX.Element => {
      const lastName = row.original.last_name ?? " ";
      return <div className="text-align">{lastName}</div>;
    },
  },
  {
    accessorKey: "email",
    header: t("enrollment.email"),
    cell: ({ row }) => <div className="text-align">{row.original.email}</div>,
  },
];
