import { useTranslation } from "react-i18next";

export function Footer(): React.JSX.Element {
  const { t } = useTranslation();
  return (
    <div className="justify-end bottom-4  z-30 p-4 border border-1 border-gray-300">
      <div className="rounded-none border-none text-sm bg-transparent text-black ">
        <div>
          {t("dashboard.footer.copyright")}
          <span className="text-[#15CCE5] ">
            <b>SmartLearn</b>
          </span>
          {t("dashboard.footer.rightsReserved")}
        </div>
        {/* <div className="text-xs p-2 ml-12">Version: 0.1.0</div> */}
      </div>
    </div>
  );
}
[];
