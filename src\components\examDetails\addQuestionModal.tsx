"use client";
import React, { useState, useEffect, type KeyboardEvent } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { AddquestionFormSchema } from "@/schema/schema";
import type {
  AddQuestionSchemaType,
  ComboData,
  ErrorCatch,
  LogUserActivityRequest,
  ToastType,
  TopicDataType,
  richTextType,
} from "@/types";
import { Input } from "@/components/ui/input";
import { Combobox } from "@/components/ui/combobox";
import { Card, CardContent } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { questionType, contentType, AppConfig, ORG_KEY } from "@/lib/constants";
import useQuestionBank from "@/hooks/useQuestionBank";
import { useToast } from "@/components/ui/use-toast";
import { Editor } from "primereact/editor";

import dynamic from "next/dynamic";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import useImportQuestions from "@/hooks/useImportQuestions";
import { Modal } from "../ui/modal";
import { AddNewQuesionCategoryForm } from "@/app/questionbank/addNewQuestionCategory";
import { ModalButton } from "@/components/ui/modalButton";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import { useTranslation } from "react-i18next";

const MathFormulaEditor = dynamic(() => import("../formulaEditor"), {
  ssr: false,
});

const MathComponent = dynamic(
  () => import("mathjax-react").then((mod) => mod.MathComponent),
  {
    ssr: false,
  },
);

interface AddQuestionModalProps {
  closeDialog: (value: boolean) => void;
  examId: string;
  onQuestionAdded: (value: boolean) => void;
}

export default function AddQuestionModal({
  closeDialog,
  examId,
  onQuestionAdded,
}: AddQuestionModalProps): React.JSX.Element {
  const { t } = useTranslation();
  const { toast } = useToast() as ToastType;
  const { updateUserActivity } = useLogUserActivity();
  const noOfChoices = AppConfig.noOfChoices;
  const [comboData, setComboData] = useState<ComboData[]>([]);
  const [categoryId, setCategoryId] = React.useState("");
  const { addQuestions, getQuestionBankList, getPublishedQuestionCategory } =
    useQuestionBank();
  const { addImportQuestionsFromBank } = useImportQuestions();
  const [categoryAddStatus, setCategoryAddStatus] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [richTextValues, setRichTextValues] = React.useState<
    richTextType | undefined
  >(undefined);
  const [isOpen, setIsOpen] = React.useState<boolean>(false);
  const [formulaValue, setFormulaValue] = useState<string>("");
  const [defaultEquation, setDefaultEquation] = useState<string>("");

  const form = useForm<AddQuestionSchemaType>({
    resolver: zodResolver(AddquestionFormSchema),
  });

  const { register } = form;

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  async function onSubmit(): Promise<void> {
    const formData = form.getValues();
    const org_id = localStorage.getItem(ORG_KEY);

    if (richTextValues?.htmlValue !== undefined) {
      formData.question = richTextValues?.htmlValue;
    }

    const getIsAnswer = (formData.answers ?? []).every(
      (answer) => !answer.isAnswer,
    );

    if (getIsAnswer) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.selectOneAns"),
      });
      return;
    }

    const transformedData = {
      answer_datas: (formData.answers ?? []).map((answer, index) => ({
        slot: index + 1,
        answer: answer.text,
        fraction: answer.fraction,
        answer_type: answer.type ?? "PLAIN_TEXT",
      })),
      org_id: org_id ?? "",
      ques_category_id: categoryId,
      question_data: {
        name: formData.question,
        question_text: formData.question,
        default_mark: formData.markassigned ?? 0,
        penalty: formData.penality ?? 0,
        question_type: formData.contenttype ?? "PLAIN_TEXT",
      },
      quiz_id: examId,
    };

    try {
      const questionList = await getQuestionBankList(categoryId);
      const questionExists = questionList.some(
        (question) => question.question_text === formData.question,
      );
      if (questionExists) {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: t("errorMessages.questionAlreadyExists"),
        });
        return;
      }
    } catch (error) {
      console.error("Error fetching question list:", error);
      return;
    }

    try {
      const result = await addQuestions(transformedData);

      if (result.status === "success") {
        // Import the question to the exam after successful addition
        const importData = {
          org_id: org_id ?? "",
          question_ids: [result.question_id as string],
          quiz_id: examId,
        };

        try {
          const importResult = await addImportQuestionsFromBank(importData);

          if (importResult.status === "success") {
            toast({
              variant: SUCCESS_MESSAGES.toast_variant_default,
              title: t("successMessages.toast_success_title"),
              description: t("successMessages.questionAddedAndImported"),
            });
          } else {
            toast({
              variant: ERROR_MESSAGES.toast_variant_destructive,
              title: t("errorMessages.toast_error_title"),
              description: t("errorMessages.failedToImportExam"),
            });
          }
        } catch (importError) {
          console.error("Import error:", importError);
          toast({
            variant: ERROR_MESSAGES.toast_variant_destructive,
            title: t("errorMessages.toast_error_title"),
            description: t("errorMessages.failedToImportExam"),
          });
        }

        const params = {
          activity_type: "Question_Bank",
          screen_name: "Question_Bank",
          action_details: "Question added and imported to exam successfully ",
          target_id: result.question_id as string,
          log_result: "SUCCESS",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
        onQuestionAdded(true);
        closeDialog(true);
      } else {
        const params = {
          activity_type: "Question_Bank",
          screen_name: "Question_Bank",
          action_details: "Failed to add question",
          target_id: result.question_id as string,
          log_result: "ERROR",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
      console.error("An unexpected error occurred:", error);
    }
  }

  useEffect(() => {
    questionCategoryList();
    markAssigned();
  }, [categoryAddStatus]);

  function saveModal(mathmlInput: string, latexValue: string): void {
    console.log(mathmlInput);
    setDefaultEquation(latexValue);
    const mathMLContent = `<math xmlns="http://www.w3.org/1998/Math/MathML">${mathmlInput}</math>`;
    setFormulaValue(mathMLContent);
    form.setValue("question", mathMLContent);
  }

  function cancelModal(): void {
    setIsOpen(false);
  }

  const handleAddCategory = (): void => {
    setCategoryAddStatus(!categoryAddStatus);
  };

  const questionCategoryList = (): void => {
    const fetchData = async (): Promise<void> => {
      try {
        const category: TopicDataType[] = await getPublishedQuestionCategory();
        const comboData: ComboData[] = category.map((cat) => ({
          value: cat.value,
          label: cat.label,
        }));

        setComboData(comboData);
        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching data:");
        setIsLoading(false);
      }
    };
    fetchData().catch((error) => console.log(error));
  };

  const markAssigned = (): void => {
    form.setValue("markassigned", 1);
    form.setValue("penality", 0);
  };

  const handleComboValueChange = (selectedValue: string): void => {
    setCategoryId(selectedValue);
    form.setValue("category", selectedValue);
  };

  const updateFractions = (): void => {
    const formData = form.getValues();
    const updatedAnswers = formData.answers?.map((answer) => {
      if (answer.isAnswer) {
        const totalIsAnswerTrue = formData.answers?.filter(
          (ans) => ans.isAnswer,
        ).length;
        const fraction =
          totalIsAnswerTrue !== undefined && totalIsAnswerTrue > 0
            ? 1 / totalIsAnswerTrue
            : 0;
        return {
          ...answer,
          fraction,
        };
      } else {
        return {
          ...answer,
          fraction: 0,
        };
      }
    });
    form.setValue("answers", updatedAnswers);
  };

  const setRichTextValue = (richTextValue: richTextType | undefined): void => {
    if (richTextValue && richTextValue.htmlValue == null) {
      richTextValue = undefined;
    }
    form.setValue("question", richTextValue?.htmlValue ?? "");
    setRichTextValues(richTextValue);
  };

  const handleKeyDown = (event: KeyboardEvent<HTMLInputElement>): void => {
    const forbiddenKeys = ["e", "E", "-", "+"];
    if (forbiddenKeys.includes(event.key)) {
      event.preventDefault();
    }
  };

  const handlePenaltyKeyPress = (
    event: KeyboardEvent<HTMLInputElement>,
  ): void => {
    event.preventDefault();
  };

  const openModal = (): void => {
    setIsOpen(true);
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="space-y-4">
      <Form {...form}>
        <form
          onSubmit={(event) => void form.handleSubmit(onSubmit)(event)}
          className="space-y-4"
        >
          <div className="w-full flex flex-wrap">
            <div className="w-full sm:w-1/3 pr-4">
              <FormField
                control={form.control}
                name="category"
                render={() => (
                  <FormItem>
                    <FormLabel>
                      {t("questionBank.selectQuestionCategory")}
                      <span className="text-red-700">*</span>
                    </FormLabel>
                    <FormControl>
                      <div className="overflow-x-auto">
                        <Combobox
                          data={comboData}
                          onSelectChange={handleComboValueChange}
                          defaultLabel={t("exams.selectCategroy")}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="w-full sm:w-1/3 pr-4">
              <FormField
                control={form.control}
                name="questiontype"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {t("questionBank.typeOfQuestions")}{" "}
                      <span className="text-red-700">*</span>
                    </FormLabel>
                    <FormControl>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={questionType[0].label}
                        disabled={true}
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Select" />
                        </SelectTrigger>
                        <SelectContent>
                          {questionType.map((type) => (
                            <SelectItem key={type.value} value={type.value}>
                              {type.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="w-full sm:w-1/3 pr-4">
              <FormField
                control={form.control}
                name="contenttype"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {t("questionBank.type")}{" "}
                      <span className="text-red-700">*</span>
                    </FormLabel>
                    <FormControl>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue="PLAIN_TEXT"
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder={t("questionBank.select")} />
                        </SelectTrigger>
                        <SelectContent>
                          {contentType.map((type) => (
                            <SelectItem key={type.value} value={type.value}>
                              {type.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          <Button
            type="button"
            className="mb-2 float-right bg-[#fb8500]"
            onClick={openModal}
          >
            {formulaValue !== undefined && formulaValue !== ""
              ? t("questionBank.editEquations")
              : t("questionBank.addEquations")}
          </Button>

          <div className="w-full flex">
            <div className="w-full pr-4">
              <FormField
                name="question"
                render={() => (
                  <FormItem>
                    <FormLabel>
                      {defaultEquation.length > 0 ? (
                        <>
                          {t("questionBank.question")}{" "}
                          <span className="text-red-700">*</span>
                        </>
                      ) : (
                        <>
                          {t("questionBank.enterTheQuestion")}{" "}
                          <span className="text-red-700">*</span>
                        </>
                      )}
                    </FormLabel>
                    {formulaValue === "" && (
                      <Editor
                        value=""
                        maxLength={400}
                        onTextChange={(event) => {
                          const htmlValue = event.htmlValue;
                          const richTextValue = {
                            htmlValue: htmlValue,
                          };
                          setRichTextValue(richTextValue as richTextType);
                        }}
                        style={{ height: "200px" }}
                      />
                    )}
                    {formulaValue !== "" && (
                      <div
                        style={{
                          border: "1px solid #000",
                          padding: "10px",
                          borderRadius: "8px",
                          marginTop: "20px",
                          width: "100%",
                          height: "200px",
                        }}
                      >
                        <MathComponent mathml={formulaValue} display={true} />
                      </div>
                    )}
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          <div className="w-full flex">
            <div className="w-full sm:w-1/3 pr-4">
              <FormField
                name="markassigned"
                render={(field) => (
                  <FormItem>
                    <FormLabel>
                      {t("questionBank.markAssigned")}
                      <span className="text-red-700">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        id="markassigned"
                        type="number"
                        // disabled={isDisabled}
                        min={0}
                        onKeyDown={handleKeyDown}
                        placeholder={t("questionBank.markAssigned")}
                        autoComplete="off"
                        {...register("markassigned")}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="w-full sm:w-1/4 pr-4">
              <FormField
                name="penality"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("questionBank.penaltyApplicable")}</FormLabel>
                    <FormControl>
                      <Input
                        id="penality"
                        type="number"
                        min={0.0}
                        max={1.0}
                        step=".05"
                        onKeyDown={handlePenaltyKeyPress}
                        autoComplete="off"
                        placeholder={t("questionBank.penaltyApplicable")}
                        defaultValue={0.0}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          {noOfChoices !== 0 && (
            <div className="border p-4 mt-4 bg-[#fff]">
              <div className="w-full flex ml-2 mt-2 mb-2 flex-wrap">
                {Array.from({ length: noOfChoices }, (_, index) => (
                  <div key={index} className="w-full sm:w-1/2 pr-4 mb-4">
                    <Card className="mt-2 rounded-none shadow-none">
                      <CardContent className="flex flex-row bg-[#FFF] pt-4">
                        <div className="w-1/2 pr-4 rounded-none">
                          <FormField
                            name={`answers.${index}.text`}
                            render={() => (
                              <FormItem>
                                <FormLabel className="mr-2">
                                  {t("questionBank.choice")} {index + 1}{" "}
                                  <span className="text-red-700">*</span>
                                </FormLabel>
                                <FormControl>
                                  <Input
                                    autoComplete="off"
                                    {...register(`answers.${index}.text`)}
                                    onChange={(e) => {
                                      const value = e.target.value;
                                      const sanitizedValue = value
                                        .trimStart()
                                        .replace(/\s{2,}/g, " ")
                                        .replace(/\s+$/, (match) =>
                                          match.length > 1
                                            ? match.slice(0, -1)
                                            : match,
                                        );
                                      e.target.value = sanitizedValue;
                                    }}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        <div className="w-1/2 pr-4 flex-grow">
                          <FormField
                            control={form.control}
                            name={`answers.${index}.type`}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="mr-2">
                                  {t("questionBank.type")}{" "}
                                  <span className="text-red-700">*</span>
                                </FormLabel>
                                <FormControl>
                                  <Select
                                    onValueChange={field.onChange}
                                    defaultValue={
                                      (field.value as string) ?? "PLAIN_TEXT"
                                    }
                                  >
                                    <SelectTrigger className="w-full">
                                      <SelectValue placeholder="Select" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      {contentType.map((type) => (
                                        <SelectItem
                                          key={type.value}
                                          value={type.value}
                                        >
                                          {type.label}
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </CardContent>
                      <CardContent className="flex flex-row bg-[#FFF]">
                        <div className="w-1/2 pr-4 flex-grow">
                          <FormField
                            control={form.control}
                            name={`answers.${index}.isAnswer`}
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <Checkbox
                                    checked={field.value}
                                    onCheckedChange={(value) => {
                                      field.onChange(value);
                                      updateFractions();
                                    }}
                                  />
                                </FormControl>
                                <FormLabel className="ml-2">
                                  {t("questionBank.markAsAnswer")}
                                </FormLabel>
                              </FormItem>
                            )}
                          />
                        </div>
                        <div className="w-1/2 pr-4 flex-grow">
                          <FormField
                            control={form.control}
                            name={`answers.${index}.fraction`}
                            render={() => (
                              <FormItem>
                                <FormLabel>
                                  {t("questionBank.fraction")}
                                </FormLabel>
                                <FormControl>
                                  <Input
                                    autoComplete="off"
                                    {...register(`answers.${index}.fraction`)}
                                    defaultValue={0}
                                    disabled={true}
                                  />
                                </FormControl>
                              </FormItem>
                            )}
                          />
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                ))}
              </div>
            </div>
          )}

          <ModalButton
            closeDialog={() => closeDialog(false)}
            closeLabel={t("buttons.cancel")}
            submitLabel={t("questionBank.addQuestion")}
          />
        </form>
      </Form>

      {categoryAddStatus && (
        <Modal
          title={t("questionBank.addQuestionCategory")}
          header=""
          openDialog={categoryAddStatus}
          closeDialog={handleAddCategory}
        >
          <AddNewQuesionCategoryForm
            closeDialog={() => handleAddCategory()}
            isDialogOpen={() => categoryAddStatus}
            categoryList={() => {}}
            data={{ value: "", label: "", description: "" }}
          />
        </Modal>
      )}

      {isOpen && (
        <Modal
          title={t("questionBank.addEquations")}
          header=""
          openDialog={isOpen}
          closeDialog={() => setIsOpen(false)}
          type="max-w-5xl"
        >
          <MathFormulaEditor
            onSave={saveModal}
            onCancel={cancelModal}
            isModal={true}
            data={defaultEquation}
          />
        </Modal>
      )}
    </div>
  );
}
