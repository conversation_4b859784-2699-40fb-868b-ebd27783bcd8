"use client";
import type { ColumnDef } from "@tanstack/react-table";
import type {
  CheckpointsUserStatsReturnType,
  UserSessionColumnDefinition,
  UserSessionRowDefinition,
} from "@/types";
import moment from "moment";
import { ArrowUpDown } from "lucide-react";
import { Button } from "@/components/ui/button";
export const columns: ColumnDef<CheckpointsUserStatsReturnType>[] = [
  /* {
   accessorKey: "first_name",
    header: ({ column }: UserSessionColumnDefinition): React.JSX.Element => {
      return (
        <Button
          className="px-0"
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          User
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    }, */
  /* cell: ({ row }: UserSessionRowDefinition): React.JSX.Element => (
      <div className="text-align">
        {row.original !== null
          ? row.original.first_name ?? "" + " " + row.original.last_name ?? ""
          : ""}
      </div>
    ), 
  },*/
  {
    accessorKey: "first_name",
    header: ({ column }: UserSessionColumnDefinition): React.JSX.Element => {
      return (
        <Button
          className="px-0"
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          User
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },

    cell: ({ row }: UserSessionRowDefinition): React.JSX.Element => {
      const firstName = row.original.first_name ?? " ";
      const lastName = row.original.last_name ?? " ";
      return <div className="text-align">{`${firstName} ${lastName}`}</div>;
    },
  },
  {
    accessorKey: "email",
    header: "Email",

    cell: ({ row }: UserSessionRowDefinition): React.JSX.Element => (
      <div className="text-align">{row.original.email ?? ""}</div>
    ),
  },
  {
    accessorKey: "enrollment_time",
    header: "Enrolled On",
    cell: ({ row }: UserSessionRowDefinition): React.JSX.Element => {
      const formattedDate =
        row.original !== null
          ? moment
              .utc(row.original.enrollment_time)
              .local()
              .format("DD-MMM-YYYY hh:mm a")
          : "";

      return <div>{formattedDate}</div>;
    },
  },
  // {
  //   accessorKey: "video_name",
  //   header: "Video Name",

  //   cell: ({ row }: UserSessionRowDefinition): React.JSX.Element => (
  //     <div className="text-align">
  //       {row.original !== null ? row.original?.video_name : ""}
  //     </div>
  //   ),
  // },
  {
    accessorKey: "last_watched_on",
    header: "Last Watched On",
    cell: ({ row }: UserSessionRowDefinition): React.JSX.Element => {
      const formattedDate =
        row.original !== null
          ? moment
              .utc(row.original?.last_watched_on ?? "")
              .local()
              .format("DD-MMM-YYYY hh:mm a")
          : "";
      return <div>{formattedDate}</div>;
    },
  },
  // {
  //   accessorKey: "checkpoint_name",
  //   header: "Check Point Name",
  //   cell: ({ row }: UserSessionRowDefinition): React.JSX.Element => (
  //     <div className="text-align">
  //       {row.original !== null ? row.original?.checkpoint_name : ""}
  //     </div>
  //   ),
  // },

  // {
  //   accessorKey: "exam_start_time",
  //   header: "Exam Attended On",
  //   cell: ({ row }: UserSessionRowDefinition): React.JSX.Element => {
  //     const formattedDate =
  //       row.original !== null
  //         ? moment
  //             .utc(row.original?.exam_start_time ?? "")
  //             .local()
  //             .format("DD-MMM-YYYY hh:mm a")
  //         : "";
  //     return <div>{formattedDate}</div>;
  //   },
  // },
  {
    accessorKey: "checkpoint_result",
    header: "Exam Result",

    cell: ({ row }: UserSessionRowDefinition): React.JSX.Element => (
      <div className="text-align">
        {row.original !== null ? row.original.exam_result : ""}
      </div>
    ),
  },
];
