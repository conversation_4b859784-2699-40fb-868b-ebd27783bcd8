"use client";
import React, { useState, useEffect, type KeyboardEvent } from "react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useForm } from "react-hook-form";
import type {
  AddNewExamFormType,
  richTextType,
  ErrorCatch,
  ToastType,
  CourseDetailsRequest,
  TopicDataType,
  ComboData,
  folderListResponse,
  InnerItem,
  LogUserActivityRequest,
} from "@/types";
import { AddExamSchema } from "../../schema/schema";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import TreeSelectComponent from "@/components/ui/tree-select/tree-select";
import { Checkbox } from "@/components/ui/checkbox";
import { <PERSON><PERSON> } from "@/components/ui/button";
import useTopics from "../../hooks/useTopics";
import useCourse from "../../hooks/useCourse";
import { Combobox } from "../../components/ui/combobox";
import useAddExams from "@/hooks/useAddExams";
import moment from "moment";
import type { TreeDataItem } from "@/components/ui/tree";
import { Editor } from "primereact/editor";
import { useToast } from "@/components/ui/use-toast";
import { useRouter } from "next/navigation";
import { DateTimePicker } from "@/components/ui/date-time-picker/date-time-picker";
import { parseZonedDateTime } from "@internationalized/date";
import type { DateValue } from "@internationalized/date";
import "moment-timezone";
import { useSearchParams } from "next/navigation";
import {
  TIME_ZONE,
  ORG_KEY,
  FORMATTED_DATE_FORMAT,
  DATE_FORMAT,
  DAY,
  DAYS,
  MINUTE,
  examTypes,
  marksTypes,
  pageUrl,
} from "@/lib/constants";
import NextBreadcrumb from "../breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumb";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";

export default function ExamAddPage(): React.JSX.Element {
  const { t } = useTranslation();
  const router = useRouter();
  const searchParams = useSearchParams();
  const sectionId = searchParams.get("sectionId") ?? null;
  const { getSections, addNewExam } = useAddExams();
  const { getCourseList, convertDataToTreeNode, getFolderList } = useCourse();
  const { getCategoryHierarchy } = useTopics();
  const { toast } = useToast() as ToastType;
  const form = useForm<AddNewExamFormType>({
    resolver: zodResolver(AddExamSchema),
  });
  const { handleSubmit, register } = form;
  const { updateUserActivity } = useLogUserActivity();

  const [nodeData, setNodeData] = React.useState<TreeDataItem[]>([]);
  const [selectedTopic, setSelectedTopic] = React.useState<string | null>();
  const [courseData, setCourseData] = useState<
    { value: string; label: string }[]
  >([]);
  const [selectCourse, setSelectCourse] = useState<string>("");
  const [selectSection, setSelectSection] = useState<string>("");
  const [sectionData, setSectionData] = React.useState<
    { value: string; label: string }[]
  >([]);
  const [startDate, setStartDate] = React.useState<DateValue | undefined>();
  const [endDate, setEndDate] = React.useState<DateValue | undefined>();
  const [minEndDate, setMinEndDate] = React.useState<DateValue | undefined>();
  const [richTextValues, setRichTextValues] = React.useState<
    richTextType | undefined
  >(undefined);
  const [isCourseDisabled, setIsCourseDisabled] = useState(true);
  const [isSectionDisabled, setIsSectionDisabled] = useState(true);
  const [selectedExamType, setSelectedExamType] = useState<string>("");
  const [selectedMarksType, setSelectedMarksType] = useState<string>("");
  const [isExamDisabled, setIsExamDisabled] = useState(false);
  const [folderData, setFolderData] = useState<ComboData[]>([]);
  const [selectFolder, setSelectFolder] = useState<string | null>();
  const [isFolderDisabled, setIsFolderDisabled] = useState(true);
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);
  const [openPenaltyDiv, setOpenPenaltyDiv] = useState<boolean>(false);
  const [wrongAnswers, setWrongAnswers] = useState<number>();
  const [wrongAnswerWeightage, setWrongAnswerWeightage] = useState<number>();

  useEffect(() => {
    topicList();

    setSelectCourse("");
    setSelectSection("");
    setSectionData([]);
    setIsSectionDisabled(true);
  }, [selectedTopic]);

  useEffect(() => {
    setBreadcrumbItems(
      getBreadCrumbItems(t, t("breadcrumb.addExams"), { "": "" }),
    );
  }, [t]);

  useEffect(() => {
    const fetchCourseData = async (): Promise<void> => {
      try {
        const courses = await getCourseList(selectedTopic);
        if (courses !== null && courses !== undefined) {
          const filteredCourses = courses
            .filter(
              (course) =>
                course.course_id !== null && course.short_name !== null,
            )
            .map((course) => ({
              value: course.course_id ?? "",
              label: course.short_name ?? "",
            }));
          setCourseData(filteredCourses);
          const selectedCourseData = courses.find(
            (course) => course.course_id === selectCourse,
          );

          const start_date = selectedCourseData?.start_date;
          const end_date = selectedCourseData?.end_date;

          const currentTimezone = moment.tz.guess();
          const originalDatetime = moment().tz(currentTimezone);

          const newDatetime = originalDatetime.clone().add(10, MINUTE);
          const formattedDatetime =
            newDatetime.format(DATE_FORMAT) + `[${currentTimezone}]`;
          const currentDateTime = parseZonedDateTime(formattedDatetime);

          if (end_date !== null && end_date !== undefined) {
            const formattedEndDatetime = moment
              .tz(end_date, TIME_ZONE)
              .subtract(1, DAYS)
              .format(DATE_FORMAT);
            if (originalDatetime.isAfter(formattedEndDatetime)) {
              toast({
                variant: ERROR_MESSAGES.toast_variant_destructive,
                title: t("errorMessages.toast_validation_error"),
                description: t("errorMessages.course_expired"),
              });
              return;
            }
            const parsedEndDatetime = parseZonedDateTime(
              `${formattedEndDatetime}[${currentTimezone}]`,
            );
            form.setValue("end_time", parsedEndDatetime);
            setEndDate(parsedEndDatetime);
          } else {
            setEndDate(undefined);
          }

          if (start_date !== null && start_date !== undefined) {
            const formattedStartDatetime = moment.tz(start_date, TIME_ZONE);

            let displayDate;
            if (formattedStartDatetime.isBefore(originalDatetime, DAY)) {
              displayDate = currentDateTime;

              console.log("start date: " + displayDate);

              form.setValue("start_time", displayDate);
              setStartDate(displayDate);
            } else {
              const formattedNextDayStart = formattedStartDatetime
                .clone()
                // .add(1, DAYS)
                .format(DATE_FORMAT);
              displayDate = parseZonedDateTime(
                `${formattedNextDayStart}[${currentTimezone}]`,
              );
              form.setValue("start_time", displayDate);
              setStartDate(displayDate);
            }
          } else {
            setStartDate(undefined);
          }
        } else {
          setCourseData([]);
        }
      } catch (error) {
        console.error(error);
      }
    };
    fetchCourseData().catch((error) => console.log("Error", error));
  }, [selectCourse, selectedTopic]);

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  useEffect(() => {
    const sectionDetails = async (): Promise<void> => {
      try {
        if (selectCourse !== null && selectCourse !== "") {
          // Check if selectCourse is not null
          const orgId = localStorage.getItem(ORG_KEY);
          const reqParams: CourseDetailsRequest = {
            course_id: selectCourse,
            org_id: orgId ?? "",
          };
          const data = await getSections(reqParams);
          if (data.length > 0) {
            const filterSections = data[0].sections.map((section) => {
              return {
                value: section.section_id,
                label: section.name,
              };
            });
            setSectionData(filterSections);
          } else {
            setSectionData([]); // If no sections found, reset sectionData
          }
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };
    sectionDetails().catch((error) => console.log("Error", error));
  }, [selectCourse]);

  useEffect(() => {
    // Use watch to get the latest values for total_mark and num_of_questions
    const totalMark = form.watch("total_mark");
    const noOfQuestions = form.watch("num_of_questions");

    if (noOfQuestions > 0) {
      const marksPerQuest = String(totalMark / noOfQuestions); // Calculate marks per question
      form.setValue("marks", marksPerQuest); // Update the marks field
      console.log(
        "totalMark / noOfQuestions",
        totalMark,
        noOfQuestions,
        marksPerQuest,
      );
      handleMarksTypeChange(marksPerQuest);
    }
  }, [form.watch("total_mark"), form.watch("num_of_questions")]);

  const handleCourseChange = (selectedOption: string): void => {
    setSelectCourse(selectedOption);
    form.setValue("course_id", selectedOption);
    setIsSectionDisabled(false);
  };

  const handleSectionChange = (selectedOption: string): void => {
    setSelectSection(selectedOption);
    form.setValue("section_id", selectSection);
    setIsFolderDisabled(false);
  };

  useEffect(() => {
    const fetchFolderData = async (): Promise<void> => {
      try {
        const data = await getFolderList(selectCourse, selectSection);
        const datas: ComboData[] = data.map((item: folderListResponse) => ({
          value: item.id,
          label: item.folder_name,
        }));
        setFolderData(datas);
      } catch (error) {
        console.error("Error fetching data:");
      }
    };
    fetchFolderData().catch((error) => console.log("Error", error));
    form.setValue("penalty_mode", "Fixed");
    form.setValue("penalty_type", "Exam");
  }, [selectCourse, selectSection]);

  const handleFolderChange = (selectedOption: string): void => {
    setSelectFolder(selectedOption);
    form.setValue("section_id", selectedOption);
  };

  const setRichTextValue = (richTextValue: richTextType | undefined): void => {
    if (richTextValue && richTextValue.htmlValue == null) {
      richTextValue = undefined;
    }
    form.setValue("description", richTextValue?.htmlValue as string);
    setRichTextValues(richTextValue);
  };

  const topicList = (): void => {
    const fetchData = async (): Promise<void> => {
      const orgId = localStorage.getItem(ORG_KEY) as string;
      const params = {
        org_id: orgId,
        filter_data: 1,
      };
      try {
        const topics = await getCategoryHierarchy(params);
        if (topics.length > 0) {
          const treeData: TreeDataItem[] = convertDataToTreeNode(
            topics as TopicDataType[],
          );
          setNodeData(treeData);
        }
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
      }
    };
    fetchData().catch((error) => console.log(error));
  };

  useEffect(() => {
    form.setValue("allowed_attempts", 1);
  }, [form]);

  const handleExamTypeChange = (selectedValue: string): void => {
    form.setValue("quiz_type", selectedValue);
    setSelectedExamType(selectedValue);
  };
  const handleMarksTypeChange = (selectedValue: string): void => {
    form.setValue("marks", selectedValue);
    setSelectedMarksType(selectedValue);
  };
  async function onSubmit(): Promise<void> {
    const orgId = localStorage.getItem(ORG_KEY) as string;
    const formData = form.getValues();
    if (richTextValues?.htmlValue !== undefined) {
      formData.description = richTextValues?.htmlValue;
    }
    const passMark = formData.pass_mark;
    const totalMark = formData.total_mark;
    const noOfQuestions = formData.num_of_questions;

    if (Number(passMark) > Number(totalMark)) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_validation_error"),
        description: t("errorMessages.pasmark_greaterthan_totalmark"),
      });
      return;
    }

    if (isExamDisabled) {
      if (totalMark / noOfQuestions != parseInt(selectedMarksType)) {
        const mark = totalMark / noOfQuestions;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_validation_error"),
          description: t("errorMessages.mark_for_all_questions", { mark }),
        });
        return;
      }
    }
    const dateStart = formData.start_time;
    const startDate = new Date(
      dateStart.year,
      dateStart.month - 1,
      dateStart.day,
      dateStart.hour,
      dateStart.minute,
    );
    const dateEnd = formData.end_time;
    const endDate = new Date(
      dateEnd.year,
      dateEnd.month - 1,
      dateEnd.day,
      dateEnd.hour,
      dateEnd.minute,
    );
    const momentStartDate = moment(startDate).utc();
    const momentEndDate = moment(endDate).utc();

    const formattedValidFrom = momentStartDate.format(FORMATTED_DATE_FORMAT);

    const formattedValidTo = momentEndDate.format(FORMATTED_DATE_FORMAT);
    if (selectedTopic === "" || selectedTopic === null) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_validation_error"),
        description: t("errorMessages.select_category"),
      });
      return;
    }
    const examData = {
      course_id: selectCourse,
      section_id: selectSection,
      org_id: orgId ?? "",
      folder_id: selectFolder ?? null,
      quiz_data: {
        name: formData.name,
        description: formData.description,
        main_topic: selectedTopic ?? "",
        num_of_questions: formData.num_of_questions,
        total_mark: formData.total_mark,
        pass_mark: formData.pass_mark,
        start_time: formattedValidFrom,
        end_time: formattedValidTo,
        duration: formData.duration,
        allowed_attempts: formData.allowed_attempts,
        quiz_type: selectedExamType,
        penalty_available: formData.penalty_available ?? false,
        is_premium: formData.is_premium ?? false,
        is_equal_weightage: formData.is_weightage ?? false,
        eq_weightage_marks: parseInt(selectedMarksType),
        no_wrong_answers: wrongAnswers ?? 0,
        minus_mark_applicable: wrongAnswerWeightage ?? 0,
        penalty_type: formData.penalty_type as string,
        calculation_type: formData.penalty_mode as string,
      },
    };
    try {
      const result = await addNewExam(examData);
      if (result.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.toast_success_title"),
          description: t("successMessages.exam_added"),
        });
        const params = {
          activity_type: "Exam",
          screen_name: "Exam",
          action_details: "Exam added successfully",
          target_id: result.quiz_id as string,
          log_result: "SUCCESS",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
        if (sectionId !== null) {
          router.push(`${pageUrl.resourceListView}?sectionId=${sectionId}`);
        } else {
          router.push(`${pageUrl.exams}`);
        }
      } else if (result.status === "error") {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: result.status,
        });
        const params = {
          activity_type: "Exam",
          screen_name: "Exam",
          action_details: "Failed to add exam",
          target_id: result.quiz_id as string,
          log_result: "ERROR",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
    }
  }

  const handleValidFromChanage = (dateObject: DateValue): void => {
    setMinEndDate(dateObject);
    const modifiedTime = moment.utc(dateObject);
    const startTime = moment.utc(startDate);

    if (modifiedTime.isBefore(startTime)) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.exam_time_before_course_time"),
      });
      return;
    }
  };

  const handleValidToChanage = (dateObject: DateValue): void => {
    const modifiedTime = moment.utc(dateObject);
    const endTime = moment.utc(endDate);

    if (modifiedTime.isAfter(endTime)) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.exam_time_after_course_time"),
      });
    }
  };

  const handleCancel = (): void => {
    if (sectionId !== null) {
      router.push(`${pageUrl.resourceListView}?sectionId=${sectionId}`);
    } else {
      router.push(`${pageUrl.exams}`);
    }
  };

  const handleKeyDown = (event: KeyboardEvent<HTMLInputElement>): void => {
    const forbiddenKeys = ["e", "E", "-", "+"];
    if (forbiddenKeys.includes(event.key)) {
      event.preventDefault();
    }
  };

  const handleKeyDownDuration = (
    event: React.KeyboardEvent<HTMLInputElement>,
  ): void => {
    const forbiddenKeys = ["e", "E", "-", "+"];
    const { value } = event.currentTarget;
    const isDeleteKey = event.key === "Backspace" || event.key === "Delete";
    if (
      forbiddenKeys.includes(event.key) || // Prevent typing forbidden keys
      (value.length >= 3 && !isDeleteKey) // Limit to 3 digits
    ) {
      event.preventDefault();
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    const value = e.target.value;
    const sanitizedValue = value
      .trimStart()
      .replace(/^\p{P}+/u, "")
      .replace(/[^\p{L}\p{M}\p{N}\s&-]/gu, "")
      .replace(/\s{2,}/g, " ")
      .replace(/^[^\p{L}\p{M}\p{N}]|[^\p{L}\p{M}\p{N}\s&-]/gu, "")
      .replace(/\s+$/, (match) =>
        match.length > 1 ? match.slice(0, -1) : match,
      );
    form.setValue("name", sanitizedValue);
  };

  return (
    <div>
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      <div className="w-full mb-4">
        <h1 className="text-2xl font-semibold tracking-tight">{t("exams.addExam")}</h1>
      </div>
      <div className="w-full border rounded-md ps-4 pb-4 pe-4 mb-2 bg-[#fff]">
        <div>
          <Form {...form}>
            <form
              onSubmit={(event) => void handleSubmit(onSubmit)(event)}
              className="space-y-4 flex flex-wrap"
            >
              <div className="w-full md:w-1/3 mt-4 pe-4">
                <FormField
                  name="main_topic"
                  control={form.control}
                  render={() => (
                    <FormItem>
                      <FormLabel>
                        {t("exams.selectCategroy")}
                        <span className="text-red-700">*</span>
                      </FormLabel>
                      <FormControl>
                        <TreeSelectComponent
                          nodeData={nodeData}
                          selectLabel="Select Category"
                          onNodeSelect={(selectedValue) => {
                            setSelectedTopic(selectedValue);
                            setIsCourseDisabled(false);
                            form.setValue(
                              "main_topic",
                              selectedValue as string,
                            );
                          }}
                        ></TreeSelectComponent>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="w-full md:w-1/3 pe-4">
                <FormField
                  name="course_id"
                  control={form.control}
                  render={() => (
                    <FormItem>
                      <>
                        <FormLabel>
                          {t("exams.selectCourse")}
                          <span className="text-red-700">*</span>
                        </FormLabel>
                      </>
                      <>
                        <FormControl>
                          <Combobox
                            data={courseData}
                            onSelectChange={handleCourseChange}
                            isDisabled={isCourseDisabled}
                          />
                        </FormControl>
                      </>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="w-full md:w-1/3">
                <FormField
                  name="section_id"
                  control={form.control}
                  render={() => (
                    <FormItem>
                      <FormLabel>
                        {t("exams.selectSection")}
                        <span className="text-red-700">*</span>
                      </FormLabel>
                      <FormControl>
                        <Combobox
                          data={sectionData}
                          onSelectChange={handleSectionChange}
                          isDisabled={isSectionDisabled}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="w-full md:w-1/3 pe-4">
                <FormField
                  name="folder_id"
                  control={form.control}
                  render={() => (
                    <FormItem>
                      <FormLabel>{t("exams.selectFolder")}</FormLabel>
                      <FormControl>
                        <Combobox
                          data={folderData}
                          onSelectChange={handleFolderChange}
                          isDisabled={isFolderDisabled}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="w-full md:w-1/3 pe-4">
                <FormField
                  name="quiz_type"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t("exams.selectExamType")}
                        <span className="text-red-700">*</span>
                      </FormLabel>
                      <FormControl>
                        <Select
                          onValueChange={handleExamTypeChange}
                          defaultValue={field.value}
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Select" />
                          </SelectTrigger>
                          <SelectContent>
                            {examTypes.map((option) => (
                              <SelectItem
                                key={option.value}
                                value={option.value}
                              >
                                {option.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="w-full md:w-1/3">
                <FormField
                  name="name"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t("exams.examName")}{" "}
                        <span className="text-red-700">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          autoComplete="off"
                          type="text"
                          maxLength={100}
                          {...field}
                          onChange={handleInputChange}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="w-full md:w-1/3 pe-4">
                <FormField
                  name="num_of_questions"
                  // control={form.control}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t("exams.noOfQuestions")}{" "}
                        <span className="text-red-700">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          autoComplete="off"
                          type="number"
                          min={0}
                          onKeyDown={handleKeyDownDuration}
                          {...field}
                          // {...register("num_of_questions", {
                          //   required: "required",
                          //   minLength: {
                          //     value: 1,
                          //     message: "min length is 1"
                          //   }
                          // })}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="w-full md:w-1/3 pe-4">
                <FormField
                  name="total_mark"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t("exams.totalMarks")}
                        <span className="text-red-700">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          autoComplete="off"
                          type="number"
                          min={1}
                          onKeyDown={handleKeyDown}
                          {...field}
                          // {...register("total_mark", {
                          //   validate: (value) => {
                          //     return !isNaN(value);
                          //   },
                          //   valueAsNumber: true,
                          // })}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="w-full md:w-1/3">
                <FormField
                  name="pass_mark"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t("exams.passMark")}{" "}
                        <span className="text-red-700">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          autoComplete="off"
                          type="number"
                          min={1}
                          onKeyDown={handleKeyDown}
                          {...field}
                          // {...register("pass_mark", { valueAsNumber: true })}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="w-full md:w-1/3 pe-4">
                <FormField
                  name="duration"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t("exams.duration")}
                        <span className="text-red-700">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          autoComplete="off"
                          type="number"
                          min={1}
                          onKeyDown={handleKeyDownDuration}
                          {...field}
                          // {...register("duration", { valueAsNumber: true })}
                          // {...register("duration", {
                          //   validate: (value) => {
                          //     return (
                          //       value <= 180 ||
                          //       "Duration must be up to 180 minutes"
                          //     );
                          //   },
                          //   valueAsNumber: true,
                          // })}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="w-full md:w-1/3 pe-4">
                <FormField
                  name="allowed_attempts"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t("exams.allowedAttempts")}
                        <span className="text-red-700">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          autoComplete="off"
                          type="number"
                          min={1}
                          onKeyDown={handleKeyDown}
                          {...field}
                          {...register("allowed_attempts", {
                            value: 1,
                            valueAsNumber: true,
                          })}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="w-full flex flex-wrap">
                <div className="w-full md:w-1/3 pe-4 mb-2">
                  <FormField
                    name="penalty_available"
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center mt-7">
                          <FormControl>
                            <>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={(value) => {
                                  field.onChange(value);
                                  if (value === true) {
                                    setOpenPenaltyDiv(true);
                                  } else {
                                    setOpenPenaltyDiv(false);
                                  }
                                }}
                              />
                              <FormLabel className="px-4">
                                {t("exams.penaltyApplicable")}
                              </FormLabel>
                            </>
                          </FormControl>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {openPenaltyDiv && (
                  <>
                    <div className="w-full md:w-1/3 pe-4 mb-2">
                      <FormItem>
                        <FormLabel>{t("exams.penaltyType")}</FormLabel>
                        <FormControl>
                          <Select
                            onValueChange={(value) => {
                              form.setValue("penalty_type", value);
                              if (value === "Question") {
                                form.setValue("num_of_questions", 1);
                                setWrongAnswers(1);
                              } else {
                                setWrongAnswers(undefined);
                              }
                            }}
                            defaultValue="Exam"
                          >
                            <SelectTrigger className="w-full">
                              <SelectValue
                                placeholder={t("exams.selectPenaltyType")}
                              />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="Question">
                                {t("exams.questionBased")}
                              </SelectItem>
                              <SelectItem value="Exam">
                                {t("exams.examBased")}
                              </SelectItem>
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    </div>
                    <div className="w-full md:w-1/3 pe-4 mb-2">
                      <FormItem>
                        <FormLabel>{t("exams.penaltyMode")}</FormLabel>
                        <FormControl>
                          <Select
                            onValueChange={(value) => {
                              form.setValue("penalty_mode", value);
                            }}
                            defaultValue={t("exams.fixed")}
                          >
                            <SelectTrigger className="w-full">
                              <SelectValue
                                placeholder={t("exams.selectPenaltyMode")}
                              />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="Fixed">
                                {t("exams.fixed")}
                              </SelectItem>
                              <SelectItem value="Percentage">
                                {t("exams.percentageWise")}
                              </SelectItem>
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    </div>
                    <div className="w-full md:w-1/3 pe-4 mb-2"></div>
                    <div className="w-full md:w-1/3 pe-4">
                      <FormItem>
                        <FormLabel>{t("exams.noOfWrongAnswers")}</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min={1}
                            // max={(form.getValues("num_of_questions") !== 0) ? Number(form.getValues("num_of_questions")) - 1 : undefined}
                            autoComplete="off"
                            value={wrongAnswers ?? ""}
                            onChange={(e) => {
                              const numQuestions = Number(
                                form.getValues("num_of_questions"),
                              );
                              const value = Number(e.target.value);
                              if (numQuestions !== 0 && value >= numQuestions) {
                                setWrongAnswers(numQuestions - 1);
                              } else {
                                setWrongAnswers(value);
                              }
                            }}
                            onKeyDown={handleKeyDown}
                            disabled={
                              form.getValues("penalty_type") === "Question"
                            }
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    </div>
                    <div className="w-full md:w-1/3 pe-4">
                      <FormItem>
                        <FormLabel>
                          {form.watch("penalty_mode") === "Percentage"
                            ? t("exams.penalty%")
                            : t("exams.penaltyMarksDeducted")}
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min={0}
                            step={
                              form.watch("penalty_mode") === "Percentage"
                                ? "1"
                                : "any"
                            }
                            pattern={
                              form.watch("penalty_mode") === "Percentage"
                                ? "\\d*"
                                : undefined
                            }
                            autoComplete="off"
                            value={wrongAnswerWeightage ?? ""}
                            onChange={(e) =>
                              setWrongAnswerWeightage(Number(e.target.value))
                            }
                            onKeyDown={handleKeyDown}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    </div>
                  </>
                )}
              </div>

              <div className="w-full md:w-1/3 pe-4">
                <FormField
                  control={form.control}
                  name="start_time"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t("exams.examValidFrom")}{" "}
                        <span className="text-red-700">*</span>
                      </FormLabel>
                      <FormControl>
                        <DateTimePicker
                          granularity="minute"
                          minValue={startDate}
                          value={field.value as DateValue}
                          hideTimeZone={true}
                          onChange={(newDate) => {
                            handleValidFromChanage(newDate as DateValue);
                            field.onChange(newDate);
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="w-full md:w-1/3 pe-4">
                <FormField
                  control={form.control}
                  name="end_time"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t("exams.examValidTo")}{" "}
                        <span className="text-red-700">*</span>
                      </FormLabel>
                      <FormControl>
                        <DateTimePicker
                          granularity="minute"
                          minValue={minEndDate}
                          maxValue={endDate}
                          hideTimeZone={true}
                          value={field.value as DateValue}
                          // onChange={(newDate) => handleEndDateChange(newDate)}
                          onChange={(newDate) => {
                            handleValidToChanage(newDate as DateValue);
                            field.onChange(newDate);
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="w-full md:w-1/3 flex items-center">
                <FormField
                  name="is_premium"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex items-center mt-7">
                        <FormControl>
                          <>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={(value) => {
                                field.onChange(value);
                              }}
                            />
                            <FormLabel className="px-4">
                              {t("exams.premiumContent")}
                            </FormLabel>
                          </>
                        </FormControl>
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="w-full md:w-1/3 flex items-center">
                <FormField
                  name="is_weightage"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex items-center mt-2">
                        <FormControl>
                          <>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={(value: boolean) => {
                                field.onChange(value);
                                setIsExamDisabled(value);
                                !value ? setSelectedMarksType("") : "";
                              }}
                            />
                            <FormLabel className="px-2">
                              {t("exams.isEqualWeightage")}
                            </FormLabel>
                          </>
                        </FormControl>
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {isExamDisabled && (
                <div className="w-full md:w-1/3 pe-4">
                  <FormField
                    name="marks"
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t("exams.marks")}{" "}
                          <span className="text-red-700">*</span>
                        </FormLabel>
                        <FormControl>
                          <Select
                            onValueChange={handleMarksTypeChange}
                            defaultValue={field.value}
                          >
                            <SelectTrigger className="w-full">
                              <SelectValue
                                placeholder={t("exams.selectMarks")}
                              />
                            </SelectTrigger>
                            <SelectContent>
                              {marksTypes.map((option) => (
                                <SelectItem
                                  key={option.value}
                                  value={option.value}
                                >
                                  {option.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              )}
              <div className="w-full">
                <FormField
                  name="description"
                  control={form.control}
                  render={() => (
                    <FormItem>
                      <FormLabel>
                        {t("exams.examRules")}
                        <span className="text-red-700">*</span>
                      </FormLabel>
                      <FormControl>
                        <Editor
                          value=""
                          onTextChange={(event) => {
                            const htmlValue = event.htmlValue; // Convert the HTML value to your richTextType
                            const richTextValue = {
                              htmlValue: htmlValue,
                            };
                            setRichTextValue(richTextValue as richTextType);
                          }}
                          style={{ height: "320px" }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="w-full flex justify-end mt-4">
                <div className="px-4">
                  <Button
                    // variant="outline"
                    onClick={handleCancel}
                    className="w-full sm:w-auto bg-[#33363F]"
                  >
                    {t("buttons.cancel")}
                  </Button>
                </div>
                <div>
                  <Button type="submit" className="bg-[#9FC089]">
                    {t("buttons.submit")}
                  </Button>
                </div>
              </div>
            </form>
          </Form>
        </div>
      </div>
    </div>
  );
}
