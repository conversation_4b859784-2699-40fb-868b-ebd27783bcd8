import React, { useEffect, useState } from "react";
import { getColumns } from "./columns";
import type { SessionViewsTableType } from "@/types";
import { DataTable } from "../data-table/data-table";
import { useTranslation } from "react-i18next";

interface SessionViewsProps {
  checkpointSessions: SessionViewsTableType[];
}

export function DBSessionViews({
  checkpointSessions,
}: SessionViewsProps): React.JSX.Element {
  const { t } = useTranslation();
  const columns = getColumns(t);
  const [sessionData, setSessionData] = useState<SessionViewsTableType[]>([]);

  useEffect(() => {
    setSessionData(checkpointSessions);
  }, [checkpointSessions]);

  return (
    <div className="p-2 rounded-md">
      <div className="overflow-x-auto border rounded-md p-2 ">
        <DataTable
          columns={columns}
          data={sessionData}
          FilterLabel={t("dashboard.newRegistrations.filterByName")}
          FilterBy={"first_name"}
          actions={[]}
          onSelectedDataChange={() => {}}
        />
      </div>
    </div>
  );
}
