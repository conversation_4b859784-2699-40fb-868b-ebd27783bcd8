"use client";
import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import MainLayout from "../layout/mainlayout";
import { Upload } from "lucide-react";
import { DATE_FORMAT, logos } from "@/lib/constants";
import type {
  CustomBrandingDetails,
  InnerItem,
  LoginUserData,
  ToastType,
} from "@/types";
import { DateTimePicker } from "@/components/ui/date-time-picker/date-time-picker";
import moment from "moment-timezone";
import type { DateValue, ZonedDateTime } from "@internationalized/date";
import { parseZonedDateTime } from "@internationalized/date";
import { Spinner } from "@/components/ui/progressiveLoader";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import useCustomBranding from "@/hooks/useCustomBranding";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/lib/client";
import Image from "next/image";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumb";
import { useTranslation } from "react-i18next";

export default function CustomBranding(): JSX.Element {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState("branding");
  const [validFrom, setValidFrom] = useState<DateValue | null>();
  const [validTo, setValidTo] = useState<DateValue | null>();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [previews, setPreviews] = useState<Record<string, string>>({});
  const [fonts, setFonts] = useState<string>("");
  // const [fontSize, setFontSize] = useState<string>("");
  const [welcomeText, setWelcomeText] = useState<string>("");
  const [footerText, setFooterText] = useState<string>("");
  const [colors, setColors] = useState<Record<string, string>>({});
  const { addCustomBranding, getCustomBrandingDetails } = useCustomBranding();
  const { toast } = useToast() as ToastType;
  const [theme, setTheme] = useState("light");
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);

  useEffect(() => {
    void getBrandingDetails();
  }, [theme]);

  useEffect(() => {
    setBreadcrumbItems(
      getBreadCrumbItems(t, t("breadcrumb.configuration"), { "": "" }),
    );
  }, [t]);
  const getBrandingDetails = async (): Promise<void> => {
    try {
      const orgId = localStorage.getItem("orgId") as string;
      const themes = await getCustomBrandingDetails(orgId as string);
      setIsLoading(false);
      const data = themes.find(
        (item: CustomBrandingDetails) => item.theme_name === theme,
      );
      setBrandingDetails(data as CustomBrandingDetails);
      console.log("Branding Details:", data);
    } catch (error) {
      console.log("Branding Details:", error);
    }
  };
  const updateColor = (label: string, value: string): void => {
    setColors((prevColors) => ({
      ...prevColors,
      [label]: value,
    }));
  };

  const setBrandingDetails = (data: CustomBrandingDetails): void => {
    if (data === null || data === undefined) {
      setColors({});
      setWelcomeText("");
      setPreviews({});
      setFonts("");
      setFooterText("");
      setValidFrom(null); // Cast to any to bypass type checking
      setValidTo(null);
    } else {
      setColors({
        "Primary Background Color": data.app_background_color,
        "Topbar Background Color": data.top_bar_background_color,
        "Topbar Text Color": data.top_bar_text_color,
        "Topbar Active Text Color": data.top_bar_active_color,
        "Navbar Background Color": data.navbar_background_color,
        "Navbar Text Color": data.navbar_text_color,
        "Navbar Text Hover Color": data.navbar_text_color_hover,
        "Sidebar Background Color": data.sidebar_background_color,
        "Sidebar Text Color": data.sidebar_text_color,
        "Sidebar active Color": data.sidebar_active_color,
        "Footer Background Color": data.footer_background_color,
        "Footer Text Color": data.footer_text_color,
        "Toast Success Color": data.toast_success_color,
        "Toast Error Color": data.toast_error_color,
        "Toast Warning Color": data.toast_warning_color,
        "Toast Info Color": data.toast_info_color,
        "Primary Button Background Color": data.button_primary_color,
        "Primary Button Text Color": data.button_primary_text_color,
        "Secondary Button Background Color": data.button_secondary_color,
        "Secondary Button Text Color": data.button_secondary_text_color,
        "Dismiss Button Background Color": data.button_dismiss_bg_color,
        "Dismiss Button Text Color": data.button_dismiss_text_color,
        "Info Button Background Color": data.button_info_background_color,
        "Info Button Text Color": data.button_info_text_color,
        "Basic Font Color": data.font_color,
      });
      setWelcomeText(data.welcome_text);
      setPreviews({
        "Main Logo": data.main_logo,
        "Mobile Logo": data.app_logo,
        "Banner Image": data.banner_image,
      });
      setFonts(data.font_family);
      setFooterText(data.footer_text);
      setTheme(data.theme_name);
      const currentTimezone = moment.tz.guess();
      const parsedDatetime = moment.tz(
        data.valid_from.split("+")[0],
        currentTimezone,
      );

      const parsedValidTo = moment.tz(
        data.valid_to.split("+")[0],
        currentTimezone,
      );
      const formattedDatetime =
        parsedDatetime.format(DATE_FORMAT) + `[${currentTimezone}]`;
      const dateTime = parseZonedDateTime(formattedDatetime);
      setValidFrom(dateTime);
      const formattedDatetimeTo =
        parsedValidTo.format(DATE_FORMAT) + `[${currentTimezone}]`;

      const valid_to = parseZonedDateTime(formattedDatetimeTo);
      setValidTo(valid_to);
    }
  };

  const handleLogoChange = async (
    event: React.ChangeEvent<HTMLInputElement>,
    label: string,
  ): Promise<void> => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();

      reader.readAsDataURL(file);
      const userDetails = localStorage.getItem("userDetails");
      if (userDetails != null && userDetails != undefined) {
        const users = JSON.parse(userDetails) as LoginUserData;
        const org_id = localStorage.getItem("orgId") as string;
        const userId = users?.id;
        const fileExt = file.name.split(".").pop();
        const fileName = `${Math.random()}.${fileExt}`;

        try {
          await supabase.storage
            .from(`${org_id}`)
            .upload(`avatars/${userId}/${fileName}`, file, {
              cacheControl: "3600",
              upsert: true,
            });
        } catch (error) {
          console.error("Error uploading avatar: ", error);
        }
        const { data } = supabase.storage
          .from(`${org_id}`)
          .getPublicUrl(`avatars/${userId}/${fileName}`);

        setPreviews((prev) => ({ ...prev, [label]: data.publicUrl as string }));
        console.log("data", data.publicUrl);
      } else {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: t("errorMessages.selectValidImage"),
        });
      }
    }
  };

  const areRequiredColorsFilled = (): boolean => {
    return !!(
      colors["Primary Background Color"]?.length > 0 &&
      colors["Topbar Background Color"]?.length > 0 &&
      colors["Primary Button Background Color"]?.length > 0 &&
      colors["Primary Button Text Color"]?.length > 0
    );
  };
  const ColorInput = ({ label }: { label: string }): JSX.Element => {
    const colorValue = colors[label as keyof typeof colors];

    return (
      <div className="mb-4">
        <div className="flex items-center justify-between mb-1">
          <label className="text-sm font-medium text-gray-700 flex items-center">
            {label}
            {[
              "Primary Background Color",
              "Topbar Background Color",
              "Primary Button Background Color",
              "Primary Button Text Color",
            ].includes(label) && <span className="text-red-500 ">*</span>}
          </label>
          <div className="flex items-center space-x-2">
            <div
              className="w-6 h-6 rounded-full border border-gray-300 shadow-inner"
              style={{ backgroundColor: colorValue }}
            />
          </div>
        </div>
        <div className="flex gap-2 items-center">
          <input
            type="color"
            value={colorValue}
            onChange={(e) => updateColor(label, e.target.value)}
            className="w-10 h-10 p-0 border-0 rounded cursor-pointer"
          />
          <input
            type="text"
            value={colorValue}
            onChange={(e) => updateColor(label, e.target.value)}
            className="flex-1 border border-gray-300 rounded px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>
    );
  };

  const LogoUploader = ({ label }: { label: string }): JSX.Element => {
    const isMandatory = ["Main Logo", "Mobile Logo", "Banner Image"].includes(
      label,
    );

    return (
      <div className="mb-6 bg-white p-4 rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
        <label className="block font-medium text-sm mb-3 text-gray-700 flex ">
          {label}
          {isMandatory && <span className="text-red-500 ">*</span>}
        </label>

        <div className="flex flex-col">
          {previews[label]?.length > 0 ? (
            <div className="mb-3 flex items-center justify-center bg-gray-50 rounded-lg p-3 border-2 border-dashed border-gray-200">
              <Image
                src={previews[label]}
                alt={`${label} preview`}
                width={150}
                height={150}
                className="object-contain h-40"
              />
            </div>
          ) : (
            <div className="mb-3 flex items-center justify-center bg-gray-50 h-24 rounded-lg border-2 border-dashed border-gray-200">
              <Upload size={24} className="text-gray-400" />
            </div>
          )}

          <label className="flex items-center justify-center px-4 py-2 bg-blue-50 text-blue-600 rounded-md hover:bg-blue-100 transition-colors cursor-pointer">
            <input
              type="file"
              accept="image/*"
              onChange={(e) => {
                void handleLogoChange(e, label);
              }}
              className="hidden"
            />
            <Upload size={16} className="mr-2" />
            {previews[label]?.length > 0 ? "Replace" : "Upload"}
          </label>
        </div>
      </div>
    );
  };

  const handleSave = async (): Promise<void> => {
    console.log("fonts,", fonts);
    const dateStart = validFrom as ZonedDateTime;
    const startDate = new Date(
      dateStart.year,
      dateStart.month - 1,
      dateStart.day,
      dateStart.hour,
      dateStart.minute,
    );
    const dateEnd = validTo as ZonedDateTime;
    const endDate = new Date(
      dateEnd.year,
      dateEnd.month - 1,
      dateEnd.day,
      dateEnd.hour,
      dateEnd.minute,
    );
    const momentStartDate = moment(startDate);
    const momentEndDate = moment(endDate);
    const formattedStartDate = momentStartDate.format("YYYY-MM-DD HH:mm");
    const formattedEndDate = momentEndDate.format("YYYY-MM-DD HH:mm");
    const fromDateObj = new Date(formattedStartDate);
    const toDateObj = new Date(formattedEndDate);
    if (fromDateObj > toDateObj) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.custom_branding_date_error"),
      });
      return;
    } else {
      const org_id = localStorage.getItem("orgId") as string;
      try {
        const payload: CustomBrandingDetails = {
          org_id: org_id,
          main_logo: previews["Main Logo"] ?? "",
          app_logo: previews["Mobile Logo"] ?? "",
          banner_image: previews["Banner Image"] ?? "",
          font_family: fonts,
          app_background_color: colors["Primary Background Color"] ?? "",
          // top_bar_color: colors["Header Background Color"] ?? "",
          theme_name: theme,
          button_primary_color: colors["Primary Button Background Color"] ?? "",
          button_primary_text_color: colors["Primary Button Text Color"] ?? "",
          button_secondary_color:
            colors["Secondary Button Background Color"] ?? "",
          button_secondary_text_color:
            colors["Secondary Button Text Color"] ?? "",
          button_dismiss_bg_color:
            colors["Dismiss Button Background Color"] ?? "",
          button_dismiss_text_color: colors["Dismiss Button Text Color"] ?? "",
          button_info_background_color:
            colors["Info Button Background Color"] ?? "",
          button_info_text_color: colors["Info Button Text Color"] ?? "",
          toast_success_color: colors["Toast Success Color"] ?? "",
          toast_error_color: colors["Toast Error Color"] ?? "",
          toast_warning_color: colors["Toast Warning Color"] ?? "",
          toast_info_color: colors["Toast Info Color"] ?? "",
          // navigation_text_color: colors["Navigation Text Color"] ?? "",
          footer_background_color: colors["Footer Background Color"] ?? "",
          footer_text_color: colors["Footer Text Color"] ?? "",
          top_bar_text_color: colors["Topbar Text Color"] ?? "",
          top_bar_background_color: colors["Topbar Background Color"] ?? "",
          top_bar_active_color: colors["Topbar Active Text Color"] ?? "",
          navbar_background_color: "",
          navbar_text_color: colors["Navbar Text Color"] ?? "",
          navbar_text_color_hover: colors["Navbar Text Hover Color"] ?? "",
          sidebar_background_color: colors["Sidebar Background Color"] ?? "",
          sidebar_text_color: colors["Sidebar Text Color"] ?? "",
          sidebar_active_background_color: colors["Sidebar Text Color"] ?? "",
          sidebar_active_color: colors["Sidebar Text Color"] ?? "",
          welcome_text: welcomeText,
          footer_text: footerText,
          valid_from: formattedStartDate,
          valid_to: formattedEndDate,
          favicon: "",
          font_color: colors["Basic Font Color"] ?? "",
          font_base_size: "16px",
        };
        const result = await addCustomBranding(payload);
        if (result.status === "success") {
          toast({
            variant: SUCCESS_MESSAGES.toast_variant_default,
            title: t("successMessages.toast_success_title"),
            description: t("successMessages.custom_branding_msg"),
          });
          void getBrandingDetails();
        }
      } catch (error) {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: t("errorMessages.custom_branding_msg"),
        });
      }
    }
  };

  return (
    <MainLayout>
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      <div className="mx-auto px-4 py-6">
        <h1 className="text-2xl font-semibold tracking-tight">
          {t("config.title")}
        </h1>
        {!isLoading ? (
          <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100 mt-4">
            <div className="px-6 pt-6 pb-4 bg-gray-50 border-b border-gray-100">
              <div className="flex flex-wrap gap-4 items-center">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t("config.theme")}
                  </label>
                  <select
                    value={theme}
                    onChange={(e) => {
                      const value = e.target.value;
                      setTheme(value);
                    }}
                    className="w-full px-4 py-2 border-2 rounded-md focus:outline-none focus:ring-2 transition-all duration-200 appearance-none pr-8"
                  >
                    <option value="" disabled>
                      {t("config.selectTheme")}
                    </option>
                    <option value="light">Light</option>
                    <option value="dark">Dark</option>
                    <option value="custom">Custom</option>
                  </select>
                </div>
                <div className="w-1/5">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t("config.validFrom")}
                    <span className="text-red-500 ">*</span>
                  </label>
                  <DateTimePicker
                    granularity={"minute"}
                    hideTimeZone={true}
                    value={validFrom}
                    onChange={(date) => {
                      if (date !== null) {
                        setValidFrom(date);
                      }
                    }}
                  />
                </div>
                <div className="w-1/5">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t("config.validTo")}
                    <span className="text-red-500 ">*</span>
                  </label>
                  <DateTimePicker
                    granularity={"minute"}
                    hideTimeZone={true}
                    value={validTo}
                    onChange={(date) => {
                      if (date !== null) {
                        setValidTo(date);
                      }
                    }}
                  />
                </div>
              </div>
            </div>

            {/* Tabs */}
            <div className="px-6 pt-4 border-b border-gray-200">
              <nav className="flex space-x-8">
                {["branding", "fonts", "colors"].map((tab) => (
                  <button
                    key={tab}
                    onClick={() => setActiveTab(tab)}
                    className={`pb-4 px-1 text-sm font-medium transition-colors ${
                      activeTab === tab
                        ? "border-b-2 border-blue-500 text-blue-600"
                        : "text-gray-500 hover:text-gray-700 hover:border-b-2 hover:border-gray-300"
                    }`}
                  >
                    {tab.charAt(0).toUpperCase() + tab.slice(1)}
                  </button>
                ))}
              </nav>
            </div>

            {/* Tab Content */}
            <div className="p-6">
              {activeTab === "branding" && (
                <div className="bg-white">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {logos.map((label) => (
                      <LogoUploader key={label} label={label} />
                    ))}
                  </div>
                  <div className="flex justify-end mt-8 space-x-3">
                    <Button
                      className={`px-5 py-2 ${
                        ["Main Logo", "Mobile Logo", "Banner Image"].every(
                          (mandatory) => previews[mandatory],
                        )
                          ? "bg-[#9FC089] hover:bg-blue-700 text-white"
                          : "bg-gray-300 text-gray-500 cursor-not-allowed"
                      } rounded-md shadow-sm`}
                      disabled={
                        !["Main Logo", "Mobile Logo", "Banner Image"].every(
                          (mandatory) => previews[mandatory],
                        ) ||
                        !validFrom ||
                        !validTo
                      }
                      onClick={() => {
                        setActiveTab("fonts");
                      }}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}

              {activeTab === "fonts" && (
                <div className="mb-8">
                  <div className="mb-8 p-4 bg-white rounded-lg border border-gray-100 shadow-sm">
                    <div className="flex items-center gap-6 flex-wrap">
                      {/* Font Family */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Font Family<span className="text-red-500">*</span>
                        </label>
                        <select
                          className="w-48 p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                          value={fonts}
                          onChange={(e) => setFonts(e.target.value)}
                        >
                          <option value="default">System Default</option>
                          <option value="arial">Arial</option>
                          <option value="cursive">Cursive</option>
                          <option value="verdana">Verdana</option>
                          <option value="tahoma">Tahoma</option>
                          <option value="trebuchet">Trebuchet MS</option>
                          <option value="times">Times New Roman</option>
                          <option value="georgia">Georgia</option>
                          <option value="courier">Courier New</option>
                          <option value="lucida">Lucida Console</option>
                          <option value="roboto">Roboto</option>
                          <option value="opensans">Open Sans</option>
                          <option value="montserrat">Montserrat</option>
                          <option value="lato">Lato</option>
                          <option value="poppins">Poppins</option>
                          <option value="raleway">Raleway</option>
                          <option value="noto">Noto Sans</option>
                          <option value="inter">Inter</option>
                          <option value="ubuntu">Ubuntu</option>
                          <option value="merriweather">Merriweather</option>
                          <option value="playfair">Playfair Display</option>
                          <option value="source-sans">Source Sans Pro</option>
                          <option value="cabin">Cabin</option>
                          <option value="quicksand">Quicksand</option>
                        </select>
                      </div>

                      {/* Font Size */}
                      {/* <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Font Size<span className="text-red-500">*</span>
                        </label>
                        <select
                          className="w-32 p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                          value={fontSize}
                          onChange={(e) => setFontSize(e.target.value)}
                        >
                          <option value="12px">12px</option>
                          <option value="14px">14px</option>
                          <option value="16px">16px</option>
                          <option value="18px">18px</option>
                          <option value="20px">20px</option>
                          <option value="22px">22px</option>
                          <option value="24px">24px</option>
                        </select>
                      </div> */}
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button
                      className="bg-[#9FC089]"
                      onClick={() => {
                        setActiveTab("colors");
                      }}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}
              {activeTab === "colors" && (
                <div className="bg-white">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Primary Colors */}
                    <div className="p-4 bg-white rounded-lg border border-gray-100 shadow-sm">
                      <h3 className="text-md font-semibold mb-4 text-gray-800">
                        Primary Colors
                      </h3>
                      <ColorInput label="Primary Background Color" />
                      <ColorInput label="Basic Font Color" />
                    </div>
                    <div className="p-4 bg-white rounded-lg border border-gray-100 shadow-sm">
                      <h3 className="text-md font-semibold mb-4 text-gray-800">
                        Toast Colors
                      </h3>
                      <ColorInput label="Toast Success Color" />
                      <ColorInput label="Toast Error Color" />
                      <ColorInput label="Toast Warning Color" />
                      <ColorInput label="Toast Info Color" />
                    </div>
                    <div className="p-4 bg-white rounded-lg border border-gray-100 shadow-sm">
                      <h3 className="text-md font-semibold mb-4 text-gray-800">
                        Layout Colors
                      </h3>
                      <ColorInput label="Topbar Background Color" />
                      <ColorInput label="Topbar Text Color" />
                      <ColorInput label="Topbar Active Text Color" />
                      <ColorInput label="Navbar Text Color" />
                      <ColorInput label="Navbar Text Hover Color" />
                      <ColorInput label="Sidebar Background Color" />
                      <ColorInput label="Sidebar Text Color" />
                      <ColorInput label="Sidebar active  Color" />
                      <ColorInput label="Footer Background Color" />
                      <ColorInput label="Footer Text Color" />
                    </div>
                    {/* UI Elements */}
                    <div className="p-4 bg-white rounded-lg border border-gray-100 shadow-sm">
                      <h3 className="text-md font-semibold mb-4 text-gray-800">
                        UI Elements
                      </h3>
                      <ColorInput label="Primary Button Background Color" />
                      <ColorInput label="Primary Button Text Color" />
                      <ColorInput label="Secondary Button Background Color" />
                      <ColorInput label="Secondary Button Text Color" />
                      <ColorInput label="Dismiss Button Background Color" />
                      <ColorInput label="Dismiss Button Text Color" />
                      <ColorInput label="Info Button Background Color" />
                      <ColorInput label="Info Button Text Color" />
                      {/* <ColorInput label="Navigation Text Color" /> */}
                      {/* <ColorInput label="Input Focus" /> */}
                    </div>

                    {/* Navigation */}
                  </div>

                  {/* Theme Options */}
                  {/* <div className="mt-8 p-4 bg-white rounded-lg border border-gray-100 shadow-sm">
                  <h3 className="text-md font-semibold mb-4 text-gray-800">
                    Theme Options
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <ToggleSwitch
                      label="Enable Dark Mode"
                      defaultChecked={true}
                    />
                  </div>
                </div> */}

                  {/* Save Changes Button */}
                  <div className="flex justify-end mt-8">
                    <Button
                      className={`px-5 py-2 ${
                        areRequiredColorsFilled()
                          ? "bg-[#9FC089] hover:bg-blue-700 text-white"
                          : "bg-gray-300 text-gray-500 cursor-not-allowed"
                      } rounded-md shadow-sm`}
                      disabled={!areRequiredColorsFilled()}
                      onClick={() => {
                        // handleSaveChanges("colors").catch(console.error);
                        handleSave().catch(console.error);
                      }}
                    >
                      Save
                    </Button>
                  </div>
                </div>
              )}
              {activeTab === "text" && (
                <div className="mb-8">
                  {/* Welcome Text */}
                  <div className="mb-8 p-4 bg-white rounded-lg border border-gray-100 shadow-sm">
                    <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                      Welcome Text<span className="text-red-500 ">*</span>
                    </label>
                    <textarea
                      className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter welcome text..."
                      required
                      defaultValue={welcomeText}
                      onChange={(e) => setWelcomeText(e.target.value)}
                    />
                  </div>

                  {/* Footer Text */}
                  <div className="mb-8 p-4 bg-white rounded-lg border border-gray-100 shadow-sm">
                    <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                      Footer Text<span className="text-red-500 ">*</span>
                    </label>
                    <textarea
                      className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter footer text..."
                      defaultValue={footerText}
                      required
                      onChange={(e) => setFooterText(e.target.value)}
                    />
                  </div>

                  <div className="flex justify-end">
                    <Button
                      className="bg-[#9FC089] "
                      onClick={() => {
                        handleSave().catch(console.error);
                      }}
                      disabled={
                        welcomeText?.trim().length === 0 ||
                        footerText.trim().length === 0
                      }
                    >
                      Save
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        ) : (
          <Spinner />
        )}
      </div>
    </MainLayout>
  );
}
