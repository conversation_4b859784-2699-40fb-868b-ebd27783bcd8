"use client";
import React from "react";
import type {
  // Column,
  ColumnDef,
  Row,
} from "@tanstack/react-table";
// import { ArrowUpDown } from "lucide-react";
// import { Button } from "@/components/ui/button";
import type { SubscriptionList } from "@/types";
import moment from "moment";
import { DATE_FORMAT_DMY_HM_AM_PM } from "@/lib/constants";
const toCamelCase = (str: string): string => {
  return str
    .split(/[-_\s]+/) // Split the string by hyphens, underscores, or spaces
    .map((word, index) =>
      index === 0
        ? word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
        : "",
    )
    .join(""); // Join the words back together
};

interface RowDefinition {
  row: Row<SubscriptionList>;
}
// interface ColumnDefinition {
//   column: Column<SubscriptionList, unknown>;
// }

export const getColumns = (
  t: (key: string) => string,
): ColumnDef<SubscriptionList>[] => [
  {
    accessorKey: "name",
    header: t("subscriptionPlan.planName"),
    // header: ({ column }: ColumnDefinition): React.JSX.Element => {
    //   return (
    //     <Button
    //       className="px-0"
    //       variant="ghost"
    //       onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
    //     >
    //       Plan name
    //       <ArrowUpDown className="ml-2 h-4 w-4" />
    //     </Button>
    //   );
    // },
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div>{row.original.name}</div>
    ),
  },
  {
    accessorKey: "description",
    header: t("subscriptionPlan.description"),
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div>{row.original.description}</div>
    ),
  },
  {
    accessorKey: "subscription_type",
    header: t("subscriptionPlan.type"),
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div>{row.original.subscription_type}</div>
    ),
  },
  {
    accessorKey: "price",
    header: t("subscriptionPlan.price"),
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div>{row.original.price}</div>
    ),
  },
  {
    accessorKey: "currency",
    header: t("subscriptionPlan.currency"),
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div>{row.original.currency}</div>
    ),
  },
  {
    accessorKey: "valid_from",
    header: t("subscriptionPlan.validFrom"),
    cell: ({ row }: RowDefinition): React.JSX.Element => {
      const formattedDate = moment
        .utc(row.original.valid_from)
        .format("DD-MMM-YYYY hh:mm a");
      return <div>{formattedDate}</div>;
    },
  },
  {
    accessorKey: "valid_to",
    header: t("subscriptionPlan.validTo"),
    cell: ({ row }: RowDefinition): React.JSX.Element => {
      const formattedDate = moment
        .utc(row.original.valid_to)
        .format(DATE_FORMAT_DMY_HM_AM_PM);
      return <div>{formattedDate}</div>;
    },
  },
  {
    accessorKey: "subscription_plan_status",
    header: t("subscriptionPlan.status"),
    cell: ({ row }: RowDefinition): React.JSX.Element => {
      const result = row.original.subscription_plan_status;
      const active = result === "active";
      const cellColor = active ? "text-green-700" : "text-red-600";
      return <div className={cellColor}>{toCamelCase(result)}</div>;
    },
  },
  {
    accessorKey: "subscription_expiry",
    header: t("subscriptionPlan.expiryStatus"),
    cell: ({ row }: RowDefinition): React.JSX.Element => {
      const result = row.original.is_expired;
      const active = result === true;
      const cellColor = active ? "text-red-600" : "";
      const resultName = active ? "Expired" : " ";
      return <div className={cellColor}>{resultName}</div>;
    },
  },
];
