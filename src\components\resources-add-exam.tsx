"use client";
import React, { useState, useEffect, type KeyboardEvent } from "react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useForm } from "react-hook-form";
import type {
  richTextType,
  ErrorCatch,
  ToastType,
  TopicDataType,
  ComboData,
  AddExamResourceForm,
  LogUserActivityRequest,
} from "@/types";
import { ExamAddResourceSchema } from "@/schema/schema";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import TreeSelectComponent from "@/components/ui/tree-select/tree-select";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import useTopics from "@/hooks/useTopics";
import useCourse from "@/hooks/useCourse";
import type { TreeDataItem } from "@/components/ui/tree";
import { Editor } from "primereact/editor";
import { useToast } from "@/components/ui/use-toast";
import { ORG_KEY, examTypes, marksTypes } from "@/lib/constants";
import { Combobox } from "./ui/combobox";
import useExams from "@/hooks/useExams";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";

export default function AddExamFromResource({
  onCancel,
  folderList,
  onSave,
}: {
  onCancel: () => void;
  folderList: ComboData[];
  onSave: () => void;
}): React.JSX.Element {
  const { t } = useTranslation();
  const { convertDataToTreeNode } = useCourse();
  const { getCategoryHierarchy } = useTopics();
  const { importExamToResource } = useExams();
  const { toast } = useToast() as ToastType;
  const form = useForm<AddExamResourceForm>({
    resolver: zodResolver(ExamAddResourceSchema),
  });
  const { handleSubmit, register } = form;
  const { updateUserActivity } = useLogUserActivity();

  const [nodeData, setNodeData] = React.useState<TreeDataItem[]>([]);
  const [selectedTopic, setSelectedTopic] = React.useState<string | null>();
  const [richTextValues, setRichTextValues] = React.useState<
    richTextType | undefined
  >(undefined);
  const [selectedExamType, setSelectedExamType] = useState<string>("");
  const [selectedMarksType, setSelectedMarksType] = useState<string>("");
  const [isExamDisabled, setIsExamDisabled] = useState(false);
  const [selectFolder, setSelectFolder] = useState<string | null>();
  const [openPenaltyDiv, setOpenPenaltyDiv] = useState<boolean>(false);
  const [wrongAnswers, setWrongAnswers] = useState<number>();
  const [wrongAnswerWeightage, setWrongAnswerWeightage] = useState<number>();

  useEffect(() => {
    topicList();
    form.setValue("penalty_mode", "Fixed");
    form.setValue("penalty_type", "Exam");
  }, []);

  useEffect(() => {
    // Use watch to get the latest values for total_mark and num_of_questions
    const totalMark = form.watch("total_mark");
    const noOfQuestions = form.watch("num_of_questions");

    if (noOfQuestions > 0) {
      const marksPerQuest = String(totalMark / noOfQuestions); // Calculate marks per question
      form.setValue("marks", marksPerQuest); // Update the marks field
      console.log(
        "totalMark / noOfQuestions",
        totalMark,
        noOfQuestions,
        marksPerQuest,
      );
      handleMarksTypeChange(marksPerQuest);
    }
  }, [form.watch("total_mark"), form.watch("num_of_questions")]);

  const setRichTextValue = (richTextValue: richTextType | undefined): void => {
    if (richTextValue && richTextValue.htmlValue == null) {
      richTextValue = undefined;
    }
    form.setValue("description", richTextValue?.htmlValue as string);
    setRichTextValues(richTextValue);
  };

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  const topicList = (): void => {
    const fetchData = async (): Promise<void> => {
      const orgId = localStorage.getItem(ORG_KEY) as string;
      const params = {
        org_id: orgId,
        filter_data: 1,
      };
      try {
        const topics = await getCategoryHierarchy(params);
        if (topics.length > 0) {
          const treeData: TreeDataItem[] = convertDataToTreeNode(
            topics as TopicDataType[],
          );
          setNodeData(treeData);
        }
      } catch (error: unknown) {
        console.error("Error fetching data:");
      }
    };
    fetchData().catch((error) => console.log(error));
  };

  useEffect(() => {
    form.setValue("allowed_attempts", 1);
  }, [form]);

  const handleExamTypeChange = (selectedValue: string): void => {
    form.setValue("quiz_type", selectedValue);
    setSelectedExamType(selectedValue);
  };
  const handleMarksTypeChange = (selectedValue: string): void => {
    form.setValue("marks", selectedValue);
    setSelectedMarksType(selectedValue);
  };
  async function onSubmit(): Promise<void> {
    const orgId = localStorage.getItem(ORG_KEY) as string;
    const formData = form.getValues();
    if (richTextValues?.htmlValue !== undefined) {
      formData.description = richTextValues?.htmlValue;
    }
    const passMark = formData.pass_mark;
    const totalMark = formData.total_mark;
    const noOfQuestions = formData.num_of_questions;
    const noOfWrongAnswers = wrongAnswers ?? 0;

    if (Number(passMark) > Number(totalMark)) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.pasmark_greaterthan_totalmark"),
      });
      return;
    }

    if (isExamDisabled) {
      if (totalMark / noOfQuestions != parseInt(selectedMarksType)) {
        const mark = totalMark / noOfQuestions;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: t("errorMessages.mark_for_all_questions", { mark }),
        });
        return;
      }
    }

    if (selectedTopic === "" || selectedTopic === null) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.select_category"),
      });
      return;
    }

    if (noOfWrongAnswers > noOfQuestions) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_validation_error"),
        description: t("errorMessages.wrong_ans_warning"),
      });
      return;
    }

    const params = {
      org_id: orgId ?? "",
      quiz_data: {
        name: formData.name ?? "",
        description: formData.description as string,
        main_topic: selectedTopic ?? "",
        num_of_questions: formData.num_of_questions,
        total_mark: formData.total_mark,
        pass_mark: formData.pass_mark,
        duration: formData.duration,
        penalty_available: formData.penalty_available ?? false,
        allowed_attempts: formData.allowed_attempts,
        quiz_type: selectedExamType,
        is_premium: formData.is_premium ?? false,
        is_deleted: false,
        status: "Draft",
        is_equal_weightage: formData.is_weightage ?? false,
        eq_weightage_marks: parseInt(selectedMarksType),
        folder_id: selectFolder as string,
        no_wrong_answers: wrongAnswers ?? 0,
        minus_mark_applicable: wrongAnswerWeightage ?? 0,
        parent_quiz_id: null,
        penalty_type: formData.penalty_type,
        calculation_type: formData.penalty_mode,
      },
      folder_id: selectFolder as string,
    };

    try {
      const result = await importExamToResource(params);
      if (result.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.toast_success_title"),
          description: t("successMessages.exam_added"),
        });
        const params = {
          activity_type: "Exam",
          screen_name: "Exam",
          action_details: "Exam added successfully",
          target_id: result.instance_id as string,
          log_result: "SUCCESS",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
        onSave();
        onCancel();
      } else if (result.status === "error") {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: result.status,
        });
        const params = {
          activity_type: "Exam",
          screen_name: "Exam",
          action_details: "Failed to add exam",
          target_id: result.instance_id as string,
          log_result: "ERROR",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
    }
  }

  const handleCancel = (): void => {
    onCancel();
  };

  const handleKeyDown = (event: KeyboardEvent<HTMLInputElement>): void => {
    const forbiddenKeys = ["e", "E", "-", "+"];
    if (forbiddenKeys.includes(event.key)) {
      event.preventDefault();
    }
  };

  const handleKeyDownDuration = (
    event: React.KeyboardEvent<HTMLInputElement>,
  ): void => {
    const forbiddenKeys = ["e", "E", "-", "+"];
    const { value } = event.currentTarget;
    const isDeleteKey = event.key === "Backspace" || event.key === "Delete";
    if (
      forbiddenKeys.includes(event.key) || // Prevent typing forbidden keys
      (value.length >= 3 && !isDeleteKey) // Limit to 3 digits
    ) {
      event.preventDefault();
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    const value = e.target.value;
    const sanitizedValue = value
      .trimStart()
      .replace(/^\p{P}+/u, "")
      .replace(/[^\p{L}\p{M}\p{N}\s&-]/gu, "")
      .replace(/\s{2,}/g, " ")
      .replace(/^[^\p{L}\p{M}\p{N}]|[^\p{L}\p{M}\p{N}\s&-]/gu, "")
      .replace(/\s+$/, (match) =>
        match.length > 1 ? match.slice(0, -1) : match,
      );
    form.setValue("name", sanitizedValue);
  };

  return (
    <div>
      <div className="w-full border rounded-md ps-4 pb-4 pe-4 mb-2 bg-[#fff]">
        <div>
          <Form {...form}>
            <form
              onSubmit={(event) => void handleSubmit(onSubmit)(event)}
              className="space-y-4 flex flex-wrap"
            >
              <div className="w-full md:w-1/3 mt-4 pe-4">
                <FormField
                  name="main_topic"
                  control={form.control}
                  render={() => (
                    <FormItem>
                      <FormLabel>
                        {t("resourceLibrary.selectCategory")}
                        <span className="text-red-700">*</span>
                      </FormLabel>
                      <FormControl>
                        <TreeSelectComponent
                          nodeData={nodeData}
                          selectLabel="Select Category"
                          onNodeSelect={(selectedValue) => {
                            setSelectedTopic(selectedValue);
                            form.setValue(
                              "main_topic",
                              selectedValue as string,
                            );
                          }}
                        ></TreeSelectComponent>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="w-full md:w-1/3 pe-4">
                <FormField
                  name="folder_id"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t("resourceLibrary.selectFolder")}
                        <span className="text-red-700">*</span>
                      </FormLabel>
                      <FormControl>
                        <Combobox
                          data={folderList}
                          onSelectChange={(selectedOption: string) => {
                            field.onChange(selectedOption);
                            setSelectFolder(selectedOption);
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="w-full md:w-1/3 pe-4">
                <FormField
                  name="quiz_type"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t("resourceLibrary.selectExamType")}{" "}
                        <span className="text-red-700">*</span>
                      </FormLabel>
                      <FormControl>
                        <Select
                          onValueChange={handleExamTypeChange}
                          defaultValue={field.value}
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Select" />
                          </SelectTrigger>
                          <SelectContent>
                            {examTypes.map((option) => (
                              <SelectItem
                                key={option.value}
                                value={option.value}
                              >
                                {option.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="w-full md:w-1/3 pe-4">
                <FormField
                  name="name"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t("resourceLibrary.examName")}{" "}
                        <span className="text-red-700">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          autoComplete="off"
                          type="text"
                          maxLength={100}
                          {...field}
                          onChange={handleInputChange}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="w-full md:w-1/3 pe-4">
                <FormField
                  name="num_of_questions"
                  // control={form.control}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t("resourceLibrary.noOfQuestions")}
                        <span className="text-red-700">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          autoComplete="off"
                          type="number"
                          min={0}
                          onKeyDown={handleKeyDownDuration}
                          {...field}
                          // {...register("num_of_questions", {
                          //   required: "required",
                          //   minLength: {
                          //     value: 1,
                          //     message: "min length is 1"
                          //   }
                          // })}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="w-full md:w-1/3 pe-4">
                <FormField
                  name="total_mark"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t("resourceLibrary.totalMarks")}
                        <span className="text-red-700">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          autoComplete="off"
                          type="number"
                          min={1}
                          onKeyDown={handleKeyDown}
                          {...field}
                          // {...register("total_mark", {
                          //   validate: (value) => {
                          //     return !isNaN(value);
                          //   },
                          //   valueAsNumber: true,
                          // })}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="w-full md:w-1/3 pe-4">
                <FormField
                  name="pass_mark"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t("resourceLibrary.passMarks")}{" "}
                        <span className="text-red-700">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          autoComplete="off"
                          type="number"
                          min={1}
                          onKeyDown={handleKeyDown}
                          {...field}
                          // {...register("pass_mark", { valueAsNumber: true })}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="w-full md:w-1/3 pe-4">
                <FormField
                  name="duration"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t("resourceLibrary.examDuration")}{" "}
                        <span className="text-red-700">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          autoComplete="off"
                          type="number"
                          min={1}
                          onKeyDown={handleKeyDownDuration}
                          {...field}
                          // {...register("duration", { valueAsNumber: true })}
                          // {...register("duration", {
                          //   validate: (value) => {
                          //     return (
                          //       value <= 180 ||
                          //       "Duration must be up to 180 minutes"
                          //     );
                          //   },
                          //   valueAsNumber: true,
                          // })}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="w-full md:w-1/3 pe-4">
                <FormField
                  name="allowed_attempts"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t("resourceLibrary.allowedAttempts")}{" "}
                        <span className="text-red-700">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          autoComplete="off"
                          type="number"
                          min={1}
                          onKeyDown={handleKeyDown}
                          {...field}
                          {...register("allowed_attempts", {
                            value: 1,
                            valueAsNumber: true,
                          })}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="w-full flex flex-wrap">
                <div className="w-full md:w-1/3 pe-4 mb-2">
                  <FormField
                    name="penalty_available"
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center mt-7">
                          <FormControl>
                            <>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={(value) => {
                                  field.onChange(value);
                                  if (value === true) {
                                    setOpenPenaltyDiv(true);
                                  } else {
                                    setOpenPenaltyDiv(false);
                                  }
                                }}
                              />
                              <FormLabel className="px-4">
                                {t("resourceLibrary.penaltyApplicable")}
                              </FormLabel>
                            </>
                          </FormControl>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                {openPenaltyDiv && (
                  <>
                    <div className="w-full md:w-1/3 pe-4 mb-2">
                      <FormItem>
                        <FormLabel>
                          {t("resourceLibrary.penaltyType")}
                        </FormLabel>
                        <FormControl>
                          <Select
                            onValueChange={(value) => {
                              form.setValue("penalty_type", value);
                              if (value === "Question") {
                                form.setValue("num_of_questions", 1);
                                setWrongAnswers(1);
                              } else {
                                setWrongAnswers(undefined);
                              }
                            }}
                            defaultValue="Exam"
                          >
                            <SelectTrigger className="w-full">
                              <SelectValue
                                placeholder={t(
                                  "resourceLibrary.selectPenaltyType",
                                )}
                              />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="Question">
                                {t("resourceLibrary.questionBased")}
                              </SelectItem>
                              <SelectItem value="Exam">
                                {t("resourceLibrary.examBased")}
                              </SelectItem>
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    </div>
                    <div className="w-full md:w-1/3 pe-4 mb-2">
                      <FormItem>
                        <FormLabel>
                          {t("resourceLibrary.penaltyMode")}
                        </FormLabel>
                        <FormControl>
                          <Select
                            onValueChange={(value) => {
                              form.setValue("penalty_mode", value);
                            }}
                            defaultValue="Fixed"
                          >
                            <SelectTrigger className="w-full">
                              <SelectValue
                                placeholder={t(
                                  "resourceLibrary.selectPenaltyMode",
                                )}
                              />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="Fixed">
                                {t("resourceLibrary.fixed")}
                              </SelectItem>
                              <SelectItem value="Percentage">
                                {t("resourceLibrary.percentageWise")}
                              </SelectItem>
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    </div>
                    <div className="w-full md:w-1/3 pe-4 mb-2"></div>
                    <div className="w-full md:w-1/3 pe-4">
                      <FormItem>
                        <FormLabel>
                          {t("resourceLibrary.noOfWrongAnswers")}
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min={1}
                            // max={(form.getValues("num_of_questions") !== 0) ? Number(form.getValues("num_of_questions")) - 1 : undefined}
                            autoComplete="off"
                            value={wrongAnswers ?? ""}
                            onChange={(e) => {
                              const numQuestions = Number(
                                form.getValues("num_of_questions"),
                              );
                              const value = Number(e.target.value);
                              if (numQuestions !== 0 && value >= numQuestions) {
                                setWrongAnswers(numQuestions - 1);
                              } else {
                                setWrongAnswers(value);
                              }
                            }}
                            onKeyDown={handleKeyDown}
                            disabled={
                              form.getValues("penalty_type") === "Question"
                            }
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    </div>
                    <div className="w-full md:w-1/3 pe-4">
                      <FormItem>
                        <FormLabel>
                          {t(
                            form.watch("penalty_mode") === "Percentage"
                              ? "resourceLibrary.penalty%"
                              : "resourceLibrary.penaltyMarksDeducted",
                          )}
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min={0}
                            step={
                              form.watch("penalty_mode") === "Percentage"
                                ? "1"
                                : "any"
                            }
                            pattern={
                              form.watch("penalty_mode") === "Percentage"
                                ? "\\d*"
                                : undefined
                            }
                            autoComplete="off"
                            value={wrongAnswerWeightage ?? ""}
                            onChange={(e) =>
                              setWrongAnswerWeightage(Number(e.target.value))
                            }
                            onKeyDown={handleKeyDown}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    </div>
                  </>
                )}
              </div>

              <div className="w-full md:w-1/3 flex items-center">
                <FormField
                  name="is_premium"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex items-center">
                        <FormControl>
                          <>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={(value) => {
                                field.onChange(value);
                              }}
                            />
                            <FormLabel className="px-4">
                              {t("resourceLibrary.premiumContent")}
                            </FormLabel>
                          </>
                        </FormControl>
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="w-full md:w-1/3 flex items-center">
                <FormField
                  name="is_weightage"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex items-center mt-2">
                        <FormControl>
                          <>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={(value: boolean) => {
                                field.onChange(value);
                                setIsExamDisabled(value);
                                !value ? setSelectedMarksType("") : "";
                              }}
                            />
                            <FormLabel className="px-2">
                              {t("resourceLibrary.isEqualWeightage")}
                            </FormLabel>
                          </>
                        </FormControl>
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {isExamDisabled && (
                <div className="w-full md:w-1/3 pe-4">
                  <FormField
                    name="marks"
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t("resourceLibrary.marks")}{" "}
                          <span className="text-red-700">*</span>
                        </FormLabel>
                        <FormControl>
                          <Select
                            onValueChange={handleMarksTypeChange}
                            defaultValue={field.value}
                          >
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select Marks" />
                            </SelectTrigger>
                            <SelectContent>
                              {marksTypes.map((option) => (
                                <SelectItem
                                  key={option.value}
                                  value={option.value}
                                >
                                  {option.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              )}
              <div className="w-full">
                <FormField
                  name="description"
                  control={form.control}
                  render={() => (
                    <FormItem>
                      <FormLabel>
                        {t("resourceLibrary.examRules")}{" "}
                        <span className="text-red-700">*</span>
                      </FormLabel>
                      <FormControl>
                        <Editor
                          value=""
                          onTextChange={(event) => {
                            const htmlValue = event.htmlValue; // Convert the HTML value to your richTextType
                            const richTextValue = {
                              htmlValue: htmlValue,
                            };
                            setRichTextValue(richTextValue as richTextType);
                          }}
                          style={{ height: "300px" }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="w-full flex justify-end mt-4">
                <div className="px-4">
                  <Button
                    // variant="outline"
                    onClick={handleCancel}
                    className="w-full sm:w-auto bg-[#33363F]"
                  >
                    {t("buttons.cancel")}
                  </Button>
                </div>
                <div>
                  <Button type="submit" className="bg-[#9FC089]">
                    {t("buttons.submit")}
                  </Button>
                </div>
              </div>
            </form>
          </Form>
        </div>
      </div>
    </div>
  );
}
