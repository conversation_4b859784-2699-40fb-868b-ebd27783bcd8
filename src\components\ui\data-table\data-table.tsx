"use client";

import React, { useEffect } from "react";
import type {
  ColumnDef,
  SortingState,
  ColumnFiltersState,
  VisibilityState,
} from "@tanstack/react-table";
import {
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
  getSortedRowModel,
  getFilteredRowModel,
} from "@tanstack/react-table";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
// import {
//   DropdownMenu,
//   DropdownMenuCheckboxItem,
//   DropdownMenuContent,
//   DropdownMenuTrigger,
// } from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  DoubleArrowLeftIcon,
  DoubleArrowRightIcon,
} from "@radix-ui/react-icons";
import type { LucideIcon } from "lucide-react";
import { Lock, Check, Ban } from "lucide-react";
import type { CallbackType } from "../../../types";
// import { act } from "react-dom/test-utils";
import { useTranslation } from "react-i18next";

type RowSelectionState = Record<string, boolean>;
interface CustomColumnWidth {
  width: number;
  align: string;
}
type CustomColumnWidths = Record<string, CustomColumnWidth>;
interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  FilterLabel: string;
  isSelectedColumn?: string | boolean;
  disableIcon?: string | boolean;
  disableEditDelete?: string | boolean;
  disableView?: string | boolean;
  selectedPrivileges?: string[];
  FilterBy: string;
  hideIcon?: boolean;
  hideEdit?: string | boolean;
  disableExpired?: string | boolean;
  disableLinked?: string | boolean;
  disablePublish?: string | boolean;
  disableStatus?: string | boolean;
  disableActiveUsers?: string | boolean;
  hideAdminUsers?: string | boolean;
  hideStart?: string | boolean;
  disableEmail?: string | boolean;
  changeApproveIcon?: string | boolean;
  changeRejectIcon?: string | boolean;
  approveIcon?: string | boolean;
  rejectIcon?: string | boolean;
  hideExtendExpiry?: string | boolean;
  isCourseAssigned?: string | boolean;
  actions:
    | {
        title: string;
        icon: LucideIcon;
        varient: string;
        iconName?: string;
        handleClick: CallbackType;
        isEnable?: boolean;
        disabled?: CallbackType;
        color?: string;
        className?: string;
      }[]
    | [];
  onSelectedDataChange?: (selectedData: TData[]) => void;
  onSetRowSelection?: (rowCount: number) => void;
  onRowCountChange?: (rowCount: number) => void;
  onSetCheckedStatus?: (rowId: string, selectStatus: boolean) => void;
  customColumnWidths?: CustomColumnWidths;
}
enum TextAlign {
  Left = "left",
  Center = "center",
  Right = "right",
  Justify = "justify",
}

export function DataTable<TData, TValue>({
  columns,
  data,
  FilterLabel,
  FilterBy,
  actions,
  isSelectedColumn = false,
  disableIcon = false,
  disableEditDelete = false,
  disableView = false,
  disablePublish = false,
  disableStatus = false,
  disableActiveUsers = false,
  disableEmail = false,
  hideAdminUsers = false,
  hideEdit = false,
  disableExpired = false,
  hideStart = false,
  changeApproveIcon = false,
  changeRejectIcon = false,
  selectedPrivileges = [],
  onSelectedDataChange,
  onSetRowSelection,
  onSetCheckedStatus,
  onRowCountChange,
  customColumnWidths = {},
  isCourseAssigned = false,
}: DataTableProps<TData, TValue>): React.JSX.Element {
  const { t } = useTranslation();
  const hasRowSelection = columns.some((column) => column.id === "select");
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [selquestion, setSelquestion] = React.useState<number>();
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    [],
  );
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState<RowSelectionState>({});
  const [rowCount, setRowCount] = React.useState<number>(0);
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  useEffect(() => {
    if (data !== null && data?.length > 0 && onRowCountChange) {
      setRowCount(data.length);
      onRowCountChange(data.length); // Call the prop function to update rowCount in parent
    }
    if (onSetRowSelection) {
      setRowSelection({});
    }
    console.log(rowCount);
  }, [data, onRowCountChange]);

  const handleRowSelectionChange = (
    rowId: string,
    isSelected: boolean,
  ): void => {
    setRowSelection((prevSelection) => {
      const updatedSelection = { ...prevSelection };
      if (isSelected) {
        updatedSelection[rowId] = true;
      } else {
        if (rowId in updatedSelection) {
          delete updatedSelection[rowId];
        }
      }
      const selectedRowCount = Object.keys(updatedSelection).length;
      setSelquestion(selectedRowCount);
      if (onSetRowSelection) {
        onSetRowSelection(selectedRowCount);
      }
      if (onSetCheckedStatus) {
        onSetCheckedStatus(rowId, isSelected);
      }
      return updatedSelection;
    });
  };

  useEffect(() => {
    const selectedData = table
      .getFilteredSelectedRowModel()
      .rows.map((row) => row.original);
    if (selectedData.length >= 0 && onSelectedDataChange !== undefined)
      onSelectedDataChange(selectedData);
  }, [table, selquestion]);

  useEffect(() => {
    if (onSetRowSelection) {
      onSetRowSelection(table.getFilteredSelectedRowModel().rows.length);
    }
  }, [data, onSetRowSelection]);

  const applyHeaderStyle = (columnId: string): React.CSSProperties => {
    const styles: React.CSSProperties = {};

    if (customColumnWidths?.[columnId] !== undefined) {
      const { width, align } = customColumnWidths[columnId];
      styles.width = `${width}px`;
      if (Object.values(TextAlign).includes(align as TextAlign)) {
        styles.textAlign = align as TextAlign;
      }
    }
    return styles;
  };

  return (
    <div className="w-full">
      <div className="w-full flex justify-between space-x-4 mt-4">
        <div className="w-full sm:w-1/2 md:w-1/4">
          <Input
            placeholder={FilterLabel}
            value={
              (table.getColumn(FilterBy)?.getFilterValue() as string) ?? ""
            }
            onChange={(event) => {
              const trimValue = event.target.value.trimStart();
              table.getColumn(FilterBy)?.setFilterValue(trimValue);
            }}
          />
        </div>
        {/* <div className="flex items-center justify-end">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="columns-button">
                Columns
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table
                .getAllColumns()
                .filter(
                  (column) => column.getCanHide() && column.id === "select",
                )
                .map((column) => {
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) =>
                        column.toggleVisibility(!!value)
                      }
                    >
                      {column.id}
                    </DropdownMenuCheckboxItem>
                  );
                })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div> */}
      </div>
      <div className="rounded-md border mt-4">
        <Table>
          <TableHeader>
            <TableRow className="bg-secondary">
              {hasRowSelection && (
                <TableHead>
                  <Checkbox
                    checked={table.getIsAllPageRowsSelected()}
                    onCheckedChange={(value: boolean) => {
                      table.toggleAllPageRowsSelected(!!value);
                      handleRowSelectionChange("", false);
                      // Call the handler when the checkbox is clicked
                    }}
                    aria-label="Select All"
                  />
                </TableHead>
              )}
              {isSelectedColumn !== false &&
                typeof isSelectedColumn === "string" && (
                  <TableHead className=" text-white"></TableHead>
                )}
              <TableHead className="text-center text-white w-20">
                {String(t("dataTable.slno"))}
              </TableHead>
              {table.getHeaderGroups().map((headerGroup) =>
                headerGroup.headers.map((header) => (
                  <TableHead
                    key={header.id}
                    className="text-white"
                    style={applyHeaderStyle(header.id)}
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext(),
                        )}
                  </TableHead>
                )),
              )}
              {actions.length > 0 &&
                actions.map((action, index) => (
                  <TableCell
                    className={
                      action.isEnable === false ? "hidden" : "text-white"
                    }
                    key={index}
                  >
                    <div className="text-sm">{action.title}</div>
                  </TableCell>
                ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length !== 0 ? (
              table.getRowModel().rows.map((row, index) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {hasRowSelection && (
                    <TableCell>
                      <Checkbox
                        checked={row.getIsSelected()}
                        onCheckedChange={(value: boolean) => {
                          row.toggleSelected(!!value);
                          // handleRowSelectionChange(index);
                          handleRowSelectionChange(row.id, !!value);
                        }}
                        // aria-label="Select row"
                        aria-label={`Select row ${row.id}`}
                      />
                    </TableCell>
                  )}

                  {isSelectedColumn !== false &&
                    typeof isSelectedColumn === "string" && (
                      <TableCell>
                        <Checkbox
                          checked={
                            (row?.original?.[
                              isSelectedColumn as keyof typeof row.original
                            ] as boolean) ||
                            selectedPrivileges.includes(
                              row.original[
                                isSelectedColumn as keyof typeof row.original
                              ] as string,
                            )
                          }
                          onCheckedChange={(value: boolean) => {
                            row.toggleSelected(!!value);
                            handleRowSelectionChange(row.id, !!value);
                          }}
                          aria-label={`Select row ${row.id}`}
                          disabled={
                            disableExpired === true ||
                            (row?.original?.[
                              disableExpired as keyof typeof row.original
                            ] as boolean)
                          }
                        />
                      </TableCell>
                    )}

                  <TableCell className="text-center">
                    {table.getState().pagination.pageIndex *
                      table.getState().pagination.pageSize +
                      index +
                      1}
                  </TableCell>
                  {row.getVisibleCells().map((cell) => {
                    const cellValue = cell.getValue() as string;
                    const hasHTML = /<\/?[a-z][\s\S]*>/i.test(cellValue);
                    const columnId = cell.column.id;
                    const columnWidth = customColumnWidths[columnId]?.width;
                    const columnAlign = customColumnWidths[columnId]?.align;
                    return (
                      <TableCell
                        key={cell.id}
                        className={`text-${columnAlign}`}
                        style={{ width: columnWidth, wordBreak: "break-word" }}
                      >
                        {hasHTML ? (
                          <div
                            dangerouslySetInnerHTML={{ __html: cellValue }}
                          />
                        ) : (
                          flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext(),
                          )
                        )}
                      </TableCell>
                    );
                  })}

                  {actions.length > 0 &&
                    actions.map((action, index) => {
                      if (action.title === "Approve") {
                        return (
                          <TableCell
                            className={` ${
                              action.isEnable === false
                                ? "non-interactive-approve-element"
                                : ""
                            }`}
                            key={index}
                          >
                            <div
                              className="text-sm cursor-pointer inline-block "
                              onClick={() =>
                                action.handleClick(row.original as [])
                              }
                            >
                              {action.isEnable === false ||
                              (row?.original?.[
                                disableIcon as keyof typeof row.original
                              ] as boolean) ? (
                                <Check color="#15cce5" className="w-5 h-5" />
                              ) : (
                                <action.icon
                                  color={action.color}
                                  className="w-5 h-5"
                                />
                              )}
                            </div>
                          </TableCell>
                        );
                      } else if (
                        action.title === "Status" &&
                        action.isEnable === true
                      ) {
                        return (
                          <TableCell key={index}>
                            <div
                              className="text-sm cursor-pointer inline-block "
                              onClick={() =>
                                action.handleClick(row.original as [])
                              }
                            >
                              {(row?.original?.[
                                disableIcon as keyof typeof row.original
                              ] as boolean) ? (
                                <Check color="#15cce5" className="w-5 h-5" />
                              ) : (
                                <action.icon
                                  color={action.color}
                                  className="w-5 h-5"
                                />
                              )}
                            </div>
                          </TableCell>
                        );
                      }
                      // else if (
                      //   action.title === "Published Status" &&
                      //   action.isEnable === true
                      // ) {
                      //   return (
                      //     <TableCell key={index}>
                      //       <div
                      //         className="text-sm cursor-pointer inline-block "
                      //         onClick={() =>
                      //           action.handleClick(row.original as [])
                      //         }
                      //       >
                      //         {(row?.original?.[
                      //           disableIcon as keyof typeof row.original
                      //         ] as boolean) ? (
                      //           <RotateCcw
                      //             color="#15cce5"
                      //             className="w-5 h-5"
                      //           />
                      //         ) : (
                      //           <action.icon
                      //             color={action.color}
                      //             className="w-5 h-5"
                      //           />
                      //         )}
                      //       </div>
                      //     </TableCell>
                      //   );
                      // }
                      else if (
                        action.title === "Published Status" &&
                        action.isEnable === true
                      ) {
                        const hideStatus = row?.original?.[
                          disableStatus as keyof typeof row.original
                        ] as boolean;
                        const disabledIcons = row?.original?.[
                          disableIcon as keyof typeof row.original
                        ] as boolean;

                        return (
                          <TableCell key={index} className="text-center">
                            {!hideStatus && (
                              <div
                                className="text-sm cursor-pointer inline-block"
                                onClick={() =>
                                  action.handleClick(row.original as [])
                                }
                              >
                                {disabledIcons ? (
                                  <Check color="#15cce5" className="w-5 h-5" />
                                ) : (
                                  <action.icon
                                    color={action.color}
                                    className="w-5 h-5"
                                  />
                                )}
                              </div>
                            )}
                          </TableCell>
                        );
                      } else if (
                        action.title === "Publish" &&
                        action.isEnable === true
                      ) {
                        if (
                          row?.original?.[
                            disablePublish as keyof typeof row.original
                          ]
                        ) {
                          if (action.disabled) {
                            return (
                              <TableCell key={index}>
                                <div
                                  className="text-sm cursor-pointer inline-block"
                                  onClick={() =>
                                    action.disabled
                                      ? action.disabled(row.original as [])
                                      : console.log("disable")
                                  }
                                >
                                  <Check color="#15cce5" className="w-5 h-5" />
                                </div>
                              </TableCell>
                            );
                          } else {
                            return null;
                          }
                        } else {
                          return (
                            <TableCell className="" key={index}>
                              <div
                                className="text-sm cursor-pointer inline-block "
                                onClick={() =>
                                  action.handleClick(row.original as [])
                                }
                              >
                                <action.icon
                                  color={action.color}
                                  className="w-5 h-5"
                                />
                              </div>
                            </TableCell>
                          );
                        }
                      } else if (action.title === "Change Role") {
                        if (
                          row?.original?.[
                            hideAdminUsers as keyof typeof row.original
                          ]
                        ) {
                          return (
                            <TableCell key={index}>
                              <div
                                className="text-sm cursor-pointer inline-block"
                                onClick={() =>
                                  action.handleClick(row.original as [])
                                }
                              ></div>
                            </TableCell>
                          );
                        } else {
                          return (
                            <TableCell className="" key={index}>
                              <div
                                className="text-sm cursor-pointer inline-block "
                                onClick={() =>
                                  action.handleClick(row.original as [])
                                }
                              >
                                <action.icon
                                  color={action.color}
                                  className="w-5 h-5"
                                />
                              </div>
                            </TableCell>
                          );
                        }
                      } else if (action.title === "Deactivate/Activate") {
                        if (
                          row?.original?.[
                            disableActiveUsers as keyof typeof row.original
                          ]
                        ) {
                          return (
                            <TableCell key={index}>
                              <div
                                className="text-sm cursor-pointer inline-block"
                                onClick={() =>
                                  action.handleClick(row.original as [])
                                }
                              >
                                <Lock color="#15cce5" className="w-5 h-5" />
                              </div>
                            </TableCell>
                          );
                        } else {
                          return (
                            <TableCell className="" key={index}>
                              <div
                                className="text-sm cursor-pointer inline-block "
                                onClick={() =>
                                  action.handleClick(row.original as [])
                                }
                              >
                                <action.icon
                                  color={action.color}
                                  className="w-5 h-5"
                                />
                              </div>
                            </TableCell>
                          );
                        }
                      } else if (
                        action.title === "Approve/Reject"
                        // &&
                        // action.isEnable === true
                      ) {
                        return (
                          // <TableCell key={index}>
                          <TableCell
                            className={` ${
                              action.isEnable === false
                                ? "non-interactive-approve-element"
                                : ""
                            }`}
                            key={index}
                          >
                            <div
                              className="text-sm cursor-pointer inline-block "
                              onClick={() =>
                                action.handleClick(row.original as [])
                              }
                            >
                              {action.isEnable === false ||
                              (row?.original?.[
                                changeApproveIcon as keyof typeof row.original
                              ] as boolean) ? (
                                <Check color="#15cce5" className="w-5 h-5" />
                              ) : (row?.original?.[
                                  changeRejectIcon as keyof typeof row.original
                                ] as boolean) ? (
                                <Ban color="#ff0000" className="w-5 h-5" />
                              ) : (
                                <action.icon
                                  color={action.color}
                                  className="w-5 h-5"
                                />
                              )}
                            </div>
                          </TableCell>
                        );
                      } else if (action.title === "Edit") {
                        if (
                          row?.original?.[hideEdit as keyof typeof row.original]
                        ) {
                          return (
                            <TableCell
                              className={`text-center ${
                                action.isEnable === false ? "hidden" : ""
                              }`}
                              key={index}
                            ></TableCell>
                          );
                        } else {
                          return (
                            <TableCell
                              className={`text-center ${
                                action.isEnable === false ||
                                (row?.original?.[
                                  hideEdit as keyof typeof row.original
                                ] as boolean)
                                  ? "non-interactive-element"
                                  : ""
                              }`}
                              key={index}
                            >
                              <div
                                className="text-sm cursor-pointer flex justify-center items-center"
                                onClick={() =>
                                  action.handleClick(row.original as [])
                                }
                              >
                                <action.icon
                                  color={action.color}
                                  className="w-5 h-5"
                                />
                              </div>
                            </TableCell>
                          );
                        }
                      } else if (action.title === "Change Url") {
                        if (
                          !row?.original?.[
                            hideEdit as keyof typeof row.original
                          ]
                        ) {
                          return (
                            <TableCell
                              className={`text-center ${
                                action.isEnable === true ? "hidden" : ""
                              }`}
                              key={index}
                            ></TableCell>
                          );
                        } else {
                          return (
                            <TableCell
                              className={`text-center ${
                                action.isEnable === false
                                  ? "non-interactive-element"
                                  : ""
                              }`}
                              key={index}
                            >
                              <div
                                className="text-sm cursor-pointer flex justify-center items-center"
                                onClick={() =>
                                  action.handleClick(row.original as [])
                                }
                              >
                                <action.icon
                                  color={action.color}
                                  className="w-5 h-5"
                                />
                              </div>
                            </TableCell>
                          );
                        }
                      } else if (action.title === "Start Exam") {
                        if (
                          row?.original?.[
                            hideStart as keyof typeof row.original
                          ]
                        ) {
                          return (
                            <TableCell
                              className={`text-center ${
                                action.isEnable === false ? "hidden" : ""
                              }`}
                              key={index}
                            ></TableCell>
                          );
                        } else {
                          return (
                            <TableCell
                              className={`text-center ${
                                action.isEnable === false ||
                                (row?.original?.[
                                  hideStart as keyof typeof row.original
                                ] as boolean)
                                  ? "non-interactive-element"
                                  : ""
                              }`}
                              key={index}
                            >
                              <div
                                className="text-sm cursor-pointer"
                                onClick={() =>
                                  action.handleClick(row.original as [])
                                }
                              >
                                <action.icon
                                  color={action.color}
                                  className="w-5 h-5"
                                />
                              </div>
                            </TableCell>
                          );
                        }
                      } else if (action.title === "Send Email") {
                        if (
                          row?.original?.[
                            disableEmail as keyof typeof row.original
                          ]
                        ) {
                          return (
                            <TableCell
                              className="text-center hidden"
                              key={index}
                            ></TableCell>
                          );
                        } else {
                          return (
                            <TableCell className="text-center" key={index}>
                              <div
                                className="text-sm cursor-pointer"
                                onClick={() =>
                                  action.handleClick(row.original as [])
                                }
                              >
                                <action.icon
                                  color={action.color}
                                  className="w-5 h-5"
                                />
                              </div>
                            </TableCell>
                          );
                        }
                      } else if (action.title === "Certificate") {
                        if (
                          row?.original?.[
                            disableIcon as keyof typeof row.original
                          ]
                        ) {
                          return (
                            <TableCell
                              className="text-center hidden"
                              key={index}
                            ></TableCell>
                          );
                        } else {
                          return (
                            <TableCell className="text-center" key={index}>
                              <div
                                className="text-sm cursor-pointer"
                                onClick={() =>
                                  action.handleClick(row.original as [])
                                }
                              >
                                <action.icon
                                  color={action.color}
                                  className="w-5 h-5"
                                />
                              </div>
                            </TableCell>
                          );
                        }
                      }
                      //

                      //
                      else if (action.title === "Delete") {
                        if (
                          row?.original?.[
                            disableEditDelete as keyof typeof row.original
                          ]
                        ) {
                          // return null;
                          return (
                            <TableCell
                              className={`text-center ${
                                action.isEnable === false ? "hidden" : ""
                              }`}
                              key={index}
                            ></TableCell>
                          );
                        } else {
                          return (
                            <TableCell
                              className={`text-center ${
                                action.isEnable === false ||
                                (row?.original?.[
                                  disableEditDelete as keyof typeof row.original
                                ] as boolean)
                                  ? "non-interactive-element"
                                  : ""
                              }`}
                              key={index}
                            >
                              <div
                                className="text-sm cursor-pointer flex justify-center items-center"
                                onClick={() =>
                                  action.handleClick(row.original as [])
                                }
                              >
                                <action.icon
                                  color={action.color}
                                  className="w-5 h-5"
                                />
                              </div>
                            </TableCell>
                          );
                        }
                      } else if (
                        action.title === "View" &&
                        row?.original?.[
                          disableView as keyof typeof row.original
                        ]
                      ) {
                        return (
                          <TableCell
                            className={`text-center ${
                              action.isEnable === false ||
                              (row?.original?.[
                                disableView as keyof typeof row.original
                              ] as boolean)
                                ? "non-interactive-element"
                                : ""
                            }`}
                            key={index}
                          >
                            <div
                              className="text-sm cursor-pointer flex justify-center items-center"
                              onClick={() =>
                                action.handleClick(row.original as [])
                              }
                            >
                              <action.icon
                                color={action.color}
                                className="w-5 h-5"
                              />
                            </div>
                          </TableCell>
                        );
                      } else if (action.title === "View Linked Course") {
                        return (
                          <TableCell
                            className={`text-center ${
                              action.isEnable === false ||
                              (row?.original?.[
                                disableView as keyof typeof row.original
                              ] as boolean)
                                ? "non-interactive-element"
                                : ""
                            }`}
                            key={index}
                          >
                            <div
                              className="text-sm cursor-pointer flex justify-center items-center"
                              onClick={() =>
                                action.handleClick(row.original as [])
                              }
                            >
                              <action.icon
                                color={action.color}
                                className="w-5 h-5"
                              />
                            </div>
                          </TableCell>
                        );
                      } else if (action.title === "View/Link Course/Resource") {
                        return (
                          <TableCell
                            className={`text-center ${
                              action.isEnable === false ? "hidden" : ""
                            }`}
                            key={index}
                          >
                            <div
                              className="text-sm cursor-pointer"
                              onClick={() =>
                                action.handleClick(row.original as [])
                              }
                            >
                              <action.icon
                                color={
                                  row?.original?.[
                                    isCourseAssigned as unknown as keyof typeof row.original
                                  ]
                                    ? "green"
                                    : "red"
                                }
                                className="w-5 h-5"
                              />
                            </div>
                          </TableCell>
                        );
                      } else {
                        return (
                          <TableCell
                            className={`text-center ${
                              action.isEnable === false ? "hidden" : ""
                            }`}
                            key={index}
                          >
                            <div
                              // className="text-sm cursor-pointer"
                              className={`text-sm cursor-pointer ${
                                action?.className ??
                                "flex justify-center items-center"
                              }`}
                              onClick={() =>
                                action.handleClick(row.original as [])
                              }
                            >
                              <action.icon
                                color={action.color}
                                className="w-5 h-5"
                              />
                            </div>
                          </TableCell>
                        );
                      }
                    })}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={
                    hasRowSelection ? columns.length + 2 : columns.length + 1
                  }
                  className="h-24 text-center"
                >
                  {String(t("dataTable.noDataFound"))}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="w-full mt-2 flex items-center space-x-4 lg:space-x-8 bg-[#273B4A] text-white p-2">
        <div className="flex-1 text-sm text-muted-foreground text-center lg:text-left lg:mt-0">
          {hasRowSelection && (
            <p>
              {table.getFilteredSelectedRowModel().rows.length} of{" "}
              {table.getFilteredRowModel().rows.length} row(s) selected.
            </p>
          )}
        </div>
        <div className="flex items-center space-x-2">
          <p className="text-sm font-medium">{t("dataTable.rowsPerPage")}</p>
          <Select
            value={`${table.getState().pagination.pageSize}`}
            onValueChange={(value) => {
              table.setPageSize(Number(value));
            }}
          >
            <SelectTrigger className="h-8 w-[70px]">
              <SelectValue placeholder={table.getState().pagination.pageSize} />
            </SelectTrigger>
            <SelectContent side="top">
              {[10, 20, 30, 40, 50].map((pageSize) => (
                <SelectItem key={pageSize} value={`${pageSize}`}>
                  {pageSize}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="flex items-center space-x-2 lg:space-x-4">
          <Button
            variant="outline"
            className="h-8 w-8 p-0 lg:hidden"
            onClick={() => table.setPageIndex(0)}
            disabled={!table.getCanPreviousPage()}
          >
            <span className="sr-only">Go to first page</span>
            <DoubleArrowLeftIcon className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            className="h-8 w-8 p-0"
            onClick={() => {
              table.previousPage();
              // window.scrollTo(0, 0);
            }}
            disabled={!table.getCanPreviousPage()}
          >
            <span className="sr-only">Go to previous page</span>
            <ChevronLeftIcon className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            className="h-8 w-8 p-0"
            onClick={() => {
              table.nextPage();
              // window.scrollTo(0, 0);
            }}
            disabled={!table.getCanNextPage()}
          >
            <span className="sr-only">Go to next page </span>
            <ChevronRightIcon className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            className="h-8 w-8 p-0 lg:hidden"
            onClick={() => table.setPageIndex(table.getPageCount() - 1)}
            disabled={!table.getCanNextPage()}
          >
            <span className="sr-only">Go to last page</span>
            <DoubleArrowRightIcon className="h-4 w-4" />
          </Button>
        </div>
        <div className="flex w-full items-center justify-center text-sm font-medium lg:w-auto">
          {/* Page {table.getState().pagination.pageIndex + 1} of{" "}
          {table.getPageCount()} */}
          {`${t("dataTable.pageInfo", {
            current: table.getState().pagination.pageIndex + 1,
            total: table.getPageCount(),
          })}`}
        </div>
      </div>
    </div>
  );
}
