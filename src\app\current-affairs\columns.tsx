" use client";

import type { ColumnDef } from "@tanstack/react-table";
// import { ArrowUpDown } from "lucide-react";
// import { Button } from "@/components/ui/button";
import type {
  CurrentAffairsData,
  // CurrentAffairsColumnDefinition,
  CurrentAffairsRowDefinition,
} from "@/types";
import moment from "moment";

export const getColumns = (
  t: (key: string) => string,
): ColumnDef<CurrentAffairsData>[] => [
  {
    accessorKey: "title",
    header: t("currentAffairs.eventTitle"),
    // header: ({ column }: CurrentAffairsColumnDefinition): React.JSX.Element => {
    //   return (
    //     <Button
    //       className="px-0"
    //       variant="ghost"
    //       onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
    //     >
    //       Event title
    //       <ArrowUpDown className="ml-2 h-4 w-4" />
    //     </Button>
    //   );
    // },
  },
  // {
  //   accessorKey: "content",
  //   header: "Content",
  //   cell: ({ row }: CurrentAffairsRowDefinition): React.JSX.Element => (
  //     <div className="text-center">{row.original.content}</div>
  //   ),
  // },
  {
    accessorKey: "publish_date",
    header: t("currentAffairs.publishedDate"),
    cell: ({ row }: CurrentAffairsRowDefinition): React.JSX.Element => {
      if (row.original.publish_status === "Draft") {
        return <div></div>;
      }
      const formattedDate = moment(row.original.publish_date).format(
        "DD-MMM-YYYY",
      );
      return <div>{formattedDate}</div>;
    },
  },
];
