"use client";
import React, { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Editor } from "primereact/editor";
import { useForm } from "react-hook-form";
import type {
  ErrorCatch,
  fetchTokenRequest,
  insertMessageRequest,
  Notification,
  pushNotificationRequest,
  ToastType,
} from "@/types";
import { zodResolver } from "@hookform/resolvers/zod";
import { NotificationSchema } from "@/schema/schema";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import useEnrollments from "@/hooks/useEnrollment";
import { useToast } from "@/components/ui/use-toast";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import { useTranslation } from "next-i18next";

export default function Notifications({
  onCancel,
  onSave,
  userId,
}: {
  onCancel: () => void;
  onSave: () => void;
  userId: string;
}): React.JSX.Element {
  const { fetDeviceToken, pushNotification, insertMessage } = useEnrollments();
  const [token, setToken] = useState<string>("");
  const { t } = useTranslation();

  const form = useForm<Notification>({
    resolver: zodResolver(NotificationSchema),
  });
  const { toast } = useToast() as ToastType;
  useEffect(() => {
    const orgId = localStorage.getItem("orgId");
    const params: fetchTokenRequest = {
      org_id: orgId as string,
      user_id: userId,
    };
    const fetchTokenData = async (): Promise<void> => {
      try {
        const resp = await fetDeviceToken(params);
        setToken(resp.result[0]);
      } catch (error) {
        const err = error as ErrorCatch;
        console.log(err);
      }
    };
    fetchTokenData().catch((error) => console.log(error));
  }, []);

  const insertMessageToTable = async (): Promise<void> => {
    const formData = form.getValues();
    const orgId = localStorage.getItem("orgId");
    const reqParams: insertMessageRequest = {
      org_id: orgId as string,
      user_id: userId,
      notification_data: {
        target_id: null,
        device_token_id: token,
        message_text: formData.message,
      },
    };

    try {
      const resp = await insertMessage(reqParams);
      console.log("Message inserted:", resp);
      toast({
        variant: SUCCESS_MESSAGES.toast_variant_default,
        title: t("successMessages.toast_success_title"),
        description: "successMessages.send_notification",
      });
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err.message,
      });
      console.log(err);
    }
  };

  const setRichTextValue = (val: string): void => {
    form.setValue("message", val);
  };

  const onSubmit = async (data: Notification): Promise<void> => {
    const params: pushNotificationRequest = {
      userid: userId,
      message: data.message,
      fcmtoken: token,
    };
    try {
      const resp = await pushNotification(params);
      await insertMessageToTable();
      onSave();
      console.log("Notification sent:", resp);
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: SUCCESS_MESSAGES.toast_variant_default,
        title: t("successMessages.toast_success_title"),
        description: "successMessages.send_notification",
      });
      console.log(err);
    }
  };

  return (
    <div>
      <h1 className="text-xl font-semibold mb-4">
        {String(t("enrollment.composeMailContent"))}
      </h1>
      <div className="border rounded-md p-4 bg-[#fff]">
        <Form {...form}>
          <form
            // eslint-disable-next-line @typescript-eslint/no-misused-promises
            onSubmit={form.handleSubmit(onSubmit)}
            className="space-y-8"
          >
            <div className="mt-4">
              <FormField
                name="message"
                control={form.control}
                render={() => (
                  <FormItem>
                    <FormLabel>
                      {String(t("enrollment.message"))}{" "}
                      <span className="text-red-700">*</span>
                    </FormLabel>
                    <FormControl>
                      <Editor
                        value=""
                        onTextChange={(event) => {
                          const value = event.textValue.trim();
                          setRichTextValue(value);
                        }}
                        style={{ height: "320px" }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="w-full flex justify-end mt-4 gap-3">
              <Button type="submit" onClick={() => onCancel()}>
                {String(t("buttons.cancel"))}
              </Button>
              <Button type="submit" className="bg-[#9FC089]">
                {String(t("buttons.send"))}
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
}
