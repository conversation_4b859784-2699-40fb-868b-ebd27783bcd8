
@tailwind base;
@tailwind components;
@tailwind utilities;
 
html {
  --color-primary: #4CB5F5;
  --color-secondary: #00afbb;
  --color-buttons: #4C8055;
  --color-typography: #c2b490;
}

@layer base {
  :root {
    /* Name: custom color palette
       Author: <PERSON><PERSON> Ism
       URL: https://gradient.page */

    /* CSS: .bg-gradient { background: var(--gradient) } */
    --gradient: #243949;
    --background: 219 96.7% 98.44%;
    --foreground: 219 3.****************% 0.88%;

    --muted: 219 3.****************% 92.2%;
    --muted-foreground: 219 1.7000000000000002% 42.2%;

    --popover: 219 62.8% 92.2%;
    --popover-foreground: 219 3.****************% 1.1%;

    --white: 0 0% 100%;
    --card: 219 62.8% 92.2%;
    --card-foreground: 219 3.****************% 1.1%;

    --border: 219 8.4% 89.88%;
    --input: 219 8.4% 89.88%;

    --primary: 	340 13% 23%;
    --primary-foreground: 219 0.68% 92.2%;

    --secondary: 184 100% 37%;
    --secondary-foreground: 219 3.04% 12.2%;

    --accent: 219 1.7000000000000002% 96.1%;
    --accent-foreground: 219 3.04% 12.2%;

    --destructive: 0 44.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --ring: 219 34% 22%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 0 0% 4% /* neutral-950 */;
    --foreground: 0 0% 99% /* neutral-50 */;
    --card: 0 0% 4% /* neutral-950 */;
    --card-foreground: 0 0% 99% /* neutral-50 */;
    --popover: 0 0% 4% /* neutral-950 */;
    --popover-foreground: 0 0% 99% /* neutral-50 */;
    --primary: 21 91% 49% /* orange-600 */;
    --primary-foreground: 34 100% 97% /* orange-50 */;
    --secondary: 143 65% 25% /* green-800 */;
    --secondary-foreground: 139 77% 97% /* green-50 */;
    --muted: 0 0% 15% /* neutral-800 */;
    --muted-foreground: 0 0% 64% /* neutral-400 */;
    --accent: 336 75% 36% /* pink-800 */;
    --accent-foreground: 328 74% 98% /* pink-50 */;
    --destructive: 0 63% 31% /* red-900 */;
    --destructive-foreground: 0 0% 99% /* neutral-50 */;
    --border: 0 0% 15% /* neutral-800 */;
    --input: 0 0% 15% /* neutral-800 */;
    --ring: 21 91% 49% /* orange-600 */;
  }

}
 
@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}