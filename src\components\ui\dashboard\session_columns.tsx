"use client";

import type { ColumnDef } from "@tanstack/react-table";
import { ArrowUpDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import type {
  UserSession,
  UserSessionViewsColumnDefinition,
  UserSessionViewsRowDefinition,
} from "@/types";
import moment from "moment";

export const columns: ColumnDef<UserSession>[] = [
  {
    accessorKey: "sequence",
    header: ({
      column,
    }: UserSessionViewsColumnDefinition): React.JSX.Element => {
      return (
        <Button
          className="px-0"
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Sequence
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => <div>{row.original.sequence}</div>,
  },
  {
    accessorKey: "checkpoint_name",
    header: "Checkpoint",
    cell: ({ row }: UserSessionViewsRowDefinition): React.JSX.Element => (
      <div>{row.original.checkpoint_name}</div>
    ),
  },
  {
    accessorKey: "first_name",
    header: "User",
    cell: ({ row }: UserSessionViewsRowDefinition): React.JSX.Element => {
      const firstName = row.original.first_name ?? " ";
      const lastName = row.original.lastname ?? " ";
      return <div className="text-align">{`${firstName} ${lastName}`}</div>;
    },
  },
  {
    accessorKey: "start_time",
    header: "Time",
    cell: ({ row }: UserSessionViewsRowDefinition): React.JSX.Element => (
      <div>{row.original.start_time}</div>
    ),
  },
  {
    accessorKey: "quiz_name",
    header: "Exam Name",
    cell: ({ row }: UserSessionViewsRowDefinition): React.JSX.Element => (
      <div>{row.original.quiz_name}</div>
    ),
  },
  {
    accessorKey: "session_end_time",
    header: "Exam Attended On",
    cell: ({ row }: UserSessionViewsRowDefinition): React.JSX.Element => {
      const formattedDate =
        row.original !== null
          ? moment
              .utc(row.original.session_end_time)
              .local()
              .format("DD-MMM-YYYY hh:mm a")
          : "";

      return <div>{formattedDate}</div>;
    },
  },

  {
    accessorKey: "resuilt",
    header: "Result",
    cell: ({ row }: UserSessionViewsRowDefinition): React.JSX.Element => (
      <div>{row.original.result}</div>
    ),
  },
];
