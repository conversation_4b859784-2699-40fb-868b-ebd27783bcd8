"use client";
import React, { useEffect, useState } from "react";
import "../../styles/main.css";
import MainLayout from "../layout/mainlayout";
import { Label } from "@/components/ui/label";
import { Combobox } from "@/components/ui/combobox";
import type { CheckpointsUserStatsReturnType } from "@/types";
import { DataTable } from "@/components/ui/data-table/data-table";
import { columns } from "./column";
import type { ComboData } from "@/types";
import { useSearchParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { ORG_KEY, pageUrl } from "@/lib/constants";
import { useRouter } from "next/navigation";
import useSessionViews from "@/hooks/useSessionViews";
import useUsers from "@/hooks/useUsers";
import useCourse from "@/hooks/useCourse";

export default function UserGroupPage(): React.JSX.Element {
  const [courseId, setCourseId] = useState("");
  const [sessionData, setSessionData] = useState<
    CheckpointsUserStatsReturnType[]
  >([]);

  const searchParams = useSearchParams();
  const { getSessionUsersDetails } = useSessionViews();

  const [courseData, setCourseData] = useState<ComboData[]>([]);

  const { getCourseList } = useCourse();

  const [userName, setUserName] = useState("");
  const [courseName, setCourseName] = useState("");
  const { getUsers } = useUsers();
  const [users, setUsers] = useState<{ value: string; label: string }[]>([]);

  const [userId, setUserId] = useState("");

  const router = useRouter();

  useEffect(() => {
    setCourseId(searchParams.get("course") ?? "");

    const fetchInitialData = async (): Promise<void> => {
      const courseList = await getCourseList();
      const filteredCourses = courseList
        .filter((course) => course.course_id == courseId)
        .map((course) => ({
          value: course.course_id,
          label: course.full_name,
        }));

      if (filteredCourses.length > 0) {
        setCourseData(filteredCourses);
        setCourseName(filteredCourses[0].label as string);
      } else {
        setCourseData([]);
      }

      const id_org = localStorage.getItem("orgId");
      const orgId = id_org ?? "";
      const fetchedUsers = await getUsers(orgId);
      const filteredUsers = fetchedUsers
        .filter(
          (user) =>
            user.id !== null &&
            user.first_name !== null &&
            user.last_name !== null,
        )
        .map((user) => ({
          value: user.id,
          label: user.first_name + " " + user.last_name,
        }));

      if (filteredUsers.length > 0) {
        setUsers(filteredUsers);
      } else {
        setUsers([]);
      }

      setUserId(filteredUsers[0].value);
      setUserName(filteredUsers[0].label);
    };

    fetchInitialData()
      .then((response) => {
        console.log(response);
      })
      .catch((error) => {
        console.log(error);
      });
  }, [courseId]);

  useEffect(() => {
    const fetchSessionData = async (): Promise<void> => {
      console.log("ids");
      console.log(userId);
      if (userId !== "") {
        try {
          const userSessionStatistics = await getSessionUsersDetails(
            localStorage.getItem(ORG_KEY),
            courseId,
            userId,
          );

          const userSessions: CheckpointsUserStatsReturnType[] = [];
          if (
            userSessionStatistics !== null ||
            userSessionStatistics !== undefined
          ) {
            userSessionStatistics.forEach((element) => {
              userSessions.push({
                email: element.email,
                user_id: element.user_id,
                video_id: element.video_id,
                last_name: element.last_name,
                first_name: element.first_name,
                video_name: element.video_name,
                exam_result: element.exam_result,
                checkpoint_name: element.checkpoint_name,
                enrollment_time: element.enrollment_time,
                exam_start_time: element.exam_start_time,
                last_watched_on: element.last_watched_on,
              });
            });

            setSessionData(userSessions as CheckpointsUserStatsReturnType[]);
          }
        } catch (error) {
          console.error("Error fetching data:", error);
        }
      }
    };
    fetchSessionData()
      .then((response) => {
        console.log(response);
      })
      .catch((error) => {
        console.log(error);
      });
  }, [userId]);

  function handleResourseChange(value: string): void {
    //to do
    //console.log("module change");
    console.log(value);
    //setModuleResourceId(value);
  }
  function handleUserChange(value: string): void {
    //to do
    //console.log("user change");
    console.log(value);
    setUserId(value);
    const sessionData: CheckpointsUserStatsReturnType[] = [];
    setSessionData(sessionData);
  }

  const addSessionCancel = (): void => {
    router.push(pageUrl.dashboard);
  };

  console.log("sessions 2");
  console.log(sessionData);
  return (
    <MainLayout>
      <h1 className="text-2xl font-semibold tracking-tight pb-5">
        Session Details
      </h1>
      <div className="border rounded-md p-4 mt-4">
        <div className="w-full flex flex-wrap justify-between space-x-4">
          <div className="flex space-x-4">
            <div className="w-full sm:w-1/2">
              <div className="w-full">
                <Label className="block">Course Selected</Label>
                <div className="w-full course-width mt-2">
                  <Combobox
                    data={courseData}
                    onSelectChange={handleResourseChange}
                    defaultLabel={courseName}
                    isDisabled={true}
                  />
                </div>
              </div>
            </div>
            <div className="w-full sm:w-1/2">
              <div className="w-full">
                <Label className="block">Select User</Label>
                <div className="w-full course-width mt-2">
                  <Combobox
                    data={users}
                    onSelectChange={handleUserChange}
                    defaultLabel={userName}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div>
          {sessionData !== null ? (
            <DataTable
              columns={columns}
              data={sessionData}
              FilterLabel={"Filter by First Name"}
              FilterBy={"first_name"}
              actions={[]}
              onSelectedDataChange={() => {}}
            />
          ) : (
            ""
          )}
        </div>
        <div className="flex flex-wrap justify-end mt-8">
          <div className="mt-6 flex items-center justify-end gap-x-6">
            <Button
              type="button"
              variant="outline"
              className="primary"
              onClick={addSessionCancel}
            >
              Back
            </Button>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
