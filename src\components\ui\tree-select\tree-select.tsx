import React, { useState, useEffect } from "react";
import { TreeSelect, type TreeSelectChangeEvent } from "primereact/treeselect";
import "primereact/resources/themes/lara-light-indigo/theme.css";
import "primereact/resources/primereact.min.css";
// import "primeicons/primeicons.css";
import "../../../styles/tree-select.css";
import { stringCheck } from "@/lib/utils";
import type { TreeDataItem } from "@/components/ui/tree";
import type { TreeNode } from "@/types";

interface TreeSelectComponentProps {
  nodeData: TreeDataItem[];
  selectLabel?: string;
  onNodeSelect: (selectedValue: string | null) => void;
  defaultValueKey?: string | null;
}

export default function TreeSelectComponent({
  nodeData,
  selectLabel,
  onNodeSelect,
  defaultValueKey,
}: TreeSelectComponentProps): React.JSX.Element {
  const [nodes, setNodes] = useState<TreeDataItem[]>([]);
  const [selectedNodeKey, setSelectedNodeKey] = useState<string | null>(
    defaultValueKey ?? "",
  );

  useEffect(() => {
    if (selectLabel === defaultValueKey) {
      setSelectedNodeKey(null);
    }
  }, [selectLabel, defaultValueKey]);
  
  useEffect(() => {
    setNodes(nodeData);
  }, [nodeData]);

  const findValueFromKey = (
    key: string | null,
    nodes: TreeDataItem[],
  ): string | null => {
    if (stringCheck(key)) {
      return null;
    }

    const findValue = (
      nodeKey: string | null,
      nodesToSearch: TreeDataItem[],
    ): string | null => {
      for (const node of nodesToSearch) {
        if (node.key === nodeKey) {
          return node.data;
        }
        if (node.children) {
          const childValue = findValue(nodeKey, node.children);
          if (childValue !== null) {
            return childValue;
          }
        }
      }
      return null;
    };

    return findValue(key, nodes);
  };

  return (
    <div className="card flex justify-content-center">
      <TreeSelect
        value={selectedNodeKey}
        options={nodes as TreeNode[]}
        onChange={(e: TreeSelectChangeEvent) => {
          if (typeof e.value === "string" || e.value === null) {
            const selectedValue = findValueFromKey(e.value, nodes);
            setSelectedNodeKey(e.value);
            onNodeSelect(selectedValue);
          }
        }}
        filter
        className="md:w-20rem w-full treeSelect border text-sm font-medium"
        placeholder={stringCheck(selectLabel) ? selectLabel : "Select Item"}
        pt={{
          labelContainer: {
            className: "text-left ",
          },
        }}
        resetFilterOnHide={true}
        filterInputAutoFocus={true}
      />
    </div>
  );
}
