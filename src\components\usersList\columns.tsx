"use client";

import type { ColumnDef } from "@tanstack/react-table";
// import { ArrowUpDown } from "lucide-react";
// import { Button } from "@/components/ui/button";
import Image from "next/image";
import type {
  UserDetailsTableType,
  // UserDetailsColumnDefinition,
  UserDetailsRowDefinition,
} from "@/types";

export const getColumns = (
  t: (key: string) => string,
): ColumnDef<UserDetailsTableType>[] => [
  {
    accessorKey: "first_name",
    header: t("dashboard.users.firstName"),
    cell: ({ row }: UserDetailsRowDefinition): React.JSX.Element => {
      const firstName = row.original.first_name ?? " ";
      return <div className="text-align">{firstName}</div>;
    },
  },
  {
    accessorKey: "last_name",
    header: t("dashboard.users.lastName"),
    cell: ({ row }: UserDetailsRowDefinition): React.JSX.Element => {
      const lastName = row.original.last_name ?? " ";
      return <div className="text-align">{lastName}</div>;
    },
  },
  {
    accessorKey: "email",
    header: t("dashboard.users.email"),
    cell: ({ row }: UserDetailsRowDefinition) => (
      <div className="text-align">{row.original.email}</div>
    ),
  },
  {
    accessorKey: "avatar_url",
    header: t("dashboard.users.profile"),
    cell: ({ row }) => (
      <div className="h-10 w-10">
        {row.original.avatar_url !== "" && row.original.avatar_url !== null ? (
          <Image
            src={row.original.avatar_url}
            alt=""
            width={45}
            height={45}
            objectFit="cover"
            className="rounded-full"
            style={{
              width: "45px",
              height: "38px",
              border: "2px solid #000",
              borderRadius: "50%",
            }}
          />
        ) : (
          <div className="rounded-full h-25 w-25 bg-gray-300"></div>
        )}
      </div>
    ),
  },

  {
    accessorKey: "phonenumber1",
    header: t("dashboard.users.phone"),
    cell: ({ row }: UserDetailsRowDefinition) => (
      <div className="text-align">{row.original.phonenumber1}</div>
    ),
  },
  {
    accessorKey: "roles",
    header: t("dashboard.users.role"),
    cell: ({ row }: UserDetailsRowDefinition) => (
      <div className="text-align">{row.original.roles}</div>
    ),
  },
];
