# You can override the included template(s) by including variable overrides
# SAST customization: https://docs.gitlab.com/ee/user/application_security/sast/#customizing-the-sast-settings
# Secret Detection customization: https://docs.gitlab.com/ee/user/application_security/secret_detection/#customizing-settings
# Dependency Scanning customization: https://docs.gitlab.com/ee/user/application_security/dependency_scanning/#customizing-the-dependency-scanning-settings
# Container Scanning customization: https://docs.gitlab.com/ee/user/application_security/container_scanning/#customizing-the-container-scanning-settings
# Note that environment variables can be set in several places
# See https://docs.gitlab.com/ee/ci/variables/#cicd-variable-precedence
stages:
  - format
  - lint
  - test
  - sast

format:
  image: node:latest
  stage: format
  before_script:
    - npm install --legacy-peer-deps
  script:
    - npm run format

lint:
  image: node:latest
  stage: lint
  before_script:
    - npm install --legacy-peer-deps
  script:
    - npm run lint

unit-testing:
  image: node:latest
  stage: test
  before_script:
    - npm install --legacy-peer-deps
  script:
    - npm run test:ci
  coverage: /All files[^|]*\|[^|]*\s+([\d\.]+)/
  artifacts:
    paths:
      - coverage/
    when: always
    reports:
      junit:
        - junit.xml

sast:
  stage: sast
include:
  - template: Security/SAST.gitlab-ci.yml
