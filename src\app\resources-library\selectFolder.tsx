"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import type { ComboData, FolderData } from "@/types";
import React, { useEffect, useState } from "react";
import { Label } from "@radix-ui/react-label";
import { Combobox } from "@/components/ui/combobox";
import { useTranslation } from "react-i18next";

const SelectFolder = ({
  onCancel,
  folderData,
  onSave,
}: {
  onCancel: () => void;
  folderData: FolderData[];
  onSave: (folderId: string | null) => void;
}): JSX.Element => {
  const { t } = useTranslation();
  const [folderDetails, setfolderDetails] = useState<ComboData[]>([]);
  const [selFolderValue, setSelfolderValue] = useState<string>("");

  useEffect(() => {
    const convertedData = folderData?.map((folder) => ({
      value: folder.folder_id,
      label: folder.folder_name,
    }));
    setfolderDetails(convertedData);
  }, []);

  return (
    <div>
      <div className="flex gap-x-4 mt-4">
        <div className="sm:w-1/2 md:w-1/4 gap-x-4">
          <Label className="block">{t("resourceLibrary.selectFolder")}</Label>
          <Combobox
            data={folderDetails}
            onSelectChange={(value) => {
              setSelfolderValue(value);
            }}
          />
        </div>
      </div>
      <div className="flex justify-end gap-4 mt-8 pt-6">
        <Button className="bg-[#33363F]" onClick={onCancel}>
          {t("buttons.cancel")}
        </Button>
        <Button
          onClick={() => onSave(selFolderValue ?? null)}
          className="bg-[#9FC089]"
        >
          {t("buttons.submit")}
        </Button>
      </div>
    </div>
  );
};
export default SelectFolder;
