import type { BaseSyntheticEvent } from "react";
import React from "react";
import type { ErrorCatch, LogUserActivityRequest, ToastType } from "@/types";
import { Button } from "@/components/ui/button";
import { DOCUMENT_DRAFT, DOCUMENT_PUBLISHED, ORG_KEY } from "@/lib/constants";
import { useToast } from "@/components/ui/use-toast";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import useTopic from "@/hooks/useTopics";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";

export default function PublishCategory({
  publishStatus,
  onCancel,
  onSave,
  categoryId,
}: {
  onCancel: () => void;
  onSave: () => void;
  categoryId: string;
  publishStatus: string;
}): React.JSX.Element {
  const { toast } = useToast() as ToastType;
  const { t } = useTranslation();
  const { publishCategory } = useTopic();
  const { updateUserActivity } = useLogUserActivity();

  const handlePublishClick = async (e: BaseSyntheticEvent): Promise<void> => {
    console.log(e);
    const orgId = localStorage.getItem(ORG_KEY);
    const params = {
      category_id: categoryId,
      org_id: orgId ?? "",
      status:
        publishStatus === DOCUMENT_PUBLISHED
          ? DOCUMENT_DRAFT
          : DOCUMENT_PUBLISHED,
    };
    try {
      const result = await publishCategory(params);
      if (result.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_success,
          title: t("successMessages.categoryStatus"),
          description:
            publishStatus === DOCUMENT_PUBLISHED
              ? t("successMessages.draftCategoryMsg")
              : t("successMessages.publishCategoryMsg"),
        });
        const reqParams = {
          activity_type: "Topic",
          screen_name: "Topic",
          action_details: "Topic published successfully",
          target_id: categoryId,
          log_result: "SUCCESS",
        };
        void updateLogUserActivity(reqParams).catch((error) => {
          console.error(error);
        });
        onSave();
        onCancel();
      } else {
        const reqParams = {
          activity_type: "Topic",
          screen_name: "Topic",
          action_details: "Failed to publish topic",
          target_id: categoryId,
          log_result: "ERROR",
        };
        void updateLogUserActivity(reqParams).catch((error) => {
          console.error(error);
        });
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
    }
  };

  const handleCancel = (): void => {
    onCancel();
  };

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  return (
    <>
      <div className="mb-2 mr-4">
        {publishStatus === "Published" ? (
          <p className="ml-0 ">{String(t("topics.datftPrompt"))}</p>
        ) : (
          <p className="ml-0 ">{String(t("topics.publishPrompt"))}</p>
        )}
      </div>
      <div className="flex topic-icons pr-4">
        <div className="flex items-center justify-center float-right gap-3">
          <Button type="button" className="bg-[#33363F]" onClick={handleCancel}>
            {String(t("buttons.cancel"))}
          </Button>

          <Button
            type="submit"
            className="bg-[#9FC089]"
            onClick={(e: BaseSyntheticEvent) => {
              handlePublishClick(e).catch((error) => console.log(error));
            }}
          >
            {String(t("buttons.submit"))}
          </Button>
        </div>
      </div>
    </>
  );
}
