"use client";
import React, { useEffect, useState } from "react";
import MainLayout from "../layout/mainlayout";
import { ERROR_FETCHING_DATA, ORG_KEY } from "@/lib/constants";
import { Spinner } from "@/components/ui/progressiveLoader";
import type { CommentsListRequest, CommentResponse } from "@/types";
import { Ban, CheckCircle, MessageSquarePlus, Reply } from "lucide-react";
import useComments from "@/hooks/useComments";
import { Modal } from "@/components/ui/modal";
import ApproveRejectComments from "./commentsApproveReject";
import ReplyMessage from "./replyMessage";
import type { TreeTableToggleEvent } from "primereact/treetable";
import { TreeTable } from "primereact/treetable";
import { Column } from "primereact/column";
import Image from "next/image";
import "primereact/resources/themes/lara-light-indigo/theme.css";
import moment from "moment";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { useTranslation } from "react-i18next";

interface TreeNode {
  key: string;
  data: {
    message: string;
    message_type: string;
    subject: string;
    user_name: string;
    profile: string;
    created_at: string;
    status: string;
    created_date: string;
    fullComment: CommentResponse;
    role_name?: string;
    resource_name?: string;
  };
  children?: TreeNode[];
}

export default function CommentsPage(): React.JSX.Element {
  const {t} = useTranslation();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isOpenDialog, setIsOpenDialog] = useState<boolean>(false);
  const [isOpenReplyDialog, setIsOpenReplyDialog] = useState<boolean>(false);
  const [commentId, setCommentId] = useState<string>("");
  const [commentStatus, setCommentStatus] = useState<string>("");
  const [commentString, setCommentString] = useState<string>("");
  const [dataForReply, setDataForReply] = useState<CommentResponse>();
  const [data, setData] = useState<TreeNode[]>([]);
  const [initialData, setInitialData] = useState<TreeNode[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<Record<string, boolean>>({});
  const [searchInput, setSearchInput] = React.useState("");
  const { getCommentsList } = useComments();

  useEffect(() => {
    fetchCommentsList().catch((error) => console.log(error));
  }, []);

  const fetchCommentsList = async (): Promise<void> => {
    const org_id = localStorage.getItem(ORG_KEY);
    const params = {
      org_id: org_id as string,
    };
    try {
      const response = await getCommentsList(params as CommentsListRequest);
      if (response != null) {
        setInitialData(transformCommentsToTreeNodes(response));
        setData(transformCommentsToTreeNodes(response));
        setIsLoading(false);
      } else {
        setIsLoading(false);
      }
      response.map((item) => {
        if (item.status === "Approved") {
          item.approveIcon = true;
        } else {
          item.approveIcon = false;
        }
        if (item.status === "Rejected") {
          item.rejectIcon = true;
        } else {
          item.rejectIcon = false;
        }
      });
    } catch (error) {
      console.error(ERROR_FETCHING_DATA, error);
    }
  };

  const transformCommentsToTreeNodes = (
    comments: CommentResponse[],
  ): TreeNode[] => {
    const transform = (comment: CommentResponse): TreeNode => ({      
      key: comment.id,
      data: {
        message: comment.message,
        message_type: comment.type,
        subject: comment.subject,
        user_name: comment.name,
        profile: comment.avatar_url,
        created_at: comment.created_at,
        status: comment.status ?? "",
        created_date: comment.created_at,
        fullComment: comment,
        role_name: comment.role_name,
        resource_name: comment.resource_name,
      },
      children:
        Array.isArray(comment.children) && comment.children.length > 0
          ? comment.children.map(transform)
          : [],
    });

    return comments.map(transform);
  };

  const onNodeToggle = (event: TreeTableToggleEvent): void => {
    const newExpandedKeys: Record<string, boolean> = {};
    for (const key in event.value) {
      if (event.value[key]) {
        newExpandedKeys[key] = true;
      }
    }
    setExpandedKeys(newExpandedKeys);
  };

  const handleSearch = (): void => {
    if (searchInput !== "") {
      const filteredData =
        initialData?.filter((topic) => {
          return searchNode(topic, searchInput.toLowerCase());
        }) ?? [];
      setData(filteredData);
    } else {
      setData(initialData ?? []);
    }
  };

  const handleClearSearch = (): void => {
    setSearchInput("");
    setData(initialData ?? []);
  };

  const searchNode = (node: TreeNode, searchValue: string): boolean => {
    const nodeMessage = node.data.message.toLowerCase();
    if (nodeMessage.includes(searchValue)) {
      return true;
    }

    if (node.children && node.children.length > 0) {
      for (const childNode of node.children) {
        if (searchNode(childNode, searchValue)) {
          setExpandedKeys((prevExpandedKeys) => ({
            ...prevExpandedKeys,
            [node.key]: true,
          }));
          return true;
        }
      }
    }

    return false;
  };

  const commentApproveReject = (data: CommentResponse): void => {
    setCommentId(data.id);
    setCommentStatus(data.status as string);
    setCommentString(data.message);
    setIsOpenDialog(true);
  };

  const onSubmit = (): void => {
    fetchCommentsList().catch((error) => console.log(error));
  };

  const closeCommentDialog = (): void => {
    setIsOpenDialog(false);
  };

  const replyMessage = (data: CommentResponse): void => {
    console.log("Reply message", data);
    setDataForReply(data);
    setIsOpenReplyDialog(true);
  };

  const closeReplyDialog = (): void => {
    setIsOpenReplyDialog(false);
    fetchCommentsList().catch((error) => console.log(error));
  };

  return (
    <MainLayout>
      <div className="w-full">
        <>
          <h1 className="text-2xl font-semibold tracking-tight">{t("comments.title")}</h1>
        </>
        <div className="mt-4 flex flex-col md:flex-row items-start md:items-end justify-between mb-4">
          <div className="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4 items-start md:items-center w-full">
            <div className="w-full md:w-auto">
              <Input
                autoComplete="off"
                type="text"
                className="min-w-[250px] md:min-w-[430px] font-normal flex justify-between items-center"
                placeholder={t("comments.searchName")}
                value={searchInput}
                onChange={(e) => setSearchInput(e.target.value.trimStart())}
              />
            </div>
            <Button
              className="px-4 py-2 text-sm w-full md:w-auto mt-4 md:mt-0"
              onClick={handleSearch}
              style={{ backgroundColor: "#9FC089" }}
            >
              {t("buttons.search")}
            </Button>
            <Button
              className="px-2 py-2 text-sm w-full md:w-auto justify-end "
              onClick={handleClearSearch}
              style={{ backgroundColor: "#33363F" }}
            >
              {t("buttons.clear")}
            </Button>
          </div>
        </div>
        <>
          {isLoading ? (
            <Spinner />
          ) : (
            <div className="overflow-x-auto ps-1">
              <TreeTable
                value={data}
                className="custom-tree-table w-full min-w-[50rem]"
                expandedKeys={expandedKeys}
                onToggle={onNodeToggle}
                paginator
                rows={10}
                paginatorTemplate="RowsPerPageDropdown FirstPageLink PrevPageLink CurrentPageReport NextPageLink LastPageLink"
                currentPageReportTemplate="{first} to {last} of {totalRecords}"
                rowsPerPageOptions={[5, 10, 25, 50]}
                tableStyle={{ minWidth: "50rem" }}
              >
                <Column expander className="w-[1rem]" />
                <Column
                  field="data.message"
                  header={t("comments.message")}
                  body={(rowData: TreeNode) => {
                    const isChild = !rowData.children;
                    const hasPendingChild =
                      rowData.children &&
                      rowData.children.some(
                        (child) => child.data.status === "Pending",
                      );
                    return (
                      <div
                        className={`whitespace-pre-wrap ${
                          isChild ? "pl-6" : "pl-3"
                        }`}
                      >
                        {rowData.data.message}
                        {!isChild && (hasPendingChild ?? false) && (
                          <span className="text-orange-500 text-xs font-medium px-2 py-1 bg-orange-100 rounded">
                            {t("comments.pendingReply")}
                          </span>
                        )}
                      </div>
                    );
                  }}
                  className="custom-tree-table-column w-[250px]"
                />
                <Column
                  field="data.message_type"
                  header={t("comments.messageType")}
                  body={(rowData: TreeNode) => (
                    <div className="px-3 whitespace-pre-wrap">
                      {rowData.data.message_type}
                    </div>
                  )}
                  className="w-[150px]"
                />
                <Column
                  field="data.subject"
                  header={t("comments.subject")}
                  body={(rowData: TreeNode) => (
                    <div className="px-3 whitespace-pre-wrap">
                      {rowData.data.subject}
                    </div>
                  )}
                  className="w-[150px]"
                />
                <Column
                  field="data.user_name"
                  header={t("comments.userName")}
                  body={(rowData: TreeNode) => (
                    <div className="px-3 whitespace-pre-wrap">
                      {rowData.data.user_name}
                    </div>
                  )}
                  className="w-[150px]"
                />
                <Column
                  field="data.profile"
                  header={t("comments.profile")}
                  body={(rowData: TreeNode) => (
                    <div className="flex items-center justify-center h-full">
                      <Image
                        src={rowData.data.profile ?? "/assets/user.png"}
                        alt="Profile"
                        width={45}
                        height={45}
                        className="rounded-full border-2 border-black"
                        style={{
                          objectFit: "cover",
                          width: "45px",
                          height: "45px",
                        }}
                      />
                    </div>
                  )}
                  className="w-[150px] text-center"
                />

                <Column
                  field="data.created_at"
                  header={t("comments.createdDate")}
                  body={(rowData: TreeNode) => {
                    if (rowData.data.created_at === "Draft") {
                      return <div></div>;
                    }
                    const formattedDate = moment(
                      rowData.data.created_at,
                    ).format("DD-MMM-YYYY");
                    return (
                      <div className="px-3 whitespace-pre-wrap">
                        {formattedDate}
                      </div>
                    );
                  }}
                  className="w-[150px]"
                />
                <Column
                  field="data.resource_name"
                  header={t("comments.resourceName")}
                  body={(rowData: TreeNode) => (
                    <div className="px-3 whitespace-pre-wrap">
                      {rowData.data.resource_name}
                    </div>
                  )}
                  className="w-[300px]"
                />
                <Column
                  field="data.status"
                  header={t("comments.status")}
                  body={(rowData: TreeNode) => (
                    <div className="px-3 whitespace-pre-wrap">
                      {rowData.data.status}
                    </div>
                  )}
                  className="w-[150px]"
                />
                <Column
                  header={<div className="text-center">{t("comments.approveReject")}</div>}
                  body={(rowData: TreeNode) => {
                    const { status, role_name, fullComment } = rowData.data;
                    const isApproved = status === "Approved";
                    const isRejected = status === "Rejected";

                    // Don't show anything if user is Admin
                    if (role_name === "Admin") {
                      return null;
                    }

                    return (
                      <div className="flex justify-center gap-2">
                        {status === "Pending" ? (
                          <MessageSquarePlus
                            size={20}
                            color="#fb8500"
                            onClick={() =>
                              commentApproveReject(
                                fullComment as CommentResponse,
                              )
                            }
                            className="cursor-pointer"
                          />
                        ) : isApproved ? (
                          <CheckCircle
                            size={20}
                            color="#15cce5"
                            onClick={() =>
                              commentApproveReject(
                                fullComment as CommentResponse,
                              )
                            }
                            className="cursor-pointer"
                          />
                        ) : isRejected ? (
                          <Ban
                            size={20}
                            color="#ff0000"
                            onClick={() =>
                              commentApproveReject(
                                fullComment as CommentResponse,
                              )
                            }
                            className="cursor-pointer"
                          />
                        ) : null}
                      </div>
                    );
                  }}
                  className="text-center"
                  style={{ width: "150px" }}
                />

                <Column
                  header={<div className="text-center">{t("comments.reply")}</div>}
                  body={(rowData: TreeNode) => {
                    return (
                      <div className="flex justify-center gap-2">
                        {rowData.data.status === "Approved" && (
                          <Reply
                            size={20}
                            color="#9bbb5c"
                            onClick={() =>
                              replyMessage(
                                rowData.data.fullComment as CommentResponse,
                              )
                            }
                            className="cursor-pointer"
                          />
                        )}
                      </div>
                    );
                  }}
                  className="text-center"
                  style={{ width: "150px" }}
                />
              </TreeTable>
            </div>
          )}
        </>
        {isOpenDialog && (
          <Modal
            title={t("comments.approveRejectComments")}
            header=""
            openDialog={isOpenDialog}
            closeDialog={closeCommentDialog}
            type="max-w-2xl"
          >
            <ApproveRejectComments
              onSave={onSubmit}
              commentId={commentId}
              commentStatus={commentStatus}
              commentString={commentString}
              closeDialog={closeCommentDialog}
            />
          </Modal>
        )}
        {isOpenReplyDialog && (
          <Modal
            title={t("comments.replyMessage")}
            header=""
            openDialog={isOpenReplyDialog}
            closeDialog={closeReplyDialog}
            type="max-w-2xl"
          >
            <ReplyMessage
              onCancel={closeReplyDialog}
              commentData={dataForReply as CommentResponse}
            />
          </Modal>
        )}
      </div>
    </MainLayout>
  );
}
