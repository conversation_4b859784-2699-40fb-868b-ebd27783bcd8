export const ERROR_MESSAGES = {
  add_subscription_msg: "Failed to add subscription plan",
  add_subscription_title: "Failed to add",
  update_subscription_msg: "Failed to update subscription plan",
  update_subscription_title: "Failed to update",
  update_course_to_plan_title: "Failed to update",
  update_resource_to_plan_title: "Failed to update",
  delete_subscription_plan_message: "Failed to delete",
  approve_subscription_plan_message: "Failed to publish",
  delete_course_plan_message: "Failed to delete",
  error_fetching_data: "Error fetching data",
  toast_error_title: "Error",
  toast_variant_destructive: "destructive",
  check_point_sequence: "Check point sequence must be unique",
  check_point_name: "Check point name must be unique",
  check_point_time: "Check point start time must be unique",
  check_point_resource: "Check point resource must be unique",
  check_point_length: "Check point start time exceeds video length",
  toast_validation_error: "Validation error",
  select_role_msg: "Please select a role",
  select_user_msg: "Please select a Reporting to",
  user_not_found_msg: "User not found in the response",
  mandatory_error: "You can only set Man<PERSON>tory to true once",
  something_went_wrong: "Something went wrong",
  user_not_select_msg: "Please select at least one user",
  edit_not_allowed: "Edit not allowed for this item",
  delete_not_allowed: "Delete not allowed for this item",
  publish_not_allowed: "Publish not allowed for this item",
  course_date:
    "Course end date should be greater course start date and the difference should be at least 24 hours",
  course_not_select_choice:
    "Please confirm your choice by selecting 'Yes' or 'No'",
  comment_status_not_selected:
    "Please confirm your choice by selecting 'Approve' or 'Reject'",
  custom_branding_date_error:
    "Valid From date should be less than Valid To date",
  subscription_add_date:
    "Invalid validity date:Start date must be before end date, and the difference should be at least 24 hours",
  subscription_update_date:
    "Invalid date range. Start date must be before end date, and the difference should be at least 24 hours",
  invalid_date_msg: "Invalid date range",
  invalid_validity:
    "Invalid validity period: Valid from of Subscription plan should not be greater than Valid to",
  invalid_valid_from:
    "Valid from of Subscription should not be greater than Valid to.",
  select_resource: "Please select only one resource for import",
  not_select_resource: "Please select resource",
  valid_name: "Please enter a valid name",
  valid_description: "Please enter a valid description",
  valid_url: "Please enter a valid URL",
  send_notification: "Failed to send notification",
  send_email: "Failed to send email",
  invite_user: "Failed to invite user",
  select_valid_image: "Please select a valid image file (PNG or JPG)",
  file_size_exceed: "File size exceeds limit.",
  select_folder: "Please select a folder before proceeding",
  select_one_folder: "Please select at least one resource.",
  order_exist: "The following order values already exist",
  extend_validity: "Failed to update plan validity",
  link_resource_to_course: "Failed to link resource to course",
  link_exam_to_course: "Failed to link exam to course",

  duplicate_resource_alert:
    "Duplicate order values detected. Please choose unique order values for each item",
  order_mandatory_alert: "Please select an order for all selected resources",
  order_already_exist_alert: "The following order values already exist",
  custom_branding_msg: "Failed to save custom branding",
  publish_meeting_details: "Failed to publish meeting details",
  // publish_meeting_details: "Failed to publish meeting details",
  missingUrl: "Please provide either a Meeting ID or Meeting URL",
  course_request: "Failed to approve course request",
  select_request: "Please select at least one request",
};
export const SUCCESS_MESSAGES = {
  add_subscription_msg: "Successfully updated subscription plan",
  add_subscription_title: "Subscription added",
  update_subscription_msg: "Successfully updated subscription plan",
  update_subscription_title: "Subscription updated",
  update_course_to_plan_msg: "Successfully updated course to plan",
  update_course_to_plan_title: "Successfully updated",
  update_resource_to_plan_msg: "Successfully updated resource to plan",
  update_resource_to_plan_title: "Successfully updated",
  subscription_approved_msg: "Subscription approved",
  toast_variant_default: "default",
  pending_subscription: "Pending subscription",
  exam_trial_run: "Exam test Trial completed",
  toast_success_title: "Success",
  update_user_msg: "User updated successfully",
  api_status_success: "success",
  course_duplicate_title: "Course Duplicate",
  course_duplicate_msg: "Course Duplicated Successfully",
  course_add_msg: "Course add succcessfully",
  course_add_title: "Course Add",
  course_update_msg: "Course Updated Successfully",
  comment_approve_msg: "Comment approved successfully",
  comment_reject_msg: "Comment rejected successfully",
  category_update_title: "Category Updated",
  category_add_title: "Category Added",
  category_update_msg: "Category Updated Successfully",
  category_add_msg: "Category Added Successfully",
  category_cancelled_msg: "Action cancelled",
  course_update_title: "Course Updated",
  import_resource: "Resource imported successfully",
  deleteResourceNotification: "Do you want to delete the resource?",
  deleteEventMsg: "Do you want to delete the Event",
  deleteResourceMsg: "Resource deleted successfully",
  deleteCategoryMsg: "Category deleted successfully",
  publishCategoryMsg: "Category published successfully",
  draftCategoryMsg: "Category moved to Draft",
  publishExamMsg: "Exam published successfully",
  userRemovedTitleMsg: "User Removed Successfully",
  deleteResourceTitle: "Resource Deleted",
  deleteCategoryTitle: "Category Deleted",
  publishCategoryTitle: "Category Published",
  categoryStatus: "Category Status",
  draftCategoryTitle: "Category Status",
  pulishExamTitle: "Exam Status",
  deaftExamTitle: "Exam Status",
  userRemovedTitle: "User Removed",
  approvResourceMsg: "Resource approved successfully",
  draftResourceMsg: "Resource moved to draft",
  approvResourceTitle: "Resource approved",
  draftResourceTitle: "Resource Drafted",
  approvalNotification: "Do you want to approve the resource?",
  draftNotification: "Do you want to draft the resource?",
  addPageMsg: "Page content added successfully",
  addPageTitle: "Page content added",
  editPageMsg: "Page content updated successfully",
  editPageTitle: "Page content updated",
  toast_variant_success: "success",
  resource_add_success: "Added resource successfully!",
  resource_update_success: "Resource updated successfully",
  add_folder_msg: "Folder added successfully",
  add_folder_title: "Folder added",
  add_question: "Question added successfully",
  edit_question: "Question updated successfully",
  quiz_delete_msg: "Question deleted from the quiz successfully",
  quiz_delete_title: "Question deleted",
  pulishExamMsg: "Exam published",
  deaftExamMsg: "Exam moved to Draft",
  add_user_to_plan_msg: "User Added successfully",
  add_user_to_plan_title: "User Added",
  send_notification: "Notification sent successfully",
  send_email: "Email sent successfully",
  invite_user: "User invited successfully",
  delete_section: "Section deleted successfully",
  delete_section_title: "Section deleted ",
  import_folder: "Folder imported successfully",
  import_folder_title: "Folder imported",
  extend_validity: "Plan validity updated successfully",
  link_resource_to_course: "Resource linked to course successfully",
  custom_branding_msg: "Custom branding saved successfully",
  publish_meeting_details: "Meeting details published successfully",
  exam_linked_to_course: "Exam linked to course successfully",
  assign_organization: "User assigned to organization successfully",
  folderNameUpdate: "Folder name updated successfully",
  order_resource: "Resource order updated successfully",
  order_folder: "Folder order updated successfully",
  order_section: "Section order updated successfully",
  course_request: "Course request approved successfully",
};
