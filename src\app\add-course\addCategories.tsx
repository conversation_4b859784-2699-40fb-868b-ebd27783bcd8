"use client";
import React, { useState, useEffect } from "react";
import { Combobox } from "@/components/ui/combobox";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import useTopic from "@/hooks/useTopics";
import useCourse from "../../hooks/useCourse";
import TreeSelectComponent from "@/components/ui/tree-select/tree-select";
import type { TreeDataItem } from "@/components/ui/tree";
import type {
  TopicDataType,
  ErrorCatch,
  ToastType,
  AddCategoryInCourse,
} from "@/types";
import { useToast } from "@/components/ui/use-toast";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { AddCategoryInCourseSchema } from "../../schema/schema";
import { pageUrl, categoryType } from "@/lib/constants";
import { useRouter } from "next/navigation";
import { Checkbox } from "@/components/ui/checkbox";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import { t } from "i18next";

export default function AddCategories({
  onCancel,
  onSave,
}: {
  onCancel: () => void;
  onSave: () => void;
}): React.JSX.Element {
  const form = useForm<AddCategoryInCourse>({
    resolver: zodResolver(AddCategoryInCourseSchema),
  });
  const router = useRouter();
  const { handleSubmit } = form;
  const { toast } = useToast() as ToastType;
  const { addCategory, getCategoryHierarchy } = useTopic();
  const { convertDataToTreeNode } = useCourse();
  const [nodeData, setNodeData] = useState<TreeDataItem[]>([]);
  const [selectedTopic, setSelectedTopic] = useState<string | null>();
  const orgId = localStorage.getItem("orgId") as string;

  useEffect(() => {
    topicList();
  }, []);

  const topicList = (): void => {
    const fetchData = async (): Promise<void> => {
      try {
        const params = {
          org_id: orgId,
          filter_data: 0,
        };
        const topics = await getCategoryHierarchy(params);
        if (topics.length > 0) {
          const treeData: TreeDataItem[] = convertDataToTreeNode(
            topics as TopicDataType[],
          );
          setNodeData(treeData);
        }
      } catch (error: unknown) {
        console.error("Error fetching data:");
      }
    };
    fetchData().catch((error) => console.log(error));
  };

  async function onSubmit(): Promise<void> {
    const formData = form.getValues();
    const data = {
      org_id: orgId ?? "",
      category_data: {
        name: formData.name.trimStart(),
        description: formData.description.trimStart(),
        parent_id: selectedTopic ?? null,
        is_premium: formData.is_premium ?? false,
      },
    };
    const params = {
      org_id: orgId,
      filter_data: 0,
    };
    try {
      const result = await addCategory(data);

      if (result.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.category_add_title"),
          description: t("successMessages.addNewCategory"),
        });
        await getCategoryHierarchy(params);
        router.push(pageUrl.addCourse);
        onCancel();
        onSave();
      } else if (result.status === "error") {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: result.status,
        });
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
      console.error("An unexpected error occurred:", error);
    }
  }

  return (
    <div className="w-full">
      <Form {...form}>
        <form
          onSubmit={(event) => void handleSubmit(onSubmit)(event)}
          className="space-y-8 flex flex-wrap"
        >
          <div className="w-full">
            <FormField
              name="category_type"
              control={form.control}
              render={() => (
                <FormItem>
                  <>
                    <FormLabel>
                     {t("courses.courseModule.selectCategoryType")}
                      <span className="text-red-700">*</span>
                    </FormLabel>
                  </>
                  <>
                    <FormControl>
                      <Combobox
                        data={categoryType}
                        onSelectChange={(value) => {
                          form.setValue("category_type", value);
                        }}
                      />
                    </FormControl>
                  </>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <div className="w-full">
            <FormField
              name="topic"
              control={form.control}
              render={() => (
                <FormItem>
                  <FormLabel>
                  {t("courses.courseModule.selectTopicCategory")}
                    <span className="text-red-700">*</span>
                  </FormLabel>
                  <FormControl>
                    <TreeSelectComponent
                      nodeData={nodeData}
                      selectLabel= {t("courses.courseModule.selectTopic")}
                      onNodeSelect={(selectedValue) => {
                        setSelectedTopic(selectedValue);
                        form.setValue("topic", selectedValue as string);
                      }}
                    ></TreeSelectComponent>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <div className="w-full">
            <FormField
              name="name"
              control={form.control}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                  {t("courses.courseModule.categoryName")} <span className="text-red-700">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      autoComplete="off"
                      {...field}
                      maxLength={30}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <div className="w-full">
            <FormField
              name="description"
              control={form.control}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                  {t("courses.description")}  <span className="text-red-700">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      autoComplete="off"
                      {...field}
                      maxLength={50}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <>
            <FormField
              name="is_premium"
              control={form.control}
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center">
                    <FormControl>
                      <>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={(value) => {
                            field.onChange(value);
                          }}
                        />
                        {/* Added padding to this FormLabel */}
                        <FormLabel className="px-4">{t("courses.courseModule.isPremium")}</FormLabel>
                      </>
                    </FormControl>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
          </>
          <div className="w-full flex justify-end mt-8">
            <div className="w-full flex justify-end gap-4">
              <Button className="w-auto bg-[#33363F]" onClick={onCancel}>
                
              </Button>
              <Button className="w-auto bg-[#9FC089]">{t("buttons.submit")}</Button>
            </div>
          </div>
        </form>
      </Form>
    </div>
  );
}
