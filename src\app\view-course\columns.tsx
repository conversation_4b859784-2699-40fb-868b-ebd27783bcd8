"use client";

import type { ColumnDef, Row } from "@tanstack/react-table";
import moment from "moment";
import React from "react";
import type { Enrollment } from "@/types";
interface RowDefinition {
  row: Row<Enrollment>;
}

export const getParticipantColumns = (
  t: (key: string) => string
): ColumnDef<Enrollment>[] => [
  {
    accessorKey: "first_name",
    header: t("courses.firstName"),
    cell: ({ row }: RowDefinition): React.JSX.Element => {
      const firstName = row.original.first_name ?? " ";
      return <div className="text-align">{firstName}</div>;
    },
  },
  {
    accessorKey: "last_name",
    header: t("courses.lastName"),
    cell: ({ row }: RowDefinition): React.JSX.Element => {
      const lastName = row.original.last_name ?? " ";
      return <div className="text-align">{lastName}</div>;
    },
  },
  {
    accessorKey: "enrolled_date",
    header: t("courses.enrolledDate"),
    cell: ({ row }: RowDefinition): React.JSX.Element => {
      const formattedDate = moment
        .utc(row.original.enrolled_date)
        .local()
        .format("DD-MMM-YYYY hh:mm a");
      return <div>{formattedDate}</div>;
    },
  },
];
