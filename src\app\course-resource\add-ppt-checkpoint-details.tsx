"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import React, { useEffect, useState } from "react";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "../../components/ui/input";
import { milestoneType } from "@/lib/constants";
import { useForm } from "react-hook-form";
import { Combobox } from "../../components/ui/combobox";
import useCourse from "@/hooks/useCourse";
import type {
  ComboData,
  PPTCheckPointForm,
  PPTCheckPointList,
  ToastType,
} from "@/types";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { PPTCheckPointFormShema } from "@/schema/schema";
import { zodResolver } from "@hookform/resolvers/zod";
import { useToast } from "@/components/ui/use-toast";
import { ERROR_MESSAGES } from "@/lib/messages";
import { useTranslation } from "react-i18next";

interface CheckPointProps {
  closeMilestoneDialog: (value: boolean) => void;
  checkpointNumber: number | undefined;
  randomEnabledStatus: boolean | false;
  checkpointTime: number;
  onAddedValuesChange: (values: PPTCheckPointForm[]) => void;
  allCheckPointData: PPTCheckPointForm[];
  videoLength: string | undefined;
  existingCheckPoints: PPTCheckPointForm[];
}

export const PPTCheckPointDetails: React.FC<CheckPointProps> = ({
  closeMilestoneDialog,
  // checkpointNumber,
  checkpointTime,
  onAddedValuesChange,
  allCheckPointData,
  videoLength,
  randomEnabledStatus,
  existingCheckPoints,
}): React.JSX.Element => {
  const { getCheckPointQuizList } = useCourse();
  const [comboData, setComboData] = useState<ComboData[]>([]);
  const [selResourceId, setSelResourceId] = useState("");
  const [addedValues, setAddedValues] = useState<PPTCheckPointForm[]>([]);
  const [isMandatoryType, setIsMandatoryType] = useState<boolean>(false);
  const [sequenceNo, setSequenceNo] = useState<ComboData[]>([]);
  // const [selSequenceNo, setSelSequenceNo] = useState("");
  const { t } = useTranslation();
  const { toast } = useToast() as ToastType;
  const form = useForm<PPTCheckPointList>({
    resolver: zodResolver(PPTCheckPointFormShema),
  });
  console.log(checkpointTime);
  console.log(videoLength);
  const checkpoint_no = localStorage.getItem("checkpointNoPPT");

  // const searchParams = useSearchParams();
  // const router = useRouter();
  // const type = searchParams.get("sectionId");
  const handleSequenceValueChange = (selectedValue: string): void => {
    // setSelSequenceNo(selectedValue);
    form.setValue("sequence_number", selectedValue);
    // if (randomEnabledStatus === true) {
    //   const checkpointData = allCheckPointData.concat(existingCheckPoints);
    // }
  };

  async function onSubmit(): Promise<void> {
    const checkpointData = allCheckPointData.concat(existingCheckPoints);
    const checkpointNum = parseInt(checkpoint_no ?? "0");
    if (checkpointData.length < checkpointNum) {
      const formData = form.getValues();

      const isUniqueName = checkpointData.every(
        (item) => item.checkpoint_name !== formData.checkpoint_name,
      );

      const isUniqueResource = checkpointData.every(
        (item) => item.checkpoint_resid !== selResourceId,
      );

      const hasMandatoryTrue = checkpointData.some(
        (item) => item.isMandatory === "true",
      );
      if (isMandatoryType) {
        if (hasMandatoryTrue) {
          formData.isMandatory = "false";
        } else {
          formData.isMandatory = "true";
        }
      } else {
        formData.isMandatory = "false";
      }

      if (randomEnabledStatus === true) {
        if (isUniqueName && isUniqueResource) {
          if (formData.checkpoint_resid === "") {
            form.setValue("checkpoint_resid", selResourceId);
          }
          const updatedValues = [...addedValues, formData];
          await new Promise<void>((resolve) => {
            setAddedValues(updatedValues);
            resolve();
          });
          console.log("updated", addedValues);
          closeMilestoneDialog(true);
        } else {
          if (!isUniqueName) {
            toast({
              variant: ERROR_MESSAGES.toast_variant_destructive,
              title: t("errorMessages.toast_error_title"),
              description: t("errorMessages.check_point_name"),
            });
          }
          if (!isUniqueResource) {
            toast({
              variant: ERROR_MESSAGES.toast_variant_destructive,
              title: t("errorMessages.toast_error_title"),
              description: t("errorMessages.check_point_resource"),
            });
          }
          closeMilestoneDialog(true);
        }
      } else {
        // const isUniqueSequence = checkpointData.every(
        //   (item) => item.sequence_number !== formData.sequence_number,
        // );

        // if (isUniqueSequence && isUniqueName && isUniqueResource) {
        if (formData.checkpoint_resid === "") {
          form.setValue("checkpoint_resid", selResourceId);
        }
        const updatedValues = [...addedValues, formData];
        await new Promise<void>((resolve) => {
          setAddedValues(updatedValues);
          resolve();
        });
        closeMilestoneDialog(true);
        // }
      }
    }
  }

  const handleComboValueChange = (selectedValue: string): void => {
    setSelResourceId(selectedValue);
    form.setValue("checkpoint_resid", selectedValue);
    form.setValue("checkpoint_type", "Exam");
  };

  const generateSequenceNumber = (): void => {
    console.log("checkpoint_no", checkpoint_no);

    const maxSequenceNo = parseInt(checkpoint_no ?? "0");
    console.log("sequence", maxSequenceNo);
    const generatedSequenceNo = [];
    for (let i = 1; i <= maxSequenceNo; i++) {
      const sequenceNoData: ComboData = {
        value: i.toString(),
        label: i.toString(),
      };
      generatedSequenceNo.push(sequenceNoData);
      setSequenceNo(generatedSequenceNo);
    }
  };

  useEffect(() => {
    onAddedValuesChange(addedValues);
    generateSequenceNumber();
    console.log("addedata", addedValues);
  }, [addedValues]);

  const CheckPointQuizList = (): void => {
    const fetchData = async (): Promise<void> => {
      try {
        const cpList = await getCheckPointQuizList();
        const comboData: ComboData[] = cpList.map((item) => ({
          value: item.id,
          label: item.name,
        }));

        setComboData(comboData);
        // console.log(category);
      } catch (error) {
        console.error("Error fetching data:");
      }
    };
    fetchData().catch((error) => console.log(error));
  };
  useEffect(() => {
    CheckPointQuizList();
  }, []);

  const isMandatoryenabled = (isMandatoryType: string): void => {
    {
      isMandatoryType === "true"
        ? setIsMandatoryType(true)
        : setIsMandatoryType(false);
    }
  };

  return (
    <div className="p-4 mt-4">
      <Form {...form}>
        <form
          onSubmit={(event) => void form.handleSubmit(onSubmit)(event)}
          className="space-y-8"
        >
          <div className="w-full flex flex-wrap">
            <div className="w-full sm:w-1/3 pr-4">
              <FormField
                control={form.control}
                name="sequence_number"
                render={() => (
                  <FormItem>
                    {" "}
                    <FormLabel>
                      {t("courses.courseModule.sequenceNumber")}
                      <span className="text-red-700">*</span>
                    </FormLabel>
                    <FormControl>
                      {/* <Input {...field} /> */}
                      <Combobox
                        data={sequenceNo}
                        onSelectChange={handleSequenceValueChange}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="w-full sm:w-1/3 pr-4">
              <FormField
                control={form.control}
                name="checkpoint_name"
                render={({ field }) => (
                  <FormItem>
                    {" "}
                    <FormLabel>
                      {" "}
                      {t("courses.courseModule.checkpointName")}
                      <span className="text-red-700">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        autoComplete="off"
                        {...field}
                        onChange={(event) => {
                          field.onChange(event.target.value.trimStart());
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="w-full sm:w-1/3">
              <FormField
                control={form.control}
                name="checkpoint_slide"
                render={() => (
                  <FormControl>
                    <FormItem>
                      <FormLabel>
                        {t("courses.courseModule.checkpointSlide")}{" "}
                        <span className="text-red-700">*</span>
                      </FormLabel>
                      <FormControl>
                        <div className="flex space-x-2">
                          <Input
                            autoComplete="off"
                            type="text"
                            placeholder=""
                            {...form.register("checkpoint_slide")}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  </FormControl>
                )}
              />
            </div>
          </div>
          <div className="w-full flex flex-wrap">
            <div className="w-full sm:w-1/3 pr-4">
              <FormField
                control={form.control}
                name="checkpoint_type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {t("courses.courseModule.checkpointType")}{" "}
                      <span className="text-red-700">*</span>
                    </FormLabel>

                    <Select
                      name="courseVisibility"
                      onValueChange={field.onChange}
                      defaultValue={"exam"}
                      disabled
                    >
                      {" "}
                      <FormControl>
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Select" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {milestoneType.map((item, index) => (
                          <SelectItem key={index} value={item.value.toString()}>
                            {item.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>

                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="w-full sm:w-1/3 pr-4">
              <FormField
                control={form.control}
                name="checkpoint_resid"
                render={() => (
                  <FormItem>
                    <FormLabel>
                      {t("courses.courseModule.checkpointResourceId")}
                      <span className="text-red-700">*</span>
                    </FormLabel>
                    <FormControl>
                      <div className="overflow-x-auto">
                        <Combobox
                          data={comboData}
                          onSelectChange={handleComboValueChange}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="w-full sm:w-1/3 pr-4">
              <FormField
                control={form.control}
                name="isMandatory"
                render={() => (
                  <FormItem className="space-y-3">
                    <FormLabel>
                      {t("courses.courseModule.isMandatory")}{" "}
                      <span className="text-red-700">*</span>
                    </FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={(value) => {
                          const checkpointData =
                            allCheckPointData.concat(existingCheckPoints);
                          const hasMandatoryTrue = checkpointData.some(
                            (item) => item.isMandatory === "true",
                          );

                          if (hasMandatoryTrue && value === "true") {
                            toast({
                              variant: ERROR_MESSAGES.toast_variant_destructive,
                              title: t("errorMessages.toast_error_title"),
                              description: t("ERROR_MESSAGES.mandatory_error"),
                            });

                            form.setValue("isMandatory", "false");
                          } else {
                            form.setValue("isMandatory", value);
                            isMandatoryenabled(value);
                          }
                        }}
                        defaultValue="false"
                        className="flex flex-row space-x-3"
                      >
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="true" />
                          </FormControl>
                          <FormLabel className="font-normal">Yes</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="false" />
                          </FormControl>
                          <FormLabel className="font-normal">No</FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="w-full flex ">
              <div className="flex justify-end w-full">
                {" "}
                <Button type="submit">{t("buttons.addToGrid")}</Button>
              </div>
            </div>
          </div>
        </form>
      </Form>
    </div>
  );
};
