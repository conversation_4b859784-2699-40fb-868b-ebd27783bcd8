import type { ChangeEvent } from "react";
import React, { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { supabase } from "@/lib/client";
import type { publicURL } from "@/types";
import { Spinner } from "@/components/ui/progressiveLoader";
import { useTranslation } from "react-i18next";

export default function UploadResources({
  onSave,
  onCancel,
  resource,
}: {
  onSave: (data: publicURL) => void;
  onCancel: () => void;
  isModal?: boolean;
  resource: string;
}): React.JSX.Element {
  const { t } = useTranslation();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [resourceType, setresourceType] = useState<string>("*/*");
  const [uploading, setUploading] = useState<boolean>(false);

  useEffect(() => {
    switch (resource) {
      case "File":
        setresourceType("*/*");
        break;
      case "Page":
        setresourceType(".page");
        break;
      case "Video":
        setresourceType("video/*");
        break;
    }
  }, [resourceType]);
  const handleFileChange = (event: ChangeEvent<HTMLInputElement>): void => {
    const file = event.target.files ? event.target.files[0] : null;
    console.log(file);
    setSelectedFile(file as File);
  };

  const handleUpload = async (): Promise<void> => {
    if (selectedFile) {
      setUploading(true);
      const orgId = localStorage.getItem("orgId") as string;
      const { data, error } = await supabase.storage
        .from(`${orgId}`)
        .upload(`resources/${selectedFile.name}`, selectedFile, {
          cacheControl: "3600",
          upsert: true,
        });
      console.log("File uploaded:", data);
      setUploading(false);
      if (error) {
        console.error("Error uploading file:", error);
        return;
      }
      const publicURL = supabase.storage.from(orgId).getPublicUrl(data.path);
      onSave(publicURL);
    }

    onCancel();
  };
  // Intermediate function to handle the event
  const handleUploadClick = (): void => {
    handleUpload().catch((error) => {
      console.error("Error in handleUpload:", error);
    });
  };

  return (
    <div className="mb-2 mr-4">
      <div>
        <div>
          <Input
            type="file"
            accept={resourceType}
            onChange={handleFileChange}
          />
        </div>
        <div className="flex topic-icons pr-4 mt-4">
          <div className="flex items-center justify-center float-right">
            <Button
              type="button"
              className="primary"
              variant="outline"
              onClick={onCancel}
            >
              {t("buttons.cancel")}
            </Button>
            &nbsp;
            <Button
              type="submit"
              className="primary"
              onClick={handleUploadClick}
              disabled={!selectedFile}
            >
              {t("resourceLibrary.upload")}
            </Button>
            {uploading && (
              <div className="primary pl-3">
                <Spinner />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
