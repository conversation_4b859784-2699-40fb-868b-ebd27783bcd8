"use client";

import type { ColumnDef } from "@tanstack/react-table";
import { ArrowUpDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import type {
  CourseGroup,
  CourseGroupRowDefinition,
  CourseGroupColumnDefinition,
} from "@/types";
export const getColumns = (
  t: (key: string) => string
): ColumnDef<CourseGroup>[] => [
  {
    accessorKey: "short_name",
    header: ({ column }: CourseGroupColumnDefinition): React.JSX.Element => {
      return (
        <Button
          className="px-0"
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          {t("groups.courseGroup.columns.course")}
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }: CourseGroupRowDefinition): React.JSX.Element => (
      <div className="text-align">{row.original?.short_name}</div>
    ),
  },
];
