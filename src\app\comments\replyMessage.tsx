"use client";
import { Button } from "@/components/ui/button";
import React, { useState } from "react";
import { Label } from "@/components/ui/label";
import useComments from "@/hooks/useComments";
import type {
  AddCommentRequest,
  CommentResponse,
  LoginUserData,
  LogUserActivityRequest,
  ToastType,
} from "@/types";
import { useToast } from "@/components/ui/use-toast";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { Textarea } from "@/components/ui/textarea";
import { useTranslation } from "react-i18next";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";

export default function ReplyMessage({
  onCancel,
  commentData,
}: {
  onCancel: () => void;
  commentData: CommentResponse;
}): React.JSX.Element {
  const { t } = useTranslation();
  const [feedback, setFeedback] = useState("");
  const { addComments } = useComments();
  const { toast } = useToast() as ToastType;
  const { updateUserActivity } = useLogUserActivity();
  const handleSubmit = async (): Promise<void> => {
    let userId: string | undefined;

    const userDetails = localStorage.getItem("userDetails");
    if (userDetails != null && userDetails != undefined) {
      const users = JSON.parse(userDetails) as LoginUserData;
      userId = users?.id;
    }

    const reqParams: AddCommentRequest = {
      comment_data: {
        subject: "",
        message: feedback,
        type: commentData.type,
        parent_id: commentData.id,
        activity_type: "comment",
      },
      instance_id: commentData.instance as string,
      user_id: userId as string,
    };

    try {
      const result = await addComments(reqParams);
      if (result.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.toast_success_title"),
          description: t("successMessages.commentAdded"),
        });
        const params = {
          activity_type: "Comment",
          screen_name: "Replay Comment ",
          action_details: "Replied to comment ",
          target_id: commentData.id,
          log_result: "SUCCESS",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
        onCancel();
      }
    } catch (error) {
      console.log(error);
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.failToAddComments"),
      });
      const params = {
        activity_type: "Comment",
        screen_name: "Replay Comment ",
        action_details: "Failed to reply to comment ",
        target_id: commentData.id,
        log_result: "ERROR",
      };
      void updateLogUserActivity(params).catch((error) => {
        console.error(error);
      });
      onCancel();
    }
  };
  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  const handleDescriptionChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>,
  ): void => {
    let value = e.target.value;
    if (value.startsWith(" ")) {
      value = "";
    }
    const sanitizedValue = value
      .trimStart()
      .replace(/^\p{P}+/u, "")
      .replace(/[^\p{L}\p{M}\p{N}\s\-!@#$%^&*()_+={}[\]:;"'<>,.?/\\|~`]/gu, "")
      .replace(/\s{2,}/g, " ")
      .replace(
        /^[^\p{L}\p{M}\p{N}]|[^\p{L}\p{M}\p{N}\s\-!@#$%^&*()_+={}[\]:;"'<>,.?/\\|~`]/gu,
        "",
      )
      .replace(/\s+$/, (match) =>
        match.length > 1 ? match.slice(0, -1) : match,
      );
    setFeedback(sanitizedValue);
  };

  return (
    <>
      <div className="w-full">
        <Label>
          {t("comments.replyMessageFor")}
          <span>
            <strong>&ldquo;{commentData.message}&rdquo;</strong>
          </span>
        </Label>
        <div className="mt-2">
          <Textarea
            className="w-full p-2 bg-[#FFFF] rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#9FC089]"
            placeholder={t("comments.typeHere")}
            rows={4}
            onChange={handleDescriptionChange}
            onInput={(e) => {
              const input = e.currentTarget;
              if (input.value.startsWith(" ")) {
                input.value = input.value.trimStart();
              }
            }}
          />
        </div>
      </div>
      <div className="w-full flex justify-end mt-8">
        <div className="w-full flex justify-end gap-4">
          <Button className="w-auto bg-[#33363F]" onClick={onCancel}>
            {t("buttons.cancel")}
          </Button>
          <Button
            className="w-auto bg-[#9FC089]"
            onClick={() => {
              handleSubmit().catch((error) => console.log(error));
            }}
          >
            {t("buttons.submit")}{" "}
          </Button>
        </div>
      </div>
    </>
  );
}
