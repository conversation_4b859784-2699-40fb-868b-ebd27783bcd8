import React, { useEffect, useState } from "react";
import { getColumns } from "./completed_users_columns";
import type { CourseProgressResult, ResourceProgress } from "@/types";
import {} from "../use-toast";
import { DataTable } from "../data-table/data-table";
import { ExpandIcon, Eye, FileDown } from "lucide-react";
import { Modal } from "../modal";
import Certificate from "../drawCertificate";
import ResourceCompletion from "./resourceCompletion";
import * as XLSX from "xlsx";
import { ORG_NAME } from "@/lib/constants";
import moment from "moment";
import { useTranslation } from "react-i18next";
interface CourseProgressedViewsProps {
  completedUsersList: CourseProgressResult[];
}

export function CourseProgressedUsers({
  completedUsersList,
}: CourseProgressedViewsProps): React.JSX.Element {
  const { t } = useTranslation();
  const columns = getColumns(t);
  const [completedUsers, setCompletedUsers] = useState<CourseProgressResult[]>(
    [],
  );

  const [certificateData, setCertificateData] =
    useState<CourseProgressResult>();
  const [openCanvas, setOpenCanvas] = useState(false);
  const [openModal, setOpenModal] = useState(false);
  const [resourceData, setResourceData] = useState<ResourceProgress[]>([]);
  const [exportList, setExportList] = useState<unknown[]>([]);
  useEffect(() => {
    setCompletedUsers(completedUsersList);
    setExportList(completedUsersList);

    const exportData = completedUsersList?.map((item, index) => ({
      SlNo: index + 1,
      Name: item.user_name,
      Email: item.user_email,
      Course: item.course_name,
      Attended_on: moment
        .utc(item.attended_at)
        .local()
        .format("DD-MMM-YYYY hh:mm a"),
      Progress: item.total_progress,
    }));
    setExportList(exportData);
  }, [completedUsersList]);

  const downloadCertificate = async (
    val: CourseProgressResult,
  ): Promise<void> => {
    setCertificateData(val);
    setOpenCanvas(true);
  };

  const viewResourceDetails = async (
    val: CourseProgressResult,
  ): Promise<void> => {
    setResourceData(val.resources);
    setOpenModal(true);
  };
  const cancelModal = (): void => {
    setOpenModal(false);
  };
  const handleDialogClose = (): void => {
    setOpenCanvas(false);
  };
  useEffect(() => {
    completedUsers?.map((item) => {
      if (item.resource_count * 100 === item.total_progress) {
        item.hideIcon = false;
      } else {
        item.hideIcon = true;
      }
    });
  });
  const handleExport = (): void => {
    const orgNameSelected = localStorage.getItem(ORG_NAME)?.toString();

    if (exportList.length === 0) {
      // toast({
      //   variant: "destructive",
      //   title: "Error",
      //   description: "No data available to export!",
      // });

      return;
    }

    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet([], {});

    // Add headers with styling
    XLSX.utils.sheet_add_aoa(
      worksheet,
      [[orgNameSelected], ["Attendee Details List"], []],
      { origin: "A1" },
    );

    // Add the actual data
    XLSX.utils.sheet_add_json(worksheet, exportList, {
      origin: "A4",
      skipHeader: false,
    });

    // Set column widths (measured in characters)
    const columnWidths = [
      { wch: 5 }, // slno column
      { wch: 25 }, // name column
      { wch: 40 }, // email column
      { wch: 20 }, // course column
      { wch: 20 }, // attended_at column
      { wch: 10 }, // column
    ];

    // Apply column widths
    worksheet["!cols"] = columnWidths;

    // Style the headers (merge cells for headers)
    worksheet["!merges"] = [
      { s: { r: 0, c: 0 }, e: { r: 0, c: 5 } }, // Merge first row across all columns
      { s: { r: 1, c: 0 }, e: { r: 1, c: 5 } }, // Merge second row across all columns
    ];

    // Add cell styles for center alignment and bold text

    // Style OrgName header (Row 1)
    worksheet["A1"] = {
      v: orgNameSelected,
      t: "s",
      s: {
        alignment: { horizontal: "center", vertical: "center" },
        font: { bold: true },
      },
    };

    // Style Attendee Details header (Row 2)
    worksheet["A2"] = {
      v: "Attendee Details List",
      t: "s",
      s: {
        alignment: { horizontal: "center", vertical: "center" },
        font: { bold: true },
      },
    };

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, "Course Progress");

    // Export the file
    XLSX.writeFile(workbook, `${orgNameSelected}_Course_Progressed_Users.xlsx`);
  };

  return (
    <div className="">
      <div>
        <div
          className="border rounded-lg shadow-lg bg-white relative overflow-hidden"
          style={{ height: "450px", overflow: "auto" }}
        >
          <div className="p-2 border-b flex justify-between items-center dashboard-session text-black rounded-t-lg">
            <h3 className="text-md font-semibold">
              {String(t("dashboard.courseProgressedUsers"))}
            </h3>
            <div title="Export excel" className="cursor-pointer">
              <FileDown className="h-5 w-5" onClick={handleExport} />
            </div>
          </div>
          <div className="p-2">
            <div className="overflow-x-auto border rounded-md p-2 ">
              <DataTable
                columns={columns}
                data={completedUsers}
                FilterLabel={t("dashboard.filterByName")}
                FilterBy={"user_name"}
                disableIcon={"hideIcon"}
                actions={[
                  {
                    title: t("dashboard.view"),
                    icon: Eye,
                    color: "#10b981 ",
                    varient: "icon",
                    isEnable: true,

                    handleClick: (val: unknown) => {
                      void viewResourceDetails(val as CourseProgressResult);
                    },
                  },
                  {
                    title: t("dashboard.certificate"),
                    icon: ExpandIcon,
                    color: "#10b981 ",
                    varient: "icon",
                    isEnable: true,

                    handleClick: (val: unknown) => {
                      void downloadCertificate(val as CourseProgressResult);
                    },
                  },
                ]}
                onSelectedDataChange={() => {}}
              />
            </div>
          </div>
        </div>
      </div>
      {openCanvas && (
        <Modal
          title=""
          header=""
          openDialog={openCanvas}
          closeDialog={handleDialogClose}
          type="max-w-7xl"
        >
          <Certificate
            certificateData={certificateData as CourseProgressResult}
          />
        </Modal>
      )}
      {openModal && (
        <Modal
          title=""
          header=""
          openDialog={openModal}
          closeDialog={cancelModal}
          type="max-w-4xl"
        >
          <ResourceCompletion resources={resourceData} onCancel={cancelModal} />
        </Modal>
      )}
    </div>
  );
}
