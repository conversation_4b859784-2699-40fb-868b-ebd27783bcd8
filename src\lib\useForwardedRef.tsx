import React from "react";
import type { MutableRefObject } from "react";
export function useForwardedRef<T>(
  ref: React.ForwardedRef<T>,
): MutableRefObject<T | null> {
  const innerRef = React.useRef<T>(null);

  React.useEffect(() => {
    if (!ref) return;
    if (typeof ref === "function") {
      ref(innerRef.current);
    } else {
      ref.current = innerRef.current;
    }
  });

  return innerRef;
}
