"use client";
import React, { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import type {
  AddUserEnrollment,
  ErrorCatch,
  ErrorType,
  insertMessageRequest,
  ToastType,
  UsersNotEnrolled,
} from "@/types";
import { Combobox } from "../combobox";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import useCourse from "@/hooks/useCourse";
import useAddEnrollments from "@/hooks/useAddEnrollments";
import { useRouter } from "next/navigation";
import { pageUrl } from "@/lib/constants";
import { useTranslation } from "react-i18next";
import useEnrollments from "@/hooks/useEnrollment";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";

interface userEnroll {
  onCancel: () => void;
  enrollData: UsersNotEnrolled;
  onUserEnroll: (userId: string) => void;
}
export default function UserEnrollment({
  onCancel,
  enrollData,
  onUserEnroll,
}: userEnroll): React.JSX.Element {
  const { t } = useTranslation();

  const { toast } = useToast() as ToastType;
  const { getCourseListForEnrollments } = useCourse();
  const { addNewEnrollment } = useAddEnrollments();
  const router = useRouter();
  const [courseData, setCourseData] = useState<
    { value?: string; label?: string }[]
  >([]);
  const [selectCourse, setSelectCourse] = useState<string>("");
  const { fetDeviceToken, pushNotification, insertMessage } = useEnrollments();
  useEffect(() => {
    const fetchCourseData = async (): Promise<void> => {
      try {
        const courses = await getCourseListForEnrollments("");
        if (
          courses !== null &&
          courses !== undefined &&
          Object.keys(courses).length > 0
        ) {
          const filteredCourses = courses
            .filter((course) => course.course_id != null && course.full_name)
            .map((course) => ({
              value: course.course_id,
              label: course.full_name,
            }));
          setCourseData(filteredCourses);
        } else {
          setCourseData([]);
        }
      } catch (error) {
        const err = error as ErrorType;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
      }
    };

    fetchCourseData().catch((error) => console.log(error));
  }, []);

  const handleCourseChange = (selectedOption: string): void => {
    setSelectCourse(selectedOption);
  };

  const handleSaveClick = async (): Promise<void> => {
    const orgId = localStorage.getItem("orgId");

    const requestBody = {
      course_id: selectCourse ?? "",
      org_id: orgId ?? "",
      users_ids: [enrollData.id],
    };
    try {
      const result = await addNewEnrollment(requestBody as AddUserEnrollment);
      if (result?.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.toast_success_title"),
          description: t("successMessages.enrollment_added"),
        });

        const userId = enrollData.id;
        const tokenParams = {
          org_id: orgId as string,
          user_id: userId,
        };

        try {
          const msg =
            "You have been enrolled to " +
            courseData.find((course) => course.value === selectCourse)?.label;
          const resp = await fetDeviceToken(tokenParams);
          const token = resp.result[0];
          const reqParams = {
            userid: userId,
            message: msg,
            fcmtoken: token,
          };
          if (resp.result.length > 0) {
            const resp = await pushNotification(reqParams);
            if (resp.message === "Notification sent successfully") {
              void insertMessageToTable(userId, token, msg);
            }
          }
        } catch (error) {
          const err = error as ErrorCatch;
          toast({
            variant: "destructive",
            title: "Error",
            description: err.message,
          });
          console.log(err);
        }

        onUserEnroll(enrollData.id);
        onCancel();
        router.push(`${pageUrl.dashboard}`);
      } else if (result.status === "error") {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: result.status,
        });
        console.log("API Error:", result.status);
      }
    } catch (error) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.select_course_and_user"),
      });
    }
  };
  const insertMessageToTable = async (
    userId: string,
    token: string,
    msg: string,
  ): Promise<void> => {
    const orgId = localStorage.getItem("orgId");
    const reqParams: insertMessageRequest = {
      org_id: orgId as string,
      user_id: userId,
      notification_data: {
        target_id: null,
        device_token_id: token,
        message_text: msg,
      },
    };

    try {
      const resp = await insertMessage(reqParams);
      console.log("Message inserted:", resp);
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: "destructive",
        title: "Error",
        description: err.message,
      });
      console.log(err);
    }
  };
  return (
    <>
      <div>
        <h1 className="text-2xl font-semibold mb-4">{"Enroll User"}</h1>
      </div>
      <div className="overflow-x-auto">
        <div className="text-left">
          <Label> {String(t("dashboard.selectCourse"))}</Label>
          <div className="w-1/2 flex">
            <Combobox
              data={courseData}
              onSelectChange={handleCourseChange}
              placeHolder="Select Course"
            />
          </div>
        </div>
        <div className="flex topic-icons pr-4">
          <div className="flex items-center justify-center float-right gap-3">
            <Button type="button" className="bg-[#33363F]" onClick={onCancel}>
              {String(t("buttons.cancel"))}
            </Button>

            <Button
              type="submit"
              className="bg-[#9FC089]"
              onClick={() => {
                handleSaveClick().catch((error) => console.log(error));
              }}
            >
              {String(t("buttons.submit"))}
            </Button>
          </div>
        </div>
      </div>
    </>
  );
}
