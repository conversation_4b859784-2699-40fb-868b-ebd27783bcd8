"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import React, { useEffect, useState } from "react";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "../../components/ui/input";
import { milestoneType } from "@/lib/constants";
import { useForm } from "react-hook-form";
import { Combobox } from "../../components/ui/combobox";
import useCourse from "@/hooks/useCourse";
import type {
  CheckPointForm,
  CheckPointList,
  ComboData,
  ToastType,
} from "@/types";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { CheckPointFormShema } from "@/schema/schema";
import { zodResolver } from "@hookform/resolvers/zod";
import { useToast } from "@/components/ui/use-toast";
import { ERROR_MESSAGES } from "@/lib/messages";
import { useTranslation } from "react-i18next";
interface CheckPointProps {
  closeMilestoneDialog: (value: boolean) => void;
  checkpointNumber: number;
  randomEnabledStatus: boolean | false;
  checkpointTime: number;
  onAddedValuesChange: (values: CheckPointForm[]) => void;
  allCheckPointData: CheckPointForm[];
  videoLength: string | undefined;
  existingCheckPoints: CheckPointForm[];
}

interface StartTime {
  HH: string;
  MM: string;
  SS: string;
}

export const CheckPointDetails: React.FC<CheckPointProps> = ({
  closeMilestoneDialog,
  checkpointNumber,
  checkpointTime,
  onAddedValuesChange,
  allCheckPointData,
  videoLength,
  randomEnabledStatus,
  existingCheckPoints,
}): React.JSX.Element => {
  const { t } = useTranslation();
  const { getCheckPointQuizList } = useCourse();
  const [comboData, setComboData] = useState<ComboData[]>([]);
  const [selResourceId, setSelResourceId] = useState("");
  const [addedValues, setAddedValues] = useState<CheckPointForm[]>([]);
  const [isMandatoryType, setIsMandatoryType] = useState<boolean>(false);
  const [sequenceNo, setSequenceNo] = useState<ComboData[]>([]);
  // const [selSequenceNo, setSelSequenceNo] = useState("");
  const [isTimeDisabled, setIsTimeDisabled] = useState(false);
  const { toast } = useToast() as ToastType;
  const form = useForm<CheckPointList>({
    resolver: zodResolver(CheckPointFormShema),
  });
  localStorage.setItem("checkpointNo", `${checkpointNumber ?? 0}`);
  const checkpoint_no = localStorage.getItem("checkpointNo");

  // const searchParams = useSearchParams();
  // const router = useRouter();
  // const type = searchParams.get("sectionId");
  const handleSequenceValueChange = (selectedValue: string): void => {
    // setSelSequenceNo(selectedValue);
    form.setValue("sequence_number", selectedValue);
    if (randomEnabledStatus === true) {
      const checkpointData = allCheckPointData.concat(existingCheckPoints);
      const checkpointSequence = checkpointData.find(
        (item) => item.sequence_number === selectedValue,
      );

      if (checkpointSequence) {
        const { HH, MM, SS } = checkpointSequence.checkpoint_startTime;
        form.setValue("checkpoint_startTime.HH", HH);
        form.setValue("checkpoint_startTime.MM", MM);
        form.setValue("checkpoint_startTime.SS", SS);
        setIsTimeDisabled(true);
      } else {
        setIsTimeDisabled(false);
      }
    } else {
      setIsTimeDisabled(false);
    }
  };

  async function onSubmit(): Promise<void> {
    const checkpointData = allCheckPointData.concat(existingCheckPoints);
    const checkpointNum = parseInt(checkpoint_no ?? "0");
    if (checkpointData.length < checkpointNum) {
      const formData = form.getValues();

      const isUniqueName = checkpointData.every(
        (item) => item.checkpoint_name !== formData.checkpoint_name,
      );

      const startTime = formData.checkpoint_startTime;
      const isGreaterLength = compareTimes(startTime, videoLength as string);

      const isUniqueResource = checkpointData.every(
        (item) => item.checkpoint_resid !== selResourceId,
      );

      const hasMandatoryTrue = checkpointData.some(
        (item) => item.isMandatory === "true",
      );

      if (isMandatoryType) {
        if (hasMandatoryTrue) {
          formData.isMandatory = "false";
        } else {
          formData.isMandatory = "true";
        }
      } else {
        formData.isMandatory = "false";
      }

      if (randomEnabledStatus === true) {
        if (isUniqueName && isUniqueResource && !isGreaterLength) {
          if (formData.checkpoint_resid === "") {
            form.setValue("checkpoint_resid", selResourceId);
          }
          const updatedValues = [...addedValues, formData];
          await new Promise<void>((resolve) => {
            setAddedValues(updatedValues);
            resolve();
          });
          closeMilestoneDialog(true);
        } else {
          if (isGreaterLength) {
            toast({
              variant: ERROR_MESSAGES.toast_variant_destructive,
              title: t("errorMessages.toast_error_title"),
              description: t("errorMessages.check_point_length"),
            });
          } else {
            if (!isUniqueName) {
              toast({
                variant: ERROR_MESSAGES.toast_variant_destructive,
                title: t("errorMessages.toast_error_title"),
                description: t("errorMessages.check_point_name"),
              });
            }
            if (!isUniqueResource) {
              toast({
                variant: ERROR_MESSAGES.toast_variant_destructive,
                title: t("errorMessages.toast_error_title"),
                description: t("errorMessages.check_point_resource"),
              });
            }
          }
        }
      } else {
        const isUniqueSequence = checkpointData.every(
          (item) => item.sequence_number !== formData.sequence_number,
        );

        const isUniqueStartTime = checkpointData.every((item) => {
          const {
            HH: itemHH,
            MM: itemMM,
            SS: itemSS,
          } = item.checkpoint_startTime;
          // const { HH, MM, SS } = formData.checkpoint_startTime || {
          //   HH: "00",
          //   MM: "00",
          //   SS: "00",
          // };
          const {
            HH = "00",
            MM = "00",
            SS = "00",
          } = formData.checkpoint_startTime ?? {};
          const newHH = HH !== "" ? HH : "00";
          const newMM = MM !== "" ? MM : "00";
          const newSS = SS !== "" ? SS : "00";
          return itemHH !== newHH || itemMM !== newMM || itemSS !== newSS;
        });

        if (
          isUniqueSequence &&
          isUniqueName &&
          isUniqueStartTime &&
          isUniqueResource &&
          !isGreaterLength
        ) {
          if (formData.checkpoint_resid === "") {
            form.setValue("checkpoint_resid", selResourceId);
          }
          const updatedValues = [...addedValues, formData];
          await new Promise<void>((resolve) => {
            setAddedValues(updatedValues);
            resolve();
          });
          closeMilestoneDialog(true);
        } else {
          if (isGreaterLength) {
            toast({
              variant: ERROR_MESSAGES.toast_variant_destructive,
              title: t("errorMessages.toast_error_title"),
              description: t("errorMessages.check_point_length"),
            });
          } else if (!isUniqueSequence) {
            toast({
              variant: ERROR_MESSAGES.toast_variant_destructive,
              title: t("errorMessages.toast_error_title"),
              description: t("errorMessages.check_point_sequence"),
            });
          } else if (!isUniqueName) {
            toast({
              variant: ERROR_MESSAGES.toast_variant_destructive,
              title: t("errorMessages.toast_error_title"),
              description: t("errorMessages.check_point_name"),
            });
          } else if (!isUniqueStartTime) {
            toast({
              variant: ERROR_MESSAGES.toast_variant_destructive,
              title: t("errorMessages.toast_error_title"),
              description: t("errorMessages.check_point_time"),
            });
          } else if (!isUniqueResource) {
            toast({
              variant: ERROR_MESSAGES.toast_variant_destructive,
              title: t("errorMessages.toast_error_title"),
              description: t("errorMessages.check_point_resource"),
            });
          }
        }
      }
    } else {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.checkCheckpointNo"),
      });
    }
  }

  const compareTimes = (
    startTime: StartTime,
    totalVideoLength: string,
  ): boolean => {
    const startTimeSeconds =
      parseInt(startTime.HH) * 3600 +
      parseInt(startTime.MM) * 60 +
      parseInt(startTime.SS);
    const [totalHours, totalMinutes, totalSeconds] =
      totalVideoLength.split(":");
    const totalVideoLengthSeconds =
      parseInt(totalHours) * 3600 +
      parseInt(totalMinutes) * 60 +
      parseInt(totalSeconds);
    if (startTimeSeconds >= totalVideoLengthSeconds) {
      // toast({
      //   variant: "destructive",
      //   title: "Error",
      //   description: "Check point start time exceeds video length",
      // });
      return true;
    } else {
      return false;
    }
  };

  const handleComboValueChange = (selectedValue: string): void => {
    setSelResourceId(selectedValue);
    form.setValue("checkpoint_resid", selectedValue);
    form.setValue("checkpoint_type", "Exam");
  };

  // const generateSequenceNumber = (): void => {
  //   const checkpoint_no = localStorage.getItem("checkpointNo");
  //   console.log("checkpoint_no", checkpoint_no);

  //   const maxSequenceNo = checkpointNumber ;
  //   console.log("checkpointNumber", checkpointNumber);
  //   console.log("maxSequenceNo", );

  //   const generatedSequenceNo = [];
  //   for (let i = 1; i <= maxSequenceNo; i++) {
  //     const sequenceNoData: ComboData = {
  //       value: i.toString(),
  //       label: i.toString(),
  //     };
  //     generatedSequenceNo.push(sequenceNoData);
  //     setSequenceNo(generatedSequenceNo);
  //   }
  // };

  const generateSequenceNumber = (): void => {
    console.log("checkpoint_no", checkpoint_no);

    const maxSequenceNo = parseInt(checkpoint_no ?? "0");
    const generatedSequenceNo = [];
    for (let i = 1; i <= maxSequenceNo; i++) {
      const sequenceNoData: ComboData = {
        value: i.toString(),
        label: i.toString(),
      };
      generatedSequenceNo.push(sequenceNoData);
      setSequenceNo(generatedSequenceNo);
    }
  };

  useEffect(() => {
    onAddedValuesChange(addedValues);
    generateSequenceNumber();
  }, [addedValues]);

  const CheckPointQuizList = (): void => {
    const fetchData = async (): Promise<void> => {
      try {
        const cpList = await getCheckPointQuizList();
        const comboData: ComboData[] = cpList.map((item) => ({
          value: item.id,
          label: item.name,
        }));

        setComboData(comboData);
        // console.log(category);
      } catch (error) {
        console.error("Error fetching data:");
      }
    };
    fetchData().catch((error) => console.log(error));
  };
  useEffect(() => {
    CheckPointQuizList();
    const newtime = convertToTimeFormat(checkpointTime);
    const [HH, MM, SS] = newtime.split(":");

    // Set default values for the form fields
    form.setValue("checkpoint_startTime.HH", HH);
    form.setValue("checkpoint_startTime.MM", MM);
    form.setValue("checkpoint_startTime.SS", SS);
  }, []);

  const isMandatoryenabled = (isMandatoryType: string): void => {
    {
      isMandatoryType === "true"
        ? setIsMandatoryType(true)
        : setIsMandatoryType(false);
    }
  };
  const convertToTimeFormat = (value: number): string => {
    // Extract hours, minutes, and seconds from the decimal value
    const hours = Math.floor(value / 3600);
    const minutes = Math.floor((value % 3600) / 60);
    const seconds = Math.floor(value % 60);

    // Format hours, minutes, and seconds to have leading zeros if needed
    const formattedHours = hours.toString().padStart(2, "0");
    const formattedMinutes = minutes.toString().padStart(2, "0");
    const formattedSeconds = seconds.toString().padStart(2, "0");

    return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
  };

  return (
    <div className="p-4 mt-4">
      <Form {...form}>
        <form
          onSubmit={(event) => void form.handleSubmit(onSubmit)(event)}
          className="space-y-8"
        >
          <div className="w-full flex flex-wrap">
            <div className="w-full sm:w-1/3 pr-4">
              <FormField
                control={form.control}
                name="sequence_number"
                render={() => (
                  <FormItem>
                    {" "}
                    <FormLabel>
                      {t("courses.courseModule.sequenceNumber")}
                      <span className="text-red-700">*</span>
                    </FormLabel>
                    <FormControl>
                      {/* <Input {...field} /> */}
                      <Combobox
                        data={sequenceNo}
                        onSelectChange={handleSequenceValueChange}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="w-full sm:w-1/3 pr-4">
              <FormField
                control={form.control}
                name="checkpoint_name"
                render={({ field }) => (
                  <FormItem>
                    {" "}
                    <FormLabel>
                      {" "}
                      {t("courses.courseModule.checkpointName")}
                      <span className="text-red-700">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        autoComplete="off"
                        {...field}
                        onChange={(event) => {
                          field.onChange(event.target.value.trimStart());
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="w-full sm:w-1/3">
              <FormField
                control={form.control}
                name="checkpoint_startTime"
                render={() => (
                  <FormControl>
                    <FormItem>
                      <FormLabel>
                        {t("courses.courseModule.checkpointStartTime")}
                        <span className="text-red-700">*</span>
                      </FormLabel>
                      <FormControl>
                        <div className="flex space-x-2">
                          <Input
                            autoComplete="off"
                            type="text"
                            placeholder="00"
                            disabled={isTimeDisabled}
                            {...form.register("checkpoint_startTime.HH")}
                          />
                          <span>:</span>
                          <Input
                            autoComplete="off"
                            type="text"
                            placeholder="00"
                            disabled={isTimeDisabled}
                            {...form.register("checkpoint_startTime.MM")}
                          />
                          <span>:</span>
                          <Input
                            autoComplete="off"
                            type="text"
                            placeholder="00"
                            disabled={isTimeDisabled}
                            {...form.register("checkpoint_startTime.SS")}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  </FormControl>
                )}
              />
            </div>
          </div>
          <div className="w-full flex flex-wrap">
            <div className="w-full sm:w-1/3 pr-4">
              <FormField
                control={form.control}
                name="checkpoint_type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {" "}
                      {t("courses.courseModule.checkpointType")}
                      <span className="text-red-700">*</span>
                    </FormLabel>

                    <Select
                      name="courseVisibility"
                      onValueChange={field.onChange}
                      defaultValue={"exam"}
                      disabled
                    >
                      {" "}
                      <FormControl>
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Select" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {milestoneType.map((item, index) => (
                          <SelectItem key={index} value={item.value.toString()}>
                            {item.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>

                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="w-full sm:w-1/3 pr-4">
              <FormField
                control={form.control}
                name="checkpoint_resid"
                render={() => (
                  <FormItem>
                    <FormLabel>
                      {t("courses.courseModule.checkpointResourceId")}
                      <span className="text-red-700">*</span>
                    </FormLabel>
                    <FormControl>
                      <div className="overflow-x-auto">
                        <Combobox
                          data={comboData}
                          onSelectChange={handleComboValueChange}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="w-full sm:w-1/3 pr-4">
              <FormField
                control={form.control}
                name="isMandatory"
                render={() => (
                  <FormItem className="space-y-3">
                    <FormLabel>
                      {t("courses.courseModule.isMandatory")}
                      <span className="text-red-700">*</span>
                    </FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={(value) => {
                          const checkpointData =
                            allCheckPointData.concat(existingCheckPoints);
                          const hasMandatoryTrue = checkpointData.some(
                            (item) => item.isMandatory === "true",
                          );

                          if (hasMandatoryTrue && value === "true") {
                            toast({
                              variant: ERROR_MESSAGES.toast_variant_destructive,
                              title: t("errorMessages.toast_error_title"),
                              description: t("errorMessages.mandatory_error"),
                            });

                            form.setValue("isMandatory", "false");
                          } else {
                            form.setValue("isMandatory", value);
                            isMandatoryenabled(value);
                          }
                        }}
                        defaultValue="false"
                        className="flex flex-row space-x-3"
                      >
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="true" />
                          </FormControl>
                          <FormLabel className="font-normal">Yes</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="false" />
                          </FormControl>
                          <FormLabel className="font-normal">No</FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="w-full flex ">
              <div className="flex justify-end w-full">
                {" "}
                <Button type="submit">{String(t("buttons.addToGrid"))}</Button>
              </div>
            </div>
          </div>
        </form>
      </Form>
    </div>
  );
};
