import {
  EXAM_TYPE_CHECKPOINT,
  EXAM_TYPE_MAIN,
  EXAM_TYPE_PRACTICE,
} from "@/lib/constants";
import type {
  QuizesOfCourse,
  ErrorType,
  PublishExam,
  ExamResponse,
  DeleteQuestionRequest,
  CourseListRequest,
  CourseListResponse,
  QuizToCourseRequest,
  QuizToCourseResponse,
  ImportExamResponse,
  ImportExamRequest,
  DeleteExamRequest,
} from "@/types";
import { supabase } from "../lib/client";
import { rpc } from "@/lib/apiConfig";

interface useExamsReturn {
  getExamsList: (courseId: string) => Promise<QuizesOfCourse[]>;
  publishExam: (params: PublishExam) => Promise<ExamResponse>;
  deleteQuestion: (params: DeleteQuestionRequest) => Promise<ExamResponse>;
  getCourseList: (params: CourseListRequest) => Promise<CourseListResponse[]>;
  addQuizToCourse: (
    params: QuizToCourseRequest,
  ) => Promise<QuizToCourseResponse>;
  importExamToResource: (
    params: ImportExamRequest,
  ) => Promise<ImportExamResponse>;
  deleteExam: (params: DeleteExamRequest) => Promise<ExamResponse>;
}

const useExams = (): useExamsReturn => {
  async function getExamsList(courseId: string): Promise<QuizesOfCourse[]> {
    const requestBody = {
      org_id: localStorage.getItem("orgId"),
      course_id: courseId,
      quizes_of_course_data: {
        //optional
        quiz_type: [EXAM_TYPE_MAIN, EXAM_TYPE_PRACTICE, EXAM_TYPE_CHECKPOINT],
      },
    };
    try {
      const { data, error } = (await supabase.rpc<string, null>(
        rpc.getQuizesOfCourse,
        requestBody,
      )) as { data: QuizesOfCourse[]; error: ErrorType | null };
      if (error) {
        throw new Error(error.details);
      }
      return data as QuizesOfCourse[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function publishExam(params: PublishExam): Promise<ExamResponse> {
    try {
      const { data, error } = (await supabase.rpc(rpc.publishExam, params)) as {
        data: ExamResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as ExamResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function deleteQuestion(
    params: DeleteQuestionRequest,
  ): Promise<ExamResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.deleteQuestionFromQuiz,
        params,
      )) as {
        data: ExamResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as ExamResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function getCourseList(
    params: CourseListRequest,
  ): Promise<CourseListResponse[]> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.getCourseListForExamMapping,
        params,
      )) as {
        data: CourseListResponse[];
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as CourseListResponse[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function addQuizToCourse(
    params: QuizToCourseRequest,
  ): Promise<QuizToCourseResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.insertQuizToCourses,
        params,
      )) as {
        data: QuizToCourseResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as QuizToCourseResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function importExamToResource(
    params: ImportExamRequest,
  ): Promise<ImportExamResponse> {
    try {
      const { data, error } = (await supabase.rpc(rpc.importExam, params)) as {
        data: ImportExamResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as ImportExamResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function deleteExam(params: DeleteExamRequest): Promise<ExamResponse> {
    try {
      const { data, error } = (await supabase.rpc(rpc.deleteExam, params)) as {
        data: ExamResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as ExamResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  return {
    getExamsList,
    publishExam,
    deleteQuestion,
    getCourseList,
    addQuizToCourse,
    importExamToResource,
    deleteExam,
  };
};

export default useExams;
