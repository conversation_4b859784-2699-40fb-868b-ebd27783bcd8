import React from "react";
import type { DeleteQuestionRequest, ErrorCatch, ToastType } from "@/types";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import useExams from "@/hooks/useExams";
import { useTranslation } from "react-i18next";

export default function DeleteQuestion({
  data,
  onSave,
  onCancel,
}: {
  onSave: (value: boolean) => void;
  onCancel: () => void;
  data: DeleteQuestionRequest;
  isModal?: boolean;
}): React.JSX.Element {
  const { t } = useTranslation();
  const { toast } = useToast() as ToastType;
  const { deleteQuestion } = useExams();
  const handleDeleteClick = (): void => {
    void handleToastSave();
    onCancel();
  };
  const handleToastSave = async (): Promise<void> => {
    try {
      const result = await deleteQuestion(data);
      if (result.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.quiz_delete_title"),
          description: t("successMessages.quiz_delete_msg"),
        });
        onSave(true);
      } else if (result.status === "error") {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: result.status,
        });
        console.log("API Error:", result.status);
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
      console.error("An unexpected error occurred:", error);
    }
  };
  return (
    <>
      <div className="mb-2 mr-4">
        <p className="ml-0 ">{t("exams.deletePrompt")}</p>
      </div>
      <div className="flex topic-icons pr-4">
        <div className="flex items-center justify-center float-right">
          <Button
            type="button"
            className="primary"
            variant="outline"
            onClick={onCancel}
          >
            {t("buttons.cancel")}
          </Button>
          &nbsp;
          <Button type="submit" className="primary" onClick={handleDeleteClick}>
            {t("exams.delete")}
          </Button>
        </div>
      </div>
    </>
  );
}
