import { supabase } from "@/lib/client";
import type {
  AddCurrentAffairs,
  CurrentAffairsResult,
  ErrorType,
} from "@/types";
import { rpc } from "@/lib/apiConfig";

interface UseAddCurrentAffairsReturn {
  addCurrentAffairs: (
    params: AddCurrentAffairs,
  ) => Promise<CurrentAffairsResult>;
}

const useAddCurrentAffairs = (): UseAddCurrentAffairsReturn => {
  async function addCurrentAffairs(
    params: AddCurrentAffairs,
  ): Promise<CurrentAffairsResult> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.insertBulletinBoard,
        params,
      )) as {
        data: CurrentAffairsResult;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as CurrentAffairsResult;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  return {
    addCurrentAffairs,
  };
};

export default useAddCurrentAffairs;
