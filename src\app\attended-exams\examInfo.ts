const ExamInfo = [
  {
    quiz_id: "c65eedba-44c6-451d-bb03-63c920549a44",
    org_id: "19579370-a028-484d-8f35-8af2023068dd",
    question_with_options: "<p><strong>What is Ionic Framework?</strong></p>",
    response_summary: "A mobile app development framework",
    course_id: "************************************",
    course_name: "Ionic Framework Program",
    quiz_name: "Ionic Practise",
    quiz_start_time: "2023-07-19T12:21:07.457584+00:00",
    quiz_end_time: "2023-07-19T12:24:48.102354+00:00",
    duration: "00:03:40.64477",
    quiz_attempt_id: "2873d67a-2768-4565-a258-114ce2ded702",
  },
  {
    quiz_id: "c65eedba-44c6-451d-bb03-63c920549a44",
    org_id: "19579370-a028-484d-8f35-8af2023068dd",
    question_with_options:
      "<p><strong>Which of the following platforms is NOT supported by Ionic Framework?</strong></p>",
    response_summary: "Linux",
    course_id: "************************************",
    course_name: "Ionic Framework Program",
    quiz_name: "Ionic Practise",
    quiz_start_time: "2023-07-19T12:21:07.457584+00:00",
    quiz_end_time: "2023-07-19T12:24:48.102354+00:00",
    duration: "00:03:40.64477",
    quiz_attempt_id: "2873d67a-2768-4565-a258-114ce2ded702",
  },
  {
    quiz_id: "c65eedba-44c6-451d-bb03-63c920549a44",
    org_id: "19579370-a028-484d-8f35-8af2023068dd",
    question_with_options:
      "<p><strong>What is the command used to create a new Ionic project?</strong></p>",
    response_summary: "Ionic Start",
    course_id: "************************************",
    course_name: "Ionic Framework Program",
    quiz_name: "Ionic Practise",
    quiz_start_time: "2023-07-19T12:21:07.457584+00:00",
    quiz_end_time: "2023-07-19T12:24:48.102354+00:00",
    duration: "00:03:40.64477",
    quiz_attempt_id: "2873d67a-2768-4565-a258-114ce2ded702",
  },
  {
    quiz_id: "c65eedba-44c6-451d-bb03-63c920549a44",
    org_id: "19579370-a028-484d-8f35-8af2023068dd",
    question_with_options:
      "<p><strong>What is the latest version of Ionic Framework as of July 2023?</strong></p>",
    response_summary: "7.x",
    course_id: "************************************",
    course_name: "Ionic Framework Program",
    quiz_name: "Ionic Practise",
    quiz_start_time: "2023-07-19T12:21:07.457584+00:00",
    quiz_end_time: "2023-07-19T12:24:48.102354+00:00",
    duration: "00:03:40.64477",
    quiz_attempt_id: "2873d67a-2768-4565-a258-114ce2ded702",
  },
  {
    quiz_id: "c65eedba-44c6-451d-bb03-63c920549a44",
    org_id: "19579370-a028-484d-8f35-8af2023068dd",
    question_with_options:
      "<p><strong>Which of the following is NOT a valid way to debug an Ionic application?</strong></p>",
    response_summary: "Using the Visual Studio Code debugger",
    course_id: "************************************",
    course_name: "Ionic Framework Program",
    quiz_name: "Ionic Practise",
    quiz_start_time: "2023-07-19T12:21:07.457584+00:00",
    quiz_end_time: "2023-07-19T12:24:48.102354+00:00",
    duration: "00:03:40.64477",
    quiz_attempt_id: "2873d67a-2768-4565-a258-114ce2ded702",
  },
  {
    quiz_id: "c65eedba-44c6-451d-bb03-63c920549a44",
    org_id: "19579370-a028-484d-8f35-8af2023068dd",
    question_with_options:
      "<p><strong>Which of the following is a valid way to install a Cordova plugin in Ionic?</strong></p>",
    response_summary: "Using the npm install command",
    course_id: "************************************",
    course_name: "Ionic Framework Program",
    quiz_name: "Ionic Practise",
    quiz_start_time: "2023-07-19T12:21:07.457584+00:00",
    quiz_end_time: "2023-07-19T12:24:48.102354+00:00",
    duration: "00:03:40.64477",
    quiz_attempt_id: "2873d67a-2768-4565-a258-114ce2ded702",
  },
  {
    quiz_id: "c65eedba-44c6-451d-bb03-63c920549a44",
    org_id: "19579370-a028-484d-8f35-8af2023068dd",
    question_with_options:
      "<p><strong>Which of the following is NOT a valid Ionic Navigation component?</strong></p>",
    response_summary: "ion-carousel",
    course_id: "************************************",
    course_name: "Ionic Framework Program",
    quiz_name: "Ionic Practise",
    quiz_start_time: "2023-07-19T12:21:07.457584+00:00",
    quiz_end_time: "2023-07-19T12:24:48.102354+00:00",
    duration: "00:03:40.64477",
    quiz_attempt_id: "2873d67a-2768-4565-a258-114ce2ded702",
  },
  {
    quiz_id: "c65eedba-44c6-451d-bb03-63c920549a44",
    org_id: "19579370-a028-484d-8f35-8af2023068dd",
    question_with_options:
      "<p><strong>Which of the following is NOT a valid Ionic CLI command?</strong></p>",
    response_summary: "Ionic Compile",
    course_id: "************************************",
    course_name: "Ionic Framework Program",
    quiz_name: "Ionic Practise",
    quiz_start_time: "2023-07-19T12:21:07.457584+00:00",
    quiz_end_time: "2023-07-19T12:24:48.102354+00:00",
    duration: "00:03:40.64477",
    quiz_attempt_id: "2873d67a-2768-4565-a258-114ce2ded702",
  },
  {
    quiz_id: "c65eedba-44c6-451d-bb03-63c920549a44",
    org_id: "19579370-a028-484d-8f35-8af2023068dd",
    question_with_options:
      "<p><strong>Which of the following is a Cordova plugin used for accessing device contacts?</strong></p>",
    response_summary: "cordova-plugin-contacts",
    course_id: "************************************",
    course_name: "Ionic Framework Program",
    quiz_name: "Ionic Practise",
    quiz_start_time: "2023-07-19T12:21:07.457584+00:00",
    quiz_end_time: "2023-07-19T12:24:48.102354+00:00",
    duration: "00:03:40.64477",
    quiz_attempt_id: "2873d67a-2768-4565-a258-114ce2ded702",
  },
  {
    quiz_id: "c65eedba-44c6-451d-bb03-63c920549a44",
    org_id: "19579370-a028-484d-8f35-8af2023068dd",
    question_with_options:
      "<p><strong>Which of the following is NOT a component in Ionic Framework?</strong></p>",
    response_summary: "table",
    course_id: "************************************",
    course_name: "Ionic Framework Program",
    quiz_name: "Ionic Practise",
    quiz_start_time: "2023-07-19T12:21:07.457584+00:00",
    quiz_end_time: "2023-07-19T12:24:48.102354+00:00",
    duration: "00:03:40.64477",
    quiz_attempt_id: "2873d67a-2768-4565-a258-114ce2ded702",
  },
];
export default ExamInfo;
