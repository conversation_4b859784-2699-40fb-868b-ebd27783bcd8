import { useForm } from "react-hook-form";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";

export function CourseVideoForm(): React.JSX.Element {
  const form = useForm();
  function onSubmit(data: unknown): void {
    console.log(data);
  }

  return (
    <Form {...form}>
      <form onSubmit={() => form.handleSubmit(onSubmit)} className="space-y-8">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Paste Video Link here</FormLabel>
              <FormControl>
                <Input autoComplete="off" placeholder="Your name" {...field} />
              </FormControl>
              <FormDescription></FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
      </form>
    </Form>
  );
}
