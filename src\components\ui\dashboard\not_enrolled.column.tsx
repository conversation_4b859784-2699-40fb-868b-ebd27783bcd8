"use client";

import type { ColumnDef } from "@tanstack/react-table";
// import { ArrowUpDown } from "lucide-react";
// import { Button } from "@/components/ui/button";
import type {
  UsersNotEnrolled,
  // UserNotEnrolledViewsColumnDefinition,
  UserNotEnrolledViewsRowDefinition,
} from "@/types";
import Image from "next/image";
export const  getColumns = (
  t: (key: string) => string
): ColumnDef<UsersNotEnrolled>[] => [
  {
    accessorKey: "first_name",
    header: t("dashboard.newRegistrations.name"),
    cell: ({ row }: UserNotEnrolledViewsRowDefinition): React.JSX.Element => {
      const firstName = row.original.first_name ?? " ";
      const lastName = row.original.last_name ?? " ";
      return <div className="text-align">{`${firstName} ${lastName}`}</div>;
    },
  },
  {
    accessorKey: "email",
    header: t("dashboard.newRegistrations.email"),
    cell: ({ row }) => <div>{row.original.email}</div>,
  },
  {
    accessorKey: "avatar_url",
    header: t("dashboard.newRegistrations.profile"),
    cell: ({ row }) => (
      <div className="h-10 w-10">
        {row.original.avatar_url !== "" && row.original.avatar_url !== null ? (
          <Image
            src={row.original.avatar_url}
            alt=""
            width={45}
            height={45}
            objectFit="cover"
            className="rounded-full"
            style={{
              width: "45px",
              height: "38px",
              border: "2px solid #000",
              borderRadius: "50%",
            }}
          />
        ) : (
          <div className="rounded-full h-25 w-25 bg-gray-300"></div>
        )}
      </div>
    ),
  },
  {
    accessorKey: "phonenumber1",
    header: "Phone Number",
    cell: ({ row }) => <div>{row.original.phonenumber1}</div>,
  },
];
