"use client";
import type { ColumnDef } from "@tanstack/react-table";
// import { ArrowUpDown } from "lucide-react";
// import { Button } from "@/components/ui/button";
import type {
  ResourceList,
  // ResourcesFormDefinition,
  ResourcesFormRowDefinition,
} from "@/types";
import moment from "moment";

export const getColumns = (
  t: (key: string) => string,
): ColumnDef<ResourceList>[] => [
  {
    accessorKey: "name",
    header: t("resourceLibrary.resourceName"),
    // header: ({ column }: ResourcesFormDefinition): React.JSX.Element => {
    //   return (
    //     <Button
    //       className="px-0"
    //       variant="ghost"
    //       onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
    //     >
    //       Resource Name
    //       <ArrowUpDown className="ml-2 h-4 w-4" />
    //     </Button>
    //   );
    // },
    cell: ({ row }: ResourcesFormRowDefinition): React.JSX.Element => (
      <div className="text-align">{row.original.name}</div>
    ),
  },
  {
    header: t("resourceLibrary.resourceType"),
    cell: ({ row }: ResourcesFormRowDefinition): React.JSX.Element => (
      <div className="text-align">{row.original.file_type}</div>
    ),
  },
  {
    header: t("resourceLibrary.folderName"),
    cell: ({ row }: ResourcesFormRowDefinition): React.JSX.Element => (
      <div className="text-align">{row.original.folder_name ?? ""}</div>
    ),
  },
  // {
  //   header: "Linked Cource",
  //   cell: ({ row }: ResourcesFormRowDefinition): React.JSX.Element => (
  //     <div className="text-align">{row.original.linked_course}</div>
  //   ),
  // },
  // {
  //   header: "Linked Resource",
  //   cell: ({ row }: ResourcesFormRowDefinition): React.JSX.Element => (
  //     <div className="text-align">{row.original.linked_resource}</div>
  //   ),
  // },
  {
    header: t("resourceLibrary.createdOn"),
    cell: ({ row }: ResourcesFormRowDefinition): React.JSX.Element => {
      const formattedDate = moment
        .utc(row.original.created_at)
        .local()
        .format("DD-MMM-YYYY hh:mm a");

      return <div className="text-align">{formattedDate}</div>;
    },
  },
  {
    accessorKey: "is_watched",
    header: "",
    cell: ({ row }: ResourcesFormRowDefinition): React.JSX.Element => (
      <div className="text-align text-blue-700">{row.original.is_watched === false ? "No views" : ""}</div>
    ),
  },
  // {
  //   header: "Status",
  //   cell: ({ row }: ResourcesFormRowDefinition): React.JSX.Element => (
  //     <div className="text-align">{row.original.status}</div>
  //   ),
  // },
];
