import React, { type BaseSyntheticEvent } from "react";
import { But<PERSON> } from "@/components/ui/button";
import useCustomDashboard from "@/hooks/useCustomDashboard";
import { useToast } from "@/components/ui/use-toast";
import type { ErrorCatch, ToastType } from "@/types";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import { ORG_KEY } from "@/lib/constants";
import { useTranslation } from "react-i18next";

export default function DeleteCustomResources({
  onCancel,
  resourceId,
  onDeleteSuccess,
  totalResources,
  noOfgeneralResourcesItems,
}: {
  onCancel: () => void;
  resourceId: string;
  onDeleteSuccess: (deletedId: string) => void;
  totalResources: number;
  noOfgeneralResourcesItems: number;
}): React.JSX.Element {
  const { deleteResources } = useCustomDashboard();
  const { toast } = useToast() as ToastType;
  const { t } = useTranslation();

  const handleDeleteClick = async (e: BaseSyntheticEvent): Promise<void> => {
    console.log(e);
    if (totalResources <= 1) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.delete_resource_title"),
        description: t("errorMessages.description_atleat_one_resource"),
      });
      return;
    }
    if (totalResources <= noOfgeneralResourcesItems) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.delete_resource_title"),
        description: t("errorMessages.min_resources_msg", { count: noOfgeneralResourcesItems }),
      });
      return;
    }
    const orgId = localStorage.getItem(ORG_KEY);
    const params = {
      org_id: orgId ?? "",
      resource_id: resourceId,
      component_type: "general_resources",
    };

    try {
      const result = await deleteResources(params);
      if (result.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_success,
          title: t("successMessages.deleteResourceTitle"),
          description: t("successMessages.deleteResourceMsg"),
        });
        onDeleteSuccess(resourceId);
        onCancel();
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
    }
  };

  return (
    <>
      <div className="mb-2 mr-4">
        <p className="ml-0 ">
          {String(t("customDashboard.resourceDelete.deletePrompt"))}
        </p>
      </div>
      <div className="flex topic-icons pr-4">
        <div className="flex items-center justify-center float-right">
          <Button type="button" className="bg-[#33363F]" onClick={onCancel}>
            {String(t("buttons.cancel"))}
          </Button>
          &nbsp;
          <Button
            type="submit"
            className="bg-[#9FC089]"
            onClick={(e: BaseSyntheticEvent) => {
              handleDeleteClick(e).catch((error) => console.log(error));
            }}
          >
            {String(t("buttons.delete"))}
          </Button>
        </div>
      </div>
    </>
  );
}
