import { supabase } from "../lib/client";
import type {
  ImportQuestionResult,
  ErrorType,
  ImportQuestionRequest,
} from "@/types";
import { rpc } from "../lib/apiConfig";
interface useImportQuestionsReturn {
  postImportQuestions: (
    params: ImportQuestionRequest,
  ) => Promise<ImportQuestionResult>;
  addImportQuestionsFromBank: (
    params: ImportQuestionRequest,
  ) => Promise<ImportQuestionResult>;
}

const useImportQuestions = (): useImportQuestionsReturn => {
  async function postImportQuestions(
    params: ImportQuestionRequest,
  ): Promise<ImportQuestionResult> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.insertQuestionsFromBank,
        params,
      )) as {
        data: ImportQuestionResult;
        error: ErrorType | null;
      };
      if (error) {
        throw error;
      }
      return data as ImportQuestionResult;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function addImportQuestionsFromBank(
    params: ImportQuestionRequest,
  ): Promise<ImportQuestionResult> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.importQuestionsFromBank,
        params,
      )) as {
        data: ImportQuestionResult;
        error: ErrorType | null;
      };
      if (error) {
        throw error;
      }
      return data as ImportQuestionResult;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  return {
    postImportQuestions,
    addImportQuestionsFromBank,
  };
};

export default useImportQuestions;
