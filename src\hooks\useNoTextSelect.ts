import { useEffect } from "react";

function useNoTextSelect(elementId: string): void {
  useEffect(() => {
    const noSelectElements = document.querySelectorAll(
      `.${elementId}`,
    ) as NodeListOf<HTMLElement>;

    noSelectElements.forEach((element) => {
      //   element.style.webkitUserSelect = "none";
      //   element.style.mozUserSelect = "none";
      //   element.style.msUserSelect = "none";
      element.style.userSelect = "none";
    });
  }, [elementId]);
}

export default useNoTextSelect;
