import React, { useState } from "react";
import Image from "next/image";
import {
  <PERSON><PERSON><PERSON>,
  TooltipProvider,
  TooltipTrigger,
} from "@radix-ui/react-tooltip";
import { Modal } from "@/components/ui/modal";
import ResourceProgressList from "./resourceProgress";

import { Check } from "lucide-react";
import type { ResourceProgress } from "@/types";
interface MostWatchedUserProps {
  user_profile_url: string;
  user_name: string;
  // total_time_spent?: string;
  video_name?: string;
  resources: ResourceProgress[];
}

export function DBMostWatcheduser({
  user_profile_url,
  user_name,
  // total_time_spent,
  resources,
}: MostWatchedUserProps): React.JSX.Element {
  const [isOpenModal, setIsOpenModal] = useState<boolean>(false);

  const handleModal = (): void => {
    setIsOpenModal(true);
  };
  const cancelModal = (): void => {
    setIsOpenModal(false);
  };
  return (
    <div className="flex flex-col items-center  py-3 bg-[#FFF]">
      <div className="relative">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="relative">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="relative">
                        <Image
                          alt={`${user_name}'s profile`}
                          src={
                            user_profile_url ?? "/static/images/smartlearn.png"
                          }
                          className="mx-auto w-16 h-16 rounded-full object-cover border-2 border-gray-100 shadow-sm"
                          height={100}
                          width={100}
                          onClick={handleModal}
                        />
                      </div>
                    </TooltipTrigger>
                  </Tooltip>
                </TooltipProvider>

                <div className="absolute -bottom-1 -right-1 h-5 w-5 rounded-full bg-green-500 border-2 border-white flex items-center justify-center">
                  <Check className="w-3 h-3 text-white" strokeWidth={3} />
                </div>
              </div>
            </TooltipTrigger>
            {/* <TooltipContent
              className="data-[state=delayed-open]:data-[side=top]:animate-slideDownAndFade data-[state=delayed-open]:data-[side=right]:animate-slideLeftAndFade data-[state=delayed-open]:data-[side=left]:animate-slideRightAndFade data-[state=delayed-open]:data-[side=bottom]:animate-slideUpAndFade text-violet11 select-none rounded-[4px] bg-white px-[15px] py-[10px] text-[15px] leading-none shadow-[hsl(206_22%_7%_/_35%)_0px_10px_38px_-10px,_hsl(206_22%_7%_/_20%)_0px_10px_20px_-15px] will-change-[transform,opacity]"
              side="bottom"
              sideOffset={5}
            >
              <p className="text-black">{video_name}</p>
              <TooltipArrow className="fill-white" />
            </TooltipContent> */}
          </Tooltip>
        </TooltipProvider>

        {/* <svg
          width="10"
          height="10"
          fill="currentColor"
          className="absolute bottom-0 right-0  h-5 w-5 rounded-full bg-blue-600 fill-current p-1 text-white"
          viewBox="0 0 1792 1792"
          xmlns="http://www.w3.org/2000/svg"
        >
          7
          <path d="M1671 566q0 40-28 68l-724 724-136 136q-28 28-68 28t-68-28l-136-136-362-362q-28-28-28-68t28-68l136-136q28-28 68-28t68 28l294 295 656-657q28-28 68-28t68 28l136 136q28 28 28 68z" />
        </svg> */}
      </div>
      <span className="mt-2 text-xs text-gray-600 text-center">
        <div>{user_name}</div>
        {/* <p>{total_time_spent}</p> */}
      </span>
      {isOpenModal && (
        <Modal
          title=""
          header=""
          openDialog={isOpenModal}
          closeDialog={cancelModal}
          type="max-w-3xl"
        >
          <ResourceProgressList resources={resources} onCancel={cancelModal} />
        </Modal>
      )}
    </div>
  );
}
