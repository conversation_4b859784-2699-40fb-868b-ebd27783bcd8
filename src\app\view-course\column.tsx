"use client";
import React from "react";
import type { ColumnDef, Row } from "@tanstack/react-table";
import type { CoursePlanResult } from "@/types";
import moment from "moment";
import { DATE_FORMAT_DMY_HM_AM_PM } from "@/lib/constants";

interface RowDefinition {
  row: Row<CoursePlanResult>;
}

export const getPlansColumns = (
  t: (key: string) => string
): ColumnDef<CoursePlanResult>[] => [
  {
    accessorKey: "name",
    header: t("membershipPlan.planName"),
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div>{row.original.name}</div>
    ),
  },
  {
    accessorKey: "subscription_frequency",
    header: t("membershipPlan.frequency"),
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div>{row.original.subscription_frequency}</div>
    ),
  },
  {
    accessorKey: "subscription_type",
    header: t("membershipPlan.type"),
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div>{row.original.subscription_type}</div>
    ),
  },
  {
    accessorKey: "price",
    header: t("membershipPlan.price"),
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div>{row.original.price}</div>
    ),
  },
  {
    accessorKey: "currency",
    header: t("membershipPlan.currency"),
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div>{row.original.currency}</div>
    ),
  },
  {
    accessorKey: "valid_from",
    header: t("membershipPlan.validFrom"),
    cell: ({ row }: RowDefinition): React.JSX.Element => {
      const formattedDate = moment
        .utc(row.original.valid_from)
        .format("DD-MMM-YYYY hh:mm a");
      return <div>{formattedDate}</div>;
    },
  },
  {
    accessorKey: "valid_to",
    header: t("membershipPlan.validTo"),
    cell: ({ row }: RowDefinition): React.JSX.Element => {
      const formattedDate = moment
        .utc(row.original.valid_to)
        .format(DATE_FORMAT_DMY_HM_AM_PM);
      return <div>{formattedDate}</div>;
    },
  },
];
