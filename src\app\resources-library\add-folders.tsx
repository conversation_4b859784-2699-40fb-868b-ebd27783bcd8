"use client";
import React, { useEffect } from "react";
import type {
  addFolderForm,
  AddFolderFromLibraryRequest,
  LogUserActivityRequest,
  ToastType,
} from "@/types";
import { useToast } from "@/components/ui/use-toast";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { ModalButton } from "@/components/ui/modalButton";
import { Textarea } from "@/components/ui/textarea";
import { addFoldersSchema } from "@/schema/schema";
import useCourse from "@/hooks/useCourse";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";

import {
  CalendarDateTime,
  getLocalTimeZone,
  today,
  type DateValue,
} from "@internationalized/date";
import { DateTimePicker } from "@/components/ui/date-time-picker/date-time-picker";
import moment from "moment";
import { DEFAULT_FOLDER_ID, FORMATTED_DATE_FORMAT } from "@/lib/constants";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";

export default function AddFolders({
  onCancel,
  onSave,
}: {
  onSave: () => void;
  onCancel: () => void;
  courseId?: string;
  sectionsId?: string;
}): React.JSX.Element {
  const { t } = useTranslation();
  const { toast } = useToast() as ToastType;
  const { addFolderFromLibrary } = useCourse();
  const form = useForm<addFolderForm>({
    resolver: zodResolver(addFoldersSchema),
  });
  const { updateUserActivity } = useLogUserActivity();
  const handleCancelClick = (): void => {
    onCancel();
  };

  useEffect(() => {
    const currentDate = today(getLocalTimeZone());
    const currentTime = new Date();

    // Add 10 minutes to the current time
    currentTime.setMinutes(currentTime.getMinutes() + 10);

    const validFromDate = new CalendarDateTime(
      currentDate.year,
      currentDate.month,
      currentDate.day,
      currentTime.getHours(),
      currentTime.getMinutes(),
    );

    const oneYearLater = new CalendarDateTime(
      currentDate.year + 1,
      currentDate.month,
      currentDate.day,
      currentTime.getHours(),
      currentTime.getMinutes(),
    );

    form.setValue("valid_from", validFromDate);
    form.setValue("valid_to", oneYearLater);
  }, []);
  async function handleToastSave(datas: addFolderForm): Promise<void> {
    const orgId = localStorage.getItem("orgId");

    const dateStart = datas.valid_from;
    const dateEnd = datas.valid_to;

    if (!dateStart || !dateEnd) {
      console.error("Start date or end date is null");
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.date_missing"),
      });
      return;
    }

    const startDate = new Date(
      dateStart.year ?? 0,
      (dateStart.month ?? 1) - 1,
      dateStart.day ?? 1,
      dateStart.hour ?? 0,
      dateStart.minute ?? 0,
    );

    const endDate = new Date(
      dateEnd.year ?? 0,
      (dateEnd.month ?? 1) - 1,
      dateEnd.day ?? 1,
      dateEnd.hour ?? 0,
      dateEnd.minute ?? 0,
    );

    const momentStartDate = moment(startDate);
    const momentEndDate = moment(endDate);
    const formattedValidFrom = momentStartDate.format(FORMATTED_DATE_FORMAT);
    const formattedValidTo = momentEndDate.format(FORMATTED_DATE_FORMAT);

    const folderData = {
      folder_name: datas.name,
      description: datas.description,
      validity_from: formattedValidFrom,
      validity_to: formattedValidTo,
      // status: datas.status,
      org_id: orgId,
    };

    try {
      const result = await addFolderFromLibrary(
        folderData as AddFolderFromLibraryRequest,
      );

      if (result.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_success_title,
          title: t("successMessages.add_folder_title"),
          description: t("successMessages.add_folder_msg"),
        });
        onSave();
        onCancel();
        const params = {
          activity_type: "Resource_Library",
          screen_name: "Add Folder",
          action_details: "Folder added ",
          target_id: result.folder_id,
          log_result: "SUCCESS",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      } else {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: result.status,
        });
        const params = {
          activity_type: "Resource_Library",
          screen_name: "Add Folder",
          action_details: "Failed to add folder ",
          target_id: DEFAULT_FOLDER_ID,
          log_result: "ERROR",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      }
    } catch (error: unknown) {
      const errMsg = (error as Error).message;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: errMsg,
      });
      const params = {
        activity_type: "Resource_Library",
        screen_name: "Add Folder",
        action_details: "Failed to add folder ",
        target_id: DEFAULT_FOLDER_ID,
        log_result: "ERROR",
      };
      void updateLogUserActivity(params).catch((error) => {
        console.error(error);
      });
    }
  }

  const handleDescriptionChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>,
  ): void => {
    const value = e.target.value;
    const sanitizedValue = value
      .trimStart()
      .replace(/^\p{P}+/u, "")
      .replace(/[^\p{L}\p{M}\p{N}\s\-!@#$%^&*()_+={}[\]:;"'<>,.?/\\|~`]/gu, "")
      .replace(/\s{2,}/g, " ")
      .replace(
        /^[^\p{L}\p{M}\p{N}]|[^\p{L}\p{M}\p{N}\s\-!@#$%^&*()_+={}[\]:;"'<>,.?/\\|~`]/gu,
        "",
      )
      .replace(/\s+$/, (match) =>
        match.length > 1 ? match.slice(0, -1) : match,
      );
    form.setValue("description", sanitizedValue);
  };
  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };
  return (
    <div className=" border rounded-md p-4 ">
      <Form {...form}>
        <form
          onSubmit={(event) => void form.handleSubmit(handleToastSave)(event)}
          className="space-y-8"
        >
          <div className="flex gap-x-4">
            <div className="w-1/2">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {t("resourceLibrary.folderName")}{" "}
                      <span className="text-red-700">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        autoComplete="off"
                        placeholder={t("resourceLibrary.folderName")}
                        maxLength={30}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="valid_from"
              render={({ field }) => (
                <FormItem className="w-1/2">
                  <FormLabel>
                    {t("resourceLibrary.validFrom")}
                    <span className="text-red-700">*</span>
                  </FormLabel>
                  <FormControl>
                    <DateTimePicker
                      granularity={"minute"}
                      hideTimeZone={true}
                      value={field.value as DateValue}
                      onChange={(newDate) => {
                        field.onChange(newDate);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="flex items-center gap-4">
            <FormField
              control={form.control}
              name="valid_to"
              render={({ field }) => (
                <FormItem className="w-1/2">
                  <FormLabel>
                    {t("resourceLibrary.validTo")}{" "}
                    <span className="text-red-700">*</span>
                  </FormLabel>
                  <FormControl>
                    <DateTimePicker
                      granularity={"minute"}
                      hideTimeZone={true}
                      value={field.value as DateValue}
                      onChange={(newDate) => {
                        field.onChange(newDate);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  {t("resourceLibrary.description")}{" "}
                  <span className="text-red-700">*</span>
                </FormLabel>
                <FormControl>
                  <Textarea
                    placeholder={t("resourceLibrary.description")}
                    autoComplete="off"
                    maxLength={100}
                    {...field}
                    onChange={handleDescriptionChange}
                  />
                </FormControl>
                <FormDescription></FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <ModalButton
            closeDialog={handleCancelClick}
            closeLabel={t("buttons.close")}
            submitLabel={t("buttons.submit")}
          />
        </form>
      </Form>
    </div>
  );
}
