"use client";

import type {
  // Column,
  ColumnDef,
  Row,
} from "@tanstack/react-table";
// import { ArrowUpDown } from "lucide-react";
// import { Button } from "@/components/ui/button";
import moment from "moment";
import React from "react";
import type { Enrollment } from "@/types";

interface RowDefinition {
  row: Row<Enrollment>;
}

// interface ColumnDefinition {
//   column: Column<Enrollment, unknown>;
// }
export const getColumns = (
  t: (key: string) => string
): ColumnDef<Enrollment>[] => [
  {
    accessorKey: "first_name",
    header: t("enrollment.name"),
    // header: ({ column }: ColumnDefinition): React.JSX.Element => {
    //   return (
    //     <Button
    //       className="px-0"
    //       variant="ghost"
    //       onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
    //     >
    //       Name
    //       <ArrowUpDown className="ml-2 h-4 w-4" />
    //     </Button>
    //   );
    // },
    cell: ({ row }: RowDefinition): React.JSX.Element => {
      const firstName = row.original.first_name ?? " ";
      const lastName = row.original.last_name ?? " ";
      return <div className="text-align">{`${firstName} ${lastName}`}</div>;
    },
  },
  // {
  //   accessorKey: "last_name",
  //   header: "Last Name",
  //   cell: ({ row }: RowDefinition): React.JSX.Element => (
  //     <div>{row.original.last_name}</div>
  //   ),
  // },
  {
    accessorKey: "email",
    header: t("enrollment.email"),
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div>{row.original.email}</div>
    ),
  },
  {
    accessorKey: "enrolled_date",
    header: t("enrollment.enrolledDate"),
    cell: ({ row }: RowDefinition): React.JSX.Element => {
      const formattedDate = moment
        .utc(row.original.enrolled_date)
        .local()
        .format("DD-MMM-YYYY hh:mm a");
      return <div>{formattedDate}</div>;
    },
  },
];
