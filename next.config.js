/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "kfpwszxuivydjftikxts.supabase.co",
      },
      {
        protocol: "https",
        hostname: "tsghdsndkborjlbxzfyb.supabase.co",
      },
      {
        protocol: "https",
        hostname: "vwzvqyvnuxpfiosqmeps.supabase.co",
      },
      {
        protocol: "https",
        hostname: "adgglpxysanovczgltzl.supabase.co",
      },
      {
        protocol: "https",
        hostname: "ozuhxwfkkwhpmgliseia.supabase.co",
      },
      {
        protocol: "https",
        hostname: "yetumgowzgjxppvgncbm.supabase.co",
      },
     
      
    ]
  },
  webpack: (config) => {
    config.module.rules.push({
      test: /\.node/,
      use: "raw-loader",
    });
    config.resolve.alias.canvas = false
    config.resolve.alias.encoding = false
    return config;
  },
};
module.exports = nextConfig;
