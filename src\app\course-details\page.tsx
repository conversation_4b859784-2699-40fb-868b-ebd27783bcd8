"use client";

import { DataTable } from "@/components/ui/data-table/data-table";
import { getResourceColumns } from "./coloumn";

//import courseDetailsData from "./courseDetailsData";
import MainLayout from "../layout/mainlayout";
import { Button } from "@/components/ui/button";
//import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Modal } from "@/components/ui/modal";
import { CourseVideoForm } from "@/app/course-details/course-video-form";
import React, { useEffect, useState } from "react";
import moment from "moment";
import { ORG_KEY, pageUrl, privilegeData } from "@/lib/constants";
import { PlusIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import { Editor } from "primereact/editor";
import useCourse from "@/hooks/useCourse";
import { useSearchParams } from "next/navigation";
import type {
  CourseDetailsRequest,
  CourseDetailsResultType,
  CourseDetailsValueType,
  // SectionItem,
} from "@/types";
import getPrivilegeList from "@/hooks/useCheckPrivilege";
import Link from "next/link";
import { useTranslation } from "react-i18next";
export default function ViewCourse(): JSX.Element {
  const { t } = useTranslation();
  const resourceColumns = getResourceColumns(t);
  const [courseDetailsData, setCourseDetailsData] =
    React.useState<CourseDetailsValueType | null>();
  const searchParams = useSearchParams();
  const courseId = searchParams.get("courseId");
  localStorage.setItem("courseId", courseId ?? "");
  const expiry = searchParams.get("expiry");
  localStorage.setItem("expiryCourse", expiry ?? "");
  const is_premium = searchParams.get("is_premium");
  const { courseDetails } = useCourse();
  const disableEdit = getPrivilegeList(
    "Course",
    privilegeData.Course.updateCourse,
  );
  const router = useRouter();
  const [categoryId, setCategoryId] = React.useState("");
  const [isDialogOpen, setIsDialogOpen] = React.useState(false);
  const [courseDuration, setCourseDuration] = useState(0);

  const disableBtn = getPrivilegeList(
    "Course_Resource",
    privilegeData.Course_Resource.addResource,
  );
  const enableExpiry = localStorage?.getItem("expiryCourse") ?? true;
  const Expiry = !(enableExpiry == "true" ? true : false);
  const openDialog = (): void => {
    setIsDialogOpen(true);
  };

  const closeDialog = (): void => {
    setIsDialogOpen(false);
  };
  // const allCourseResources = courseDetailsData.reduce((accumulator, item) => {
  //   return accumulator.concat(item.course_resources);
  // }, []);

  // const allCourseModules = courseDetailsData.reduce((accumulator, item) => {
  //   return accumulator.concat(item.modules);
  // }, []);

  const customButton = (
    <Button onClick={closeDialog} className="primary">
      {t("buttons.delete")}
    </Button>
  );

  // const handleDetailViewModule = (sectionId: SectionItem): void => {
  //   router.push(
  //     `${pageUrl.resourceListView}?sectionId=${sectionId.section_id}&&expiry=${expiry}&&courseId=${courseId}&&is_premium=${is_premium}`,
  //   );
  // };
  const editClickHandler = (): void => {
    router.push(
      `${pageUrl.editCourse}?courseId=${courseId}&categoryId=${categoryId}&&expiry=${expiry}&&is_premium=${is_premium}`,
    );
  };
  useEffect(() => {
    const fetchData = async (): Promise<void> => {
      const orgId = localStorage.getItem(ORG_KEY);
      const reqParams: CourseDetailsRequest = {
        course_id: courseId as string,
        org_id: orgId ?? "",
      };
      try {
        const response = await courseDetails(reqParams);
        setCategoryId(response[0].category_id);
        setCourseDetailsData(response[0] as CourseDetailsResultType);
        const duration = moment(response[0].end_date).diff(
          response[0].start_date,
          "days",
        );
        setCourseDuration(duration);
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    void fetchData();
  }, [courseId, isDialogOpen]);
  return (
    <MainLayout>
      <h1 className="text-2xl font-semibold tracking-tight">
        {t("courses.courseModule.courseDetails")}
      </h1>
      <div className="border-none shadow-none  pt-4 lg:pt-4  max-w-full">
        <div className="bg-white p-4 lg:p-4 shadow-md rounded-md">
          <div className="flex justify-between">
            <div className="flex items-center space-x-4">
              <h2 className="text-3xl font-semibold text-gray-900 my-4 lg:my-0">
                {courseDetailsData?.full_name ?? ""}
                {/* -{" "}
    <span className="text-2xl">(History) </span> */}
              </h2>
              <h3 className="text-2xl font-semibold text-[#FB8500]">
                [{courseDetailsData?.short_name ?? ""}]
              </h3>
            </div>
          </div>
          <div className="mt-4">
            <div className="flex flex-col lg:flex-row text-gray-700 justify-between">
              <div className="mb-4 lg:mb-0 mr-8">
                <div className="font-semibold">
                  {t("courses.courseModule.from")}
                </div>
                <div className="text-[#00AFBB]">
                  {" "}
                  {moment(courseDetailsData?.start_date).format("DD")}&nbsp;
                  <span className="uppercase">
                    {moment(courseDetailsData?.start_date).format("MMM")}
                  </span>
                  &nbsp;
                  {moment(courseDetailsData?.start_date).format("YY")}{" "}
                </div>
                <div className="text-[#00AFBB]">
                  {" "}
                  {moment(courseDetailsData?.start_date).format("hh:mm A")}
                </div>
              </div>
              <div className="mb-4 lg:mb-0 mr-8">
                <div className="font-semibold">To</div>
                <div className="text-[#00AFBB]">
                  {" "}
                  {moment(courseDetailsData?.end_date).format("DD")}&nbsp;
                  <span className="uppercase">
                    {moment(courseDetailsData?.end_date).format("MMM")}
                  </span>
                  &nbsp;
                  {moment(courseDetailsData?.end_date).format("YY")}{" "}
                </div>
                <div className="text-[#00AFBB]">
                  {" "}
                  {moment(courseDetailsData?.end_date).format("hh:mm A")}
                </div>
              </div>
              <div className="mb-4 lg:mb-0 mr-8">
                <div className="font-semibold">{t("courses.description")}</div>
                <div className="text-[#00AFBB]">
                  {courseDuration} {t("courses.courseModule.days")}
                </div>
              </div>
              {/* <div className="mb-4 lg:mb-0">
                <div className="font-semibold">Like</div>
                <div>125</div>
              </div> */}

              <div className="float-right">
                {disableEdit && (
                  <Button className="bg-[#155264]" onClick={editClickHandler}>
                    {t("buttons.edit")}
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="border-none shadow-none mx-auto pt-4  max-w-full bg-['#fff]">
        <div className="pb-4">
          <h3>
            <b>{t("courses.description")}</b>
          </h3>
        </div>
        <Editor
          value={courseDetailsData?.summary}
          style={{ height: "320px", background: "#fff" }}
          readOnly={true}
          className="bg-['#FFF']"
        />
      </div>
      <div className="lg:pt-4 pt-8 ">
        <Tabs defaultValue="account" className="grid pt-8 bg-white rounded-md ">
          <TabsList className="mx-auto grid grid-cols-2 bg-secondary  ml-4">
            <TabsTrigger value="account">
              {t("courses.courseModule.resources")}
            </TabsTrigger>
            <TabsTrigger value="password">{t("courses.modules")}</TabsTrigger>
          </TabsList>
          <TabsContent value="account">
            <div className=" pl-4 pb-4 pr-4 shadow-md rounded-md">
              {disableBtn && Expiry && (
                <Button
                  onClick={openDialog}
                  className="primary float-right bg-[#fb8500] hover:bg-[#fb5c00]"
                >
                  <PlusIcon className="h-5 w-5" />{" "}
                  {t("courses.courseModule.addResource")}
                </Button>
              )}
              <div>
                <DataTable
                  columns={resourceColumns}
                  data={courseDetailsData?.course_resources ?? []}
                  FilterLabel={t("courses.courseModule.filterByResourceName")}
                  FilterBy={"name"}
                  actions={
                    [
                      // {
                      //   title: "View",
                      //   icon: PlaySquareIcon,
                      //   varient: "icon",
                      //   handleClick: openDialog,
                      // },
                    ]
                  }
                  onSelectedDataChange={() => {}}
                />
              </div>
            </div>
          </TabsContent>
          <TabsContent value="password">
            <div className="bg-white pl-4 pb-4 pr-4 shadow-md rounded-md ">
              {/* <DataTable
                columns={moduleColumns}
                data={courseDetailsData?.sections ?? []}
                FilterLabel={"Filter by module name"}
                FilterBy={"name"}
                actions={[
                  {
                    title: "View",
                    icon: Eye,
                    varient: "icon",
                    isEnable: getPrivilegeList(
                      "Course_Resource",
                      privilegeData.Course_Resource.getResourceList,
                    ),
                    handleClick: (val: unknown) => {
                      handleDetailViewModule(val as SectionItem);
                    },
                  },
                ]}
              /> */}
            </div>
          </TabsContent>
        </Tabs>
        <div className="flex items-center justify-end pt-5">
          <Link href={pageUrl.courseList}>
            <Button type="button" className="bg-[#33363F]">
              {t("buttons.cancel")}
            </Button>
          </Link>
        </div>
      </div>
      {isDialogOpen && (
        <Modal
          title={t("courses.courseModule.addCourseVideo")}
          header=""
          openDialog={isDialogOpen}
          closeDialog={closeDialog}
          footer={customButton}
        >
          <CourseVideoForm closeDialog={() => closeDialog()} />
        </Modal>
      )}
    </MainLayout>
  );
}
