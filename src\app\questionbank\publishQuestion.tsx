import React from "react";
import type { ErrorCatch, LogUserActivityRequest, ToastType } from "@/types";
import { Button } from "@/components/ui/button";
import useQuestionBank from "@/hooks/useQuestionBank";
import { useToast } from "@/components/ui/use-toast";
import { useRouter } from "next/navigation";
import { ORG_KEY } from "@/lib/constants";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";

export default function PublishQuestions({
  onSave,
  onCancel,
  questionId,
  publishStatus,
}: {
  onSave: (value: boolean) => void;
  onCancel: () => void;
  questionId: string | undefined;
  publishStatus: string;
}): React.JSX.Element {
  const { t } = useTranslation();
  const router = useRouter();
  const { toast } = useToast() as ToastType;
  const { publishQuestion } = useQuestionBank();
  const { updateUserActivity } = useLogUserActivity();

  const handlePublishClick = (): void => {
    void handleToastSave();
    onCancel();
  };

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  const handleToastSave = async (): Promise<void> => {
    const params = {
      question_id: questionId,
      org_id: localStorage.getItem(ORG_KEY) ?? "",
      status: publishStatus === "Published" ? "Draft" : "Published",
    };
    try {
      const result = await publishQuestion(params);
      if (result.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.questionPublished"),
          description: t("successMessages.questionPublishDesc"),
        });
        onSave(true);
        const params = {
          activity_type: "Question_Bank",
          screen_name: "Question_Bank",
          action_details: `${
            publishStatus === "Published" ? "Draft" : "Published"
          } the exam successfully`,
          target_id: questionId as string,
          log_result: "SUCCESS",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
        router.push("/questionbank");
      } else if (result.status === "error") {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: result?.status,
        });
        const params = {
          activity_type: "Question_Bank",
          screen_name: "Question_Bank",
          action_details: `Failed to ${
            publishStatus === "Published" ? "Draft" : "Published"
          } the exam`,
          target_id: questionId as string,
          log_result: "ERROR",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
      console.error("An unexpected error occurred:", error);
    }
  };
  return (
    <>
      <div className="mb-2 mr-4">
        {publishStatus === "Published" ? (
          <p className="ml-0 ">{t("questionBank.draftPrompt")}</p>
        ) : (
          <p className="ml-0 ">{t("questionBank.publishPrompt")}</p>
        )}
      </div>
      <div className="flex topic-icons pr-4">
        <div className="flex items-center justify-center float-right space-x-2">
          <Button type="button" className="bg-[#33363F]" onClick={onCancel}>
            {t("buttons.cancel")}
          </Button>
          &nbsp;
          <Button
            type="submit"
            className="bg-[#9FC089]"
            onClick={handlePublishClick}
          >
            {t("buttons.submit")}
          </Button>
        </div>
      </div>
    </>
  );
}
