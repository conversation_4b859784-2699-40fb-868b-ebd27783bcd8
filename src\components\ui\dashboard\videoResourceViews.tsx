import UserSessionDetails from "@/components/userSessionDetails/sessionDetails";
import useDashboardStatsViews from "@/hooks/useDashboardStats";
import {
  // CHECKPOINT,
  // CONFIG_VALUE,
  SESSION_FILTER_TYPE_ENROLLED,
  SESSION_FILTER_TYPE_FAILED,
  SESSION_FILTER_TYPE_NOT_WATCHED,
  SESSION_FILTER_TYPE_PASSED,
  SESSION_FILTER_TYPE_PENDING,
  SESSION_FILTER_TYPE_WATCHED,
  SESSION_GRAPH_ENROLLED_INDEX,
  SESSION_GRAPH_FAILED_INDEX,
  SESSION_GRAPH_NOT_WATCHED_INDEX,
  SESSION_GRAPH_PASSED_INDEX,
  SESSION_GRAPH_PENDING_INDEX,
  SESSION_GRAPH_WATCHED_INDEX,
  SESSION_LABEL_ENROLLED,
  SESSION_LABEL_FAILED,
  SESS<PERSON>_LABEL_NOT_WATCHED,
  SESSION_LABEL_PASSED,
  SESSION_LABEL_PENDING,
  SESSION_LABEL_WATCHED,
} from "@/lib/constants";
import type {
  CheckpointsCourseStats,
  CheckpointsCourseStatsReturnType,
} from "@/types";
import type { ActiveElement, ChartEvent } from "chart.js/auto";
import { List } from "lucide-react";
import React, { useEffect, useState } from "react";
import { Bar } from "react-chartjs-2";
import { Card, CardContent, CardHeader, CardTitle } from "../card";
import { Modal } from "../modal";
import { Spinner } from "../progressiveLoader";
import { useTranslation } from "react-i18next";

interface ResourceViewsProps {
  orgId: string;
  configurationValue: string;
}

export function DBVideoResourceViews({
  orgId,
  configurationValue,
}: ResourceViewsProps): React.JSX.Element {
  const { t } = useTranslation();
  const { getCourseWiseStatistics } = useDashboardStatsViews();
  const [checkpointsCourseStats, setcheckpointsCourseStats] =
    useState<CheckpointsCourseStatsReturnType>();
  const [isDialogOpen, setIsDialogOpen] = React.useState(false);
  const [isLoading, setIsLoading] = React.useState<boolean>(true);
  const [filterType, setFilterType] = useState("");
  const [filterTypeLabel, setFilterTypeLabel] = useState("");
  const [selectedSessionCourseId, setSessionCourseId] = useState("");
  const [selectedSessionCourseName, setSessionCourseName] = useState("");
  const [courseStatistics, setCourseStatistics] = useState<
    CheckpointsCourseStats[]
  >([]);
  const [isVisible, setIsVisible] = useState<boolean>(false);

  useEffect(() => {
    // const configValue = localStorage.getItem(CONFIG_VALUE);

    // if (configurationValue !== null) {
    //   if (configurationValue === CHECKPOINT) {
    //     setIsVisible(true);
    //   }
    // }
    setIsVisible(true);
    const fetchDashboardDataUserStats = async (): Promise<void> => {
      setIsLoading(true);
      const courseLabels: string[] = [];
      const enrolled: number[] = [];
      const watched: number[] = [];
      const passed: number[] = [];
      const failed: number[] = [];
      const pending: number[] = [];
      const unwatched: number[] = [];

      let checkpointsCourseStats: CheckpointsCourseStats[] = [];
      if (orgId !== null && orgId !== "") {
        checkpointsCourseStats = await getCourseWiseStatistics(orgId, null);
      }

      if (checkpointsCourseStats.length > 0) {
        setCourseStatistics(checkpointsCourseStats as CheckpointsCourseStats[]);

        checkpointsCourseStats.slice(0, 6).map((item) => {
          courseLabels.push(item.course_short_name);
          item.enrolled_users > 0
            ? enrolled.push(item.enrolled_users)
            : enrolled.push(0);
          item.watched_users > 0
            ? watched.push(item.watched_users)
            : watched.push(0);
          item.passed_users > 0
            ? passed.push(item.passed_users)
            : passed.push(0);
          item.failed_users > 0
            ? failed.push(item.failed_users)
            : failed.push(0);
          item.pending_users > 0
            ? pending.push(item.pending_users)
            : pending.push(0);
          item.unwatched_users > 0
            ? unwatched.push(item.unwatched_users)
            : unwatched.push(0);
        });
      }

      const graphData = {
        labels: courseLabels,
        datasets: [
          {
            data: enrolled,
            label: t("dashboard.courseWiseStatistics.enrolled"),
            // borderColor: "wheat",
            backgroundColor: "#0000FF",
            borderWidth: 2,
          },
          {
            data: watched,
            label: SESSION_FILTER_TYPE_WATCHED,
            // borderColor: "wheat",
            backgroundColor: "#9bbb5c",
            borderWidth: 2,
          },
          {
            data: unwatched,
            label: SESSION_FILTER_TYPE_NOT_WATCHED,
            // borderColor: "wheat",
            backgroundColor: "#f29b26",
            borderWidth: 2,
          },

          {
            data: passed,
            label: SESSION_FILTER_TYPE_PASSED,
            // borderColor: "wheat",
            backgroundColor: "#008000",
            borderWidth: 2,
          },
          {
            data: failed,
            label: SESSION_FILTER_TYPE_FAILED,
            // borderColor: "wheat",
            backgroundColor: "#bd392f",
            borderWidth: 2,
          },
          {
            data: pending,
            label: SESSION_FILTER_TYPE_PENDING,
            // borderColor: "wheat",
            backgroundColor: "#756544",
            borderWidth: 2,
          },
        ],
      };

      setcheckpointsCourseStats(graphData as CheckpointsCourseStatsReturnType);
      setIsLoading(false);
    };

    fetchDashboardDataUserStats()
      .then((response) => {
        console.log(response);
      })
      .catch((error) => {
        console.log(error);
      });
  }, [orgId, configurationValue]);

  const getSessionDetails = (
    event: ChartEvent,
    element: ActiveElement[],
  ): void => {
    // Log or use the index as needed
    if (element?.length > 0) {
      const courseId: string = courseStatistics[element[0].index].course_id;
      setSessionCourseId(courseId);
      setSessionCourseName(
        courseStatistics[element[0].index].course_short_name,
      );
      if (element[0].datasetIndex == SESSION_GRAPH_ENROLLED_INDEX) {
        setFilterType(SESSION_FILTER_TYPE_ENROLLED);
        setFilterTypeLabel("Report : " + SESSION_LABEL_ENROLLED);
      } else if (element[0].datasetIndex == SESSION_GRAPH_WATCHED_INDEX) {
        setFilterType(SESSION_FILTER_TYPE_WATCHED);
        setFilterTypeLabel("Report : " + SESSION_LABEL_WATCHED);
      } else if (element[0].datasetIndex == SESSION_GRAPH_NOT_WATCHED_INDEX) {
        setFilterType(SESSION_FILTER_TYPE_NOT_WATCHED);
        setFilterTypeLabel("Report : " + SESSION_LABEL_NOT_WATCHED);
      } else if (element[0].datasetIndex == SESSION_GRAPH_PASSED_INDEX) {
        setFilterType(SESSION_FILTER_TYPE_PASSED);
        setFilterTypeLabel("Report : " + SESSION_LABEL_PASSED);
      } else if (element[0].datasetIndex == SESSION_GRAPH_FAILED_INDEX) {
        setFilterType(SESSION_FILTER_TYPE_FAILED);
        setFilterTypeLabel("Report : " + SESSION_LABEL_FAILED);
      } else if (element[0].datasetIndex == SESSION_GRAPH_PENDING_INDEX) {
        setFilterType(SESSION_FILTER_TYPE_PENDING);
        setFilterTypeLabel("Report : " + SESSION_LABEL_PENDING);
      }
      // openDialog();
    }
  };

  // const openDialog = (): void => {
  //   setIsDialogOpen(true);
  // };

  const closeDialog = (value: boolean): void => {
    console.log(value);
    setIsDialogOpen(false);
  };

  return (
    <>
      {isVisible && (
        <Card className="bg-white border-none">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-lg font-medium">
              {String(t("dashboard.courseWiseStatistics.title"))}
            </CardTitle>
            <List
              color=""
              className="h-8 w-8 mr-2 text-accent-foreground/50"
              aria-hidden="true"
            />
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Spinner />
            ) : (
              <div className="mt-4 ">
                <div className="w-full bg-white rounded-md duration-500 ">
                  {checkpointsCourseStats !== null &&
                  checkpointsCourseStats !== undefined ? (
                    <Bar
                      data={
                        checkpointsCourseStats as CheckpointsCourseStatsReturnType
                      }
                      height={300}
                      options={{
                        maintainAspectRatio: false,

                        scales: {
                          y: {
                            ticks: {
                              padding: 50,
                            },
                            title: {
                              display: true,
                              text: t(
                                "dashboard.courseWiseStatistics.userCount",
                              ),
                              padding: { top: 20, bottom: -30 },
                            },
                          },
                        },
                        plugins: {
                          datalabels: {
                            display: function (context) {
                              return (
                                context.dataset.data[context.dataIndex] !== 0
                              ); // or >= 1 or ...
                            },
                            color: "black",
                            formatter: Math.round,
                            anchor: "end",
                            offset: -20,
                            align: "start",
                          },
                          legend: {
                            labels: {},
                            align: "center",
                            position: "bottom",
                          },
                        },
                        onHover: function (event) {
                          const target = event.native?.target;
                          if (target instanceof HTMLElement) {
                            target.style.cursor = "pointer";
                          }
                        },
                        onClick: getSessionDetails,
                      }}
                    />
                  ) : (
                    ""
                  )}
                </div>
                {isDialogOpen && (
                  <Modal
                    title="User Session Report"
                    header={filterTypeLabel}
                    openDialog={isDialogOpen}
                    closeDialog={closeDialog}
                    type="max-w-7xl"
                  >
                    <UserSessionDetails
                      closeDialog={(value: boolean) => closeDialog(value)}
                      courseid={selectedSessionCourseId}
                      filterType={filterType}
                      filterTypeLabel={filterTypeLabel}
                      courseTitle={selectedSessionCourseName}
                    />
                  </Modal>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </>
  );
}
