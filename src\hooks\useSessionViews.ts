import { supabase } from "../lib/client";
import { views } from "../lib/apiConfig";
import type {
  CheckpointsUserStatsReturnType,
  SessionViewsResultType,
} from "@/types";

interface UseSessionViewsReturn {
  getSessionViews: (
    courseId?: string | null,
    courseModuleId?: string | null,
    userId?: string | null,
  ) => Promise<SessionViewsResultType>;
  getSessionUsersDetails: (
    orgId?: string | null,
    courseId?: string | null,
    userId?: string | null,
    filterType?: string | null,
  ) => Promise<CheckpointsUserStatsReturnType[]>;
}

const useSessionViews = (): UseSessionViewsReturn => {
  async function getSessionViews(
    courseId?: string | null,
    courseModuleId?: string | null,
    userId?: string | null,
  ): Promise<SessionViewsResultType> {
    try {
      const requestBody = {
        course_id: courseId,
        course_module_id: courseModuleId,
        user_id: userId,
      };
      console.log("payload");
      console.log(requestBody);

      const { data, error } = await supabase.rpc<string, null>(
        views?.sessionViewsReport,
        requestBody,
      );
      if (error) {
        throw new Error(error.details);
      }

      return data as SessionViewsResultType;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function getSessionUsersDetails(
    orgId?: string | null,
    courseId?: string | null,
    userId?: string | null,
    filterType?: string | null,
  ): Promise<CheckpointsUserStatsReturnType[]> {
    try {
      const requestBody = {
        org_id: orgId,
        course_id: courseId,
        user_id: userId,
        filter_type: filterType,
      };

      const { data, error } = await supabase.rpc<string, null>(
        views?.userWiseStatistics,
        requestBody,
      );
      if (error) {
        throw new Error(error.details);
      }

      return data as CheckpointsUserStatsReturnType[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  return {
    getSessionViews,
    getSessionUsersDetails,
  };
};

export default useSessionViews;
