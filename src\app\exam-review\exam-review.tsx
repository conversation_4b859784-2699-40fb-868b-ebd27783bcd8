import React from "react";
import type { reviewAnswersType } from "@/types";
import { Button } from "@/components/ui/button";
import type { examReviewType } from "@/types";
import moment from "moment";
import { useTranslation } from "react-i18next";

const ExamReview = ({
  data,
  onSave,
}: {
  data: examReviewType[];
  onSave: () => void;
  onCancel: () => void;
  isModal?: boolean;
}): React.JSX.Element => {
  const { t } = useTranslation();
  const quizData: examReviewType[] = data;
  const removeHTMLTags = (html: string | undefined): React.JSX.Element => {
    if (html != null) {
      const tempDiv = document.createElement("div");
      tempDiv.innerHTML = html;
      return <div dangerouslySetInnerHTML={{ __html: tempDiv.innerHTML }} />;
    } else {
      return <div></div>;
    }
  };

  function clearExam(): void {
    onSave();
  }

  const InfoLabel = ({
    label,
    value,
  }: {
    label: string;
    value: string | number;
  }): React.JSX.Element => (
    <div style={{ flex: "0 0 48%", marginBottom: "10px" }}>
      <p className="flex items-center">
        {label}: <span style={{ marginLeft: "5px" }}>{value}</span>
      </p>
    </div>
  );
  return (
    <div>
      {quizData?.map((quiz: examReviewType) => (
        <div
          key={""}
          className="border border-solid border-gray-300 rounded-lg p-5 shadow-md"
        >
          <h1 className="text-left text-2xl text-black font-bold mb-8">
            {t("exams.examReview")}
          </h1>
          <h2 className="font-bold text-lg mb-6">{t("exams.quizName")} {quiz.name}</h2>
          <div className="flex flex-wrap justify-between space-y-1">
            <InfoLabel
              label={t("exams.startDate")}
              value={moment(quiz.start_time).format("YYYY-MM-DD HH:mm")}
            />
            <InfoLabel
              label={t("exams.endDate")}
              value={moment(quiz.end_time).format("YYYY-MM-DD HH:mm")}
            />
            <InfoLabel label={t("exams.totalMarks")} value={quiz.total_mark} />
            <InfoLabel label={t("exams.scoredMark")} value={quiz.scored_mark} />
            <InfoLabel label={t("exams.passMark")} value={quiz.pass_mark} />
            <InfoLabel label={t("exams.examDuration")} value={`${quiz.duration} minutes`} />
          </div>

          <div className="mt-5 border border-solid border-gray-300 rounded-lg p-15 bg-white pl-5">
            <h3 className="mb-4 text-left text-gray-800 text-xl font-bold pt-5">
            {t("exams.questions")}
            </h3>
            {quiz.quest_answers?.map((question, index) => (
              <div key={question.question_id} style={{ marginBottom: "15px" }}>
                <p className="mb-5 flex items-start text-gray-500">
                  <span className="font-bold mr-5 text-base">{index + 1}.</span>
                  {removeHTMLTags(question.question_text)}
                </p>
                <p style={{ fontWeight: "bold", color: "#333" }}>
                {t("exams.selectedAns")}
                  {question.selected_answer_ids.length > 0
                    ? question.selected_answer_ids
                        .map((selectedAnswerId) => {
                          const selectedAnswer = question.answers?.find(
                            (answer: reviewAnswersType) =>
                              answer.answer_id === selectedAnswerId,
                          );

                          return selectedAnswer
                            ? selectedAnswer.answer
                            : "Unknown Answer";
                        })
                        .join(", ")
                    : t("exams.notAns")}
                </p>
              </div>
            ))}
          </div>
          <div className="mt-6 flex items-center justify-end gap-x-6">
            <Button type="button" onClick={clearExam} className="bg-[#9FC089]">
              {t("exams.finish")}
            </Button>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ExamReview;
