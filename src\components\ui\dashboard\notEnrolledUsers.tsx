import React, { useEffect, useState } from "react";
import type { UsersNotEnrolled } from "@/types";
import { DataTable } from "../data-table/data-table";
import { getColumns } from "./not_enrolled.column";
import { UserPlus } from "lucide-react";
import { Modal } from "@/components/ui/modal";
import UserEnrollment from "./userEnrollment";
import { useTranslation } from "react-i18next";

interface NotEnrolledListProps {
  NotEnrolledList: UsersNotEnrolled[];
}

export function NotEnrolledUsers({
  NotEnrolledList,
}: NotEnrolledListProps): React.JSX.Element {
  const { t } = useTranslation();
  const columns = getColumns(t);
  const [userList, setUserList] = useState<UsersNotEnrolled[]>([]);
  const [isOpenModal, setIsOpenModal] = useState<boolean>(false);
  const [enrollData, setUserEnrollData] = useState<UsersNotEnrolled>();

  useEffect(() => {
    setUserList(NotEnrolledList);
  }, [NotEnrolledList]);

  const EnrollUser = (user: UsersNotEnrolled): void => {
    setUserEnrollData(user);
    setIsOpenModal(true);
  };

  const cancelModal = (): void => {
    setIsOpenModal(false);
  };

  const handleUserRemoval = (userId: string): void => {
    setUserList((prevList) => prevList.filter((user) => user.id !== userId));
  };

  return (
    <div>
      <div
        className=" rounded-lg shadow-lg bg-white relative overflow-hidden "
        style={{ height: "450px", overflow: "auto" }}
      >
        <div className="p-2 flex justify-between items-center dashboard-session text-black rounded-t-lg">
          <p className="text-md font-semibold">
            {String(t("dashboard.newRegistrations.title"))}
          </p>
        </div>

        {/* Content */}
        <div className="p-2 rounded-md">
          <div className="overflow-x-auto border rounded-md p-2 ">
            <DataTable
              columns={columns}
              data={userList}
              FilterLabel={t("dashboard.newRegistrations.filterByName")}
              FilterBy="first_name"
              disableIcon="hideIcon"
              actions={[
                {
                  title: t("dashboard.newRegistrations.enroll"),
                  icon: UserPlus,
                  color: "#117a65",
                  varient: "icon",
                  isEnable: true,
                  handleClick: (val) =>
                    EnrollUser(val as unknown as UsersNotEnrolled),
                },
              ]}
              onSelectedDataChange={() => {}}
            />
          </div>
        </div>

        {/* Footer */}
        {/* <div className="px-6 py-4 border-t bg-gradient-to-r from-teal-100 to-blue-100 rounded-b-lg">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-700">
                Total Users: {userList.length}
              </span>
              <button
                className="px-4 py-2 bg-gradient-to-r from-teal-500 to-blue-500 text-white rounded-md hover:scale-105 transition-all duration-300 flex items-center gap-2 shadow-md"
              >
                <Download className="h-4 w-4" />
                Export Data
              </button>
            </div>
          </div> */}
      </div>

      {/* Modal */}
      {isOpenModal && (
        <Modal
          title=""
          header=""
          openDialog={isOpenModal}
          closeDialog={cancelModal}
          type="max-w-3xl"
        >
          <UserEnrollment
            onCancel={cancelModal}
            enrollData={enrollData as UsersNotEnrolled}
            onUserEnroll={handleUserRemoval}
          />
        </Modal>
      )}
    </div>
  );
}
