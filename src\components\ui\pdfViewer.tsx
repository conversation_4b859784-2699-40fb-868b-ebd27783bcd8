"use client";
import { <PERSON><PERSON>, Worker } from "@react-pdf-viewer/core";
import "@react-pdf-viewer/core/lib/styles/index.css";
import { defaultLayoutPlugin } from "@react-pdf-viewer/default-layout";
import "@react-pdf-viewer/default-layout/lib/styles/index.css";
import type { fileDataType } from "@/types";
import { workerurl } from "@/lib/constants";
//import { useEffect, useState } from "react";
export const PdfViewer: React.FC<fileDataType> = (props): React.JSX.Element => {
  // const [pdfUrl, setPdfUrl] = useState<string | Uint8Array>("");
  // useEffect(() => {
  //   // Fetch the PDF URL from your proxy route or an external source
  //   fetch(props.url)
  //     .then((response) => {
  //       if (response.ok) {
  //         // Assuming the response contains the PDF URL
  //         return response.text();
  //       } else {
  //         throw new Error("Failed to fetch PDF URL");
  //       }
  //     })
  //     .then((pdfUrl) => {
  //       console.log("pdfUrl", pdfUrl);

  //       setPdfUrl(pdfUrl);
  //     })
  //     .catch((error) => {
  //       console.error("Error fetching PDF URL:", error);
  //       // router.push('/error'); // Redirect to an error page on failure
  //     });
  // }, [props]);
  const defaultLayoutPluginInstance = defaultLayoutPlugin();
  return (
    <>
      <Worker workerUrl={workerurl.url}>
        <Viewer fileUrl={props.url} plugins={[defaultLayoutPluginInstance]} />
      </Worker>
    </>
  );
};
