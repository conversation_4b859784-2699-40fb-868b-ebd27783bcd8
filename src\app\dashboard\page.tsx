"use client";
import { DBCheckpointsViewGraph } from "@/components/ui/dashboard/checkpointsViewChart";
import { DBSataStats } from "@/components/ui/dashboard/dbStatsComponent";
import { DBVideoCounts } from "@/components/ui/dashboard/dbVideosCount";
import { DBModuleSessionProgress } from "@/components/ui/dashboard/moduleSessionProgress";
import { DBMostWatcheduser } from "@/components/ui/dashboard/mostWatchedUser";
import { DBSessionViews } from "@/components/ui/dashboard/sessionViewDetails";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import useDashboardStatsViews from "@/hooks/useDashboardStats";
import useUsers from "@/hooks/useUsers";
import {
  API_RESPONSE_SUCCESS,
  CHECKPOINT,
  CONFIG_VALUE,
  DEVELOPMENT,
  ORG_KEY,
} from "@/lib/constants";
import type {
  CheckpointsprogressReturnType,
  CheckpointsCountReturnType,
  ComboData,
  DashboardStatsReturnType,
  SessionViewsTableType,
  CheckpointProgress,
  CourseProgressRequest,
  CourseProgressResult,
  UsersNotEnrolled,
  StatisticList,
  ToastType,
  ErrorCatch,
} from "@/types";
import * as React from "react";
import { useEffect, useState } from "react";
import MainLayout from "../layout/mainlayout";
import {
  AtomIcon,
  ComponentIcon,
  PlayIcon,
  ShieldQuestionIcon,
} from "lucide-react";
import Chart from "chart.js/auto";
import ChartDataLabels from "chartjs-plugin-datalabels";
import YoutubeDatas from "@/components/youtubeVideos/youtube-video";
import CurrentAffairsData from "@/components/currentAffairs/current-affairs";
import { Combobox } from "@/components/ui/combobox";
import useCourse from "@/hooks/useCourse";
import LatestEnrollments from "@/components/latestEnrollments/page";
import { DBUserSessionStats } from "@/components/ui/dashboard/dbUserSessionStats";
import { useToast } from "@/components/ui/use-toast";
import UsersList from "@/components/usersList/userDataList";
import "../../styles/main.css";
import { CourseProgressedUsers } from "@/components/ui/dashboard/courseProgressedUsers";
import { NotEnrolledUsers } from "@/components/ui/dashboard/notEnrolledUsers";
import { Paginator, type PaginatorPageChangeEvent } from "primereact/paginator";
import "primereact/resources/themes/lara-light-indigo/theme.css";
import DBCourseResourceViews from "@/components/ui/dashboard/courseResourceViews";
import { Document, Page, Text, View } from "@react-pdf/renderer";
import { pdfStyle } from "@/components/pdfStyle";
import moment from "moment";
import { useTranslation } from "react-i18next";
import { ERROR_MESSAGES } from "@/lib/messages";

export default function Dashboard(): React.JSX.Element {
  const { t } = useTranslation();
  const { toast } = useToast() as ToastType;
  const [selectedOrgId, setSelectedOrgId] = React.useState("");
  const [notEnrolledData, setNotEnrolledData] = React.useState<
    UsersNotEnrolled[]
  >([]);
  const [dbStatsCount, setDbStatsCount] =
    React.useState<DashboardStatsReturnType>();
  const {
    getStatsCount,
    getWatchedUserStatistics,
    getCheckPointsProgressUserWise,
    getCheckPointsCountUserWise,
    getConfigurationData,
    getCourseCompletedProgress,
    getNotEnrolledUsers,
  } = useDashboardStatsViews();
  const { getCourseList } = useCourse();
  const [courseData, setCourseData] = useState<ComboData[]>([]);
  const [watchedUsersData, setWatchedUsersData] = useState<StatisticList[]>([]);
  const [selectedCourseId, setSelectedCourseId] = useState("");
  const [selectedCourseName, setSelectedCourseName] = useState("");
  const [checkpointsProgressDataUser, setCheckpointsProgressDataUser] =
    useState<CheckpointsprogressReturnType>();
  const [checkpointsCountDataUser, setCheckpointsCountDataUser] =
    useState<CheckpointsCountReturnType>();
  const [checkpointSessions, setCheckPointsSessions] = useState<
    SessionViewsTableType[]
  >([]);
  const [userId, setUserId] = useState("");
  const [userName, setUserName] = useState("");
  const { getUsers } = useUsers();
  const [users, setUsers] = useState<{ value: string; label: string }[]>([]);
  const [sessionStatus, setSessionStatus] = useState<boolean>(false);
  const [configurationValue, setConfigValue] = useState<string>("");
  const [isVisible, setIsVisible] = useState<boolean>(false);
  const [progressList, setProgressList] = useState<CourseProgressResult[]>([]);
  const [completedList, setCompletedList] = useState<CourseProgressResult[]>(
    [],
  );
  const [first, setFirst] = useState<number>(0);
  const [rows, setRows] = useState<number>(18);
  const [displayedUsers, setDisplayedUsers] = useState(
    watchedUsersData.slice(first, first + rows),
  );

  Chart.register(ChartDataLabels);

  const bgColors = [
    "rgba(255, 99, 132, 0.2)",
    "rgba(54, 162, 235, 0.2)",
    "rgba(255, 206, 86, 0.2)",
    "rgba(75, 192, 192, 0.2)",
    "rgba(153, 102, 255, 0.2)",
    "rgba(255, 159, 64, 0.2)",
  ];

  const brColors = ["wheat", "wheat", "wheat", "wheat", "wheat", "wheat"];
  const haveSession = (session: boolean): void => {
    setSessionStatus(session);
  };
  const styles = pdfStyle;
  useEffect(() => {
    setDisplayedUsers(watchedUsersData.slice(first, first + rows)); // Slice the data to show based on page
    console.log(displayedUsers);
  }, [watchedUsersData, first, rows]);

  const onPageChange = (event: PaginatorPageChangeEvent): void => {
    setFirst(event.first);
    setRows(event.rows);
  };

  useEffect(() => {
    const fetchOrgDefaults = async (): Promise<void> => {
      const id_org = localStorage.getItem(ORG_KEY);
      const orgId = id_org ?? "";
      setSelectedOrgId(orgId);
      try {
        // get course list and fill in the dropdown
        const courseList = await getCourseList();
        const filteredCourses = courseList
          .filter((course) => course.course_id != null && course.short_name)
          .map((course) => ({
            value: course.course_id,
            label: course.short_name,
          }));

        if (filteredCourses.length > 0) {
          setCourseData(filteredCourses);
        } else {
          setCourseData([]);
        }

        setSelectedCourseId(filteredCourses[0]?.value as string);
        setSelectedCourseName(filteredCourses[0]?.label as string);
        void getCourseCompletedList(filteredCourses[0]?.value as string);
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };
    fetchOrgDefaults()
      .then((response) => {
        console.log(response);
      })
      .catch((error) => {
        console.log(error);
      });

    const getConfigurations = async (): Promise<void> => {
      const id_org = localStorage.getItem(ORG_KEY);
      const params = {
        config_env: DEVELOPMENT,
        org_id: id_org ?? "",
      };
      try {
        const resultData = await getConfigurationData(params);
        if (resultData.status === API_RESPONSE_SUCCESS) {
          const configValue = resultData?.result?.configs?.map(
            (config) => config.config_value,
          );
          setConfigValue(configValue[0]);
          console.log(configurationValue);
          if (configValue[0] === CHECKPOINT) {
            setIsVisible(true);
          }
          localStorage.setItem(CONFIG_VALUE, configValue[0]);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    getConfigurations()
      .then((response) => {
        console.log(response);
      })
      .catch((error) => {
        console.log(error);
      });

    const fetchNotEnrolledList = async (): Promise<void> => {
      const orgId = localStorage.getItem(ORG_KEY) ?? "";
      setSelectedOrgId(orgId);
      try {
        const userList = await getNotEnrolledUsers({ org_id: orgId });
        setNotEnrolledData(userList);
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };
    fetchUsers().catch((error) => console.log(error));
    fetchNotEnrolledList().catch((error) => console.log(error));
  }, []);

  const getCourseCompletedList = async (courseId: string): Promise<void> => {
    setProgressList([]);
    setCompletedList([]);
    const orgId = localStorage.getItem(ORG_KEY);
    try {
      const params: CourseProgressRequest = {
        org_id: orgId as string,
        course_id: courseId,
        user_id: null,
      };

      const resp = await getCourseCompletedProgress(params);

      const filterData = resp.result?.filter(
        (item) => item.total_progress !== null && item.total_progress !== 0,
      );
      const completedData = resp.result?.filter(
        (item) => item.total_progress / item.resource_count === 100,
      );

      setProgressList(filterData);
      setCompletedList(completedData);
    } catch (error: unknown) {
      console.error("Error fetching data:");
    }
  };
  useEffect(() => {
    const fetchDBEntitiesCount = async (orgId: string): Promise<void> => {
      try {
        const prgId = orgId ?? localStorage.getItem(ORG_KEY);
        if (orgId !== null && orgId !== "") {
          const statsCount = await getStatsCount(prgId);
          if (statsCount.status === "success") {
            setDbStatsCount(statsCount);
          }
        }
      } catch (error: unknown) {
        console.error("Error fetching data:");
      }
    };
    fetchDBEntitiesCount(selectedOrgId).catch((error) => console.log(error));
  }, [selectedOrgId]);

  useEffect(() => {
    setWatchedUsersData([]);
    const fetchDashboardData = async (): Promise<void> => {
      if (selectedCourseId !== "") {
        try {
          const watchedUsersList = await getWatchedUserStatistics(
            selectedOrgId,
            selectedCourseId,
          );
          console.log("watchedUsersList", watchedUsersList);

          if (
            watchedUsersList.status === API_RESPONSE_SUCCESS &&
            watchedUsersList?.result?.length > 0
          ) {
            const filteredData = watchedUsersList.result?.filter(
              (item) =>
                item.time_spent !== null && item.time_spent !== "00:00:00",
            );
            setWatchedUsersData(filteredData);
            // setResourceData(watchedUsersList.result);
            console.log("watchedUsersList.result", watchedUsersList.result);
          }

          // const checkpointsProgressList = await getCheckPointsProgress(
          //   selectedOrgId,
          //   selectedCourseId,
          // );

          // if (
          //   checkpointsProgressList.status === API_RESPONSE_SUCCESS &&
          //   checkpointsProgressList?.result?.length > 0
          // ) {
          //   setCheckpointsProgressData(checkpointsProgressList);
          // }

          // const checkpointsCountsList = await getCheckPointsCount(
          //   selectedOrgId,
          //   selectedCourseId,
          // );

          // if (
          //   checkpointsCountsList.status === API_RESPONSE_SUCCESS &&
          //   checkpointsCountsList?.video_check_point?.length > 0
          // ) {
          //   setCheckpointsCountData(checkpointsCountsList);
          // }
          // setisSessionProgressLoading(false);
        } catch (error) {
          console.error("Error fetching data:", error);
        }
      }
      // setisSessionProgressLoading(false);
    };

    fetchDashboardData()
      .then((response) => {
        console.log(response);
      })
      .catch((error) => {
        console.log(error);
      });
  }, [selectedCourseId]);

  useEffect(() => {
    const fetchDashboardDataUserStats = async (): Promise<void> => {
      setCheckpointsProgressDataUser({
        status: "error",
        result: [],
      });
      setCheckpointsCountDataUser({
        status: "error",
        video_check_point: [],
      });

      if (userId !== "") {
        try {
          const checkpointsProgressListUser: CheckpointsprogressReturnType =
            await getCheckPointsProgressUserWise(selectedOrgId, userId);

          if (
            checkpointsProgressListUser.status === API_RESPONSE_SUCCESS &&
            checkpointsProgressListUser?.result?.length > 0
          ) {
            setCheckpointsProgressDataUser(checkpointsProgressListUser);
          }

          const checkpointsCountsListUser = await getCheckPointsCountUserWise(
            selectedOrgId,
            userId,
          );

          if (
            checkpointsCountsListUser.status === API_RESPONSE_SUCCESS &&
            checkpointsCountsListUser?.video_check_point?.length > 0
          ) {
            setCheckpointsCountDataUser(checkpointsCountsListUser);
          }
        } catch (error) {
          console.error("Error fetching data:", error);
        }
      }
    };

    fetchDashboardDataUserStats()
      .then((response) => {
        console.log(response);
      })
      .catch((error) => {
        console.log(error);
      });
  }, [userId]);

  const handleCourseData = (data: string): void => {
    void getCourseCompletedList(data as string);
    setSelectedCourseId("");
    setSelectedCourseId(data);
    const sessionViewsList: SessionViewsTableType[] = [];
    setCheckPointsSessions(sessionViewsList);
    setCheckPointsSessions([]);
    const courseSelected = courseData.filter((course) =>
      course?.value?.includes(data),
    );
    if (courseSelected !== null) {
      setSelectedCourseName(courseSelected[0].label as string);
    }
  };

  const fetchUsers = async (): Promise<void> => {
    try {
      const org_id = localStorage.getItem("orgId");
      const orgID = org_id ?? "";
      const fetchedUsers = await getUsers(orgID);
      const filteredUsers = fetchedUsers
        .filter(
          (user) =>
            user.id !== null &&
            user.first_name !== null &&
            user.last_name !== null,
        )
        .map((user) => ({
          value: user.id,
          label: user.first_name + " " + user.last_name,
        }));
      if (filteredUsers.length > 0) {
        setUsers(filteredUsers);
      } else {
        setUsers([]);
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
    }
  };

  const handleUserData = (data: string): void => {
    setUserId(data);

    const userSelected = users.filter((user) => user.value.includes(data));
    if (userSelected !== null) {
      setUserName(userSelected[0].label);
    }
  };
  const PageToDownload: React.FC<{
    sessionData: SessionViewsTableType[];
  }> = ({ sessionData }) => (
    <Document>
      <Page size="A4" style={styles.page}>
        <View style={styles.section}>
          <View style={styles.header}>
            <Text style={styles.headerText}>
              {t("dashboard.youTubeVideos.sessionDetails")}
            </Text>
          </View>

          <View style={styles.tableContainer}>
            <View style={styles.tableRow}>
              <View style={styles.tableHeader}>
                <Text>{t("dashboard.youTubeVideos.slno")}</Text>
              </View>
              <View style={styles.tableHeader}>
                <Text>{t("dashboard.youTubeVideos.name")}</Text>
              </View>
              <View style={styles.tableHeader}>
                <Text>{t("dashboard.youTubeVideos.attendedExam")}</Text>
              </View>
              <View style={styles.tableHeader}>
                <Text>{t("dashboard.youTubeVideos.sessionEnd")}</Text>
              </View>
              <View style={styles.tableHeader}>
                <Text>{t("dashboard.youTubeVideos.result")}</Text>
              </View>
            </View>
            {sessionData.map((item, index) => (
              <View key={index} style={styles.tableRow}>
                <View style={styles.tableCell}>
                  <Text>{index + 1}</Text>
                </View>
                <View style={styles.tableCell}>
                  <Text>{item.first_name + item.lastname}</Text>
                </View>
                <View style={styles.tableCell}>
                  <Text>{item.quiz_name}</Text>
                </View>

                <View style={styles.tableCell}>
                  <Text>
                    {moment
                      .utc(item.session_end_time ?? "")
                      .local()
                      .format("DD-MMM-YYYY hh:mm a")}
                  </Text>
                </View>
                <View style={styles.tableCell}>
                  <Text>{item.result}</Text>
                </View>
              </View>
            ))}
          </View>
        </View>
      </Page>
    </Document>
  );
  console.log(PageToDownload);
  return (
    <MainLayout>
      <div>
        <section
          id="statsCounts"
          className="w-full mx-auto grid grid-cols-1 lg:grid-cols-4 md:grid-cols-4 justify-items-center justify-center gap-x-6 mb-4"
        >
          <div className="w-full bg-white  rounded-md duration-500 ">
            <a href="/courses">
              <DBSataStats
                module={String(t("dashboard.statusCounts.courses"))}
                total={dbStatsCount?.dashboard_counts?.course_count as number}
                // progress={"+10.1"}
                icon={<AtomIcon color="#006666" />}
                gradientColors={"from-cyan-500 teal-200 - teal-500"}
                trend={"+12% this month"}
              />
            </a>
          </div>
          <div className="w-full bg-white  rounded-md duration-500 border-b-2  ">
            <a href="/youtube-videos">
              <DBSataStats
                module={String(t("dashboard.statusCounts.videos"))}
                total={dbStatsCount?.dashboard_counts?.video_count as number}
                // progress={"+22.1"}
                icon={<PlayIcon color="#ffac37" />}
                gradientColors={"from-orange-400 violet-200 - pink-200"}
                trend={"+8% this month"}
              />
            </a>
          </div>
          <div className="w-full bg-white  rounded-md duration-500 border-b-2 ">
            <a href="/exams">
              <DBSataStats
                module={String(t("dashboard.statusCounts.exams"))}
                total={dbStatsCount?.dashboard_counts?.exam_count as number}
                // progress={"+2.1"}
                icon={<ShieldQuestionIcon color="#ff826a" />}
                gradientColors={"from-rose-400 indigo-400 - cyan-400"}
                trend={"+15% this month"}
              />
            </a>
          </div>
          <div className="w-full bg-white  rounded-md duration-500 border-b-2 ">
            <a href="/exams?variable=Practice">
              <DBSataStats
                module={String(t("dashboard.statusCounts.practice"))}
                total={dbStatsCount?.dashboard_counts?.practice_count as number}
                // progress={"+1.6"}
                icon={<ComponentIcon color="#85909b" />}
                gradientColors={"from-slate-500 cyan-500 - blue-500"}
                trend={"+5% this month"}
              />
            </a>
          </div>
        </section>
        <>
          {!isVisible && (
            <div className="w-full mx-auto grid grid-cols-2 lg:grid-cols-2 md:grid-cols-2 justify-items-center justify-center gap-y-6 gap-x-6 mb-5">
              <section
                id="StatsGraph"
                className="w-full  rounded-md duration-500 gap-y-6  "
              >
                <div className="">
                  <div>
                    <div
                      className="border rounded-lg shadow-lg bg-white relative overflow-hidden"
                      style={{
                        height: "450px",
                        overflow: "auto",
                      }}
                    >
                      <div className="p-2 border-b flex justify-between items-center dashboard-session text-black rounded-t-lg">
                        <h3 className="text-md font-semibold">
                          {String(t("dashboard.youTubeVideos.title"))}
                        </h3>
                      </div>
                      <div className="p-2 bg-white rounded-lg">
                        <YoutubeDatas isDashboard={true} />
                      </div>
                    </div>
                  </div>
                </div>
              </section>
              <section
                id="currentAffairs"
                className="w-full rounded-md duration-500 "
              >
                <div className="">
                  <div>
                    <div
                      className="border rounded-lg shadow-lg bg-white relative overflow-hidden"
                      style={{
                        height: "450px",
                        overflow: "auto",
                      }}
                    >
                      <div className="p-2 border-b flex justify-between items-center dashboard-session text-black rounded-t-lg">
                        <h3 className="text-md font-semibold">
                          {String(t("dashboard.currentaffairs.title"))}
                        </h3>
                      </div>
                      <div className="p-2 bg-white rounded-lg">
                        <CurrentAffairsData isDashboard={true} />
                      </div>
                    </div>
                  </div>
                </div>
              </section>
            </div>
          )}
        </>

        <section
          id="courseUserStats"
          className="w-full mx-auto grid grid-cols-2 lg:grid-cols-2 md:grid-cols-2 justify-items-center justify-center gap-x-6 mb-5"
        >
          <div className="w-full">
            <DBCourseResourceViews orgId={selectedOrgId} />
          </div>
          <div className="w-full  rounded-md duration-500 ">
            {/* <p className="mb-4 text-lg font-medium m-4 ml-4">
              New Registrations
            </p> */}
            <NotEnrolledUsers NotEnrolledList={notEnrolledData} />
          </div>
        </section>

        <>
          {!isVisible && (
            <div className="grid grid-cols-2 lg:grid-cols-2 md:grid-cols-2 gap-x-6 gap-y-6">
              <section
                // id="StatsGraph"
                className="w-full  rounded-md duration-500"
              >
                <div className="">
                  <LatestEnrollments />
                </div>
              </section>
              <section
                // id="currentAffairs"
                className="w-full  rounded-md duration-500 "
              >
                <div className="">
                  <div>
                    <UsersList />
                  </div>
                </div>
              </section>
            </div>
          )}
        </>
        <>
          {isVisible && (
            <section className="w-full mx-auto grid justify-items-center gap-x-6  ">
              <div className="w-full overflow-hidden rounded-md border p-4 text-gray-700 align-right">
                <Tabs defaultValue="course">
                  <TabsList className="w-auto bg-secondary text-xl font-semibold">
                    <TabsTrigger value="course">
                      {String(t("dashboard.courseBasedStatistics"))}
                    </TabsTrigger>
                    <TabsTrigger value="user">
                      {String(t("dashboard.userBasedStatistics"))}
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="user">
                    <div className="w-full rounded-md p-4 border ">
                      {String(t("dashboard.selectUser"))}
                      <div className="w-full bg-white">
                        <Combobox
                          data={users}
                          onSelectChange={handleUserData}
                          placeHolder="Select User"
                          defaultLabel={userName}
                        />
                      </div>
                    </div>
                    {/* <section
                      id="StatsGraph"
                      className="w-full mx-auto grid grid-cols-1 lg:grid-cols-1 md:grid-cols-1 justify-items-center justify-center mb-5"
                    >
                      <div className="w-full bg-white shadow-md rounded-xl duration-500 p-4">
                        <p className="text-xl text-gray-800">
                          Userwise session Details
                        </p>
                        <DBUserSessionDetails
                          courseData={courseData}
                          userId={userId}
                        />
                      </div>
                    </section> */}
                    <section
                      id="courseStats"
                      className="w-full mx-auto grid grid-cols-3 lg:grid-cols-3 md:grid-cols-3 justify-items-center justify-center gap-x-6 mt-6 "
                    >
                      <div className="w-full overflow-hidden rounded-md bg-white text-gray-700 justify-between border ">
                        <div className="dashboard-session text-black p-3">
                          <p className="text-white font-medium">
                            {" "}
                            {String(t("dashboard.Resources"))}
                          </p>
                        </div>
                        <div className="grid grid-cols-2 lg:grid-cols-2 md:grid-cols-2 mb-3 p-2">
                          <p className="text-base text-[#00b9c7]">
                            {String(t("dashboard.courseName"))}
                          </p>
                          <p className="text-base text-right text-[#00b9c7]">
                            {String(t("dashboard.statusCounts.videos"))}
                          </p>
                        </div>
                        <div className="grid p-2 ">
                          {checkpointsCountDataUser?.video_check_point
                            .slice(0, 6)
                            .map((checkPointCount) => (
                              <DBVideoCounts
                                key={checkPointCount.video_id}
                                course_name={checkPointCount.name}
                                category_name={
                                  checkPointCount.course_short_name
                                }
                                video_count={checkPointCount.check_point_count}
                              />
                            ))}
                        </div>
                      </div>
                      <div className="w-full overflow-hidden rounded-md bg-white text-gray-700  justify-between border">
                        <div className="dashboard-session text-black p-3">
                          <p className="text-white font-medium">
                            {String(t("dashboard.checkpointsViewInprogress"))}
                          </p>
                        </div>

                        <div className="grid grid-cols-2 lg:grid-cols-2 md:grid-cols-2 mb-3 p-2">
                          <p className="text-base text-[#00b9c7]">
                            {String(t("dashboard.videoName"))}
                          </p>
                          <p className="text-base text-right text-[#00b9c7]">
                            {String(t("dashboard.checkpointProgressCount"))}
                          </p>
                        </div>
                        <div className="grid p-2">
                          {checkpointsProgressDataUser?.result
                            .slice(0, 6)
                            .map((checkPoint) => (
                              <DBModuleSessionProgress
                                key={checkPoint.resource_id}
                                module_name={checkPoint.resource_name}
                                course_name={checkPoint.course_short_name}
                                checkpoints_count={checkPoint.max_checkpoints}
                                in_progress_count={
                                  checkPoint.progressed_checkpoints
                                }
                                progress_color={"destructive"}
                                bar_color={"accent"}
                              />
                            ))}
                        </div>
                      </div>
                      <div className="w-full overflow-hidden rounded-md bg-white text-gray-700  justify-between border">
                        <div className="dashboard-session text-black p-3">
                          <p className="text-white font-medium">
                            {String(t("dashboard.resourceProgress"))}
                          </p>
                        </div>
                        <DBUserSessionStats
                          key={
                            checkpointsProgressDataUser?.result[0]?.resource_id
                          }
                          userSessionsData={
                            checkpointsProgressDataUser?.result as CheckpointProgress[]
                          }
                        />
                      </div>
                    </section>
                  </TabsContent>
                  <>
                    {isVisible && (
                      <TabsContent value="course">
                        <div className="w-half rounded-md border p-4">
                          {String(t("dashboard.selectCourse"))}
                          <div className="w-full bg-white">
                            <Combobox
                              data={courseData}
                              onSelectChange={handleCourseData}
                              placeHolder="Select Course"
                              defaultLabel={selectedCourseName}
                            />
                          </div>
                        </div>
                        <section
                          id="courseStats"
                          className="w-full grid grid-cols-2 lg:grid-cols-2 md:grid-cols-2 justify-items-center  gap-x-6 mb-5 pt-6"
                        >
                          <div
                            className="w-full   overflow-hidden  rounded-md duration-500  text-gray-700  "
                            style={{ height: "450px" }}
                          >
                            <div className="shadow-lg  relative overflow-hidden">
                              <div className="p-2  flex justify-between items-center dashboard-session text-black rounded-t-lg">
                                <h3 className="text-md font-semibold">
                                  {" "}
                                  {String(
                                    t(
                                      "dashboard.courseStatisticsCompletedUsers",
                                    ),
                                  )}
                                </h3>
                              </div>
                              <div className="">
                                <div
                                  className="grid grid-cols-6 bg-[#FFF]"
                                  style={{ height: "350px" }}
                                >
                                  {completedList?.length > 0 ? (
                                    completedList
                                      .slice(first, first + rows)
                                      .map((user) => (
                                        <DBMostWatcheduser
                                          key={user.user_id}
                                          user_profile_url={
                                            user?.avatar_url != null &&
                                            user.avatar_url.startsWith("http")
                                              ? user.avatar_url
                                              : "/assets/user.png"
                                          }
                                          user_name={user?.user_name}
                                          resources={user?.resources}
                                        />
                                      ))
                                  ) : (
                                    <div
                                      className="col-span-6 text-center flex items-center justify-center bg-white"
                                      style={{ minHeight: "350px" }}
                                    >
                                      {String(t("dashboard.noResultsFound"))}
                                    </div>
                                  )}
                                </div>
                                <div className="">
                                  <Paginator
                                    first={first}
                                    rows={rows}
                                    template="FirstPageLink PrevPageLink CurrentPageReport NextPageLink LastPageLink"
                                    totalRecords={completedList?.length ?? 0}
                                    onPageChange={onPageChange}
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                          <div className="w-full overflow-hidden  rounded-md duration-500  text-gray-700 justify-between">
                            {selectedCourseId !== "" ? (
                              <CourseProgressedUsers
                                completedUsersList={progressList}
                              />
                            ) : null}
                          </div>
                        </section>
                        <section
                          id="StatsGraph"
                          className="w-full mx-auto grid grid-cols-2 lg:grid-cols-2 md:grid-cols-2 justify-items-center justify-center  gap-x-6  mb-5"
                        >
                          <div className="w-full  rounded-md  duration-500">
                            {/* <p className="text-lg font-medium">
                              Checkpoint Views
                            </p> */}

                            {selectedCourseId !== "" ? (
                              <DBCheckpointsViewGraph
                                courseIdSelected={selectedCourseId}
                                checkpoints={[]}
                                backgroundColors={bgColors}
                                borderColors={brColors}
                                handleClick={(
                                  data: SessionViewsTableType[],
                                ): void => {
                                  setCheckPointsSessions(data);
                                }}
                                haveSessionData={haveSession}
                              />
                            ) : null}
                          </div>
                          <div className="w-full rounded-md duration-500 ">
                            <div className="p-2  flex justify-between items-center dashboard-session  text-black rounded-t-lg">
                              <h3 className="text-md font-semibold">
                                {" "}
                                {String(
                                  t("dashboard.sessionDetailAllAttempts"),
                                )}
                              </h3>
                              {/* <PDFDownloadLink
                                document={
                                  <PageToDownload
                                    sessionData={checkpointSessions}
                                  />
                                }
                                fileName="page-to-download.pdf"
                              >
                            
                                {({ loading }: { loading: boolean }) =>
                                  loading ? (
                                    <div>Loading document...</div>
                                  ) : (
                                    <div title="Download PDF">
                                      <Download />
                                    </div>
                                  )
                                }
                              </PDFDownloadLink> */}
                            </div>

                            <div
                              className=" shadow-lg bg-white overflow-hidden"
                              style={{
                                height: "450px",
                                overflow: "auto",
                              }}
                            >
                              {checkpointSessions !== null &&
                              checkpointSessions !== undefined &&
                              checkpointSessions?.length > 0 ? (
                                <DBSessionViews
                                  checkpointSessions={
                                    sessionStatus === true
                                      ? (checkpointSessions as SessionViewsTableType[])
                                      : []
                                  }
                                />
                              ) : (
                                ""
                              )}
                            </div>
                          </div>
                        </section>
                      </TabsContent>
                    )}
                  </>
                </Tabs>
              </div>
            </section>
          )}
        </>
      </div>
    </MainLayout>
  );
}
