"use client";
import { useState, useEffect } from "react";
import { Label } from "@radix-ui/react-label";
import { CourseResourcesAddfile } from "@/components/resources-add-file";
import ResourcesAddPage from "@/components/resources-add-page";
import type {
  ComboData,
  moduleList,
  publicURL,
  ResourceLibraryData,
} from "@/types";
import { Combobox } from "@/components/ui/combobox";
import { uploadTypes } from "@/lib/constants";
import { Modal } from "@/components/ui/modal";
import React from "react";
import UploadResources from "./upload-resources";
import { useTranslation } from "react-i18next";

export default function AddResources({
  onCancel,
  onSave,
}: {
  onCancel: () => void;
  onSave: () => void;
  isModal?: boolean;
}): JSX.Element {
  const { t } = useTranslation();
  const [selLabel, setSelLabel] = useState<string>("");
  const [modules, setModules] = useState<ComboData[]>([]);
  const [selUpload, setSelUpload] = useState("URL");
  const [isDialogOpen, setIsDialogOpen] = React.useState(false);
  const [selectedFileSup, setSelectedFile] = useState<string | null>("");
  useEffect(() => {
    const data = localStorage.getItem("moduleList") as string;
    const displayData = JSON.parse(data) as moduleList[];
    const datas: ComboData[] = displayData
      .map((item: moduleList) => ({
        value: item.id,
        label: item.display_name,
      }))
      .filter((item) => item.label !== "Quiz");
    setModules(datas as ComboData[]);
    setSelLabel(datas[0]?.label as string);
  }, []);

  const onHandleChange = (selectedValue: string): void => {
    const moduleName = modules.find(
      (item) => item.value === selectedValue,
    )?.label;
    setSelLabel(moduleName as string);
  };
  const onHandleUploadChange = (selectedValue: string): void => {
    setSelUpload(selectedValue);
    console.log(selectedValue);
    selectedValue === "2" ? setIsDialogOpen(true) : "";
  };

  const closeModal = (): void => {
    onCancel();
  };
  const closeUpload = (): void => {
    console.log(isDialogOpen);
    setIsDialogOpen(false);
  };

  function onUpload(fileDetails: publicURL): void {
    setIsDialogOpen(false);
    console.log(fileDetails.data.publicUrl);
    setSelectedFile(fileDetails.data.publicUrl);
    console.log(selectedFileSup);
  }
  return (
    <div>
      <div className="border rounded-md p-4 ">
        <div className="w-full sm:w-1/3 pr-4">
          <Label
            htmlFor="firstname"
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            {t("resourceLibrary.addResource")}
          </Label>
          <Combobox
            data={modules}
            onSelectChange={onHandleChange}
            defaultLabel={selLabel}
          />
          {/* <Select onValueChange={onHandleChange} defaultValue={selResource}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {modules.map((items) => (
                <SelectItem key={items.id} value={"File"}>
                  {items.display_name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select> */}
        </div>
        <div className="w-full sm:w-1/3 pr-4 mt-4">
          <Label
            htmlFor="firstname"
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            {t("resourceLibrary.uploadType")}
          </Label>
          <Combobox
            data={uploadTypes}
            onSelectChange={onHandleUploadChange}
            defaultLabel={selUpload}
          />
        </div>

        <div className="text-left  p-4">
          {(selLabel === "File" || selLabel === "Video") && (
            <CourseResourcesAddfile
              onCancel={closeModal}
              resourceType={selLabel}
              resourceData={{} as ResourceLibraryData}
              uploadData={selectedFileSup}
              onSave={onSave}
            />
          )}
          {selLabel === "Page" && (
            <ResourcesAddPage
              resourceData={{} as ResourceLibraryData}
              onCancel={closeModal}
              onSave={onSave}
            />
          )}
        </div>

        {selUpload === "2" && (
          <div className="text-left  p-4 ">
            <Modal
              title={t("resourceLibrary.uploadResource")}
              header=""
              openDialog={isDialogOpen}
              closeDialog={closeUpload}
            >
              <UploadResources
                onSave={onUpload}
                onCancel={closeUpload}
                isModal={true}
                resource={selLabel}
              />
            </Modal>
          </div>
        )}
      </div>
    </div>
  );
}
