"use client";
import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import useAuthorization from "@/hooks/useAuth";
import { useRouter } from "next/navigation";
import { useToast } from "@/components/ui/use-toast";
import type {
  LoginUserData,
  ToastType,
  ErrorCatch,
  SignOutResponse,
  LogUserActivityRequest,
  RolePrivileges,
} from "../types";
import type { BaseSyntheticEvent } from "react";
import React from "react";
import { Modal } from "./ui/modal";
import ComfirmLogout from "./ui/logout-modal";
import {
  API_RESPONSE_SUCCESS,
  CONFIG_VALUE,
  DEFAULT_FOLDER_ID,
  DEVELOPMENT,
} from "@/lib/constants";
import useDashboardStatsViews from "@/hooks/useDashboardStats";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";

export function UserNav(): React.JSX.Element {
  const { t } = useTranslation();
  const router = useRouter();
  const { toast } = useToast() as ToastType;
  const { signOut } = useAuthorization();
  const { updateUserActivity } = useLogUserActivity();
  // const [showComment, setShowComment] = React.useState(false);
  // const [showLiveClass, setShowLiveClass] = React.useState(false);
  const [showActivityLog, setShowActivityLog] = React.useState(false);
  const [imageUrl, setImageUrl] = React.useState<string | undefined>(undefined);
  const [userDetails, setUserDetails] = React.useState<string | null>(null);
  const [showModal, setshowModal] = React.useState<boolean>(false);
  const { getConfigurationData } = useDashboardStatsViews();
  const [showTheme, setShowTheme] = React.useState<boolean>(false);

  React.useEffect(() => {
    const privilege = localStorage.getItem("role_privileges") as string;
    const privilageList = JSON.parse(privilege) as RolePrivileges[];
    privilageList?.map((item) => {
      // if (item.screen === "Course_Resource") {
      //   setShowComment(item.actions.GET_COMMENT);
      // }
      // if (item.screen === "Live_Class") {
      //   setShowLiveClass(item.actions.RB_MANAGE_LIVE_CLASS);
      // }
      if (item.screen === "Activity_List") {
        setShowActivityLog(item.actions.GET_ACTIVITY_LIST);
      }
    });
    if (typeof window !== "undefined") {
      const storedUserDetails = localStorage.getItem("userDetails") as string;
      setUserDetails(storedUserDetails);

      const profileImage = localStorage.getItem("profile_image");

      if (profileImage !== null && typeof profileImage === "string") {
        const parsedData = JSON.parse(profileImage) as {
          avatar_url?: string;
        }[];
        const image_url = parsedData[0]?.avatar_url;
        if (image_url != null) {
          setImageUrl(image_url);
        } else {
          setImageUrl("/../../../../assets/default-profile.png");
        }
      } else {
        setImageUrl("/../../../../assets/default-profile.png");
      }
    }

    void getConfiguration();
  }, []); // Empty dependency array

  const getConfiguration = async (): Promise<void> => {
    try {
      const orgId = localStorage.getItem("orgId");
      const params = {
        config_env: DEVELOPMENT,
        org_id: orgId ?? "",
      };
      const resultData = await getConfigurationData(params);
      if (resultData.status === API_RESPONSE_SUCCESS) {
        const configValue = resultData?.result?.configs;
        const value = configValue.find(
          (c) => c.config_name === "CUSTOM_BRANDING_ENABLED",
        )?.config_value;
        if (value === "false") {
          setShowTheme(false);
        } else {
          setShowTheme(true);
        }
        console.log(value);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  };
  let userName: string | undefined;
  let email: string | undefined;

  if (userDetails != null && userDetails != undefined && userDetails != "") {
    const users = JSON.parse(userDetails) as LoginUserData;
    userName =
      users?.user_metadata?.first_name + " " + users?.user_metadata?.last_name;
    email = users?.email;
  }
  const logoutSession = (): void => {
    void onSubmit();
  };

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  const onSubmit = async (): Promise<void> => {
    try {
      const result = (await signOut()) as SignOutResponse;
      if (result.status === true) {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
                    title: t("successMessages.toast_success_title"),
          description: result.message,
        });

        let userId = "";
        if (
          userDetails != null &&
          userDetails != undefined &&
          userDetails != ""
        ) {
          const users = JSON.parse(userDetails) as LoginUserData;
          userId = users?.id;
        }
        const params = {
          activity_type: "Authentication",
          screen_name: "Logout",
          action_details: "User Succesfully Logged out",
          target_id: userId,
          log_result: "SUCCESS",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });

        localStorage.removeItem("orgId");
        localStorage.removeItem("orgName");
        localStorage.removeItem("userDetails");
        localStorage.removeItem("role_privileges");
        localStorage.removeItem("orgName");
        localStorage.removeItem("orgId");
        localStorage.removeItem("courseId");
        localStorage.removeItem("profile_image");
        localStorage.removeItem("ExamDetails");
        localStorage.removeItem("access_token");
        localStorage.removeItem("checkpointNo");
        localStorage.removeItem("checkpointNoPPT");
        localStorage.removeItem("currentAffairsData");
        localStorage.removeItem(CONFIG_VALUE);

        router.push("/login");
      } else if (result.status === false) {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
                    title: t("errorMessages.toast_error_title"),
          description: result.message,
        });
        const params = {
          activity_type: "Authentication",
          screen_name: "Logout",
          action_details: "User Failed to Logged out",
          target_id: DEFAULT_FOLDER_ID,
          log_result: "ERROR",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
                  title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
      console.error("An unexpected error occurred:", error);
    }
  };
  async function handleSignOut(e: BaseSyntheticEvent): Promise<void> {
    setshowModal(true);
    console.log(e);
    // try {
    //   const result = (await signOut()) as SignOutResponse;
    //   if (result.status === true) {
    //     toast({
    //       variant: "success",
    //       title: "Success",
    //       description: result.message,
    //     });
    //     localStorage.removeItem("orgId");
    //     localStorage.removeItem("orgName");
    //     localStorage.removeItem("userDetails");
    //     localStorage.removeItem("role_privileges");
    //     router.push("/login");
    //   } else if (result.status === false) {
    //     toast({
    //       variant: "error",
    //       title: "Error",
    //       description: result.message,
    //     });
    //     console.log("API Error:", result.status);
    //   }
    // } catch (error) {
    //   const err = error as ErrorCatch;
    //   toast({
    //     variant: "error",
    //     title: "Error",
    //     description: err?.message,
    //   });
    //   console.error("An unexpected error occurred:", error);
    // }
  }
  function closeConfirmation(): void {
    setshowModal(false);
  }

  // const handleSignOut = async (): Promise<void> => {
  //   console.log(3232);
  //   const signOutResult = await signOut();
  //   toast({
  //     title: "Success",
  //     description: signOutResult,
  //   });
  //   localStorage.removeItem("orgId");
  //   router.push("/login");
  // };
  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <div className="flex items-center">
            <span className="mr-2">{userName}</span>
            <Button variant="ghost" className="relative h-8 w-8 rounded-full">
              <Avatar className="h-8 w-8">
                <AvatarImage
                  src={imageUrl}
                  alt="@shadcn"
                  style={{ border: "2px solid #000", borderRadius: "50%" }}
                />
                <AvatarFallback>SC</AvatarFallback>
              </Avatar>
            </Button>
          </div>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56 bg-white " align="end" forceMount>
          <DropdownMenuLabel className="font-normal">
            <div className="flex flex-col space-y-1">
              <p className="text-sm font-medium leading-none">{userName}</p>
              <p
                className="text-xs leading-none text-muted-foreground"
                style={{ overflowWrap: "break-word", wordWrap: "break-word" }}
              >
                {email}
              </p>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuGroup>
            <DropdownMenuItem className="cursor-pointer">
              {t("navMenu.profile")}
              <DropdownMenuShortcut></DropdownMenuShortcut>
            </DropdownMenuItem>

            {showTheme && (
              <DropdownMenuItem
                className="cursor-pointer"
                onClick={() => router.push("/config")}
              >
                {t("navMenu.theme")}
              </DropdownMenuItem>
            )}
          </DropdownMenuGroup>
          {/* {showComment && (
            <DropdownMenuGroup>
              <DropdownMenuItem className="cursor-pointer">
                <DropdownMenuShortcut></DropdownMenuShortcut>
              </DropdownMenuItem>
              <DropdownMenuItem
                className="cursor-pointer"
                onClick={() => router.push("/comments")}
              >
                {t("navMenu.commentsFeedback")}
              </DropdownMenuItem>
            </DropdownMenuGroup>
          )} */}

          {/* {showLiveClass && (
            <DropdownMenuGroup>
              <DropdownMenuItem className="cursor-pointer">
                <DropdownMenuShortcut></DropdownMenuShortcut>
              </DropdownMenuItem>
              <DropdownMenuItem
                className="cursor-pointer"
                onClick={() => router.push("/scheduleMeeting")}
              >
                 {t("navMenu.scheduleMeeting")}
              </DropdownMenuItem>
            </DropdownMenuGroup>
          )} */}

          {showActivityLog && (
            <DropdownMenuGroup>
              <DropdownMenuItem className="cursor-pointer">
                <DropdownMenuShortcut></DropdownMenuShortcut>
              </DropdownMenuItem>
              <DropdownMenuItem
                className="cursor-pointer"
                onClick={() => router.push("/activity-logs")}
              >
                 {t("navMenu.activityLog")}
              </DropdownMenuItem>
            </DropdownMenuGroup>
          )}
          <DropdownMenuSeparator />
          <DropdownMenuItem
            className="cursor-pointer"
            onClick={(e: BaseSyntheticEvent) => {
              handleSignOut(e).catch((error) => console.log(error));
            }}
          >
            {t("navMenu.logout")}
            <DropdownMenuShortcut></DropdownMenuShortcut>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      {showModal && (
        <Modal
          title={""}
          header=""
          openDialog={showModal}
          closeDialog={closeConfirmation}
        >
          <ComfirmLogout
            onSave={logoutSession}
            onCancel={closeConfirmation}
            isModal={true}
          />
        </Modal>
      )}
    </>
  );
}
