"use client";
import type { FC } from "react";
import React, { useEffect, useState } from "react";
import type {
  ErrorCatch,
  ExamDetailsType,
  LogUserActivityRequest,
  LoginUserData,
  ToastType,
  examReviewType,
  submitAnswerType,
  submitQuizType,
} from "@/types";
import useExamDetails from "@/hooks/useExamDetails";
import { useToast } from "@/components/ui/use-toast";
import { pageUrl } from "@/lib/constants";
import { useRouter } from "next/navigation";
import { Modal } from "@/components/ui/modal";
import ExamReview from "../exam-review/exam-review";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";

interface ExamTrialQuestionsProps {
  index: number;
  data: ExamDetailsType[];
  quizAttemptId: string;
  examId: string;
  seconds: number;
  isFinished: boolean;
  isStarted: boolean;
  onSecondChange: () => void;
}

const ExamTrialQuestions: FC<ExamTrialQuestionsProps> = ({
  index,
  data,
  quizAttemptId,
  examId,
  seconds,
  isStarted,
  isFinished,
  onSecondChange,
}): React.JSX.Element => {
  const { t } = useTranslation();
  const { toast } = useToast() as ToastType;
  const [currentSeconds, setCurrentSeconds] = useState<number>(seconds);
  const [examReview, setExamReview] = useState<examReviewType[]>([]);
  const [showAnswers, setShowAnswers] = useState<boolean>(false); // State to store current seconds
  const questions = data[index]?.name;
  const router = useRouter();
  const answers = data[index]?.answers;
  const { submitQuiz, evaluateAnswer, clearExam } = useExamDetails();
  const { updateUserActivity } = useLogUserActivity();

  answers?.forEach((item) => {
    item.ansMarked = item.ansMarked === true; // Set ansMarked to true if it is already true, otherwise set it to false
  });
  useEffect(() => {
    if (isFinished === true) {
      submitExam();
      onSecondChange();
    } else {
      if (currentSeconds === 1 && isFinished === false) {
        submitExam();
      }
    }
    setCurrentSeconds(seconds);
  }, [index, seconds]);

  const handleCheckboxChange = (answer: string): void => {
    answers?.forEach((item) => {
      if (item.answer === answer) {
        // Toggle the state of the selected answer
        item.ansMarked = item.ansMarked === undefined ? true : !item.ansMarked;
      }
    });
  };

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  const submitExam = (): void => {
    const submittedValues = data.map((item) => ({
      question_id: item.question_id,
      question_with_options: item.question_text,
      response_summary: item.answers
        ?.filter((ansInfo) => ansInfo.ansMarked === true)
        .map((obj) => obj.answer),
      selected_answer_ids: item.answers
        ?.filter((ansInfo) => ansInfo.ansMarked === true)
        .map((obj) => obj.answer_id),
    }));
    const submittedValue = submittedValues as submitAnswerType[];
    const orgId = localStorage.getItem("orgId");
    let user_id = "";
    const userDetails = localStorage.getItem("userDetails");
    if (userDetails != null && userDetails != undefined) {
      const users = JSON.parse(userDetails) as LoginUserData;
      user_id = users?.id;
    }
    const passData = {
      org_id: orgId,
      quiz_id: examId,
      user_id: user_id,
      quiz_attempt_id: quizAttemptId,
      submit_datas: submittedValue,
    };

    const fetchData = async (): Promise<void> => {
      try {
        const questionsData = await submitQuiz(passData as submitQuizType);
        if (questionsData.status === "success") {
          toast({
            variant: SUCCESS_MESSAGES.toast_variant_default,
            title: t("successMessages.toast_success_title"),
            description: t("successMessages.submit_exam"),
          });
          void evaluateAnswers();
          const params = {
            activity_type: "Exam",
            screen_name: "Exam",
            action_details: "Exam submitted successfully",
            target_id: examId as string,
            log_result: "SUCCESS",
          };
          void updateLogUserActivity(params).catch((error) => {
            console.error(error);
          });
        } else {
          const params = {
            activity_type: "Exam",
            screen_name: "Exam",
            action_details: "Failed to submit exam",
            target_id: examId as string,
            log_result: "ERROR",
          };
          void updateLogUserActivity(params).catch((error) => {
            console.error(error);
          });
        }
      } catch (error: unknown) {
        const errMsg: string =
          typeof error === "string"
            ? error
            : error instanceof Error
            ? error.message
            : "Unknown error";

        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: errMsg,
        });
      }
    };
    fetchData().catch((error) => console.log(error));
  };

  const evaluateAnswers = async (): Promise<void> => {
    const passData = {
      quiz_id: examId,
      quiz_attempt_id: quizAttemptId,
    };
    const fetchData = async (): Promise<void> => {
      try {
        const questionsData = await evaluateAnswer(passData as submitQuizType);
        console.log(questionsData);
        const ansEvaluation = questionsData as examReviewType[];
        setExamReview(ansEvaluation);
        setShowAnswers(true);
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
      }
    };
    fetchData().catch((error) => console.log(error));
  };
  const removeHTMLTags = (html: string | undefined): React.JSX.Element => {
    if (html != null) {
      const tempDiv = document.createElement("div");
      tempDiv.innerHTML = html;
      return <div dangerouslySetInnerHTML={{ __html: tempDiv.innerHTML }} />;
    } else {
      return <div></div>;
    }
  };
  const closeDelete = (): void => {
    setShowAnswers(false);
  };
  const onSubmit = (): void => {
    const orgId = localStorage.getItem("orgId");
    const passData = {
      org_id: orgId,
      quiz_attempt_id: quizAttemptId,
    };
    const fetchData = async (): Promise<void> => {
      try {
        await clearExam(passData as submitQuizType);
        router.push(`${pageUrl.exams}`);
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.toast_success_title"),
          description: t("successMessages.exam_trial_run"),
        });
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
      }
    };
    fetchData().catch((error) => console.log(error));
  };

  return (
    <div>
      <div>
        <label
          className="w-full pt-4"
          style={{ display: "flex", alignItems: "center" }}
        >
          {questions === "HTML" ? (
            <span
              dangerouslySetInnerHTML={{
                __html: questions.replace(/&nbsp;/g, " "),
              }}
            />
          ) : (
            <span className="flex items-start">
              <span className="mr-1 w-10">Q: {index + 1}. </span>
              {removeHTMLTags(questions)}
            </span>
          )}
        </label>

        <label className="flex flex-row justify-between items-center">
          <p>
            {t("exams.mark")} {data[index]?.default_mark}
          </p>
          <p>
            {t("exams.penaltyIfApplicable")} {data[index]?.penalty}
          </p>
        </label>
        <div className="flex flex-wrap w-full">
          {answers?.map((answer, index) => (
            <div
              key={answer.answer_id}
              className="w-[calc(33.33%-20px)] border border-solid border-gray-300 bg-white box-border flex flex-col cursor-pointer rounded-lg m-2 p-5"
            >
              <label>
                <input
                  autoComplete="off"
                  type="checkbox"
                  name={`answer-${index}`}
                  value={answer.answer}
                  checked={answer.ansMarked}
                  onChange={() =>
                    isStarted && handleCheckboxChange(answer.answer)
                  }
                />
                &nbsp;&nbsp;
                <span>{t("exams.selectAnAns")}</span>
                <div className="border border-solid border-gray-300 bg-white flex flex-col cursor-pointer rounded-lg m-2 p-5">
                  <span>
                    {index + 1}. {answer.answer}
                  </span>
                </div>
              </label>
            </div>
          ))}
        </div>
        <div>
          {showAnswers && (
            <Modal
              title={""}
              header=""
              openDialog={showAnswers}
              closeDialog={closeDelete}
              type="max-w-3xl"
            >
              <ExamReview
                onSave={onSubmit}
                onCancel={closeDelete}
                isModal={true}
                data={examReview}
              />
            </Modal>
          )}
          <div className="flex items-center justify-end pt-5">
            <Link href={pageUrl.exams}>
              <Button type="button" className="bg-[#33363F]">
                {t("buttons.cancel")}
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExamTrialQuestions;
