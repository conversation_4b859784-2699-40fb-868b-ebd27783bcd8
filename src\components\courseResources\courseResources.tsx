"use client";
import React, { useEffect, useState } from "react";
import { getColumns } from "./columns";
import { DataTable } from "../../components/ui/data-table/data-table";
import useResourceLibrary from "@/hooks/useResourceLibrary";
import type { CourseResourceList, ErrorCatch, ToastType } from "@/types";
import { useToast } from "@/components/ui/use-toast";
import { Spinner } from "@/components/ui/progressiveLoader";
import { Button } from "../ui/button";
import { useTranslation } from "react-i18next";
import { ERROR_MESSAGES } from "@/lib/messages";

export default function CourseResources({
  onCancel,
  resource,
}: {
  onCancel: () => void;
  resource: string;
}): React.JSX.Element {
  const { t } = useTranslation();
  const columns = getColumns(t);
  const { getCourseResources } = useResourceLibrary();
  const { toast } = useToast() as ToastType;

  const [isLoading, setIsLoading] = React.useState<boolean>(true);
  const [courseResourceData, setCourseResourceData] = useState<
    CourseResourceList[]
  >([]);

  useEffect(() => {
    const fetchCourseResources = async (): Promise<void> => {
      setIsLoading(true);
      const orgId = localStorage.getItem("orgId");
      const instanceId = resource;
      const params = {
        org_id: orgId ?? "",
        instance: instanceId,
      };
      try {
        const result = await getCourseResources(params);
        setIsLoading(false);
        if (result.status === "success") {
          setCourseResourceData(result.resource_list);
        }
      } catch (error) {
        const err = error as ErrorCatch;
        setIsLoading(false);
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
      }
    };
    fetchCourseResources().catch((error) => console.log(error));
  }, []);

  const dialogClose = (): void => {
    onCancel();
  };

  return (
    <>
      {isLoading ? (
        <Spinner />
      ) : (
        <div>
          <DataTable
            columns={columns}
            data={courseResourceData ?? []}
            FilterLabel={t("resourceLibrary.filterByCourseName")}
            FilterBy={"short_name"}
            actions={[]}
          />
        </div>
      )}

      <div className="flex flex-wrap justify-end mt-8">
        <div className="sm:px-2 md:px-3 lg:px-4 xl:px-5 2xl:px-6">
          <Button className="w-full sm:w-auto" onClick={dialogClose}>
            {t("resourceLibrary.back")}
          </Button>
        </div>
      </div>
    </>
  );
}
