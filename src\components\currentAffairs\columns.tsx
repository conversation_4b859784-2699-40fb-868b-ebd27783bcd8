"use client";
import type { ColumnDef } from "@tanstack/react-table";
import type {
  CurrentAffairsData,
  CurrentAffairsRowDefinition,
} from "@/types";
import moment from "moment";

export const getColumns = (
  t: (key: string) => string,
): ColumnDef<CurrentAffairsData>[] => [
  {
    accessorKey: "title",
    header: t("dashboard.currentaffairs.eventTitle"),
    cell: ({ row }: CurrentAffairsRowDefinition) => (
      <div className="text-align">{row.original.title}</div>
    ),
  },

  {
    accessorKey: "publish_date",
    header: t("dashboard.currentaffairs.publishedDate"),
    cell: ({ row }: CurrentAffairsRowDefinition): React.JSX.Element => {
      const formattedDate = moment(row.original.publish_date).format(
        "DD-MMM-YYYY",
      );
      return <div>{formattedDate}</div>;
    },
  },
];
