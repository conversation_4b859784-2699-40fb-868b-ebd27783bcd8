import type {
  ErrorType,
  ResourceLibraryRequest,
  ResourceLibrary,
  CourseResourcesRequest,
  CourseResources,
  ModuleList,
  AddResourceFile,
  AddResourcePage,
  AddResourceUrl,
  AddResourceResponse,
  moduleList,
  courseNotLinkedRequest,
  courseLinkedRequest,
  DeleteResource,
  DeleteResourceResponse,
  EditResourceFile,
  EditResourcePage,
  EditResourceUrl,
  EditResourceResponse,
  ApproveResource,
  ApproveResourceResponse,
  ImportFolderRequest,
  GetCourseListRequest,
  MapResourcesToCourseRequest,
  GetCourseListResponse,
  UpdateResourceUrlRequest,
  AllResourceRequest,
  AllResourceResponse,
  ManageFolderRequest,
} from "@/types";
import { supabase } from "../lib/client";
import { rpc, views } from "@/lib/apiConfig";

interface useResorceLibraryReturn {
  getCourseResources: (
    params?: CourseResourcesRequest,
  ) => Promise<CourseResources>;
  getResourceList: (params: ResourceLibraryRequest) => Promise<ResourceLibrary>;
  getModuleList: () => Promise<ModuleList[]>;
  addPageResource: (params?: AddResourcePage) => Promise<AddResourceResponse>;
  addResources: (
    params: AddResourceFile | AddResourceUrl,
    resourceType: string,
  ) => Promise<AddResourceResponse>;
  getResourceNotLinkedList: (
    params?: courseNotLinkedRequest,
  ) => Promise<ResourceLibrary>;
  updateResourceLinkedList: (
    params?: courseLinkedRequest,
  ) => Promise<ResourceLibrary>;
  deleteResources: (params: DeleteResource) => Promise<DeleteResourceResponse>;
  editResources: (
    params: EditResourceFile | EditResourceUrl,
    resourceType: string,
  ) => Promise<EditResourceResponse>;
  editPageResource: (
    params?: EditResourcePage,
  ) => Promise<EditResourceResponse>;
  approveResources: (
    params: ApproveResource,
  ) => Promise<ApproveResourceResponse>;

  importFolderToCourse: (
    params?: ImportFolderRequest,
  ) => Promise<ResourceLibrary>;

  getCourseList: (
    params?: GetCourseListRequest,
  ) => Promise<GetCourseListResponse>;
  mapResourcesToCourse: (
    params?: MapResourcesToCourseRequest,
  ) => Promise<CourseResources>;
  editResourcesUrl: (
    params: UpdateResourceUrlRequest,
  ) => Promise<EditResourceResponse>;

  getAllResoures: (
    queryParams: AllResourceRequest,
  ) => Promise<AllResourceResponse>;

  manageFolder: (params: ManageFolderRequest) => Promise<EditResourceResponse>;
}

const useResourceLibrary = (): useResorceLibraryReturn => {
  async function getCourseResources(
    params?: CourseResourcesRequest,
  ): Promise<CourseResources> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.getCoursesLinkedResourcesLibrary,
        params,
      )) as {
        data: CourseResources;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as CourseResources;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function getResourceList(
    params: ResourceLibraryRequest,
  ): Promise<ResourceLibrary> {
    try {
      const { data, error } = (await supabase.rpc<string, null>(
        rpc.resourceLibrary,
        params,
      )) as { data: ResourceLibrary; error: ErrorType | null };
      if (error) {
        throw new Error(error.details);
      }
      return data as ResourceLibrary;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function getModuleList(): Promise<moduleList[]> {
    try {
      const moduleList = views?.moduleList ?? "";
      const exeQuery = supabase
        .from(moduleList)
        .select()
        .order("name", { ascending: true });
      const { data, error } = await exeQuery;
      if (error) {
        throw new Error(error.details);
      }
      return data as moduleList[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function getResourceNotLinkedList(
    params?: courseNotLinkedRequest,
  ): Promise<ResourceLibrary> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.resourceLibraryNotLinked,
        params,
      )) as {
        data: ResourceLibrary;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as ResourceLibrary;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function updateResourceLinkedList(
    params?: courseLinkedRequest,
  ): Promise<ResourceLibrary> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.resourceLibraryLinked,
        params,
      )) as {
        data: ResourceLibrary;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as ResourceLibrary;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function addResources(
    params: AddResourceFile | AddResourceUrl,
    resourceType: string,
  ): Promise<AddResourceResponse> {
    try {
      let rpcFunctionName = rpc.addResourceFile;

      if (resourceType === "Video") {
        rpcFunctionName = rpc.addResourceUrl;
      }
      const { data, error } = (await supabase.rpc(rpcFunctionName, params)) as {
        data: AddResourceResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as AddResourceResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function addPageResource(
    params?: AddResourcePage,
  ): Promise<AddResourceResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.addResourcePage,
        params,
      )) as {
        data: AddResourceResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as AddResourceResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function deleteResources(
    params: DeleteResource,
  ): Promise<DeleteResourceResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.deleteResourceLibraryItem,
        params,
      )) as {
        data: DeleteResourceResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as DeleteResourceResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function editResources(
    params: EditResourceFile | EditResourceUrl,
    resourceType: string,
  ): Promise<EditResourceResponse> {
    try {
      let rpcFunctionName = rpc.editResourceFile;

      if (resourceType === "Video") {
        rpcFunctionName = rpc.editResourceUrl;
      }
      const { data, error } = (await supabase.rpc(rpcFunctionName, params)) as {
        data: EditResourceResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as EditResourceResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function editPageResource(
    params?: EditResourcePage,
  ): Promise<EditResourceResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.editResourcePage,
        params,
      )) as {
        data: EditResourceResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as EditResourceResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function approveResources(
    params: ApproveResource,
  ): Promise<ApproveResourceResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.publishResourceItem,
        params,
      )) as {
        data: ApproveResourceResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as ApproveResourceResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function importFolderToCourse(
    params?: ImportFolderRequest,
  ): Promise<ResourceLibrary> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.importFolder,
        params,
      )) as {
        data: ResourceLibrary;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as ResourceLibrary;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function getCourseList(
    params?: GetCourseListRequest,
  ): Promise<GetCourseListResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.getCourseList,
        params,
      )) as {
        data: GetCourseListResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as GetCourseListResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function mapResourcesToCourse(
    params?: MapResourcesToCourseRequest,
  ): Promise<CourseResources> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.mapResourceToCourse,
        params,
      )) as {
        data: CourseResources;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as CourseResources;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function editResourcesUrl(
    params: UpdateResourceUrlRequest,
  ): Promise<EditResourceResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.updateResourceUrl,
        params,
      )) as {
        data: EditResourceResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as EditResourceResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function getAllResoures(
    queryParams: AllResourceRequest,
  ): Promise<AllResourceResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.getCourseResourceDetails,
        queryParams,
      )) as {
        data: AllResourceResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as AllResourceResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function manageFolder(
    params: ManageFolderRequest,
  ): Promise<EditResourceResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.manageFolder,
        params,
      )) as {
        data: EditResourceResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as EditResourceResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  return {
    getCourseResources,
    getResourceList,
    getModuleList,
    addPageResource,
    addResources,
    getResourceNotLinkedList,
    updateResourceLinkedList,
    deleteResources,
    editResources,
    editPageResource,
    approveResources,
    importFolderToCourse,
    getCourseList,
    mapResourcesToCourse,
    editResourcesUrl,
    getAllResoures,
    manageFolder,
  };
};

export default useResourceLibrary;
