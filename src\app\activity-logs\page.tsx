"use client";
import MainLayout from "../layout/mainlayout";
import { Combobox } from "@/components/ui/combobox";
import type {
  ComboData,
  ActivityLogRequest,
  ActivityLogResponse,
  UserLogList,
  InnerItem,
  ToastType,
  ErrorCatch,
} from "@/types";
import { Label } from "@radix-ui/react-label";
import { DataTable } from "@/components/ui/data-table/data-table";
import { getColumns } from "./columns";
import React, { useEffect, useState } from "react";
import { useToast } from "@/components/ui/use-toast";
import { ERROR_MESSAGES } from "@/lib/messages";
import useUsers from "@/hooks/useUsers";
import useActivityLog from "@/hooks/useActivityLog";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumb";
import { useTranslation } from "react-i18next";

export default function CourseResourcePage(): React.JSX.Element {
  const { t } = useTranslation();
    const { toast } = useToast() as ToastType;
  const columns = getColumns(t);
  const [logData, setLogData] = useState<UserLogList[]>([]);
  const [userData, setUserData] = useState<ComboData[]>([]);
  const { getUsers } = useUsers();
  const { getActivityLogList } = useActivityLog();
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);
  useEffect(() => {
    setBreadcrumbItems(
      getBreadCrumbItems(t, t("breadcrumb.activityLog"), { "": "" }),
    );
    getUsersList();
  }, [t]);

  const getUsersList = (): void => {
    const fetchData = async (): Promise<void> => {
      try {
        const org_id = localStorage.getItem("orgId") as string;
        const fetchedUsers = await getUsers(org_id);
        if (fetchedUsers.length > 0) {
          const convertedData = fetchedUsers?.map((user) => ({
            value: user.id,
            label: `${user?.first_name?.trim()} ${user?.last_name?.trim()}`,
          }));
          setUserData(convertedData);
        }
      } catch (error) {
        console.log(error);
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: t("errorMessages.error_fetching_data"),
        });
      }
    };
    fetchData().catch((error) => console.log(error));
  };
  const userSelectionChange = (value: string): void => {
    const fetchData = async (): Promise<void> => {
      try {
        const org_id = localStorage.getItem("orgId") as string;
        const passData: ActivityLogRequest = {
          limit_val: 1000,
          offset_val: 0,
          org_id: org_id,
          user_id: value,
        };
        const logList: ActivityLogResponse = await getActivityLogList(passData);
        setLogData(logList.result);
      } catch (error) {
        const err = error as ErrorCatch;
        console.log(error);
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
      }
    };
    fetchData().catch((error) => console.log(error));
  };
  return (
    <MainLayout>
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      <h1 className="text-2xl font-semibold tracking-tight pb-5">
        {t("activityLog.title")}
      </h1>
      <div className="border rounded-md p-4 bg-[#fff]">
        <div className="flex gap-x-4 mt-4">
          <div className="sm:w-1/2 md:w-1/4 gap-x-4">
            <Label className="block">{t("activityLog.userSelected")}</Label>
            <Combobox
              data={userData}
              onSelectChange={(value) => {
                userSelectionChange(value);
              }}
            />
          </div>
        </div>

        <DataTable
          columns={columns}
          data={logData as UserLogList[]}
          FilterLabel={t("activityLog.filterByModule")}
          FilterBy={"activity_type"}
          actions={[]}
          onSelectedDataChange={() => {}}
        />
      </div>
    </MainLayout>
  );
}
