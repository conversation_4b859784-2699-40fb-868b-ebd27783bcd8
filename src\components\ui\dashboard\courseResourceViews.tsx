import React, { useEffect, useState } from "react";
import { Bar } from "react-chartjs-2";
import type {
  CheckpointsCourseStats,
  CheckpointsCourseStatsReturnType,
} from "@/types";
import useDashboardStatsViews from "@/hooks/useDashboardStats";
import { Modal } from "../modal";
import UserSessionDetails from "@/components/userSessionDetails/sessionDetails";
import {
  SESSION_FILTER_TYPE_ENROLLED,
  SESSION_FILTER_TYPE_WATCHED,
  SESSION_FILTER_TYPE_NOT_WATCHED,
  SESSION_FILTER_TYPE_PASSED,
  SESSION_FILTER_TYPE_FAILED,
  SESSION_FILTER_TYPE_PENDING,
  SESSION_GRAPH_ENROLLED_INDEX,
  SESSION_LABEL_ENROLLED,
  SESSION_GRAPH_WATCHED_INDEX,
  SESSION_LABEL_WATCHED,
  SESSION_GRAPH_NOT_WATCHED_INDEX,
  SESSION_LABEL_NOT_WATCHED,
  SESSION_GRAPH_PASSED_INDEX,
  SESSION_LABEL_PASSED,
  SESSION_GRAPH_FAILED_INDEX,
  SESSION_LABEL_FAILED,
  SESSION_GRAPH_PENDING_INDEX,
  SESSION_LABEL_PENDING,
} from "@/lib/constants";
import type { ChartEvent, ActiveElement } from "chart.js";
import ViewAllCourseResources from "./viewAllCourseResource";
import { useTranslation } from "react-i18next";

/* interface CourseStatsCardProps {
  isLoading: boolean;
  checkpointsCourseStats: CheckpointsCourseStatsReturnType | null;
  getSessionDetails: (event: any) => void;
  isDialogOpen: boolean;
  closeDialog: (value: boolean) => void;
  selectedSessionCourseId: string;
  selectedSessionCourseName: string;
  filterType: string;
  filterTypeLabel: string;
  onRefresh?: () => void;
} */

interface ResourceViewsProps {
  orgId: string;
  onRefresh?: () => void;
}

const DBCourseResourceViews: React.FC<ResourceViewsProps> = ({
  orgId,
}: ResourceViewsProps): React.JSX.Element => {
  const { t } = useTranslation();

  const [chartHovered, setChartHovered] = useState(false);

  const { getCourseWiseStatistics } = useDashboardStatsViews();
  const [checkpointsCourseStats, setcheckpointsCourseStats] =
    useState<CheckpointsCourseStatsReturnType>();
  const [isDialogOpen, setIsDialogOpen] = React.useState(false);
  const [isViewAllDialogOpen, setIsViewAllDialogOpen] = React.useState(false);
  const [isLoading, setIsLoading] = React.useState<boolean>(true);
  const [filterType, setFilterType] = useState("");
  const [filterTypeLabel, setFilterTypeLabel] = useState("");
  const [selectedSessionCourseId, setSessionCourseId] = useState("");
  const [selectedSessionCourseName, setSessionCourseName] = useState("");
  const [courseStatistics, setCourseStatistics] = useState<
    CheckpointsCourseStats[]
  >([]);

  useEffect(() => {
    const fetchDashboardDataUserStats = async (): Promise<void> => {
      // setIsLoading(true);
      const courseLabels: string[] = [];
      const enrolled: number[] = [];
      const watched: number[] = [];
      const passed: number[] = [];
      const failed: number[] = [];
      const pending: number[] = [];
      const unwatched: number[] = [];

      let checkpointsCourseStats: CheckpointsCourseStats[] = [];
      if (orgId !== null && orgId !== "") {
        checkpointsCourseStats = await getCourseWiseStatistics(orgId, null);
      }

      if (checkpointsCourseStats.length > 0) {
        setCourseStatistics(checkpointsCourseStats as CheckpointsCourseStats[]);

        checkpointsCourseStats.slice(0, 6).map((item) => {
          courseLabels.push(item.course_short_name);
          item.enrolled_users > 0
            ? enrolled.push(item.enrolled_users)
            : enrolled.push(0);
          item.watched_users > 0
            ? watched.push(item.watched_users)
            : watched.push(0);
          item.passed_users > 0
            ? passed.push(item.passed_users)
            : passed.push(0);
          item.failed_users > 0
            ? failed.push(item.failed_users)
            : failed.push(0);
          item.pending_users > 0
            ? pending.push(item.pending_users)
            : pending.push(0);
          item.unwatched_users > 0
            ? unwatched.push(item.unwatched_users)
            : unwatched.push(0);
        });
      }

      const graphData = {
        labels: courseLabels,
        datasets: [
          {
            data: enrolled,
            label: SESSION_FILTER_TYPE_ENROLLED,
            borderColor: "wheat",
            backgroundColor: "#1ea185",
            borderWidth: 2,
          },
          {
            data: watched,
            label: SESSION_FILTER_TYPE_WATCHED,
            borderColor: "wheat",
            backgroundColor: "#964B00",
            borderWidth: 2,
          },
          {
            data: unwatched,
            label: SESSION_FILTER_TYPE_NOT_WATCHED,
            borderColor: "wheat",
            backgroundColor: "#f29b26",
            borderWidth: 2,
          },
          {
            data: passed,
            label: SESSION_FILTER_TYPE_PASSED,
            borderColor: "wheat",
            backgroundColor: "#008000",
            borderWidth: 2,
          },
          {
            data: failed,
            label: SESSION_FILTER_TYPE_FAILED,
            borderColor: "wheat",
            backgroundColor: "#bd392f",
            borderWidth: 2,
          },
          {
            data: pending,
            label: SESSION_FILTER_TYPE_PENDING,
            borderColor: "wheat",
            backgroundColor: "#756544",
            borderWidth: 2,
          },
        ],
      };

      setcheckpointsCourseStats(graphData as CheckpointsCourseStatsReturnType);
      setIsLoading(false);
    };

    fetchDashboardDataUserStats()
      .then((response) => {
        console.log(response);
      })
      .catch((error) => {
        console.log(error);
      });
  }, [orgId]);

  const getSessionDetails = (
    event: ChartEvent,
    element: ActiveElement[],
  ): void => {
    // Log or use the index as needed
    if (element?.length > 0) {
      const courseId: string = courseStatistics[element[0].index].course_id;
      setSessionCourseId(courseId);
      setSessionCourseName(
        courseStatistics[element[0].index].course_short_name,
      );
      if (element[0].datasetIndex == SESSION_GRAPH_ENROLLED_INDEX) {
        setFilterType(SESSION_FILTER_TYPE_ENROLLED);
        setFilterTypeLabel("Report : " + SESSION_LABEL_ENROLLED);
      } else if (element[0].datasetIndex == SESSION_GRAPH_WATCHED_INDEX) {
        setFilterType(SESSION_FILTER_TYPE_WATCHED);
        setFilterTypeLabel("Report : " + SESSION_LABEL_WATCHED);
      } else if (element[0].datasetIndex == SESSION_GRAPH_NOT_WATCHED_INDEX) {
        setFilterType(SESSION_FILTER_TYPE_NOT_WATCHED);
        setFilterTypeLabel("Report : " + SESSION_LABEL_NOT_WATCHED);
      } else if (element[0].datasetIndex == SESSION_GRAPH_PASSED_INDEX) {
        setFilterType(SESSION_FILTER_TYPE_PASSED);
        setFilterTypeLabel("Report : " + SESSION_LABEL_PASSED);
      } else if (element[0].datasetIndex == SESSION_GRAPH_FAILED_INDEX) {
        setFilterType(SESSION_FILTER_TYPE_FAILED);
        setFilterTypeLabel("Report : " + SESSION_LABEL_FAILED);
      } else if (element[0].datasetIndex == SESSION_GRAPH_PENDING_INDEX) {
        setFilterType(SESSION_FILTER_TYPE_PENDING);
        setFilterTypeLabel("Report : " + SESSION_LABEL_PENDING);
      }
      openDialog();
    }
  };

  const openDialog = (): void => {
    setIsDialogOpen(true);
  };

  const closeDialog = (value: boolean): void => {
    console.log(value);
    setIsDialogOpen(false);
  };

  const handleViewAllDialogOpen = (): void => {
    setIsViewAllDialogOpen(true);
  };

  const handleViewAllDialogClose = (): void => {
    setIsViewAllDialogOpen(false);
  };

  return (
    <div
      className="border rounded-lg shadow-lg bg-white  overflow-hidden"
      style={{ height: "450px", overflow: "auto" }}
    >
      {/* Gradient Header Bar */}

      {/* Header */}
      <div className="p-2 border-b flex justify-between items-center dashboard-session text-black rounded-t-lg">
        <h3 className="text-md font-semibold">
          {String(t("dashboard.courseWiseStatistics.title"))}
        </h3>
        <button
          className="text-sm text-white transition-colors duration-200 flex items-center gap-1"
          onClick={handleViewAllDialogOpen}
        >
          {String(t("dashboard.viewAll"))}
        </button>
      </div>

      {/* Content */}
      <div className="bg-white rounded-lg">
        {isLoading ? (
          <div className="flex flex-col items-center justify-center bg-slate-50/50 rounded-lg">
            <div className="w-10 h-10 border-4 border-primary border-t-transparent rounded-full animate-spin " />
            <p className="text-sm text-slate-500 animate-pulse">
              {String(t("dashboard.loadingStatistics"))}
            </p>
          </div>
        ) : (
          <div className="w-full bg-white rounded-xl duration-500 p-4">
            {checkpointsCourseStats ? (
              <div
                className="relative transition-transform duration-300"
                style={{ transform: chartHovered ? "scale(1.01)" : "scale(1)" }}
                onMouseEnter={() => setChartHovered(true)}
                onMouseLeave={() => setChartHovered(false)}
              >
                <Bar
                  data={
                    checkpointsCourseStats as CheckpointsCourseStatsReturnType
                  }
                  height={300}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    animation: { duration: 750, easing: "easeInOutQuart" },
                    scales: {
                      y: {
                        beginAtZero: true,
                        ticks: {
                          padding: 50,
                          font: { family: "'Inter', sans-serif", size: 11 },
                          color: "#64748b",
                        },
                        title: {
                          display: true,
                          text: t("dashboard.courseWiseStatistics.userCount"),
                          padding: { top: 20, bottom: -30 },
                          font: { family: "'Inter', sans-serif", size: 12 },
                          color: "#475569",
                        },
                        grid: { color: "rgba(148, 163, 184, 0.1)" },
                      },
                      x: {
                        grid: { display: false },
                        ticks: {
                          font: { family: "'Inter', sans-serif", size: 11 },
                          color: "#64748b",
                        },
                      },
                    },
                    plugins: {
                      datalabels: {
                        display: (context) =>
                          context.dataset.data[context.dataIndex] !== 0,
                        color: "black",
                        formatter: Math.round,
                        anchor: "end",
                        offset: -20,
                        align: "start",
                      },
                      legend: {
                        labels: {},
                        align: "center",
                        position: "bottom",
                      },
                    },
                    onHover: (event) => {
                      const target = event.native?.target;
                      if (target instanceof HTMLElement)
                        target.style.cursor = "pointer";
                    },
                    onClick: getSessionDetails,
                  }}
                />
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center h-[300px] text-slate-500">
                <p>{String(t("dashboard.noDataAvailable"))}</p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Modal */}
      {isDialogOpen && (
        <Modal
          title={String(t("dashboard.userSessionReport"))}
          header={filterTypeLabel}
          openDialog={isDialogOpen}
          closeDialog={closeDialog}
          type="max-w-7xl"
        >
          <UserSessionDetails
            closeDialog={closeDialog}
            courseid={selectedSessionCourseId}
            filterType={filterType}
            filterTypeLabel={filterTypeLabel}
            courseTitle={selectedSessionCourseName}
          />
        </Modal>
      )}

      {isViewAllDialogOpen && (
        <Modal
          title={String(t("dashboard.courseResources"))}
          header={""}
          openDialog={isViewAllDialogOpen}
          closeDialog={handleViewAllDialogClose}
          type="max-w-7xl"
        >
          <ViewAllCourseResources
            courseStatisticsData={courseStatistics}
            onCancel={handleViewAllDialogClose}
          />
        </Modal>
      )}
    </div>
  );
};

export default DBCourseResourceViews;
