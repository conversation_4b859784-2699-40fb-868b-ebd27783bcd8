import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { LOGOUT_CONFIRMATION } from "@/lib/constants";
import { useTranslation } from "react-i18next";
export default function ComfirmLogout({
  onCancel,
  onSave,
}: {
  onSave: () => void;
  onCancel: () => void;
  isModal?: boolean;
}): React.JSX.Element {
  const { t } = useTranslation();
  const handleLogoutClick = (): void => {
    onSave();
  };
  const handleCancelLogout = (): void => {
    onCancel();
  };

  return (
    <>
      <div className="mb-2 mr-4">
        <p className="ml-0 ">{t(LOGOUT_CONFIRMATION)}</p>
      </div>
      <div className="flex topic-icons pr-4">
        <div className="flex items-center justify-center float-right">
          <Button
            type="button"
            className="bg-[#33363F]"
            onClick={handleCancelLogout}
          >
            {t("courses.no")}
          </Button>
          &nbsp;
          <Button
            type="submit"
            className="bg-[#9FC089]"
            onClick={handleLogoutClick}
          >
            {t("courses.yes")}
          </Button>
        </div>
      </div>
    </>
  );
}
