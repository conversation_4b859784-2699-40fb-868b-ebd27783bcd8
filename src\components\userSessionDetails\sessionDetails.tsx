"use client";
import React, { useEffect, useState } from "react";
import type { FC } from "react";
import "../../styles/main.css";
import { Label } from "@/components/ui/label";
import type {
  CheckpointsUserStatsReturnType,
  SessionDetailsProps,
  ToastType,
} from "@/types";
import { DataTable } from "@/components/ui/data-table/data-table";
import { columns } from "./column";
import { columns_not_watched } from "./columns_not_watched";
// import {
//   Document,
//   Page,
//   Text,
//   View,
//   PDFDownloadLink,
// } from "@react-pdf/renderer";
// import { pdfStyle } from "@/components/pdfStyle";
//import type { ComboData } from "@/types";
import moment from "moment";
import { FileDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  ORG_KEY,
  SESSION_FILTER_TYPE_ENROLLED,
  SESSION_FILTER_TYPE_NOT_WATCHED,
} from "@/lib/constants";
import useSessionViews from "@/hooks/useSessionViews";
import useUsers from "@/hooks/useUsers";
//import useCourse from "@/hooks/useCourse";
import useEnrollments from "@/hooks/useEnrollment";
import { Spinner } from "../ui/progressiveLoader";
import * as XLSX from "xlsx";
import { useToast } from "@/components/ui/use-toast";
import { useTranslation } from "react-i18next";
import { ERROR_MESSAGES } from "@/lib/messages";

interface ColumnDefinition {
  key: keyof CheckpointsUserStatsReturnType | "user"; // Allow "user" as a computed field
  label: string;
  getValue?: (row: CheckpointsUserStatsReturnType) => unknown;
}

const UserSessionDetails: FC<SessionDetailsProps> = ({
  closeDialog,
  courseid,
  courseTitle,
  filterType,
  filterTypeLabel,
}): React.JSX.Element => {
  const { t } = useTranslation();
  const { toast } = useToast() as ToastType;
  const [courseId, setCourseId] = useState("");
  const [sessionData, setSessionData] = useState<
    CheckpointsUserStatsReturnType[]
  >([]);

  const { getSessionUsersDetails } = useSessionViews();

  //const [courseData, setCourseData] = useState<ComboData[]>([]);

  //const { getCourseList } = useCourse();

  //const [userName, setUserName] = useState("");
  const [courseName, setCourseName] = useState("");
  const { getUsers } = useUsers();
  const { getEnrollments } = useEnrollments();
  //const [users, setUsers] = useState<{ value: string; label: string }[]>([]);

  const [userId, setUserId] = useState("");
  const [isLoading, setIsLoading] = React.useState<boolean>(true);
  // const styles = pdfStyle;

  const handleExport = (): void => {
    console.log("filterType", filterType);

    if (sessionData.length === 0) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.noDataToExport"),
      });
      return;
    }

    // Define columns dynamically based on filterType
    const getColumns = (filterType: string): ColumnDefinition[] => {
      const baseColumns: ColumnDefinition[] = [
        { key: "sl_no", label: "Sl No" },
        { key: "first_name", label: "First Name" },
        { key: "last_name", label: "Last Name" },
        {
          key: "user",
          label: "User",
          getValue: (row) =>
            `${row.first_name ?? ""} ${row.last_name ?? ""}`.trim(),
        },
        { key: "email", label: "Email" },
        // { key: "video_name", label: "Video Name" },
        { key: "last_watched_on", label: "Last Watched On" },
        // { key: "checkpoint_name", label: "Checkpoint Name" },
        // { key: "exam_start_time", label: "Exam Attended On" },
        { key: "exam_result", label: "Exam Result" },
      ];

      const enrolledColumn: ColumnDefinition = {
        key: "enrollment_time" as keyof CheckpointsUserStatsReturnType,
        label:
          filterType === SESSION_FILTER_TYPE_NOT_WATCHED ||
          filterType === SESSION_FILTER_TYPE_ENROLLED
            ? "Enrolled Date"
            : "Enrolled On",
      };

      baseColumns.splice(3, 0, enrolledColumn); // Insert the "Enrolled Date" column after "Last Name"
      return baseColumns;
    };

    const allColumns = getColumns(filterType);

    // Determine the columns to include based on filterType
    const isFilteredType =
      filterType === SESSION_FILTER_TYPE_NOT_WATCHED ||
      filterType === SESSION_FILTER_TYPE_ENROLLED;

    let filteredColumns: ColumnDefinition[];

    if (isFilteredType) {
      // For filtered types, show only specific columns
      filteredColumns = [
        allColumns.find((col) => col.key === "sl_no")!,
        allColumns.find((col) => col.key === "first_name")!,
        allColumns.find((col) => col.key === "last_name")!,
        allColumns.find((col) => col.key === "email")!,
        allColumns.find((col) => col.key === "enrollment_time")!,
      ];
    } else {
      // For non-filtered types, show all columns in the desired order
      filteredColumns = [
        allColumns.find((col) => col.key === "sl_no")!,
        allColumns.find((col) => col.key === "user")!,
        allColumns.find((col) => col.key === "email")!,
        allColumns.find((col) => col.key === "enrollment_time")!,
        // allColumns.find((col) => col.key === "video_name")!,
        allColumns.find((col) => col.key === "last_watched_on")!,
        // allColumns.find((col) => col.key === "checkpoint_name")!,
        // allColumns.find((col) => col.key === "exam_start_time")!,
        allColumns.find((col) => col.key === "exam_result")!,
      ];
    }

    // Column widths (Separate for filtered & full export)
    const filteredColumnWidths = [
      { key: "sl_no", width: 5 }, // Sl No
      { key: "first_name", width: 20 }, // First Name
      { key: "last_name", width: 20 }, // Last Name
      { key: "email", width: 40 }, // Email
      { key: "enrollment_time", width: 20 }, // Enrolled Date
    ];

    const fullColumnWidths = [
      { key: "sl_no", width: 5 }, // Sl No
      { key: "user", width: 25 }, // User (Full name)
      { key: "email", width: 40 }, // Email
      { key: "enrollment_time", width: 20 }, // Enrolled On
      // { key: "video_name", width: 30 }, // Video Name
      { key: "last_watched_on", width: 20 }, // Last Watched On
      // { key: "checkpoint_name", width: 25 }, // Checkpoint Name
      // { key: "exam_start_time", width: 20 }, // Exam Attended On
      { key: "exam_result", width: 15 }, // Exam Result
    ];

    // Select column widths based on the filter type
    const selectedColumnWidths = isFilteredType
      ? filteredColumnWidths
      : fullColumnWidths;

    // Prepare data for export
    const filteredData = sessionData.map((row, rowIndex) => {
      const rowData: Record<string, unknown> = {};

      filteredColumns.forEach((col) => {
        let value = col.getValue
          ? col.getValue(row)
          : row[col.key as keyof CheckpointsUserStatsReturnType] ?? "";

        if (
          [
            "Enrolled Date",
            "Enrolled On",
            "Last Watched On",
            // "Exam Attended On",
          ].includes(col.label) &&
          value !== null
        ) {
          value = moment.utc(value).local().format("DD-MMM-YYYY hh:mm a");
        }

        if (col.label === "Sl No") value = rowIndex + 1;

        rowData[col.label] = value;
      });

      return rowData;
    });

    // Create new workbook and worksheet
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet([], {});

    // Add the filtered data
    XLSX.utils.sheet_add_json(worksheet, filteredData, {
      origin: "A9",
      skipHeader: false,
    });

    // Set column widths dynamically based on selectedColumns
    const columnWidths = selectedColumnWidths.map((col) => ({
      wch: col.width,
    }));

    // Apply column widths
    worksheet["!cols"] = columnWidths;

    worksheet["!merges"] = [
      { s: { r: 1, c: 0 }, e: { r: 1, c: 10 } }, // Merging A1:E1 for "User Session Report"
      { s: { r: 2, c: 0 }, e: { r: 2, c: 10 } }, // Merging A2:E2 for "Report: ${filterTypeLabel}"
      { s: { r: 5, c: 0 }, e: { r: 5, c: 10 } }, // Merging A2:E2 for "Report: ${filterTypeLabel}"
    ];

    worksheet["A2"] = {
      v: "User Session Report",
      t: "s",
      s: {
        alignment: { horizontal: "center", vertical: "center" },
        font: { bold: true },
      },
    };
    worksheet["A3"] = {
      v: `${filterTypeLabel}`,
      t: "s",
      s: {
        alignment: { horizontal: "center", vertical: "center" },
        font: { bold: true },
      },
    };

    worksheet["A4"] = {
      v: "", // Empty value for an empty row
      t: "s", // String type
      s: {
        alignment: { horizontal: "center", vertical: "center" }, // You can adjust alignment if needed
        font: { bold: false }, // Optionally remove bold if you don't want the empty row styled
      },
    };

    // Add "course selected" and "user"
    worksheet["A6"] = {
      v: `Course Selected: ${courseName}`, // First cell in the merged range
      t: "s", // String type
      s: {
        alignment: { horizontal: "center", vertical: "center" },
        font: { bold: true },
      },
    };

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, "User Session Report");

    // Export the file
    XLSX.writeFile(workbook, `User_Session_Report.xlsx`);
  };

  // const PageToDownload: React.FC<{
  //   filterType: string;
  //   sessionData: CheckpointsUserStatsReturnType[];
  // }> = ({ filterType, sessionData }) => (
  //   <Document>
  //     <Page size="A4" style={styles.page}>
  //       <View style={styles.section}>
  //         <View style={styles.header}>
  //           <Text style={styles.headerText}>User Session Details</Text>
  //         </View>
  //         {filterType === SESSION_FILTER_TYPE_NOT_WATCHED ||
  //         filterType === SESSION_FILTER_TYPE_ENROLLED ? (
  //           <View style={styles.tableContainer}>
  //             <View style={styles.tableRow}>
  //               <View style={styles.tableHeader}>
  //                 <Text>SI No</Text>
  //               </View>
  //               <View style={styles.tableHeader}>
  //                 <Text>First Name</Text>
  //               </View>
  //               <View style={styles.tableHeader}>
  //                 <Text>Last Name</Text>
  //               </View>
  //               <View style={styles.tableHeader}>
  //                 <Text>Enrolled Date</Text>
  //               </View>
  //             </View>
  //             {sessionData.map((item, index) => (
  //               <View key={index} style={styles.tableRow}>
  //                 <View style={styles.tableCell}>
  //                   <Text>{index + 1}</Text>
  //                 </View>
  //                 <View style={styles.tableCell}>
  //                   <Text>{item.first_name}</Text>
  //                 </View>
  //                 <View style={styles.tableCell}>
  //                   <Text>{item.last_name}</Text>
  //                 </View>
  //                 <View style={styles.tableCell}>
  //                   <Text>
  // {moment
  //   .utc(item.enrollment_time)
  //   .local()
  //   .format("DD-MMM-YYYY hh:mm a")}
  //                   </Text>
  //                 </View>
  //               </View>
  //             ))}
  //           </View>
  //         ) : (
  //           <View style={styles.tableContainer}>
  //             <View style={styles.tableRow}>
  //               <View style={styles.tableHeader}>
  //                 <Text>SI No</Text>
  //               </View>
  //               <View style={styles.tableHeader}>
  //                 <Text>User</Text>
  //               </View>
  //               <View style={styles.tableHeader}>
  //                 <Text>Enrolled On</Text>
  //               </View>
  //               <View style={styles.tableHeader}>
  //                 <Text>Video Name</Text>
  //               </View>
  //               <View style={styles.tableHeader}>
  //                 <Text>Last Watched On</Text>
  //               </View>
  //               <View style={styles.tableHeader}>
  //                 <Text>Check Point Name</Text>
  //               </View>
  //               <View style={styles.tableHeader}>
  //                 <Text>Exam Attended On</Text>
  //               </View>
  //               <View style={styles.tableHeader}>
  //                 <Text>Checkpoint Result</Text>
  //               </View>
  //             </View>
  //             {sessionData.map((item, index) => (
  //               <View key={index} style={styles.tableRow}>
  //                 <View style={styles.tableCell}>
  //                   <Text>{index + 1}</Text>
  //                 </View>
  //                 <View style={styles.tableCell}>
  //                   <Text>{item.first_name}</Text>
  //                 </View>
  //                 <View style={styles.tableCell}>
  //                   <Text>
  // {moment
  //   .utc(item.enrollment_time)
  //   .local()
  //   .format("DD-MMM-YYYY hh:mm a")}
  //                   </Text>
  //                 </View>
  //                 <View style={styles.tableCell}>
  //                   <Text>{item.video_name}</Text>
  //                 </View>
  //                 <View style={styles.tableCell}>
  //                   <Text>
  // {moment
  //   .utc(item.last_watched_on ?? "")
  //   .local()
  //   .format("DD-MMM-YYYY hh:mm a")}
  //                   </Text>
  //                 </View>
  //                 <View style={styles.tableCell}>
  //                   <Text>{item.checkpoint_name}</Text>
  //                 </View>
  //                 <View style={styles.tableCell}>
  //                   <Text>
  // {moment
  //   .utc(item.exam_start_time ?? "")
  //   .local()
  //   .format("DD-MMM-YYYY hh:mm a")}
  //                   </Text>
  //                 </View>
  //                 <View style={styles.tableCell}>
  //                   <Text>{item.exam_result}</Text>
  //                 </View>
  //               </View>
  //             ))}
  //           </View>
  //         )}
  //       </View>
  //     </Page>
  //   </Document>
  // );
  useEffect(() => {
    setCourseId(courseid ?? "");
    setCourseName(courseTitle ?? "");

    const fetchInitialData = async (): Promise<void> => {
      /* const courseList = await getCourseList();
      const filteredCourses = courseList
        .filter((course) => course.course_id == courseId)
        .map((course) => ({
          value: course.course_id,
          label: course.full_name,
        })); */

      /* if (filteredCourses.length > 0) {
        setCourseData(filteredCourses);
        //setCourseName(filteredCourses[0].label as string);
      } else {
        setCourseData([]);
      } */

      const id_org = localStorage.getItem("orgId");
      const orgId = id_org ?? "";
      const fetchedUsers = await getUsers(orgId);
      const filteredUsers = fetchedUsers
        .filter(
          (user) =>
            user.id !== null &&
            user.first_name !== null &&
            user.last_name !== null,
        )
        .map((user) => ({
          value: user.id,
          label: user.first_name + " " + user.last_name,
        }));

      if (filteredUsers.length > 0) {
        //setUsers(filteredUsers);
      } else {
        //setUsers([]);
      }

      setUserId(filteredUsers[0]?.value);
    };

    fetchInitialData()
      .then((response) => {
        console.log(response);
      })
      .catch((error) => {
        console.log(error);
        setIsLoading(false);
      });
  }, [courseId]);

  useEffect(() => {
    const fetchSessionData = async (): Promise<void> => {
      setIsLoading(true);
      if (userId !== "") {
        try {
          const userSessions: CheckpointsUserStatsReturnType[] = [];
          if (filterType === SESSION_FILTER_TYPE_ENROLLED) {
            const userEnrollmentsStatistics = await getEnrollments(courseId);

            if (
              userEnrollmentsStatistics !== null ||
              userEnrollmentsStatistics !== undefined
            ) {
              userEnrollmentsStatistics.forEach((enrolmentElement) => {
                userSessions.push({
                  email: enrolmentElement.email ?? "",
                  user_id: enrolmentElement.id,
                  video_id: "",
                  last_name: enrolmentElement.last_name,
                  first_name: enrolmentElement.first_name,
                  video_name: "",
                  exam_result: "",
                  checkpoint_name: "",
                  enrollment_time: enrolmentElement.enrolled_date,
                  exam_start_time: "",
                  last_watched_on: "",
                });
              });

              setSessionData(userSessions as CheckpointsUserStatsReturnType[]);
            }
            setIsLoading(false);
          } else {
            const userSessionStatistics = await getSessionUsersDetails(
              localStorage.getItem(ORG_KEY),
              courseId,
              null,
              filterType,
            );

            if (
              userSessionStatistics !== null ||
              userSessionStatistics !== undefined
            ) {
              userSessionStatistics.forEach((element) => {
                userSessions.push({
                  email: element.email,
                  user_id: element.user_id,
                  video_id: element.video_id,
                  last_name: element.last_name,
                  first_name: element.first_name,
                  video_name: element.video_name,
                  exam_result: element.exam_result,
                  checkpoint_name: element.checkpoint_name,
                  enrollment_time: element.enrollment_time,
                  exam_start_time: element.exam_start_time,
                  last_watched_on: element.last_watched_on,
                });
              });

              setSessionData(userSessions as CheckpointsUserStatsReturnType[]);
            }
            setIsLoading(false);
          }
        } catch (error) {
          console.error("Error fetching data:", error);
        }
      }
    };
    fetchSessionData()
      .then((response) => {
        console.log(response);
      })
      .catch((error) => {
        console.log(error);
      });
  }, [userId]);

  /* function handleResourseChange(value: string): void {
    console.log(value);
  }
  function handleUserChange(value: string): void {
    setUserId(value);
    const sessionData: CheckpointsUserStatsReturnType[] = [];
    setSessionData(sessionData);
  } */

  const addSessionClose = (): void => {
    closeDialog(true);
  };

  return (
    <div>
      <div className="border rounded-md p-4 mt-4">
        <div className="w-full flex flex-wrap justify-between space-x-4">
          <div className="flex space-x-4">
            <div className="w-full sm:w-1/2">
              <div className="w-full">
                <Label className="block">
                  {" "}
                  {String(t("dashboard.courseSelected"))}
                </Label>
                <div className="w-full course-width mt-2">
                  {courseName}
                  {/* <Combobox
                    data={courseData}
                    onSelectChange={handleResourseChange}
                    defaultLabel={courseName}
                    isDisabled={true}
                  /> */}
                </div>
              </div>
            </div>
            <div className="w-full sm:w-1/2">
              <div className="w-full">
                <Label className="block">{String(t("dashboard.user"))}</Label>
                <div className="w-full course-width mt-2">
                  All
                  {/* <Combobox
                    data={users}
                    onSelectChange={handleUserChange}
                    placeHolder={"All"}
                    isDisabled={true}
                  /> */}
                </div>
              </div>
            </div>
          </div>
          <div
            className="flex justify-end items-center"
            title={String(t("dashboard.exportExcel"))}
          >
            <FileDown
              onClick={handleExport}
              className="text-green-600 cursor-pointer hover:text-green-700 mr-2"
              style={{ fontSize: "24px" }}
            />
          </div>

          {/* <PDFDownloadLink
            document={
              <PageToDownload
                filterType={filterType}
                sessionData={sessionData}
              />
            }
            fileName="page-to-download.pdf"
          >
            {({ loading }) =>
              loading === true ? (
                "Loading document..."
              ) : (
                <div title="Download PDF">
                  <Download />
                </div>
              )
            }
          </PDFDownloadLink> */}
        </div>
      </div>
      <div>
        {isLoading ? (
          <Spinner />
        ) : (
          <>
            <div>
              {sessionData !== null ? (
                <DataTable
                  columns={
                    filterType === SESSION_FILTER_TYPE_NOT_WATCHED ||
                    filterType === SESSION_FILTER_TYPE_ENROLLED
                      ? columns_not_watched
                      : columns
                  }
                  data={sessionData}
                  FilterLabel={
                    filterType === SESSION_FILTER_TYPE_NOT_WATCHED ||
                    filterType === SESSION_FILTER_TYPE_ENROLLED
                      ? "Filer by First Name"
                      : "Filter by User"
                  }
                  FilterBy={"first_name"}
                  actions={[]}
                  onSelectedDataChange={() => {}}
                />
              ) : (
                ""
              )}
            </div>
            <div className="flex flex-wrap justify-end mt-8">
              <div className="mt-6 flex items-center justify-end gap-x-6">
                <Button
                  type="button"
                  variant="outline"
                  className="primary"
                  onClick={addSessionClose}
                >
                  {String(t("buttons.close"))}
                </Button>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};
export default UserSessionDetails;
