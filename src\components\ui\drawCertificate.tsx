"use client";
import type { FC } from "react";
import React, { useRef, useEffect, useState } from "react";
import html2canvas from "html2canvas";
import "../../styles/main.css";
import type { CertificateProps } from "@/types";
import { Modal } from "../ui/modal";
import Email from "../ui/sent-mail";
import moment from "moment";

const Certificate: FC<CertificateProps> = ({
  certificateData,
}): React.JSX.Element => {
  const certificateRef = useRef<HTMLDivElement | null>(null);
  const [openMail, setOpenMail] = useState<boolean>(false);
  const [certificateBlob, setCertificateBlob] = useState<Blob | null>(null);
  const [certificateImage, setCertificateImage] = useState<File | null>(null);

  const drawCertificate = (): void => {
    console.log("certificateData", certificateData);
    const canvas = certificateRef.current?.querySelector("canvas");
    if (canvas) {
      const ctx = canvas.getContext("2d");
      if (ctx) {
        const canvasWidth = 800;
        const canvasHeight = 600;
        canvas.width = canvasWidth;
        canvas.height = canvasHeight;
        const backgroundColor = "#f9f9f9";
        const borderColor = "#FFD700";
        const titleColor = "#FF4500";
        const textColor = "#333";
        const titleFont = "bold 30px 'Georgia'";
        const subTitleFont = "24px 'Georgia'";
        const awardedFont = "24px 'Georgia'";
        const courseFont = "30px 'Georgia'";
        const userFont = "30px 'Georgia'";
        ctx.fillStyle = backgroundColor;
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        ctx.strokeStyle = borderColor;
        ctx.lineWidth = 6;
        ctx.strokeRect(5, 5, canvas.width - 10, canvas.height - 10);
        ctx.strokeStyle = "#b8860b";
        ctx.lineWidth = 4;
        ctx.strokeRect(15, 15, canvas.width - 30, canvas.height - 30);
        const gradient = ctx.createLinearGradient(
          15,
          15,
          canvas.width - 15,
          canvas.height - 15,
        );
        gradient.addColorStop(0, "#FFF0A3 ");
        // gradient.addColorStop(1, "#ffd700");
        ctx.fillStyle = gradient;
        ctx.fillRect(15, 15, canvas.width - 30, canvas.height - 30);

        // Top Image
        const topImage = new Image();
        topImage.src = "/assets/citrus2.jpg";
        const topImageHeight = 80;
        topImage.onload = () => {
          const topImageWidth =
            (topImage.width * topImageHeight) / topImage.height;
          ctx.drawImage(
            topImage,
            (canvas.width - topImageWidth) / 2,
            20,
            topImageWidth,
            topImageHeight,
          );

          // Title and other text
          ctx.font = titleFont;
          ctx.fillStyle = titleColor;
          ctx.textAlign = "center";
          // ctx.shadowColor = "rgba(0, 0, 0, 0.5)";
          ctx.shadowOffsetX = 3;
          ctx.shadowOffsetY = 3;
          ctx.shadowBlur = 6;
          ctx.fillText("CERTIFICATE OF ACHIEVEMENT", canvas.width / 2, 150);
          ctx.shadowColor = "transparent";
          ctx.font = subTitleFont;
          ctx.fillStyle = textColor;
          ctx.fillText("Presented to", canvas.width / 2, 220);
          ctx.font = userFont;
          ctx.fillText(certificateData.user_name, canvas.width / 2, 260); // Centered
          ctx.font = awardedFont;
          ctx.fillText("For successful completion of", canvas.width / 2, 320);
          ctx.font = courseFont;
          ctx.fillText(
            (certificateData.course_name as string) + " " + "Course",
            canvas.width / 2,
            380,
          );

          // Duration calculation
          // const totalSeconds = certificateData.resources.reduce(
          //   (total, item) => {
          //     if (item.time_spent != null) {
          //       const [hours, minutes, seconds] = item.time_spent
          //         .split(":")
          //         .map(Number);
          //       return total + hours * 3600 + minutes * 60 + seconds;
          //     }
          //     return total; // Ignore invalid values
          //   },
          //   0,
          // );

          // const hours = Math.floor(totalSeconds / 3600);
          // const minutes = Math.floor((totalSeconds % 3600) / 60);
          // const seconds = totalSeconds % 60;
          // const formattedTime = [hours, minutes, seconds]
          //   .map((unit) => String(unit).padStart(2, "0"))
          //   .join(":");

          // ctx.fillText(`Duration: ${formattedTime}`, canvas.width / 2, 450);
          ctx.font = "16px 'Georgia'";
          ctx.fillStyle = "red";
          ctx.fillText(
            "This certificate is digitally signed",
            canvas.width / 5,
            550,
          );
          ctx.fillStyle = "black";
          ctx.fillText(
            "Date: " +
              moment
                .utc(certificateData.attended_at)
                .local()
                .format("DD-MMM-YYYY hh:mm a"),
            canvas.width - 150,
            550,
          );

          // Draw logo image centered with certificate color background
          const logoImage = new Image();
          logoImage.src = "/static/images/smartlearn.png";
          logoImage.onload = () => {
            const logoHeight = 80;
            const logoWidth = (logoImage.width * logoHeight) / logoImage.height;
            const centerX = (canvas.width - logoWidth) / 2;
            const centerY = 470; // Position just under the "Successfully Signed" text
            const yellowBackgroundColor = "#FFD700"; // Yellow color

            ctx.fillStyle = yellowBackgroundColor;
            // Draw logo image at the center
            ctx.drawImage(logoImage, centerX, centerY, logoWidth, logoHeight);

            // After the image is drawn, set the certificate blob
            canvas.toBlob((blob) => {
              if (blob) {
                setCertificateBlob(blob); // Set the Blob state
              }
            }, "image/png");
          };
        };
      }
    }
  };

  //  download certificate code

  //   const downloadImage = async (): Promise<void> => {
  //     if (certificateRef.current) {
  //       const canvasElement = certificateRef.current.querySelector("canvas");
  //       if (canvasElement) {
  //         const canvas = await html2canvas(certificateRef.current);
  //         const dataUrl = canvas.toDataURL("image/png");
  //         const link = document.createElement("a");
  //         link.href = dataUrl;
  //         link.download = "certificate.png";
  //         document.body.appendChild(link);
  //         link.click();
  //         document.body.removeChild(link); // Clean up
  //       }
  //     }
  //   };

  const sentMail = async (): Promise<void> => {
    if (certificateRef.current) {
      console.log("certificateBlob", certificateBlob);
      const canvas = await html2canvas(certificateRef.current);
      canvas.toBlob((blob) => {
        if (blob) {
          const file = new File([blob], "certificate.png", {
            type: "image/png",
            lastModified: Date.now(),
          });
          const dataTransfer = new DataTransfer();
          dataTransfer.items.add(file);
          console.log(dataTransfer.files);
          setCertificateImage(dataTransfer.files[0]);
        }
      }, "image/png");
      setOpenMail(true);
    }
  };
  const closeEmailDialog = (): void => {
    setOpenMail(false);
  };

  useEffect(() => {
    drawCertificate();
    console.log(certificateData);
  }, []);

  return (
    <div>
      <div className="flex flex-col items-center">
        <span ref={certificateRef}>
          <canvas className="border border-gray-300 " />
        </span>
        <div className="flex gap-2">
          <button
            // eslint-disable-next-line @typescript-eslint/no-misused-promises
            onClick={sentMail}
            className="bg-[#9FC089] text-white rounded px-4 py-2 mt-4"
          >
            Send Email
          </button>
        </div>
      </div>
      {openMail && (
        <Modal
          title=""
          header=""
          openDialog={openMail}
          closeDialog={closeEmailDialog}
          type="max-w-5xl"
        >
          <Email
            onSave={closeEmailDialog}
            onCancel={closeEmailDialog}
            isCertificate={true}
            emailId={certificateData.user_email}
            recipient={certificateData.user_name}
            course={certificateData.course_name}
            cerificateImage={certificateImage}
          />
        </Modal>
      )}
    </div>
  );
};

export default Certificate;
