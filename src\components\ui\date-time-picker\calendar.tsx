"use client";

import type { CalendarDate } from "@internationalized/date";
import {
  isToday as _isToday,
  createCalendar,
  getLocalTimeZone,
  getWeeksInMonth,
} from "@internationalized/date";
import { ChevronLeftIcon, ChevronRightIcon } from "lucide-react";
import React, { useMemo } from "react";
import type { CalendarProps, DateValue } from "react-aria";
import {
  useButton,
  useCalendar,
  useCalendarCell,
  useCalendarGrid,
  useLocale,
} from "react-aria";
import type { CalendarState } from "react-stately";
import { useCalendarState } from "react-stately";
import { cn } from "@/lib/utils";
import { Button } from "../button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../select";

function Calendar(props: CalendarProps<DateValue>): React.JSX.Element {
  const prevButtonRef = React.useRef<HTMLButtonElement | null>(null);
  const nextButtonRef = React.useRef<HTMLButtonElement | null>(null);

  const { locale } = useLocale();
  const state = useCalendarState({
    ...props,
    locale,
    createCalendar,
  });
  const {
    calendarProps,
    prevButtonProps: _prevButtonProps,
    nextButtonProps: _nextButtonProps,
    title,
  } = useCalendar(props, state);
  const { buttonProps: prevButtonProps } = useButton(
    _prevButtonProps,
    prevButtonRef,
  );
  const { buttonProps: nextButtonProps } = useButton(
    _nextButtonProps,
    nextButtonRef,
  );

  // Generate year options (current year ± 50 years)
  const currentYear = new Date().getFullYear();
  const yearOptions = useMemo(() => {
    const years = [];
    for (let year = currentYear - 50; year <= currentYear + 50; year++) {
      years.push(year);
    }
    return years;
  }, [currentYear]);

  // Extract current year from the calendar state
  const selectedYear = state.visibleRange.start.year;

  // Handle year selection
  const handleYearChange = (year: string): void => {
    const newYear = parseInt(year);
    const newDate = state.visibleRange.start.set({ year: newYear });
    state.setFocusedDate(newDate);
  };

  // Parse title to get month name
  const titleParts = title.split(" ");
  const monthName = titleParts[0];

  return (
    <div {...calendarProps} className="space-y-4">
      <div className="relative flex items-center justify-center pt-1">
        <Button
          {...prevButtonProps}
          ref={prevButtonRef}
          variant={"outline"}
          className={cn(
            "absolute left-1 h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100",
          )}
        >
          <ChevronLeftIcon className="w-4 h-4" />
        </Button>

        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">{monthName}</span>
          <Select
            value={selectedYear.toString()}
            onValueChange={handleYearChange}
          >
            <SelectTrigger className="w-20 h-7 text-sm font-medium border-none shadow-none p-1 hover:bg-accent">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="max-h-60">
              {yearOptions.map((year) => (
                <SelectItem key={year} value={year.toString()}>
                  {year}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <Button
          {...nextButtonProps}
          ref={nextButtonRef}
          variant={"outline"}
          className={cn(
            "absolute right-1 h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100",
          )}
        >
          <ChevronRightIcon className="w-4 h-4" />
        </Button>
      </div>
      <CalendarGrid state={state} />
    </div>
  );
}

interface CalendarGridProps {
  state: CalendarState;
}

function CalendarGrid({
  state,
  ...props
}: CalendarGridProps): React.JSX.Element {
  const { locale } = useLocale();
  const { gridProps, headerProps, weekDays } = useCalendarGrid(props, state);

  // Get the number of weeks in the month so we can render the proper number of rows.
  const weeksInMonth = getWeeksInMonth(state.visibleRange.start, locale);

  return (
    <table
      {...gridProps}
      className={cn(gridProps.className, "w-full border-collapse space-y-1")}
    >
      <thead {...headerProps}>
        <tr className="flex">
          {weekDays.map((day, index) => (
            <th
              className="w-9 rounded-md text-[0.8rem] font-normal text-muted-foreground"
              key={index}
            >
              {day}
            </th>
          ))}
        </tr>
      </thead>
      <tbody>
        {[...new Array(weeksInMonth).keys()].map((weekIndex: number) => (
          <tr className="flex w-full mt-2" key={weekIndex}>
            {state
              .getDatesInWeek(weekIndex)
              .map((date, i) =>
                date ? (
                  <CalendarCell key={i} state={state} date={date} />
                ) : (
                  <td key={i} />
                ),
              )}
          </tr>
        ))}
      </tbody>
    </table>
  );
}

interface CalendarCellProps {
  state: CalendarState;
  date: CalendarDate;
}

function CalendarCell({ state, date }: CalendarCellProps): React.JSX.Element {
  const ref = React.useRef<HTMLButtonElement | null>(null);
  const {
    cellProps,
    buttonProps,
    isSelected,
    isOutsideVisibleRange,
    isDisabled,
    formattedDate,
  } = useCalendarCell({ date }, state, ref);

  const isToday = useMemo(() => {
    const timezone = getLocalTimeZone();
    return _isToday(date, timezone);
  }, [date]);

  return (
    <td
      {...cellProps}
      className={cn(
        cellProps.className,
        "relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md",
      )}
    >
      <Button
        {...buttonProps}
        type="button"
        variant={"ghost"}
        ref={ref}
        className={cn(
          buttonProps.className,
          "h-9 w-9",
          isToday ? "bg-accent text-accent-foreground" : "",
          isSelected
            ? "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground"
            : "",
          isOutsideVisibleRange ? "text-muted-foreground opacity-50" : "",
          isDisabled ? "text-muted-foreground opacity-50" : "",
        )}
      >
        {formattedDate}
      </Button>
    </td>
  );
}

export { Calendar };
