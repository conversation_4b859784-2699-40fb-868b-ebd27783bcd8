"use client";
import type {
  CheckpointsCourseStats,
  CheckpointsCourseStatsRowDefinition,
} from "@/types";
import type { ColumnDef } from "@tanstack/react-table";
export const columns: ColumnDef<CheckpointsCourseStats>[] = [
  {
    accessorKey: "course_short_name",
    header: "Course Name",
    cell: ({ row }: CheckpointsCourseStatsRowDefinition): React.JSX.Element => (
      <div className="text-align">{row.original.course_short_name ?? ""}</div>
    ),
  },
  {
    accessorKey: "enrolled_users",
    header: "Enrolled Users",
    cell: ({ row }: CheckpointsCourseStatsRowDefinition): React.JSX.Element => (
      <div className="text-center text-[#1ea185]">
        {row.original.enrolled_users ?? ""}
      </div>
    ),
  },
  {
    accessorKey: "watched_users",
    header: "Watched Users",
    cell: ({ row }: CheckpointsCourseStatsRowDefinition): React.JSX.Element => (
      <div className="text-center text-[#964B00]">
        {row.original.watched_users ?? ""}
      </div>
    ),
  },
  {
    accessorKey: "unwatched_users",
    header: "Unwatched Users",
    cell: ({ row }: CheckpointsCourseStatsRowDefinition): React.JSX.Element => (
      <div className="text-center text-[#f29b26]">
        {row.original.unwatched_users ?? ""}
      </div>
    ),
  },
  {
    accessorKey: "passed_users",
    header: "Passed Users",
    cell: ({ row }: CheckpointsCourseStatsRowDefinition): React.JSX.Element => (
      <div className="text-center text-[#008000]">
        {row.original.passed_users ?? ""}
      </div>
    ),
  },
  {
    accessorKey: "failed_users",
    header: "Failed Users",
    cell: ({ row }: CheckpointsCourseStatsRowDefinition): React.JSX.Element => (
      <div className="text-center text-[#bd392f]">
        {row.original.failed_users ?? ""}
      </div>
    ),
  },
  {
    accessorKey: "pending_users",
    header: "Pending Users",
    cell: ({ row }: CheckpointsCourseStatsRowDefinition): React.JSX.Element => (
      <div className="text-center text-[#756544]">
        {row.original.pending_users ?? ""}
      </div>
    ),
  },
];
