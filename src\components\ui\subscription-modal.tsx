import React, { useEffect, useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { MembershipPlanEditModalSchema } from "@/schema/schema";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Form,
} from "./form";
import { DateTimePicker } from "./date-time-picker/date-time-picker";
import type { DateValue, ZonedDateTime } from "@internationalized/date";
import { DATE_FORMAT, FORMATTED_DATE_FORMAT, ORG_KEY } from "@/lib/constants";
import useSubscription from "@/hooks/useSubscription";
import { parseZonedDateTime } from "@internationalized/date";
import moment from "moment-timezone";
import type {
  ErrorCatch,
  SubscriptionListResults,
  SubscriptionPlans,
  ToastType,
} from "@/types";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import { useTranslation } from "react-i18next";

export default function EditSubscription({
  data,
  onCancel,
  onSave,
}: {
  onCancel: () => void;
  onSave: () => void;
  data: SubscriptionListResults;

  isModal?: boolean;
}): React.JSX.Element {
  const { t } = useTranslation();
  const { toast } = useToast() as ToastType;
  const [status, setStatus] = useState("");
  const [defaultDate, setDefaultDate] = useState<ZonedDateTime | DateValue>();
  const [endDateTime, setEndDateTime] = useState<ZonedDateTime | DateValue>();
  const form = useForm<SubscriptionPlans>({
    resolver: zodResolver(MembershipPlanEditModalSchema),
  });
  const { updateSubscription } = useSubscription();
  // const [startDateTime, setStartDateTime] = useState<
  //   ZonedDateTime | DateValue
  // >();

  useEffect(() => {
    const subscriptionData = data;
    const currentTimezone = moment.tz.guess();
    const parsedDatetime = moment.tz(
      subscriptionData.valid_from.split("+")[0],
      currentTimezone,
    );
    const formattedDatetime =
      parsedDatetime.format(DATE_FORMAT) + `[${currentTimezone}]`;
    const dateTime = parseZonedDateTime(formattedDatetime);
    form.setValue("valid_from", dateTime);
    form.setValue(
      "subscription_plan_status",
      subscriptionData.subscription_plan_status,
    );
    setStatus(subscriptionData.subscription_plan_status);
    setDefaultDate(dateTime);
    // setStartDateTime(dateTime);
    const parsedDatetimeValidTo = moment.tz(
      subscriptionData.valid_to.split("+")[0],
      currentTimezone,
    );
    const formattedEndDatetime =
      parsedDatetimeValidTo.format(DATE_FORMAT) + `[${currentTimezone}]`;
    const endDateTime = parseZonedDateTime(formattedEndDatetime);
    form.setValue("valid_to", endDateTime);
    setEndDateTime(endDateTime);
  }, []);
  async function onSubmit(): Promise<void> {
    const formData = form.getValues();
    const dateStart = formData.valid_from;
    const startDate = new Date(
      dateStart.year,
      dateStart.month - 1,
      dateStart.day,
      dateStart.hour,
      dateStart.minute,
    );
    const dateEnd = formData.valid_to;
    const endDate = new Date(
      dateEnd.year,
      dateEnd.month - 1,
      dateEnd.day,
      dateEnd.hour,
      dateEnd.minute,
    );
    const momentStartDate = moment(startDate);
    const momentEndDate = moment(endDate);
    const formattedValidFrom = momentStartDate.format(FORMATTED_DATE_FORMAT);
    const formattedValidTo = momentEndDate.format(FORMATTED_DATE_FORMAT);
    const org_id = localStorage.getItem(ORG_KEY) ?? "";
    const subscriptionPlanData = {
      valid_from: formattedValidFrom,
      valid_to: formattedValidTo,
      status: formData.subscription_plan_status,
    };

    const editParams = {
      org_id: org_id,
      plan_id: data.id,
      subscription_plan_data: subscriptionPlanData,
    };

    const updateSubscriptions = async (): Promise<void> => {
      try {
        const subscriptions = await updateSubscription(editParams);
        onSave();
        if (subscriptions?.status === "success") {
          toast({
            variant: SUCCESS_MESSAGES.toast_variant_default,
            title: t("successMessages.update_subscription_title"),
            description: t("successMessages.update_subscription_msg"),
          });
        }
      } catch (e) {
        const err = e as ErrorCatch;
        let errMsg = "";
        let errTtile = "";
        if (err.message === ERROR_MESSAGES.subscription_update_date) {
          errMsg = err.message.split("Invalid date range.")[1].trim();
          errTtile = ERROR_MESSAGES.invalid_date_msg;
        } else if (err.message === ERROR_MESSAGES.invalid_validity) {
          errMsg = err.message.split("Invalid validity period:")[1].trim();
          errTtile = ERROR_MESSAGES.invalid_date_msg;
        } else {
          errMsg = err.message;
          errTtile = ERROR_MESSAGES.update_subscription_title;
        }
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: errTtile,
          description: errMsg,
        });
      }
    };
    updateSubscriptions().catch((error) => console.log(error));
  }

  const cancelModal = (): void => {
    onCancel();
  };

  return (
    <>
      <div className="w-full">
        <div className="border rounded-md p-4 mt-4">
          <Form {...form}>
            <form onSubmit={(event) => void form.handleSubmit(onSubmit)(event)}>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4">
                {/* <div className="row-span-2 mt-4">
                  <FormField
                    name="valid_from"
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Valid from <span className="text-red-700">*</span>
                        </FormLabel>
                        <FormControl>
                          <DateTimePicker
                            granularity={"minute"}
                            minValue={defaultDate}
                            value={field.value as DateValue}
                            defaultValue={startDateTime}
                            onChange={(newDate) => {
                              field.onChange(newDate);
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>  */}

                <div className="row-span-2">
                  <FormField
                    name="subscription_plan_status"
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t("subscriptionPlan.status")}
                          <span className="text-red-700">*</span>
                        </FormLabel>
                        <Select
                          name="subscription_plan_status"
                          onValueChange={(value) => {
                            setStatus(value);
                            field.onChange(value);
                          }}
                          value={status}
                        >
                          <FormControl>
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <>
                              <SelectItem value="active">{"Active"}</SelectItem>
                              <SelectItem value="inactive">
                                {"Inactive"}
                              </SelectItem>
                            </>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="row-span-2 ">
                  <FormField
                    name="valid_to"
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t("subscriptionPlan.validTo")}{" "}
                          <span className="text-red-700">*</span>
                        </FormLabel>
                        <FormControl>
                          <DateTimePicker
                            granularity="minute"
                            minValue={defaultDate}
                            value={field.value as DateValue}
                            defaultValue={endDateTime}
                            hideTimeZone={true}
                            onChange={(newDate) => {
                              field.onChange(newDate);
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <div className="w-full flex justify-end mt-4">
                <div className="px-4">
                  <Button
                    variant="outline"
                    className="w-full sm:w-auto"
                    onClick={cancelModal}
                    type="button"
                  >
                    {t("buttons.cancel")}
                  </Button>
                </div>
                <div>
                  <Button type="submit">{t("buttons.submit")}</Button>
                </div>
              </div>
            </form>
          </Form>
        </div>
      </div>
    </>
  );
}
