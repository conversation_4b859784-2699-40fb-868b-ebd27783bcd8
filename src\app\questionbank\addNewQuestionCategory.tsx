import React, { useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";

import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { ModalButton } from "@/components/ui/modalButton";
import type {
  AddQuestionCategoryForm,
  ErrorCatch,
  LogUserActivityRequest,
  QuestionCategory,
  QuestionCategoryRequest,
  ToastType,
} from "@/types";
import { AddQuestionCategoryschema } from "@/schema/schema";
import { useToast } from "@/components/ui/use-toast";
import useQuestionBank from "@/hooks/useQuestionBank";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";

export const AddNewQuesionCategoryForm: React.FC<QuestionCategory> = (
  props,
): JSX.Element => {
  const { toast } = useToast() as ToastType;
  const { t } = useTranslation();
  const { addQuestionCategory, updateQuestionCategory } = useQuestionBank();

  const form = useForm<AddQuestionCategoryForm>({
    resolver: zodResolver(AddQuestionCategoryschema),
  });
  const { updateUserActivity } = useLogUserActivity();

  useEffect(() => {
    if (props.data.value !== "") {
      form.setValue("categoryName", props.data.label);
      form.setValue("description", props.data.label_description as string);
    }
  }, []);

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    const value = e.target.value;
    const sanitizedValue = value
      .trimStart()
      .replace(/^\p{P}+/u, "")
      .replace(/[^\p{L}\p{M}\p{N}\s&-]/gu, "")
      .replace(/\s{2,}/g, " ")
      .replace(/^[^\p{L}\p{M}\p{N}]|[^\p{L}\p{M}\p{N}\s&-]/gu, "")
      .replace(/\s+$/, (match) =>
        match.length > 1 ? match.slice(0, -1) : match,
      );
    form.setValue("categoryName", sanitizedValue);
  };
  const handleDescriptionChange = (
    e: React.ChangeEvent<HTMLInputElement>,
  ): void => {
    const value = e.target.value;
    const sanitizedValue = value
      .trimStart()
      .replace(/^\p{P}+/u, "")
      .replace(/[^\p{L}\p{M}\p{N}\s\-!@#$%^&*()_+={}[\]:;"'<>,.?/\\|~`]/gu, "")
      .replace(/\s{2,}/g, " ")
      .replace(
        /^[^\p{L}\p{M}\p{N}]|[^\p{L}\p{M}\p{N}\s\-!@#$%^&*()_+={}[\]:;"'<>,.?/\\|~`]/gu,
        "",
      )
      .replace(/\s+$/, (match) =>
        match.length > 1 ? match.slice(0, -1) : match,
      );

    form.setValue("description", sanitizedValue);
  };

  async function onSubmit(data: AddQuestionCategoryForm): Promise<void> {
    const org_id = localStorage.getItem("orgId");

    const transformedData = {
      org_id: org_id,
      category_data: {
        name: data.categoryName,
        description: data.description,
        parent_id: null,
      },
    };

    if (props.data.value === "") {
      try {
        const result = await addQuestionCategory(
          transformedData as QuestionCategoryRequest,
        );

        if (result.status == "success") {
          toast({
            variant: SUCCESS_MESSAGES.toast_variant_default,
            title: t("successMessages.questionCategory"),
            description: t("successMessages.categoryAdded"),
          });
          const params = {
            activity_type: "Question_Category",
            screen_name: "Question_Category",
            action_details: "Question category added successfully",
            target_id: result.category_id as string,
            log_result: "SUCCESS",
          };
          void updateLogUserActivity(params).catch((error) => {
            console.error(error);
          });
          props.closeDialog(false);
          props.categoryList(true);
        } else {
          toast({
            variant: ERROR_MESSAGES.toast_variant_destructive,
            title: t("errorMessages.toast_error_title"),
            description: result.status,
          });
          const params = {
            activity_type: "Question_Category",
            screen_name: "Question_Category",
            action_details: "Failed to add question category",
            target_id: result.category_id as string,
            log_result: "ERROR",
          };
          void updateLogUserActivity(params).catch((error) => {
            console.error(error);
          });
        }
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
        console.error("An unexpected error occurred:", error);
      }
    } else {
      const params = {
        org_id: org_id ?? "",
        question_category_data: {
          id: props.data.value,
          name: data.categoryName,
          description: data.description,
        },
      };
      try {
        const result = await updateQuestionCategory(params);
        if (result.status === "success") {
          toast({
            variant: SUCCESS_MESSAGES.toast_variant_default,
            title: t("successMessages.questionCategory"),
            description: t("successMessages.categoryEdited"),
          });
          const params = {
            activity_type: "Question_Category",
            screen_name: "Question_Category",
            action_details: "Failed to add question category",
            target_id: props.data.value as string,
            log_result: "ERROR",
          };
          void updateLogUserActivity(params).catch((error) => {
            console.error(error);
          });
          props.closeDialog(false);
          props.categoryList(true);
        } else {
          toast({
            variant: ERROR_MESSAGES.toast_variant_destructive,
            title: t("errorMessages.toast_error_title"),
            description: result.status,
          });
          const params = {
            activity_type: "Question_Category",
            screen_name: "Question_Category",
            action_details: "Failed to add question category",
            target_id: props.data.value as string,
            log_result: "ERROR",
          };
          void updateLogUserActivity(params).catch((error) => {
            console.error(error);
          });
        }
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
        console.error("An unexpected error occurred:", error);
      }
    }
  }

  return (
    <Form {...form}>
      <form
        onSubmit={(event) => void form.handleSubmit(onSubmit)(event)}
        className="space-y-4"
      >
        <FormField
          control={form.control}
          name="categoryName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                {String(t("questionCategory.categoryName"))}{" "}
                <span className="text-red-700">*</span>
              </FormLabel>
              <FormControl>
                <Input
                  autoComplete="off"
                  placeholder={String(t("questionCategory.categoryName"))}
                  maxLength={50}
                  {...field}
                  onChange={handleInputChange}
                />
              </FormControl>
              <FormDescription></FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                {String(t("questionCategory.description"))}{" "}
                <span className="text-red-700">*</span>
              </FormLabel>
              <FormControl>
                <Input
                  autoComplete="off"
                  placeholder={String(t("questionCategory.description"))}
                  maxLength={150}
                  {...field}
                  onChange={handleDescriptionChange}
                />
              </FormControl>
              <FormDescription></FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <ModalButton
          closeDialog={() => props.closeDialog(false)}
          closeLabel={String(t("buttons.cancel"))}
          submitLabel={String(t("buttons.submit"))}
        />
      </form>
    </Form>
  );
};
