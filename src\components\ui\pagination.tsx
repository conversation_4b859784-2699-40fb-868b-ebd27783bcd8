import React, { useEffect } from "react";
import type { PaginationProps } from "@/types";
import { useTranslation } from "react-i18next";

const Pagination: React.FC<PaginationProps> = ({
  visiblePages,
  data,
  onPostsToDisplayChange,
  searchStatus,
  onCurrentPageChange,
}) => {
  const { t } = useTranslation();
  const [currentPage, setCurrentPage] = React.useState(1);

  // Calculate the number of posts to display per page

  const totalPages = Math.ceil(data.length / visiblePages);

  // Function to handle page change
  const onPageChange = (page: number): void => {
    setCurrentPage(page);
    onCurrentPageChange?.(page);
    // Calculate the new postsToDisplay based on the selected page
    const startIndex = (page - 1) * visiblePages;
    const endIndex = startIndex + visiblePages;
    const newPostsToDisplay = data.slice(startIndex, endIndex);

    // Call the parent's function to send the new postsToDisplay data
    onPostsToDisplayChange(newPostsToDisplay);
  };

  // Use useEffect to fetch data and set the initial postsToDisplay
  useEffect(() => {
    if (searchStatus === true) {
      setCurrentPage(1);
      onPageChange(1);
    } else {
      onPageChange(currentPage);
    }
  }, [data]); // The empty dependency array ensures this effect runs only once on component mount

  //onPageChange(currentPage);

  const pages = Array.from({ length: totalPages }, (_, index) => index + 1);

  const renderPageNumbers = (): React.JSX.Element => {
    const pageNumbers = [];

    if (totalPages <= visiblePages) {
      pageNumbers.push(...pages);
    } else {
      const halfVisiblePages = Math.floor(visiblePages / 2);
      let startPage = Math.max(1, currentPage - halfVisiblePages);
      const endPage = Math.min(totalPages, startPage + visiblePages - 1);

      if (startPage > 1) {
        pageNumbers.push(1);
        if (startPage > 2) {
          pageNumbers.push("...");
        }
      }

      while (startPage <= endPage) {
        pageNumbers.push(startPage);
        startPage++;
      }

      if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
          pageNumbers.push("...");
        }
        pageNumbers.push(totalPages);
      }
    }

    return (
      <>
        {pageNumbers.map((page, index) => (
          <li
            className="hidden lg:block justify-center col-span-full"
            key={index}
          >
            {page === "..." ? (
              <span className="px-2">...</span>
            ) : (
              <button
                onClick={() => {
                  if (typeof page === "number") {
                    onPageChange(page);
                  }
                }}
                className={`px-2 py-2 rounded-md cursor-pointer ${
                  currentPage === page
                    ? "bg-white text-black font-bold"
                    : "border border-gray-300 text-gray-700 hover:bg-white hover:text-black"
                }`}
              >
                {page}
              </button>
            )}
          </li>
        ))}
      </>
    );
  };

  return (
    <>
      {data.length > 0 && totalPages > 1 ? (
        <nav className="flex justify-center mt-10">
          <ul className="flex space-x-2">
            <li>
              <button
                onClick={() => onPageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className="px-4 py-2 bg-white text-black rounded-md cursor-pointer border disabled:bg-gray-300 border-gray-300 disabled:cursor-not-allowed"
              >
                {String(t("pagination.previous"))}
              </button>
            </li>
            {renderPageNumbers()}
            <li>
              <button
                onClick={() => onPageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="px-4 py-2 bg-white text-black rounded-md cursor-pointer border disabled:bg-gray-300 border-gray-300 disabled:cursor-not-allowed"
              >
                {String(t("pagination.next"))}
              </button>
            </li>
          </ul>
        </nav>
      ) : (
        ""
      )}
    </>
  );
};

export default Pagination;
