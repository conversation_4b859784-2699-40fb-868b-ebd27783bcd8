import { type FieldValues, useForm } from "react-hook-form";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { ModalButton } from "@/components/ui/modalButton";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { resourceTypes } from "@/lib/constants";
import React from "react";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

import type { CourseResourceAddFormType } from "@/types";
import { pageUrl } from "@/lib/constants";
import { useTranslation } from "react-i18next";
// zod validation todo
export const CourseResourceAddForm: React.FC<CourseResourceAddFormType> = (
  props,
): React.JSX.Element => {
  const { t } = useTranslation();
  const [secondDialogOpen, setSecondDialogOpen] = React.useState(false);

  const form = useForm();
  function onSubmit(data: FieldValues): void {
    {
      (data.resource == 1 || data.resource == 4) && setSecondDialogOpen(true);
    }
    {
      data.resource == 3 && (window.location.href = pageUrl.addExams);
    }
    {
      data.resource == 2 && (window.location.href = pageUrl.addPageContent);
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={() => form.handleSubmit(onSubmit)} className="space-y-8">
        {!secondDialogOpen && (
          <FormField
            control={form.control}
            name="resource"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{String(t("courses.courseModule.selectResource"))}</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value as string}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {resourceTypes.map((items, index) => (
                      <SelectItem key={index} value={items.value.toString()}>
                        {items.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        {secondDialogOpen && (
          <>
            <FormField
              control={form.control}
              name="url"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{String(t("courses.courseModule.url"))}</FormLabel>
                  <FormControl>
                    <Input
                      autoComplete="off"
                      placeholder={String(t("courses.courseModule.pasteUrl"))}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{String(t("courses.courseModule.name"))}</FormLabel>
                  <FormControl>
                    <Input autoComplete="off" placeholder={String(t("courses.courseModule.name"))} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{String(t("courses.description"))}</FormLabel>
                  <FormControl>
                    <Input
                      autoComplete="off"
                      placeholder={String(t("courses.description"))}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>{String(t("courses.courseModule.milestonePresent"))}</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={(value) => {
                        field.onChange(value);
                      }}
                      defaultValue={field.value as string}
                      className="flex flex-row space-x-3"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="true" />
                        </FormControl>
                        <FormLabel className="font-normal">Yes</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="false" />
                        </FormControl>
                        <FormLabel className="font-normal">No</FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </>
        )}
        {/* {showMilestone && (
          <Milestone courseModuleId={null} checkpointNumber={0} />
        )} */}
        <ModalButton
          closeDialog={(value: boolean) => props.closeDialog(value)}
          closeLabel={String(t("buttons.close"))}
          submitLabel={String(t("buttons.confirm"))}
        />
      </form>
    </Form>
  );
};
