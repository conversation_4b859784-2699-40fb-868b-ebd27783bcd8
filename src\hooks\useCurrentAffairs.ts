import { supabase } from "../lib/client";
import { rpc, views } from "../lib/apiConfig";
import type {
  CurrentAffairsData,
  ErrorType,
  PublishAffair,
  SuccessMessage,
  UpdateBulletinRequest,
  UpdateBulletinResponse,
  DeleteBulletinRequest,
  DeleteBulletinResponse,
} from "@/types";

interface useCurrentAffairsReturn {
  getCurrentAffairsList: () => Promise<CurrentAffairsData[]>;
  publishCurrentAffairs: (params: PublishAffair) => Promise<SuccessMessage>;
  updateBulletin: (
    params: UpdateBulletinRequest,
  ) => Promise<UpdateBulletinResponse>;
  deleteBulletin: (
    params: DeleteBulletinRequest,
  ) => Promise<DeleteBulletinResponse>;
}

const useCurrentAffairs = (): useCurrentAffairsReturn => {
  async function getCurrentAffairsList(): Promise<CurrentAffairsData[]> {
    try {
      const cuttentAffairsView = views?.currentAffairsList ?? "";
      const org_id = localStorage.getItem("orgId");
      const exeQuery = supabase
        .from(cuttentAffairsView)
        .select()
        .eq("org_id", org_id);
      const { data, error } = await exeQuery;
      if (error) {
        throw new Error(error.details);
      }
      return data as CurrentAffairsData[];
    } catch (error) {
      console.error("Error", error);
      throw error;
    }
  }
  async function publishCurrentAffairs(
    params?: PublishAffair,
  ): Promise<SuccessMessage> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.publishAffair,
        params,
      )) as { data: SuccessMessage; error: ErrorType | null };
      if (error) {
        throw new Error(error.details);
      }
      return data as SuccessMessage;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function updateBulletin(
    params?: UpdateBulletinRequest,
  ): Promise<UpdateBulletinResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.updateBulletin,
        params,
      )) as { data: UpdateBulletinResponse; error: ErrorType | null };
      if (error) {
        throw new Error(error.details);
      }
      return data as UpdateBulletinResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function deleteBulletin(
    params?: DeleteBulletinRequest,
  ): Promise<DeleteBulletinResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.deleteBulletin,
        params,
      )) as { data: DeleteBulletinResponse; error: ErrorType | null };
      if (error) {
        throw new Error(error.details);
      }
      return data as DeleteBulletinResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  return {
    getCurrentAffairsList,
    publishCurrentAffairs,
    updateBulletin,
    deleteBulletin,
  };
};

export default useCurrentAffairs;
