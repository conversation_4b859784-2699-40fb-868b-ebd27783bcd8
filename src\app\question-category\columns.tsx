"use client";

import type { ColumnDef, Row } from "@tanstack/react-table";
// import { ArrowUpDown } from "lucide-react";
import type {
  CategoryList,
  // CategoryListColumnDefinition
} from "@/types";
// import { Button } from "@/components/ui/button";

interface RowDefinition {
  row: Row<CategoryList>;
}

export const getColumns = (
  t: (key: string) => string,
): ColumnDef<CategoryList>[] => [
  {
    accessorKey: "label",
    header: t("questionCategory.categoryName"),

    // header: ({ column }: CategoryListColumnDefinition): React.JSX.Element => {
    //   return (
    //     <Button
    //       className="px-0"
    //       variant="ghost"
    //       onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
    //     >
    //       {" "}
    //       Category name
    //       <ArrowUpDown className="ml-2 h-4 w-4" />
    //     </Button>
    //   );
    // },
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div>{row.original.label}</div>
    ),
  },
  {
    accessorKey: "description",
    header:  t("questionCategory.description") ,
    // header: ({ column }: CategoryListColumnDefinition): React.JSX.Element => {
    //   return (
    //     <Button
    //       className="px-0"
    //       variant="ghost"
    //       onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
    //     >
    //       {" "}
    //       Description
    //       <ArrowUpDown className="ml-2 h-4 w-4" />
    //     </Button>
    //   );
    // },
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div>{row.original.label_description}</div>
    ),
  },
];
