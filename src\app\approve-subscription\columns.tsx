"use client";
import React from "react";
import type { ColumnDef, Row } from "@tanstack/react-table";
// import { ArrowUpDown } from "lucide-react";
// import { Button } from "@/components/ui/button";
import type { PendingSubscription } from "@/types";
import moment from "moment";
import { DATE_FORMAT_DMY_HM_AM_PM } from "@/lib/constants";

interface RowDefinition {
  row: Row<PendingSubscription>;
}
// interface ColumnDefinition {
//   column: Column<PendingSubscription, unknown>;
// }

export const getColumns = (
  t: (key: string) => string,
): ColumnDef<PendingSubscription>[] => [
  {
    id: "select",
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "user_name",
    header: t("subscriptionPlan.userName"),
  //   header: ({ column }: ColumnDefinition): React.JSX.Element => {
  //     return (
  //       <Button
  //         className="px-0"
  //         variant="ghost"
  //         onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
  //       >
  //         User name
  //         <ArrowUpDown className="ml-2 h-4 w-4" />
  //       </Button>
  //     );
  //   },
  //   cell: ({ row }: RowDefinition): React.JSX.Element => (
  //     <div>{row.original.user_name}</div>
  //   ),
  },
  {
    accessorKey: "email",
    header: t("subscriptionPlan.email"),
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div>{row.original.email}</div>
    ),
  },
  {
    accessorKey: "amount",
    header: t("subscriptionPlan.amount"),
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div>{row.original.amount}</div>
    ),
  },
  {
    accessorKey: "purchase_date",
    header: t("subscriptionPlan.purchaseDate"),
    cell: ({ row }: RowDefinition): React.JSX.Element => {
      const formattedDate = moment
        .utc(row.original.purchase_date)
        .local()
        .format(DATE_FORMAT_DMY_HM_AM_PM);
      return <div>{formattedDate}</div>;
    },
  },
  {
    accessorKey: "payment_method",
    header: t("subscriptionPlan.paymentMethod"),
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div>{row.original.payment_method}</div>
    ),
  },
];
