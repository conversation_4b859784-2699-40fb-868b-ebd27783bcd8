"use clients";

import React from "react";
import { useTranslation } from "react-i18next";

const NoDataFound = (): React.JSX.Element => {
  const { t } = useTranslation();
  return (
    <div className="flex w-full h-full items-center justify-center">
      <div className="text-center text-gray-500 text-2xl mx-auto">
      {t("subscriptionPlan.noDataFound")}
      </div>
    </div>
  );
};

export default NoDataFound;
