import { type FieldValues, useForm } from "react-hook-form";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { ModalButton } from "@/components/ui/modalButton";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import useCourse from "@/hooks/useCourse";
import type {
  CoursePublishDraftFormType,
  ErrorCatch,
  LogUserActivityRequest,
  ToastType,
} from "@/types";
import { useToast } from "@/components/ui/use-toast";
import * as React from "react";
import {
  API_RESPONSE_SUCCESS,
  COURSE_DRAFT,
  COURSE_PUBLISHED,
} from "@/lib/constants";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";
// zod validation todo

export const CoursePublishDraftForm: React.FC<CoursePublishDraftFormType> = (
  props,
): React.JSX.Element => {
  const form = useForm();
  const { publishCourse } = useCourse();
  const { toast } = useToast() as ToastType;
  const { updateUserActivity } = useLogUserActivity();
  const { t } = useTranslation();
  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  async function onSubmit(data: FieldValues): Promise<void> {
    if (data.name === undefined) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.course_not_select_choice"),
      });
    } else {
      if (data.name === "yes") {
        const course_id = props.data ?? "";
        const status =
          props.status === COURSE_DRAFT ? COURSE_PUBLISHED : COURSE_DRAFT;
        try {
          const result = await publishCourse(course_id, status);
          if (props.onSave) {
            props.onSave();
          }
          if (result.status == API_RESPONSE_SUCCESS) {
            toast({
              variant: SUCCESS_MESSAGES.toast_variant_default,
              title: t("successMessages.toast_success_title"),
              description: t("successMessages.course_update_msg"),
            });
            const params = {
              activity_type: "Course",
              screen_name: "Course",
              action_details: `${status} the course successfully`,
              target_id: props.data as string,
              log_result: "SUCCESS",
            };
            void updateLogUserActivity(params).catch((error) => {
              console.error(error);
            });
            props.closeDialog(true);
          } else {
            toast({
              variant: ERROR_MESSAGES.toast_variant_destructive,
              title: t("errorMessages.toast_error_title"),
              description: t("errorMessages.something_went_wrong"),
            });
            const params = {
              activity_type: "Course",
              screen_name: "Course",
              action_details: `Faile to ${status} the course`,
              target_id: props.data as string,
              log_result: "ERROR",
            };
            void updateLogUserActivity(params).catch((error) => {
              console.error(error);
            });
          }
        } catch (error) {
          const err = error as ErrorCatch;
          // ToDo: update error message from the API
          toast({
            variant: ERROR_MESSAGES.toast_variant_destructive,
            title: t("errorMessages.toast_error_title"),
            description: err.message,
          });
          console.error("An unexpected error occurred:", error);
        }
      } else {
        props.closeDialog(true);
      }
    }
  }

  return (
    <Form {...form}>
      <form
        onSubmit={(event) => void form.handleSubmit(onSubmit)(event)}
        className="space-y-8"
      >
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              {props.status === "Draft" ? (
                <FormLabel>
                  {String(t("courses.coursePublishPrompt"))}
                </FormLabel>
              ) : (
                <FormLabel>{String(t("courses.courseDraftPrompt"))}</FormLabel>
              )}
              <FormControl>
                <RadioGroup
                  onValueChange={field.onChange}
                  defaultValue={field.value as string}
                  className="flex flex-col space-y-1"
                >
                  <FormItem className="flex items-center space-x-3 space-y-0">
                    <FormControl>
                      <RadioGroupItem value="yes" />
                    </FormControl>
                    <FormLabel className="font-normal">
                      {" "}
                      {String(t("courses.yes"))}
                    </FormLabel>
                  </FormItem>
                  <FormItem className="flex items-center space-x-3 space-y-0">
                    <FormControl>
                      <RadioGroupItem value="no" />
                    </FormControl>
                    <FormLabel className="font-normal">
                      {" "}
                      {String(t("courses.no"))}
                    </FormLabel>
                  </FormItem>
                </RadioGroup>
              </FormControl>
              <FormDescription></FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <ModalButton
          closeDialog={() => props.closeDialog(false)}
          closeLabel={String(t("buttons.cancel"))}
          submitLabel={String(t("buttons.submit"))}
        />
      </form>
    </Form>
  );
};
