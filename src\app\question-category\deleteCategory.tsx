import React from "react";
import type {
  ErrorCatch,
  LogUserActivityRequest,
  QuestionCategoryForm,
  ToastType,
} from "@/types";
import { Button } from "@/components/ui/button";
import useQuestionBank from "@/hooks/useQuestionBank";
import { useToast } from "@/components/ui/use-toast";
import { useRouter } from "next/navigation";
import { ORG_KEY } from "@/lib/constants";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";

export default function DeleteQuestionCategory({
  data,
  onSave,
  onCancel,
}: {
  onSave: (value: boolean) => void;
  onCancel: () => void;
  data: QuestionCategoryForm;
  isModal?: boolean;
}): React.JSX.Element {
  const router = useRouter();
  const { t } = useTranslation();
  const { toast } = useToast() as ToastType;
  const { deleteQuestionCategory } = useQuestionBank();
  const { updateUserActivity } = useLogUserActivity();

  const handleDeleteClick = (): void => {
    void handleToastSave();
    onCancel();
  };

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  const handleToastSave = async (): Promise<void> => {
    const params = {
      org_id: localStorage.getItem(ORG_KEY) ?? "",
      question_category_id: data.value,
    };
    try {
      const result = await deleteQuestionCategory(params);
      if (result.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.deleteQuestionCat"),
          description: t("successMessages.deleteQuestionCatDesc"),
        });
        const params = {
          activity_type: "Question_Category",
          screen_name: "Question_Category",
          action_details: "Question category deleted successfully",
          target_id: data.value as string,
          log_result: "SUCCESS",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
        onSave(true);
        router.push("/question-category");
      } else if (result.status === "error") {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: result?.status,
        });
        const params = {
          activity_type: "Question_Category",
          screen_name: "Question_Category",
          action_details: "Failed to delete question category",
          target_id: data.value as string,
          log_result: "ERROR",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
      console.error("An unexpected error occurred:", error);
    }
  };

  return (
    <>
      <div className="mb-2 mr-4">
        <p className="ml-0 ">{String(t("questionCategory.deletePrompt"))}</p>
      </div>
      <div className="flex topic-icons pr-4">
        <div className="flex items-center justify-center float-right space-x-2">
          <Button type="button" className="bg-[#33363F]" onClick={onCancel}>
            {String(t("buttons.cancel"))}
          </Button>
          &nbsp;
          <Button
            type="submit"
            className="bg-[#9FC089]"
            onClick={handleDeleteClick}
          >
            {String(t("buttons.delete"))}
          </Button>
        </div>
      </div>
    </>
  );
}
