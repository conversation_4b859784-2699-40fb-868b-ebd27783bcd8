"use client";

import React, { useState, useEffect } from "react";
import MainLayout from "../layout/mainlayout";
import Image from "next/image";
import useOrganization from "@/hooks/useOrganization";
import type {
  ComboData,
  ErrorCatch,
  InnerItem,
  LogUserActivityRequest,
  ToastType,
} from "@/types";
import { useToast } from "@/components/ui/use-toast";
import { Combobox } from "@/components/ui/combobox";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";

import "../../styles/auth.css";
import { ORG_KEY, ORG_NAME } from "@/lib/constants";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumb";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";
import { ERROR_MESSAGES } from "@/lib/messages";

export default function Organization(): React.JSX.Element {
  const { t } = useTranslation();
  const [selectedOption, setSelectedOption] = useState("");
  const [selectedOrgName, setSelectedOrgName] = useState("");
  const { getOrganizationList } = useOrganization();
  const [comboData, setComboData] = useState<ComboData[]>([]);
  const router = useRouter();
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);
  const { toast } = useToast() as ToastType;
  const { updateUserActivity } = useLogUserActivity();

  useEffect(() => {
    setBreadcrumbItems(
      getBreadCrumbItems(t, t("breadcrumb.organization"), { "": "" }),
    );
  }, [t]);

  useEffect(() => {
    const fetchData = async (): Promise<void> => {
      try {
        const orgIdSelected = localStorage.getItem(ORG_KEY)?.toString();
        setSelectedOption(orgIdSelected as string);

        const orgNameSelected = localStorage.getItem(ORG_NAME)?.toString();
        setSelectedOrgName(orgNameSelected as string);

        const organizationList = await getOrganizationList();
        if (organizationList !== null && organizationList !== undefined) {
          const comboData: ComboData[] = organizationList.map((org) => ({
            value: org.org_id,
            label: org.org_name,
          }));
          const sortedData = comboData.sort((data, item) => {
            return (data.label ?? "").localeCompare(item.label ?? "");
          });
          setComboData(sortedData);
        }
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
        console.error("Error fetching data:");
      }
    };
    fetchData().catch((error) => console.log(error));
  }, []);
  const comboSelectedValue = (res: string): void => {
    setSelectedOption(res);
    console.log(res);
  };

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  const setOrganization = (): void => {
    if (selectedOption === "") {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.chooseDescription"),
      });
      const params = {
        activity_type: "Organization",
        screen_name: "Organization",
        action_details: "Organization not selected",
        target_id: selectedOption,
        log_result: "ERROR",
      };
      void updateLogUserActivity(params).catch((error) => {
        console.error(error);
      });
    } else {
      const selectedLabel = comboData.find(
        (orgDetails) => orgDetails.value === selectedOption,
      )?.label as string;
      const params = {
        activity_type: "Organization",
        screen_name: "Organization",
        action_details: `Selected the organization ${selectedLabel}`,
        target_id: selectedOption,
        log_result: "SUCCESS",
      };
      void updateLogUserActivity(params).catch((error) => {
        console.error(error);
      });
      localStorage.setItem(ORG_KEY, selectedOption);
      localStorage.setItem(ORG_NAME, selectedLabel);
      localStorage.removeItem("role_privileges");
      router.push("/dashboard");
    }
  };

  return (
    <MainLayout>
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      <div>
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">
            {String(t("organization.organization"))}
          </h1>
        </div>
        <div className="h-150 border rounded-md p-4 mt-4 bg-image relative hidden flex-col bg-muted p-10 text-white lg:flex">
          <div className="absolute inset-0 bg-gray-800 opacity-50 "></div>
          <div className="flex flex-col items-center space-y-6 relative z-10">
            <div className="flex flex-col space-y-2 p-20 ">
              <Image
                src="/static/images/smartlearn.png"
                alt="Card Image"
                width={400}
                height={400}
              />
            </div>

            <div className="w-full max-w-xs text-black">
              <Combobox
                data={comboData}
                onSelectChange={comboSelectedValue}
                placeHolder={"Select organization"}
                defaultLabel={selectedOrgName}
              />
            </div>

            <Button onClick={setOrganization} className="bg-[#9FC089]">
              {String(t("buttons.submit"))}
            </Button>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
