"use client";
import * as React from "react";
import { useEffect } from "react";
import { Button } from "@/components/ui/button";
import { CardTitle } from "@/components/ui/card";
import MainLayout from "../layout/mainlayout";
import Pagination from "@/components/ui/pagination";
//import { courses } from "./data";
import type {
  CoursesParams,
  ErrorCatch,
  InnerItem,
  ToastType,
  TopicDataType,
} from "@/types";
import {
  AwardIcon,
  BookCopyIcon,
  Calendar,
  CircleDollarSign,
  Clock11Icon,
  CopyIcon,
  EyeIcon,
  Gift,
  MonitorPlay,
  ShieldAlertIcon,
  TimerIcon,
  TrashIcon,
} from "lucide-react";
import moment from "moment";
import { Input } from "@/components/ui/input";
import TreeSelectComponent from "@/components/ui/tree-select/tree-select";
import { Modal } from "@/components/ui/modal";
import { Separator } from "@/components/ui/separator";
import NoDataFound from "@/components/ui/noDataFound";
import Link from "next/link";
import { CourseDuplicateForm } from "@/app/courses/course-duplicate-form";
import { CoursePublishDraftForm } from "@/app/courses/course-publish-draft-form";
import { pageUrl, privilegeData } from "@/lib/constants";
import useCourse from "@/hooks/useCourse";
import useTopics from "@/hooks/useTopics";
import { useToast } from "@/components/ui/use-toast";
import type { TreeDataItem } from "@/components/ui/tree";
import { useRouter } from "next/navigation";
import { Spinner } from "@/components/ui/progressiveLoader";
import { PlusIcon } from "@radix-ui/react-icons";
import { Label } from "@/components/ui/label";
import getPrivilegeList from "@/hooks/useCheckPrivilege";
import { ERROR_MESSAGES } from "@/lib/messages";
import DeleteCourse from "./deleteCourse";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumb";
import { StatusTypes } from "@/lib/constants";
import { Combobox } from "@/components/ui/combobox";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useTranslation } from "react-i18next";

const DateComponent = ({
  date,
  title,
}: {
  date: moment.MomentInput;
  title: string;
}): React.JSX.Element => {
  return (
    <div className="flex  text-left flex-col ">
      {/* <Calendar className="mr-1 w-4 h-4" /> */}
      <span className="font-bold mb-1 text-black">{title}</span>
      <div className="flex flex-col items-start text-secondary">
        <div className="flex items-center mb-1">
          <Calendar className="mr-1 w-3 h-3" />
          <div className="flex">
            {moment(date).format("DD")}&nbsp;
            <span className="uppercase">{moment(date).format("MMM")}</span>
            &nbsp;
            {moment(date).format("YY")}
          </div>
        </div>
        <div className="flex items-center">
          <Clock11Icon className="mr-1 w-3 h-3" />
          {moment(date).format("hh:mm A")}
        </div>
      </div>
    </div>
  );
};
export default function Courses(): React.JSX.Element {
  const { t } = useTranslation();
  const [isDialogOpen, setIsDialogOpen] = React.useState(false);
  const [courses, setCourses] = React.useState<CoursesParams[] | undefined>([]);
  const [filteredCourses, setFilteredCourses] = React.useState<
    CoursesParams[] | undefined
  >([]);
  const [initialCourses, setInitialCourses] = React.useState<
    CoursesParams[] | undefined
  >([]);
  const [buttonClicked, setButtonClicked] = React.useState(true);
  const { toast } = useToast() as ToastType;
  // const [topicData, setTopicData] = React.useState<TopicDataType[]>();
  const [nodeData, setNodeData] = React.useState<TreeDataItem[]>([]);
  const [searchInput, setSearchInput] = React.useState("");
  const { listCourses, convertDataToTreeNode } = useCourse();
  const { getCategoryHierarchy } = useTopics();
  const [disableBtn, setDisableBtn] = React.useState(true);
  const [duplicateBtn, setDuplicateBtn] = React.useState(true);
  const [publishBtn, setPublishBtn] = React.useState(true);
  /* const [deleteBtn, setDeleteBtn] = React.useState(true); */
  const [viewBtn, setViewBtn] = React.useState(true);
  const [itemValues, setItemValues] = React.useState("");
  const [itemStatus, setitemStatus] = React.useState("");
  const [isLoading, setIsLoading] = React.useState<boolean>(true);
  const [searchStatus, setSearchStatus] = React.useState<boolean>(true);
  const [selectedItem, setSelectedItem] = React.useState<CoursesParams>();
  const [selectedTopic, setSelectedTopic] = React.useState<string>(
    `${t("courses.selectItem")}`,
  );
  const [breadcrumbItems, setBreadcrumbItems] = React.useState<InnerItem[]>([]);
  const [deleteCourse, setDelete] = React.useState<boolean>(false);
  const [title, setTitle] = React.useState("");
  const [passData, setPassData] = React.useState<CoursesParams>();
  const [deleteGroupPrivilege, setDeletePrivilege] =
    React.useState<boolean>(false);
  const [statusLabel, setStatusLabel] = React.useState<string>("");
  const [postsToDisplay, setPostsToDisplay] = React.useState<CoursesParams[]>(
    [],
  );

  const openDialog = (click: boolean): void => {
    if (click === true) {
      setButtonClicked(true);
    } else {
      setButtonClicked(false);
    }
    setIsDialogOpen(true);
  };

  const closeDialog = (): void => {
    setIsDialogOpen(false);
  };
  const handleClearSearch = (): void => {
    setSearchInput("");
    setStatusLabel("");
    setCourses(initialCourses ?? []);
    setSelectedTopic("Select Item");
  };

  useEffect(() => {
    setBreadcrumbItems(getBreadCrumbItems(t, t("breadcrumb.course"), { "": "" }));
  }, [t]);

  useEffect(() => {
    setDisableBtn(getPrivilegeList("Course", privilegeData.Course.addCourse));
    setDuplicateBtn(
      getPrivilegeList("Course", privilegeData.Course.copyCourse),
    );
    setViewBtn(getPrivilegeList("Course", privilegeData?.Course?.getCourse));
    setPublishBtn(
      getPrivilegeList("Course", privilegeData?.Course?.updateCoursePublish),
    );
    setDeletePrivilege(
      getPrivilegeList("Course", privilegeData?.Course?.deleteCourse),
    );
    topicList();
  }, []);

  const fetchCourseData = async (): Promise<void> => {
    try {
      const topicId = null;
      const data = await listCourses(topicId);
      const sortedData = data; /* .sort((item, data) => {
        const firstItem = item.short_name.toUpperCase();
        const secondItem = data.short_name.toUpperCase();
        return firstItem.localeCompare(secondItem);
      }); */

      setIsLoading(false);
      setCourses(sortedData);
      setFilteredCourses(sortedData);
      setInitialCourses(sortedData);
    } catch (error) {
      setIsLoading(!isLoading);
    }
  };
  useEffect(() => {
    setIsLoading(true);
    setSearchInput("");
    setSearchStatus(true);
    fetchCourseData().catch((error) => console.log(error));
  }, []);

  const topicList = (): void => {
    const fetchTopicData = async (): Promise<void> => {
      const org_id = localStorage.getItem("orgId") as string;
      const params = {
        org_id: org_id,
        filter_data: 1,
      };
      try {
        setIsLoading(!isLoading);
        const topics = await getCategoryHierarchy(params);
        if (topics?.length > 0) {
          const treeData: TreeDataItem[] = convertDataToTreeNode(
            topics as TopicDataType[],
          );

          setNodeData(treeData);
        }
      } catch (error: unknown) {
        setIsLoading(!isLoading);
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
      }
    };
    fetchTopicData().catch((error) => console.log(error));
  };

  const handlePostsToDisplayChange = (newPostsToDisplay: unknown): void => {
    setPostsToDisplay(newPostsToDisplay as CoursesParams[]);
    setSearchStatus(false);
  };
  const handleSearch = (): void => {
    setSearchStatus(true);
    if (searchInput !== "") {
      const filteredCourses =
        initialCourses?.filter((course) => {
          const courseName = course.short_name.toLowerCase();
          return courseName.includes(searchInput.toLowerCase());
        }) ?? [];
      setPostsToDisplay(filteredCourses);
      setCourses(filteredCourses);
    }
  };
  const router = useRouter();
  const handleViewClick = (
    courseId: string,
    expiry: boolean,
    published: string,
    is_premium: boolean,
  ): void => {
    router.push(
      `${pageUrl.CourseManagement}?courseId=${courseId}&&expiry=${expiry}&&is_premium=${is_premium}&&tab=Course_details`,
    );
  };

  const onSubmit = (): void => {
    setIsLoading(true);
    setSearchInput("");
    setSearchStatus(true);
    fetchCourseData().catch((error) => console.log(error));
  };

  const delCourse = (data: CoursesParams): void => {
    setTitle("");
    setPassData(data);
    setDelete(true);
  };

  const closeDelete = (): void => {
    setIsDialogOpen(false);
    setDelete(false);
  };

  const getCoursesByCategory = async (selectedValue: string): Promise<void> => {
    const filteredCourses = initialCourses?.filter(
      (course) => course.category_id === selectedValue,
    );
    setPostsToDisplay(filteredCourses as CoursesParams[]);
    setCourses(filteredCourses);
  };

  const filterStatusType = (res: string): void => {
    setStatusLabel(
      StatusTypes.find((status) => status.value === res)?.label ??
        "Select status",
    );
    const newCourses = filteredCourses?.filter((item) => item.status === res);
    setCourses(newCourses as CoursesParams[]);
  };

  return (
    <MainLayout>
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      <div>
        <span>
          <h1 className="text-2xl font-semibold">
            {String(t("courses.title"))}
            {disableBtn && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Link href={pageUrl.addCourse}>
                      <Button className="bg-ring bg-[#fb8500] hover:bg-[#fb5c00] py-2 md:py-3 px-4 md:px-6 whitespace-nowrap ml-2">
                        <PlusIcon className="h-4 w-4 md:h-5 md:w-5" />
                      </Button>
                    </Link>
                  </TooltipTrigger>
                  <TooltipContent className="text-xl">
                    <p>{String(t("courses.addNewCourse"))}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </h1>
        </span>
        <div className="flex flex-col md:flex-row items-start md:items-center justify-between">
          <h1 className="text-2xl font-semibold tracking-tight"></h1>
          <div className="flex flex-col md:flex-row flex-wrap gap-4 items-start md:items-center">
            <div className="w-full md:w-[350px]">
              <Label>{String(t("courses.selectCategory"))}</Label>
              <TreeSelectComponent
                nodeData={nodeData}
                selectLabel={selectedTopic}
                defaultValueKey={t("courses.selectItem")}
                onNodeSelect={(selectedValue) => {
                  setSelectedTopic(
                    selectedValue ?? `${t("courses.selectItem")}`,
                  );
                  void getCoursesByCategory(selectedValue as string);
                }}
              />
            </div>

            <div className="w-full md:w-[350px]">
              <Label>{String(t("courses.searchByShortName"))}</Label>
              <Input
                autoComplete="off"
                type="text"
                placeholder={String(t("courses.searchByName"))}
                value={searchInput}
                onChange={(e) => setSearchInput(e.target.value.trimStart())}
              />
            </div>

            <div className="w-full md:w-[350px]">
              <Label>{String(t("courses.searchByPublishedStatus"))}</Label>
              <Combobox
                data={StatusTypes}
                onSelectChange={filterStatusType}
                defaultLabel={statusLabel}
              />
            </div>

            <div className="flex w-full md:w-auto gap-2 mt-5">
              <Button
                className="px-4 py-2 text-sm w-full md:w-auto justify-center bg-[#9FC089]"
                onClick={handleSearch}
              >
                {String(t("buttons.search"))}
              </Button>
              <Button
                className="px-4 py-2 text-sm w-full md:w-auto justify-center bg-[#33363F]"
                onClick={handleClearSearch}
              >
                {String(t("buttons.clear"))}
              </Button>
            </div>
          </div>
        </div>

        <div className="border rounded-md p-4 mt-4 bg-white">
          {isLoading ? (
            <Spinner />
          ) : (
            <div>
              <div className="grid gap-4 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 ">
                {postsToDisplay.length > 0 ? (
                  postsToDisplay.map((items, index) => (
                    <div
                      key={index}
                      className="w-full mt-4 bg-white border rounded-md p-4"
                    >
                      <div className="flex items-start relative justify-between">
                        <div className="w-full">
                          <CardTitle className=" text-sm font-normal truncate ... w-full md:w-[60%]">
                            {items.category_name}
                          </CardTitle>
                          <CardTitle className="my-1 text-l text-wrap">
                            {items.short_name}
                          </CardTitle>
                          <div className="flex justify-between items-center">
                            <div className="flex">
                              {!items.duration?.includes("days") &&
                                items.duration?.includes(":") && (
                                  <div className="flex items-center mr-4 truncate ... text-xs days-text">
                                    <TimerIcon className="mr-1 w-4 h-4 days-text" />
                                    {`${
                                      parseInt(
                                        items.duration?.split(":")[0],
                                      ) !== 0
                                        ? items.duration
                                            ?.split(":")[0]
                                            .replace(/^0+/, "") +
                                          " hour" +
                                          (parseInt(
                                            items.duration?.split(":")[0],
                                          ) !== 1
                                            ? "s"
                                            : "") +
                                          (parseInt(
                                            items.duration?.split(":")[1],
                                          ) !== 0
                                            ? " and "
                                            : "")
                                        : ""
                                    }${
                                      parseInt(
                                        items.duration?.split(":")[1],
                                      ) !== 0
                                        ? items.duration
                                            ?.split(":")[1]
                                            .replace(/^0+/, "") +
                                          " minute" +
                                          (parseInt(
                                            items.duration?.split(":")[1],
                                          ) !== 1
                                            ? "s"
                                            : "") +
                                          (parseInt(
                                            items.duration?.split(":")[2],
                                          ) !== 0
                                            ? " and "
                                            : "")
                                        : ""
                                    }${
                                      parseInt(
                                        items.duration?.split(":")[2],
                                      ) !== 0
                                        ? items.duration
                                            ?.split(":")[2]
                                            .replace(/^0+/, "") +
                                          " second" +
                                          (parseInt(
                                            items.duration?.split(":")[2],
                                          ) !== 1
                                            ? "s"
                                            : "")
                                        : ""
                                    }`}
                                  </div>
                                )}

                              {!items.duration?.includes(":") &&
                                !items.duration?.includes("days") && (
                                  <div className="flex items-center mr-4 truncate ... text-xs days-text">
                                    <TimerIcon className="mr-1 w-4 h-4" />
                                    {`${items.duration}`}
                                    {items.duration === "1 day" ? "" : "s"}
                                  </div>
                                )}

                              {items.duration?.includes("days") && (
                                <div className="flex items-center mr-4 truncate ... text-xs days-text">
                                  <TimerIcon className="mr-1 w-4 h-4" />
                                  {items.duration?.split(" ")[0]} days
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="flex flex-wrap items-center space-x-2 sm:space-x-3 justify-end md:space-x-4 lg:space-x-5">
                          {publishBtn && (
                            <Button
                              size={"sm"}
                              className={`bg-white border shadow-none rounded-3xl h-5 ${
                                items.status === "Draft"
                                  ? items.is_expired === true
                                    ? "bg-[#fb8500] border-[#fb8500] text-white"
                                    : "text-[#fb8500] border-[#fb8500] hover:bg-[#fb8500] hover:border-[#fb8500] hover:text-white"
                                  : items.is_expired === true
                                  ? "bg-[#15cce5] border-[#15cce5] text-white"
                                  : "text-[#15cce5] border-[#15cce5] hover:bg-[#15cce5] hover:border-[#15cce5] hover:text-white"
                              } ${
                                items.is_expired === true
                                  ? "pointer-events-none"
                                  : ""
                              }`}
                              onClick={() => {
                                if (items.is_expired === false) {
                                  openDialog(false);
                                  setItemValues(items.course_id as string);
                                  setitemStatus(items.status);
                                }
                              }}
                            >
                              {items.status === "Draft"
                                ? String(t("buttons.draft"))
                                : String(t("buttons.published"))}
                            </Button>
                          )}
                          <div className="w-full flex flex-row justify-end">
                            <div className="bg-white  hover:text-white shadow-none rounded-3xl h-7 mt-2 sm:mt-0">
                              {items.course_type === "Paid" && (
                                <span title={String(t("courses.paid"))}>
                                  <CircleDollarSign
                                    color="#468c94"
                                    className="mt-2"
                                  />
                                </span>
                              )}
                              {items.course_type === "Demo" && (
                                <span title={String(t("courses.demo"))}>
                                  <MonitorPlay
                                    color="#468c94"
                                    className="mt-2"
                                  />
                                </span>
                              )}
                              {items.course_type === "Free" && (
                                <span title={String(t("courses.free"))}>
                                  <Gift color="#468c94" className="mt-2" />
                                </span>
                              )}
                            </div>
                            {deleteGroupPrivilege &&
                              items.is_expired === false && (
                                <Button
                                  size={"icon"}
                                  className="text-[#fb4700db] bg-white shadow-none h-7 hover:text-[#fb4700db] hover:bg-white  hover:shadow-none"
                                  onClick={() => {
                                    delCourse(items as CoursesParams), false;
                                    setItemValues(items.course_id as string);
                                    setitemStatus(items.status);
                                  }}
                                >
                                  <TrashIcon className="h-4 w-4 mt-3" />
                                </Button>
                              )}
                          </div>
                        </div>
                      </div>
                      {/* <CardContent> */}
                      <Separator className="mb-3 mt-4" />
                      <div className="flex text-xs text-muted-foreground text-center justify-center">
                        <div className="flex flex-row items-center justify-between w-[100%] text-center">
                          <DateComponent
                            date={items.start_date}
                            title={String(t("courses.from"))}
                          />
                          {/* <Calendar className="mr-1 w-4 h-4" /> */}
                          {/* <Separator orientation="vertical" /> */}

                          <DateComponent
                            date={items.end_date}
                            title={String(t("courses.to"))}
                          />
                        </div>
                      </div>

                      <div className="flex text-sm text-muted-foreground justify-between mt-4">
                        {duplicateBtn && (
                          <Button
                            className="bg-[#155264] hover:bg-white hover:text-[#9fc089] border hover:border-[#9fc089]"
                            onClick={() => {
                              openDialog(true);
                              setSelectedItem(items);
                            }}
                          >
                            <CopyIcon className="mr-2 h-4 w-4" />{" "}
                            {String(t("buttons.duplicate"))}
                          </Button>
                        )}
                        {items.is_premium === true && (
                          <span title={String(t("courses.premium"))}>
                            <AwardIcon color="#15cce5" className="mt-2" />
                          </span>
                        )}
                        {items.is_expired === true && (
                          <span title={String(t("courses.expired"))}>
                            <ShieldAlertIcon color="#e60548" className="mt-2" />
                          </span>
                        )}
                        {items.is_duplicate_course === true && (
                          <span title={String(t("courses.duplicate"))}>
                            <BookCopyIcon color="#468c94" className="mt-2" />
                          </span>
                        )}
                        {viewBtn && (
                          <Button
                            className="bg-[#9fc089] hover:bg-white hover:text-[#9fc089] border hover:border-[#9fc089]"
                            onClick={() =>
                              handleViewClick(
                                items.course_id as string,
                                items.is_expired as boolean,
                                items.status,
                                items.is_premium as boolean,
                              )
                            }
                          >
                            <EyeIcon className="mr-2 h-4 w-4" />
                            {String(t("buttons.view"))}
                          </Button>
                        )}
                      </div>
                      {/* </CardContent> */}
                    </div>
                  ))
                ) : (
                  <NoDataFound />
                )}
              </div>

              <Pagination
                visiblePages={8}
                data={courses as CoursesParams[]}
                onPostsToDisplayChange={handlePostsToDisplayChange}
                searchStatus={searchStatus}
              />

              {isDialogOpen && (
                <Modal
                  title={
                    buttonClicked === true
                      ? t("courses.duplicateCourse")
                      : t("courses.publishedDraftCourse")
                  }
                  header=""
                  openDialog={isDialogOpen}
                  closeDialog={closeDialog}
                  type={buttonClicked === true ? "max-w-4xl" : ""}
                >
                  {buttonClicked === true ? (
                    <CourseDuplicateForm
                      closeDialog={closeDialog}
                      data={selectedItem}
                      isDialogOpen={() => isDialogOpen}
                      onSave={onSubmit}
                      topicList={nodeData}
                    />
                  ) : (
                    <CoursePublishDraftForm
                      data={itemValues}
                      status={itemStatus}
                      closeDialog={closeDialog}
                      onSave={onSubmit}
                    />
                  )}
                </Modal>
              )}
              {deleteCourse && (
                <Modal
                  title={title}
                  header=""
                  openDialog={deleteCourse}
                  closeDialog={closeDelete}
                >
                  <DeleteCourse
                    onSave={onSubmit}
                    onCancel={closeDelete}
                    isModal={true}
                    data={passData as CoursesParams}
                  />
                </Modal>
              )}
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
}
