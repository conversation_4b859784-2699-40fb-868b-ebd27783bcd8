"use client";

import React, { useState, useEffect } from "react";
import { getColumns } from "../../app/exam-details/columns";
import { DataTable } from "../../components/ui/data-table/data-table";
// import examDetails from "./examDetails";
// import examInfo from "./examInfo";
import { Card, CardContent } from "@/components/ui/card";
import moment from "moment";
import { useSearchParams } from "next/navigation";
import { Eye, PlusIcon, Trash } from "lucide-react";
import type {
  ExamDetailsType,
  ExamDetails,
  DeleteQuestionRequest,
  InnerItem,
  ResourceList,
  ErrorCatch,
} from "@/types";
import { XCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Modal } from "@/components/ui/modal";
import useExamDetails from "@/hooks/useExamDetails";
import ImportQuestions from "@/components/importQuestions/importQuestions";
import { useToast } from "@/components/ui/use-toast";
import type { ToastType } from "../../types";
import { Spinner } from "@/components/ui/progressiveLoader";
import { DATE_FORMAT_DMY_HM_AM_PM, ORG_KEY, pageUrl } from "@/lib/constants";
import { ERROR_MESSAGES } from "@/lib/messages";
import Link from "next/link";
import DeleteQuestion from "./deleteQuestion";
import NextBreadcrumb from "../breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumb";
import AddQuestionModal from "./addQuestionModal";

import { useTranslation } from "react-i18next";
interface ExamDetailsProps {
  examId?: string;
  examData?: ResourceList;
  onCancel?: () => void;
}

export default function ExamDetails({
  examId,
  examData,
  onCancel,
}: ExamDetailsProps): React.JSX.Element {
  const { t } = useTranslation();
  const columns = getColumns(t);
  //const router = useRouter();
  const searchParams = useSearchParams();
  // const type: string = searchParams.get("type") ?? "";
  const type: string | undefined =
    examId !== undefined ? examId! : (searchParams.get("type") ?? examData?.id);
  const courseId = searchParams.get("courseId") ?? "";
  const { toast } = useToast() as ToastType;
  const { getQuestions, getQuestionsOfQuiz } = useExamDetails();
  const [selectedRow, setSelectedRow] = useState<ExamDetailsType>({});
  const [questions, setQuestions] = useState<ExamDetailsType[]>([]);
  const [showQuestions, setShowQuestions] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = React.useState(false);
  const [examInfo, setExamInfo] = useState<ExamDetails[]>([]);
  const [numberOfQuestions, setNumberOfQuestions] = useState<number>(0);
  const [rowCount, setRowCount] = useState<number>(0);
  const [isButtonDisabled, setIsButtonDisabled] = useState(false);
  const [isLoading, setIsLoading] = React.useState<boolean>(true);
  const [questionIds, setQuestionIds] = useState<(string | undefined)[]>([]);
  const [deleteData, setDeleteData] = useState<DeleteQuestionRequest>();
  const [deleteGroup, setDeleteGroup] = useState<boolean>(false);
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);
  const [isAddQuestionModalOpen, setIsAddQuestionModalOpen] =
    useState<boolean>(false);
  const examDetailView = (data: ExamDetailsType): void => {
    setSelectedRow(data);
    setShowQuestions(true);
  };
  const closeQuestionSection = (): void => {
    setSelectedRow({});
    setShowQuestions(false);
  };

  const openDialog = (): void => {
    setIsDialogOpen(true);
  };

  const closeDialog = (value: boolean): void => {
    console.log("value", value);
    setIsDialogOpen(false);
  };
  const deleteQuestionFromQuiz = (data: ExamDetailsType): void => {
    setDeleteGroup(true);
    const datas: DeleteQuestionRequest = {
      course_id: courseId,
      question_id: data.question_id as string,
      org_id: localStorage.getItem(ORG_KEY) as string,
      quiz_id: type ?? "",
    };
    setDeleteData(datas);
  };
  useEffect(() => {
    setBreadcrumbItems(
      getBreadCrumbItems(t, t("breadcrumb.examDetails"), { "": "" }),
    );
    examDetailsList(true);
  }, [t]);

  const examDetailsList = (value: boolean): void => {
    console.log(value);
    const fetchData = async (): Promise<void> => {
      const quiz_id = type;
      setIsLoading(true);
      try {
        const questionsData = examData
          ? await getQuestionsOfQuiz(quiz_id)
          : await getQuestions(quiz_id);
        console.log("questionsData", questionsData);

        questionsData.map((item) => {
          if (item.description != null) {
            item.description = item.description.replace(
              /<pre\b[^>]*>(.*?)<\/pre>/s,
              "<p>$1</p>",
            );
          }
        });
        setIsLoading(false);
        setExamInfo(questionsData);
        if (questionsData !== null && questionsData !== undefined) {
          const extractedData: ExamDetailsType[] =
            questionsData[0].quest_answers.map((question) => ({
              name: question.name,
              default_mark: question.default_mark,
              penalty: question.penalty,
              question_id: question.question_id,
              question_text: question.question_text,
              question_type: question.question_type,
              answers: question.answers,
            }));
          setQuestions(extractedData);
          const questionTextData = extractedData.map(
            (question) => question.question_id,
          );

          setQuestionIds(questionTextData);
          setNumberOfQuestions(questionsData[0].num_of_questions);
        }
      } catch (error) {
        const err = error as ErrorCatch;
        setIsLoading(false);
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
      }
    };
    fetchData().catch((error) => console.log(error));
  };

  const removeHTMLTags = (html: string | undefined): React.JSX.Element => {
    if (html != null) {
      const tempDiv = document.createElement("div");
      tempDiv.innerHTML = html;
      return <div dangerouslySetInnerHTML={{ __html: tempDiv.innerHTML }} />;
    } else {
      return <div></div>;
    }
  };

  const handleTotalRowCountChange = (rowCount: number): void => {
    setRowCount(rowCount);
    if (rowCount >= numberOfQuestions) {
      setIsButtonDisabled(true);
    } else {
      setIsButtonDisabled(false);
    }
  };
  const closeDeleteDialog = (): void => {
    setDeleteGroup(false);
  };

  const openAddQuestionModal = (): void => {
    setIsAddQuestionModalOpen(true);
  };

  const closeAddQuestionModal = (): void => {
    setIsAddQuestionModalOpen(false);
  };

  /* const addNewQuestion = (): void => {
    router.push(`${pageUrl.addQuestion}?type=${type}`);
  }; */

  return (
    <div>
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      <div className="flex">
        <div className="w-full">
          <div className="w-full mb-4">
            <h1 className="text-2xl font-semibold tracking-tight">
              {t("exams.examDetails")}
            </h1>
          </div>
          <div className="w-full border rounded-md p-4 bg-[#fff]">
            {isLoading ? (
              <Spinner />
            ) : (
              <>
                <div className="w-full flex">
                  {examInfo?.length > 0 &&
                    examInfo.map((item, index) => (
                      <div key={index} className="w-full">
                        <Card className="w-full pt-4">
                          <CardContent>
                            <div className="w-full text-xl font-semibold">
                              <h1>{item.name}</h1>
                            </div>
                            <div className="md:grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 mt-4">
                              {!examData && (
                                <>
                                  <div className="md:col-span-2 lg:col-span-1 xl:col-span-1">
                                    <div className="font-semibold">
                                      {t("exams.fromDate")}
                                    </div>
                                    <div>
                                      {moment(item.start_time).format(
                                        DATE_FORMAT_DMY_HM_AM_PM,
                                      )}
                                    </div>
                                  </div>
                                  <div className="md:col-span-2 lg:col-span-1 xl:col-span-1">
                                    <div className="font-semibold">
                                      {t("exams.toDate")}
                                    </div>
                                    <div>
                                      {moment(item.end_time).format(
                                        DATE_FORMAT_DMY_HM_AM_PM,
                                      )}
                                    </div>
                                  </div>
                                </>
                              )}
                              <div className="md:col-span-1 lg:col-span-1 xl:col-span-1">
                                <div className="font-semibold">
                                  {t("exams.noOfQuestions")}
                                </div>
                                <div>{item.num_of_questions}</div>
                              </div>
                              <div className="md:col-span-1 lg:col-span-1 xl:col-span-1">
                                <div className="font-semibold">
                                  {t("exams.totalMarks")}
                                </div>
                                <div>{item.total_mark}</div>
                              </div>
                              <div className="md:col-span-1 lg:col-span-1 xl:col-span-1">
                                <div className="font-semibold">
                                  {t("exams.passMark")}
                                </div>
                                <div>{item.pass_mark}</div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>

                        <div className="mt-4">
                          <Label className="font-semibold">
                            {t("exams.description")}
                          </Label>
                          <Card>
                            <CardContent className="max-h-48 overflow-y-auto">
                              <div className="w-full pt-4">
                                {item.description === "HTML" ? (
                                  <span
                                    className="overflow-hidden line-clamp-3"
                                    dangerouslySetInnerHTML={{
                                      __html: item.description.replace(
                                        /&nbsp;/g,
                                        " ",
                                      ),
                                    }}
                                  />
                                ) : (
                                  <span>
                                    {removeHTMLTags(item.description)}
                                  </span>
                                )}
                              </div>
                            </CardContent>
                          </Card>
                        </div>
                        <>
                          {!isButtonDisabled && (
                            <div className="flex justify-end mt-8">
                              <div className="sm:flex">
                                <div className="mb-4 sm:mb-0 me-4">
                                  <Button
                                    className="w-full md:w-auto px-2  bg-[#fb8500] hover:bg-[#fb5c00]"
                                    onClick={openAddQuestionModal}
                                  >
                                    <PlusIcon className="h-5 w-5" />
                                    Add New Question
                                  </Button>
                                </div>
                              </div>
                              <div className="sm:flex">
                                {/* <div className="mb-4 sm:mb-0 me-4">
                                <Button
                                  className="w-full md:w-auto px-2  bg-[#fb8500] hover:bg-[#fb5c00]"
                                  onClick={addNewQuestion}
                                >
                                  <PlusIcon className="h-5 w-5" />
                                  Add New Question
                                </Button>
                              </div>
 */}
                                <div className="mb-4 sm:mb-0">
                                  <Button
                                    className="w-full sm:w-auto px-2  bg-[#fb8500] hover:bg-[#fb5c00]"
                                    onClick={() => openDialog()}
                                  >
                                    {t("exams.importQuestions")}
                                  </Button>
                                </div>
                              </div>
                            </div>
                          )}
                        </>
                      </div>
                    ))}
                </div>

                <div className="flex mt-6 ">
                  <div
                    className={`w-full border rounded-md relative p-4 ${
                      showQuestions ? "md:w-3/4" : ""
                    }`}
                  >
                    <DataTable
                      columns={columns}
                      data={questions as ExamDetailsType[]}
                      FilterLabel={t("exams.filterByQuestions")}
                      FilterBy={"name"}
                      actions={[
                        {
                          title: t("exams.view"),
                          icon: Eye,
                          varient: "icon",
                          handleClick: (val: unknown) =>
                            examDetailView(val as ExamDetailsType),
                        },
                        {
                          title: t("exams.delete"),
                          icon: Trash,
                          varient: "icon",
                          handleClick: (val: unknown) =>
                            deleteQuestionFromQuiz(val as ExamDetailsType),
                        },
                      ]}
                      onSelectedDataChange={() => {}}
                      onRowCountChange={handleTotalRowCountChange}
                    />
                  </div>
                  {showQuestions && (
                    <div className={`w-full md:w-1/4 ms-4`}>
                      <div className="p-4 border rounded-md relative">
                        <button
                          className="absolute top-0 right-0 p-2 mb-2"
                          onClick={closeQuestionSection}
                        >
                          <XCircle />
                        </button>

                        {selectedRow?.question_type === "HTML" ? (
                          <div className="text-gray-600 ">
                            {selectedRow?.question_text != null &&
                            selectedRow?.question_text !== "" ? (
                              <div
                                dangerouslySetInnerHTML={{
                                  __html: selectedRow.question_text,
                                }}
                              />
                            ) : (
                              <div></div>
                            )}
                          </div>
                        ) : (
                          <p className="text-gray-600 mt-6">
                            {removeHTMLTags(selectedRow.question_text)}
                          </p>
                        )}

                        <h2 className="text-xl font-semibold mt-4">
                          {t("exams.answers")}
                        </h2>
                        <ul className="list-disc list-inside mt-2">
                          {/* {selectedRow.answer_type === "HTML" */}
                          {selectedRow?.answers?.map((answer) =>
                            answer.answer_type === "HTML" ? (
                              <li
                                key={answer.answer_id}
                                dangerouslySetInnerHTML={{
                                  __html: answer.answer,
                                }}
                              />
                            ) : (
                              <li key={answer.answer_id}>{answer.answer}</li>
                            ),
                          )}
                        </ul>
                      </div>
                    </div>
                  )}
                </div>
              </>
            )}

            {isDialogOpen && (
              <Modal
                title={t("exams.importQuestions")}
                header=""
                openDialog={isDialogOpen}
                closeDialog={() => closeDialog(true)}
                type="max-w-6xl"
              >
                <ImportQuestions
                  closeDialog={(value: boolean) => closeDialog(value)}
                  questionType={type ?? ""}
                  examDetailsList={(value: boolean) => examDetailsList(value)}
                  numberOfQuestions={numberOfQuestions}
                  rowCount={rowCount}
                  questions={questionIds}
                  isExamData={!!examData}
                />
              </Modal>
            )}
            {deleteGroup && (
              <Modal
                title=""
                header=""
                openDialog={deleteGroup}
                closeDialog={closeDeleteDialog}
              >
                <DeleteQuestion
                  onSave={(value: boolean) => examDetailsList(value)}
                  onCancel={closeDeleteDialog}
                  isModal={true}
                  data={deleteData as DeleteQuestionRequest}
                />
              </Modal>
            )}
            {isAddQuestionModalOpen && (
              <Modal
                title="Add New Question"
                header=""
                openDialog={isAddQuestionModalOpen}
                closeDialog={closeAddQuestionModal}
                type="max-w-6xl"
              >
                <AddQuestionModal
                  closeDialog={closeAddQuestionModal}
                  examId={type ?? ""}
                  onQuestionAdded={(value: boolean) => examDetailsList(value)}
                />
              </Modal>
            )}
            <div className="flex items-center justify-end pt-5">
              {onCancel ? (
                <Button
                  type="button"
                  className="bg-[#33363F]"
                  onClick={onCancel}
                >
                  {t("exams.back")}
                </Button>
              ) : (
                <Link href={pageUrl.exams}>
                  <Button type="button" className="bg-[#33363F]">
                    {t("exams.back")}
                  </Button>
                </Link>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
