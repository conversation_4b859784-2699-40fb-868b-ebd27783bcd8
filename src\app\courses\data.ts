export const courses = [
  {
    course_id: "7a6d1ee9-a570-4791-a1f1-b9893c8cf7f2",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "3d1ff1f9-e36f-46cd-a11d-6753cbbd97eb",
    category_name: "Science",
    short_name: " Earth sciences",
    full_name: " Earth sciences",
    start_date: "2023-09-19T11:37:00",
    end_date: "2023-11-29T11:37:00",
    duration: "71 days",
    status: "Published",
    description: `Beautifully designed components built with Radix UI and
    Tailwind CSS. dfdfdfdfdfdfdf Beautifully designed components
    built with Radix UI and Tailwind CSS. Beautifully designed
    components built with Radix UI and Tailwind CSS. Beautifully
    designed components built with Radix UI and Tailwind CSS.`,
    rating: 4,
  },
  {
    course_id: "71144020-aa85-4049-98b8-4f9a872ac51b",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "48d335fd-b70d-48be-9365-66c8427f9f8f",
    category_name: "Cryptography",
    short_name: " Post Quantum Encryption",
    full_name: " Post Quantum Encryption",
    start_date: "2023-08-21T15:58:00",
    end_date: "2023-11-30T15:58:00",
    duration: "101 days",
    description: `Beautifully designed components built with Radix UI and
    Tailwind CSS. dfdfdfdfdfdfdf Beautifully designed components
    built with Radix UI and Tailwind CSS.`,
    status: "Draft",
  },
  {
    course_id: "c13dfa44-1d31-486f-a3d9-25be4861f482",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "a9358ea2-08b1-4e89-87c7-ea3d9a620867",
    category_name: "Programming in Angular",
    short_name: "Angular",
    full_name: "Angular Programming",
    start_date: "2023-08-01T00:00:00",
    end_date: "2023-10-31T00:00:00",
    duration: "91 days",
    description: `Beautifully designed components built with Radix UI and
    Tailwind CSS.`,
    status: "Draft",
  },
  {
    course_id: "a579d0c4-142a-4c12-8902-d417cad6528c",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "13fc6185-0b55-4ea6-81b5-6fca4d787de2",
    category_name: "APPIUM",
    short_name: "Appium",
    full_name: "Appium Tool",
    start_date: "2023-08-07T17:13:00",
    end_date: "2023-08-07T17:13:00",
    duration: "00:00:00",
    description: `Beautifully designed components built.`,
    status: "Draft",
  },
  {
    course_id: "33ad360a-d610-426d-a8f2-01a2aa0f615f",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "05fe6b55-e337-4f91-8635-a3d0a94195f1",
    category_name: "SBI PO",
    short_name: "Bank course",
    full_name: "Bank new course",
    start_date: "2023-11-01T00:00:00",
    end_date: "2024-04-02T18:30:00",
    duration: "153 days 18:30:00",
    description: `Beautifully designed components built with Radix UI and
    Tailwind CSS. dfdfdfdfdfdfdf Beautifully designed components
    built with Radix UI and Tailwind CSS. Beautifully designed
    components built with Radix UI and Tailwind CSS. Beautifully
    designed components built with Radix UI and Tailwind CSS. Beautifully designed components built with Radix UI and
    Tailwind CSS. dfdfdfdfdfdfdf Beautifully designed components
    built with Radix UI and Tailwind CSS. Beautifully designed
    components built with Radix UI and Tailwind CSS. Beautifully
    designed components built with Radix UI and Tailwind CSS.`,
    status: "Published",
  },
  {
    course_id: "77545d33-e8b1-4c36-9b08-0b6765011bdd",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "05fe6b55-e337-4f91-8635-a3d0a94195f1",
    category_name: "SBI PO",
    short_name: "Bank course -Batch 2",
    full_name: "Bank new course-Batch 2",
    start_date: "2023-08-25T12:09:08",
    end_date: "2023-08-30T12:09:08",
    duration: "5 days",
    status: "Draft",
  },
  {
    course_id: "a2027f3a-9c25-4a67-9b33-57aedb3b763a",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "05fe6b55-e337-4f91-8635-a3d0a94195f1",
    category_name: "SBI PO",
    short_name: "Bank course copy",
    full_name: "Bank new course copy",
    start_date: "2023-08-26T13:00:36",
    end_date: "2023-11-30T15:04:36",
    duration: "96 days 02:04:00",
    status: "Draft",
  },
  {
    course_id: "0d841392-e9f3-4c20-80e1-e68d7c887f4b",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "05fe6b55-e337-4f91-8635-a3d0a94195f1",
    category_name: "SBI PO",
    short_name: "Bank course dt",
    full_name: "Bank new course dt",
    start_date: "2023-08-25T12:20:15",
    end_date: "2023-08-25T12:20:15",
    duration: "00:00:00",
    status: "Draft",
  },
  {
    course_id: "a22ee02c-1a24-4e62-ab75-fc48e45233b4",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "868e809d-0a9d-47ee-aaac-afd7ee7964c5",
    category_name: "Plustwo Maths edt",
    short_name: "Basic Hindi",
    full_name: "Basic Hindi",
    start_date: "2023-08-10T10:18:00",
    end_date: "2023-12-05T04:48:00",
    duration: "116 days 18:30:00",
    status: "Published",
  },
  {
    course_id: "7af6b995-7533-4807-83a8-fb0a0f3fbcc0",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "868e809d-0a9d-47ee-aaac-afd7ee7964c5",
    category_name: "Plustwo Maths edt",
    short_name: "Basic Integration",
    full_name: "Introduction to Integration",
    start_date: "2023-08-10T15:14:00",
    end_date: "2024-01-30T04:30:00",
    duration: "172 days 13:16:00",
    status: "Published",
  },
  {
    course_id: "d5213dfb-fb38-48c0-9997-a1db52801e0c",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "2252c1c5-8ab1-4d12-a593-ed6649e9afcf",
    category_name: "Malayalam",
    short_name: "Basic Malayalam",
    full_name: "Malayalam Basic",
    start_date: "2023-08-09T09:39:00",
    end_date: "2023-11-30T09:39:00",
    duration: "113 days",
    status: "Published",
  },
  {
    course_id: "f2974840-5f19-493e-84d3-e9392d01f79a",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "5ac56579-78f5-477e-9cbd-70d3c5a534bf",
    category_name: "Bio-Chemistry",
    short_name: "BioChemistry",
    full_name: "Bio-Chemistry",
    start_date: "2023-09-19T11:37:00",
    end_date: "2023-11-29T11:37:00",
    duration: "71 days",
    status: "Published",
  },
  {
    course_id: "364a5705-0b89-4189-8593-e087289fe183",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "015449be-0388-49fd-9d7a-2a6bfeef8ab8",
    category_name: "Chemistry",
    short_name: "Bonding",
    full_name: "Chemical Bonding",
    start_date: "2023-08-24T09:24:00",
    end_date: "2023-08-24T18:24:00",
    duration: "09:00:00",
    status: "Draft",
  },
  {
    course_id: "e30a368d-9bce-4db6-bfd1-80d584982fcd",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "afc9fce0-d45b-4e8d-8ef6-50bcc3180513",
    category_name: "Types Of Cryptography",
    short_name: "Cryptography types",
    full_name: "Types Of Cryptography",
    start_date: "2023-08-18T12:48:00",
    end_date: "2023-12-27T12:48:00",
    duration: "131 days",
    status: "Published",
  },
  {
    course_id: "5f45c882-32d1-43d3-91ed-f47b3dafedd4",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "7912d488-bbd9-4b1b-b473-3367250545ba",
    category_name: "Data structures and Algorithms",
    short_name: "DSA",
    full_name: "Data Structures and Algorthms",
    start_date: "2023-08-31T14:15:00",
    end_date: "2024-04-17T00:00:00",
    duration: "229 days 09:45:00",
    status: "Published",
  },
  {
    course_id: "f17c0813-6e74-430d-ac24-b5d3e4784d9f",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "a7289ad4-4957-487d-b33a-f79cfa662717",
    category_name: "Test data",
    short_name: "Demo Coure",
    full_name: "Sample Course Demo",
    start_date: "2023-08-25T10:00:00",
    end_date: "2023-10-31T18:00:00",
    duration: "67 days 08:00:00",
    status: "Draft",
  },
  {
    course_id: "52c594b1-940d-42bf-a30c-b65acf2ee5c6",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "d13fcc48-cd60-41c6-ab66-9777f7d6d0df",
    category_name: "Physics",
    short_name: "EMFT",
    full_name: "Electro magnetic field theory",
    start_date: "2023-08-22T13:54:00",
    end_date: "2023-09-22T18:56:00",
    duration: "31 days 05:02:00",
    status: "Draft",
  },
  {
    course_id: "88607823-cb54-4648-b574-0b7ecb1373fe",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "d13fcc48-cd60-41c6-ab66-9777f7d6d0df",
    category_name: "Physics",
    short_name: "Electro magnetic induction",
    full_name: "Electro magnetic induction",
    start_date: "2023-08-17T12:28:00",
    end_date: "2023-10-19T12:28:00",
    duration: "63 days",
    status: "Published",
  },
  {
    course_id: "3e87b65d-b6c7-40da-be72-acc85d0100d4",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "c76ab83c-c350-443d-b839-12fd880c2e36",
    category_name: "English",
    short_name: "English Tutorial",
    full_name: "English Tutorial",
    start_date: "2023-08-14T10:06:00",
    end_date: "2023-12-28T18:30:00",
    duration: "136 days 08:24:00",
    status: "Published",
  },
  {
    course_id: "79b134f0-a63c-446f-a957-8fbf6ef4adb0",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "e6d4aaf2-fad2-4575-bab7-30469809bad6",
    category_name: "Features Of Cryptography",
    short_name: "Features of Cryptography",
    full_name: "Features",
    start_date: "2023-08-18T12:52:00",
    end_date: "2023-08-31T12:52:00",
    duration: "13 days",
    status: "Published",
  },
  {
    course_id: "f145c800-ba50-4ee8-ba23-0080c4827d81",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "54873169-d822-4170-9218-409f1a112329",
    category_name: "Python Full Stack Development ",
    short_name: "Flask",
    full_name: "Flask (web framework)",
    start_date: "2023-08-08T13:44:00",
    end_date: "2023-12-08T13:44:00",
    duration: "122 days",
    status: "Published",
  },
  {
    course_id: "4b6ecc65-abcc-41d2-b9c3-f476c35f51a7",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "e9eacdc0-12a1-4736-9a33-f7b930ec0a5c",
    category_name: "Python Full Course",
    short_name: "HTML",
    full_name: "HTML",
    start_date: "2023-07-19T00:00:00",
    end_date: "2024-02-21T08:55:34.379",
    duration: "217 days 08:55:34.379",
    status: "Published",
  },
  {
    course_id: "da97c2da-d8df-437d-8741-0d6673835224",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "6a669b6a-fd7a-4fad-810d-66f658e4cbb2",
    category_name: "Organic Chemistry",
    short_name: "HaloAlkenes and haloarenes",
    full_name: "HaloAlkenes and haloarenes",
    start_date: "2023-08-16T17:55:00",
    end_date: "2023-08-16T17:55:00",
    duration: "00:00:00",
    status: "Published",
  },
  {
    course_id: "daf331c1-e93b-4761-900c-6e56b0fd56e9",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "80241483-4567-4c86-b94c-abb92874c014",
    category_name: "Updated Mathematics",
    short_name: "Ionic Frameowkr",
    full_name: "Ionic Framework Program",
    start_date: "2023-07-10T00:00:00",
    end_date: "2023-07-31T00:00:00",
    duration: "21 days",
    status: "Draft",
  },
  {
    course_id: "bc919909-8b9b-4d4b-b1de-533bdf1e7c5b",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "3e38af63-bf85-45c3-a4fb-6eff77c0cc81",
    category_name: "Java Fundamentals",
    short_name: "JDBC",
    full_name: "JDBC",
    start_date: "2023-07-28T18:07:00",
    end_date: "2023-07-31T18:07:00",
    duration: "3 days",
    status: "Published",
  },
  {
    course_id: "a13634eb-bbd4-4b9a-a5ee-286bdceb8f74",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "6cf3a663-d45c-4cd6-8a1c-e04bd6978137",
    category_name: "Javascript",
    short_name: "JS",
    full_name: "Javascript",
    start_date: "2023-08-08T13:55:00",
    end_date: "2023-11-30T13:55:00",
    duration: "114 days",
    status: "Published",
  },
  {
    course_id: "eb51cdb9-0be5-4c50-95c9-97a99f2ce4dc",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "3e38af63-bf85-45c3-a4fb-6eff77c0cc81",
    category_name: "Java Fundamentals",
    short_name: "Java Basics",
    full_name: "Java Basics",
    start_date: "2023-07-31T18:07:00",
    end_date: "2023-12-29T18:07:00",
    duration: "151 days",
    status: "Published",
  },
  {
    course_id: "85d96b1e-1bc1-4f0e-b768-62f27395364d",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "11e334e1-4b82-492a-9357-c22ece3623cd",
    category_name: "Kerala History",
    short_name: "Kerala History Course",
    full_name: "Kerala History",
    start_date: "2023-08-23T08:00:00",
    end_date: "2023-10-31T18:00:00",
    duration: "69 days 10:00:00",
    status: "Published",
  },
  {
    course_id: "3c1168c1-a39f-4b7c-80ed-c33ce6e2ebe0",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "153e6e8a-41ac-4a0d-8a60-29c086ec209b",
    category_name: "LDC New",
    short_name: "LDC Practice Course",
    full_name: "LDC Course",
    start_date: "2023-08-14T00:00:00",
    end_date: "2023-10-10T13:00:00",
    duration: "57 days 13:00:00",
    status: "Published",
  },
  {
    course_id: "04f1c8cf-92d4-471e-9a12-bf92b748526c",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "8cc882db-3112-4347-be76-fb409948d804",
    category_name: "Hindi",
    short_name: "MAlayalam",
    full_name: "Basic Malayalam Course",
    start_date: "2023-08-16T09:14:00",
    end_date: "2024-01-17T22:14:00",
    duration: "154 days 13:00:00",
    status: "Published",
  },
  {
    course_id: "31c902f6-22cd-416f-b705-db22305c7782",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "2252c1c5-8ab1-4d12-a593-ed6649e9afcf",
    category_name: "Malayalam",
    short_name: "Malayalam",
    full_name: "Malayalam",
    start_date: "2023-08-09T09:11:00",
    end_date: "2023-08-09T09:11:00",
    duration: "00:00:00",
    status: "Published",
  },
  {
    course_id: "4dcd26ae-2014-41b7-beaf-fb7836035970",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "2252c1c5-8ab1-4d12-a593-ed6649e9afcf",
    category_name: "Malayalam",
    short_name: "Malayalam G K",
    full_name: "Malayalam G K",
    start_date: "2023-08-09T11:00:00",
    end_date: "2023-10-31T11:00:00",
    duration: "83 days",
    status: "Published",
  },
  {
    course_id: "47fb669a-c8e8-428f-8f1f-28cc9ab69f42",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "60a24978-c174-4615-bcb2-70d083953b8b",
    category_name: "Mathematics",
    short_name: "Maths Course",
    full_name: "Maths Course",
    start_date: "2023-07-01T00:00:00",
    end_date: "2023-08-31T00:00:00",
    duration: "61 days",
    status: "Published",
  },
  {
    course_id: "f0f65770-91ce-4216-85e1-8468a9d55bc2",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "1edf4874-d90e-4336-9dae-2495aa9e62e0",
    category_name: "Reinforcement Learning",
    short_name: "NLP",
    full_name: "Natural language processing (NLP)",
    start_date: "2023-10-16T00:00:00",
    end_date: "2024-03-28T23:30:00",
    duration: "164 days 23:30:00",
    status: "Published",
  },
  {
    course_id: "24164b9f-209e-4c3b-9ef5-f4335607f131",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "76def241-ee18-4f22-a7c3-902c761fdfd1",
    category_name: "Applied Statistics",
    short_name: "NLP II",
    full_name: "Natural language processing (NLP) II",
    start_date: "2023-08-17T00:00:00",
    end_date: "2023-08-31T00:00:00",
    duration: "14 days",
    status: "Draft",
  },
  {
    course_id: "fd6b1104-e368-47e5-96f8-5c8502fb73fa",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "1edf4874-d90e-4336-9dae-2495aa9e62e0",
    category_name: "Reinforcement Learning",
    short_name: "NN",
    full_name: "Neural Networks and Deep Learning",
    start_date: "2023-06-28T00:00:00",
    end_date: "2023-06-26T00:00:00",
    duration: "-2 days",
    status: "Published",
  },
  {
    course_id: "5c246a6b-e48d-4bc7-80ab-61fa73572fe0",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "1c516980-0b0b-45b3-a728-5f968bcb81e8",
    category_name: "Natural Science",
    short_name: "Natural-science",
    full_name: "Natural science -Batch 1",
    start_date: "2023-09-12T14:38:00",
    end_date: "2023-10-26T14:38:00",
    duration: "44 days",
    status: "Published",
  },
  {
    course_id: "6d678063-8d1a-40a6-9dbe-f37f650f5831",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "3d1ff1f9-e36f-46cd-a11d-6753cbbd97eb",
    category_name: "Science",
    short_name: "Physical -science",
    full_name: "Physical science -Batch 1",
    start_date: "2023-09-14T14:38:00",
    end_date: "2024-06-28T14:38:00",
    duration: "288 days",
    status: "Published",
  },
  {
    course_id: "fddd9fe4-dcf0-418b-8fd2-1c97f4675836",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "54873169-d822-4170-9218-409f1a112329",
    category_name: "Python Full Stack Development ",
    short_name: "Python",
    full_name: "Python",
    start_date: "2023-06-30T00:00:00",
    end_date: "2023-12-13T00:00:00",
    duration: "166 days",
    status: "Published",
  },
  {
    course_id: "27b3b4cf-5b4a-4a47-9cd0-b30c5ed8cdd6",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "f4b3ad8e-6c39-4c41-a4cf-a5030ca9f6d7",
    category_name: "Structural biology",
    short_name: "SB",
    full_name: "Structural Biology",
    start_date: "2023-09-20T14:10:00",
    end_date: "2023-09-30T14:10:00",
    duration: "10 days",
    status: "Published",
  },
  {
    course_id: "0843b5da-6b61-4945-8917-83b1ca63b804",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "05fe6b55-e337-4f91-8635-a3d0a94195f1",
    category_name: "SBI PO",
    short_name: "SBI PO Prelims",
    full_name: "SBI PO Prelims",
    start_date: "2023-07-01T21:19:00",
    end_date: "2023-07-31T00:00:00",
    duration: "29 days 02:41:00",
    status: "Published",
  },
  {
    course_id: "1c1abd4f-9342-4220-b2e5-618e65b30b7d",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "05fe6b55-e337-4f91-8635-a3d0a94195f1",
    category_name: "SBI PO",
    short_name: "Sample",
    full_name: "New course",
    start_date: "2023-07-06T10:41:00",
    end_date: "2023-07-31T10:41:00",
    duration: "25 days",
    status: "Published",
  },
  {
    course_id: "4e1a9e7d-5e03-4a58-9cc2-7c083e7de604",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "a7289ad4-4957-487d-b33a-f79cfa662717",
    category_name: "Test data",
    short_name: "Test cource batch 2",
    full_name: "Test cource batch 2",
    start_date: "2023-09-01T00:00:00",
    end_date: "2023-10-05T00:00:00",
    duration: "34 days",
    status: "Draft",
  },
  {
    course_id: "db47d83f-213e-4e8c-9be9-505a92676af0",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "a7289ad4-4957-487d-b33a-f79cfa662717",
    category_name: "Test data",
    short_name: "Test cource batch 3",
    full_name: "Test cource batch 3 -edit 2",
    start_date: "2023-09-11T00:00:00",
    end_date: "2023-10-22T17:00:00",
    duration: "41 days 17:00:00",
    status: "Draft",
  },
  {
    course_id: "fe03fb29-7158-4bbf-bb53-4cc68954195c",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "a7289ad4-4957-487d-b33a-f79cfa662717",
    category_name: "Test data",
    short_name: "Test cource batch 4",
    full_name: "Test cource batch 4",
    start_date: "2023-09-01T00:00:00",
    end_date: "2023-09-30T00:00:00",
    duration: "29 days",
    status: "Draft",
  },
  {
    course_id: "6dfd5f4e-3f67-4ccc-8bba-9e56e7c4f890",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "a7289ad4-4957-487d-b33a-f79cfa662717",
    category_name: "Test data",
    short_name: "Test cource new batch",
    full_name: "Test cource new batch",
    start_date: "2023-08-22T08:00:00",
    end_date: "2023-09-30T17:00:00",
    duration: "39 days 09:00:00",
    status: "Draft",
  },
  {
    course_id: "ee1641aa-2fe2-4b32-9dfd-7073c15d61fa",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "015449be-0388-49fd-9d7a-2a6bfeef8ab8",
    category_name: "Chemistry",
    short_name: "The Atom",
    full_name: "Study of  Atom",
    start_date: "2023-08-22T14:21:00",
    end_date: "2023-09-22T19:21:00",
    duration: "31 days 05:00:00",
    status: "Published",
  },
  {
    course_id: "fc20f2d7-d220-413b-8cf0-8ff000da6d8c",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "015449be-0388-49fd-9d7a-2a6bfeef8ab8",
    category_name: "Chemistry",
    short_name: "Thermochem",
    full_name: "Thermochemistry",
    start_date: "2023-08-23T12:00:00",
    end_date: "2023-08-23T05:00:00",
    duration: "-07:00:00",
    status: "Published",
  },
  {
    course_id: "93e18723-bacc-4e3e-ab3e-6d77b742bc46",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "2c4c35ac-3351-41d7-a413-aa865f549d5d",
    category_name: "UPSC",
    short_name: "UPSC -Paper 1",
    full_name: "UPSC Syllabus for Paper-I ",
    start_date: "2023-08-02T00:00:00",
    end_date: "2023-10-31T12:37:00",
    duration: "90 days 12:37:00",
    status: "Published",
  },
  {
    course_id: "1c7fb527-c4b3-4d9e-ad44-cb579a1d9a19",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "2c4c35ac-3351-41d7-a413-aa865f549d5d",
    category_name: "UPSC",
    short_name: "UPSC Paper-II ",
    full_name: "UPSC Syllabus for Paper-II – 200 marks",
    start_date: "2023-08-02T00:00:00",
    end_date: "2023-12-28T18:07:00",
    duration: "148 days 18:07:00",
    status: "Published",
  },
  {
    course_id: "3c10016d-fad8-474f-9369-310335eb5ca8",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "54873169-d822-4170-9218-409f1a112329",
    category_name: "Python Full Stack Development ",
    short_name: "Web Servers",
    full_name: "Web servers",
    start_date: "2023-08-07T13:34:00",
    end_date: "2023-12-29T13:34:00",
    duration: "144 days",
    status: "Draft",
  },
  {
    course_id: "5a0d7709-27de-4b81-8a13-130c110aeb03",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "2252c1c5-8ab1-4d12-a593-ed6649e9afcf",
    category_name: "Malayalam",
    short_name: "ഇന്ത്യൻ ഭരണഘടന ",
    full_name: "ഇന്ത്യൻ ഭരണഘടന (Indian Constitution in Malayalam)",
    start_date: "2023-08-09T00:00:00",
    end_date: "2023-08-09T00:00:00",
    duration: "00:00:00",
    status: "Draft",
  },
  {
    course_id: "effbd264-42f8-4bf8-9f1c-5eb82679bb14",
    org_id: "********-a028-484d-8f35-8af2023068dd",
    category_id: "2252c1c5-8ab1-4d12-a593-ed6649e9afcf",
    category_name: "Malayalam",
    short_name: "കേരള ചരിത്രം GK",
    full_name: "കേരള ചരിത്രം GK",
    start_date: "2023-08-09T12:01:00",
    end_date: "2023-08-31T12:01:00",
    duration: "22 days",
    status: "Draft",
  },
];
