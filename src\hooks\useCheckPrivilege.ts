"use client";
import type { RolePrivileges } from "@/types";
const getPrivilegeList = (screen: string, action: string): boolean => {
  if (typeof window !== "undefined") {
    const privilege = localStorage?.getItem("role_privileges");
    if (privilege !== null && privilege !== undefined) {
      const privilegeList: RolePrivileges[] = JSON.parse(
        privilege,
      ) as RolePrivileges[];
      const hasPrivilege = privilegeList?.some(
        (rolePrivilege) =>
          rolePrivilege.screen === screen && rolePrivilege.actions[action],
      );
      return hasPrivilege;
    } else {
      return false;
    }
  } else {
    return false;
  }
};

export default getPrivilegeList;
