"use client";
import React from "react";
import type { ColumnDef } from "@tanstack/react-table";
// import { ArrowUpDown } from "lucide-react";
// import { Button } from "@/components/ui/button";
import moment from "moment";
import type {
  AttendedExamsType,
  // ColumnDefinition,
  RowDefinition,
} from "@/types";
import { DATE_FORMAT_DMY_HM_AM_PM } from "@/lib/constants";

export const getColumns = (
  t: (key: string) => string
): ColumnDef<AttendedExamsType>[] => {

  return [
    {
      accessorKey: "course_name",
      header: t("attendedExams.columns.course"),
      // header: ({ column }: ColumnDefinition): React.JSX.Element => {
      //   return (
      //     <Button
      //       className="px-0"
      //       variant="ghost"
      //       onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      //     >
      //       Course
      //       <ArrowUpDown className="ml-2 h-4 w-4" />
      //     </Button>
      //   );
      // },
      cell: ({ row }) => <div>{row.original.course_name}</div>,
    },
    {
      accessorKey: "first_name",
      header: t("attendedExams.columns.firstName"),
    // header: ({ column }: ColumnDefinition): React.JSX.Element => {
    //   return (
    //     <Button
    //       className="px-0"
    //       variant="ghost"
    //       onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
    //     >
    //       User name
    //       <ArrowUpDown className="ml-2 h-4 w-4" />
    //     </Button>
    //   );
    // },
    cell: ({ row }: RowDefinition): React.JSX.Element => {
      const firstName = row.original.first_name ?? " ";
      const lastName = row.original.last_name ?? " ";
      return (
        <div className="text-align">{`${firstName} ${lastName}`.trim()}</div>
      );
    },
  },
    {
      accessorKey: "name",
      header: t("attendedExams.columns.examName"),
    // header: ({ column }: ColumnDefinition): React.JSX.Element => {
    //   return (
    //     <Button
    //       className="px-0"
    //       variant="ghost"
    //       onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
    //     >
    //       Exam name
    //       <ArrowUpDown className="ml-2 h-4 w-4" />
    //     </Button>
    //   );
    // },
      cell: ({ row }) => <div>{row.original.name}</div>,
    },
    {
      accessorKey: "quiz_type",
      header: t("attendedExams.columns.examType"),
    // header: ({ column }: ColumnDefinition): React.JSX.Element => {
    //   return (
    //     <Button
    //       className="px-0"
    //       variant="ghost"
    //       onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
    //     >
    //       Exam type
    //       <ArrowUpDown className="ml-2 h-4 w-4" />
    //     </Button>
    //   );
    // },
      cell: ({ row }) => <div>{row.original.quiz_type}</div>,
    },
    {
      accessorKey: "duration",
      header: t("attendedExams.columns.duration"),
      cell: ({ row }) => <div>{row.original.duration}</div>,
    },
    {
      accessorKey: "quiz_end_time",
      header: t("attendedExams.columns.submittedOn"),
    cell: ({ row }: RowDefinition): React.JSX.Element => {
      const formattedDate =
        row.original.quiz_end_time != null
          ? moment
              .utc(row.original.quiz_end_time)
              .local()
              .format(DATE_FORMAT_DMY_HM_AM_PM)
          : "";

      return <div>{formattedDate}</div>;
    },
    },
    {
      accessorKey: "quiz_pass_mark",
      header: t("attendedExams.columns.passmark"),
      cell: ({ row }: RowDefinition): React.JSX.Element => (
        <div className="text-center">{row.original.quiz_pass_mark}</div>
      ),
    },
    {
      accessorKey: "quiz_status",
      header: t("attendedExams.columns.status"),
    cell: ({ row }: RowDefinition): React.JSX.Element => {
      const result = row.original.quiz_status;
      const status = result === "evaluated";
      const cellColor = status ? "text-blue-700" : "text-red-600";
      return <div className={cellColor}>{result}</div>;
    },
    },
    {
      accessorKey: "quiz_total_marks",
      header: t("attendedExams.columns.marksObtained"),
      cell: ({ row }: RowDefinition): React.JSX.Element => (
        <div className="text-center">
          {row.original.quiz_total_marks?.toFixed(2)}
        </div>
      ),
    },

    {
      accessorKey: "result_of_quiz",
      header: t("attendedExams.columns.result"),
      cell: ({ row }: RowDefinition): React.JSX.Element => {
        const result = row.original.result_of_quiz;
        const passed = result === "Passed";
        const cellColor = passed ? "text-green-700" : "text-red-600";
        return <div className={cellColor}>{result}</div>;
      },
    },
  ];
};
