import React from "react";
import type {
  <PERSON><PERSON>r<PERSON>atch,
  GroupForm,
  LogUserActivityRequest,
  ToastType,
} from "@/types";
import { Button } from "@/components/ui/button";
import useGroups from "@/hooks/useGroups";
import { useToast } from "@/components/ui/use-toast";
import { useRouter } from "next/navigation";
import { DEFAULT_FOLDER_ID, ORG_KEY } from "@/lib/constants";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";
export default function DeleteGroups({
  data,
  onSave,
  onCancel,
}: {
  onSave: () => void;
  onCancel: () => void;
  data: GroupForm;
  isModal?: boolean;
}): React.JSX.Element {
  const { t } = useTranslation();
  const router = useRouter();
  const { toast } = useToast() as ToastType;
  const { deleteGroups } = useGroups();
  const handleDeleteClick = (): void => {
    void handleToastSave();
    onCancel();
  };
  const { updateUserActivity } = useLogUserActivity();
  const handleToastSave = async (): Promise<void> => {
    const groupDeleteData = {
      group_id: data.id,
      org_id: localStorage.getItem(ORG_KEY) ?? "",
    };
    try {
      const result = await deleteGroups(groupDeleteData);
      if (result.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.groupDeleted"),
          description: t("successMessages.groupDeleteDesc"),
        });
        onSave();
        router.push("/teams");
        const params = {
          activity_type: "Group",
          screen_name: "Delete Group",
          action_details: "group deleted ",
          target_id: data?.id as string,
          log_result: "SUCCESS",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      } else if (result.status === "error") {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: result?.details,
        });
        console.log("API Error:", result.status);
        const params = {
          activity_type: "Group",
          screen_name: "Delete Group",
          action_details: "Failed Delete Group ",
          target_id: data?.id as string,
          log_result: "ERROR",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
      console.error("An unexpected error occurred:", error);
      const params = {
        activity_type: "Group",
        screen_name: "Delete Group",
        action_details: "Failed Delete Group ",
        target_id: DEFAULT_FOLDER_ID,
        log_result: "ERROR",
      };
      void updateLogUserActivity(params).catch((error) => {
        console.error(error);
      });
    }
  };
  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };
  return (
    <>
      <div className="mb-2 mr-4">
        <p className="ml-0 ">
          {String(t("groups.teams.deleteGroupModal.confirmMessage"))}
        </p>
      </div>
      <div className="flex topic-icons pr-4">
        <div className="flex items-center justify-center float-right gap-3">
          <Button type="button" className="bg-[#33363F]" onClick={onCancel}>
            {String(t("buttons.cancel"))}
          </Button>

          <Button
            type="submit"
            className="bg-[#9FC089]"
            onClick={handleDeleteClick}
          >
            {String(t("buttons.delete"))}
          </Button>
        </div>
      </div>
    </>
  );
}
