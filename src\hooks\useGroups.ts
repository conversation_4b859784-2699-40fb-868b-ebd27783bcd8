import { supabase } from "@/lib/client";
import type {
  AddCourseToGroupType,
  AddPrivilegeToGroupType,
  AddUserToGroupType,
  DeleteGroupRequest,
  DeleteGroupResponse,
  ErrorType,
  GroupForm,
  GroupRequest,
  GroupResponse,
} from "@/types";
import { views, rpc } from "../lib/apiConfig";
interface UseGroupsReturn {
  getGroups: () => Promise<GroupForm[]>;
  addGroups: (groupData: GroupRequest) => Promise<GroupResponse>;
  editGroups: (groupData: GroupRequest) => Promise<GroupResponse>;
  deleteGroups: (groupData: DeleteGroupRequest) => Promise<DeleteGroupResponse>;
  getUserGroups: (groupData: GroupRequest) => Promise<GroupResponse>;
  addUserToGroups: (groupData: AddUserToGroupType) => Promise<GroupResponse>;
  getUserCourses: (groupData: GroupRequest) => Promise<GroupResponse>;
  addCourseToGroups: (
    groupData: AddCourseToGroupType,
  ) => Promise<GroupResponse>;
  getPrivilegeGroups: (groupData: GroupRequest) => Promise<GroupResponse>;
  addPrivilegeToGroup: (
    groupData: AddPrivilegeToGroupType,
  ) => Promise<GroupResponse>;
}

const useGroups = (): UseGroupsReturn => {
  async function getGroups(): Promise<GroupForm[]> {
    try {
      const groupsList = views?.groups ?? "";
      const org_id = localStorage.getItem("orgId");

      const exeQuery = supabase.from(groupsList).select().eq("org_id", org_id);

      const { data, error } = await exeQuery;
      if (error) {
        throw new Error(error.details);
      }

      return data as GroupForm[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function addGroups(groupData: GroupRequest): Promise<GroupResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.insertGroup,
        groupData,
      )) as {
        data: GroupResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as GroupResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function editGroups(groupData: GroupRequest): Promise<GroupResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.updateGroup,
        groupData,
      )) as {
        data: GroupResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as GroupResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function deleteGroups(
    groupData: DeleteGroupRequest,
  ): Promise<DeleteGroupResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.deleteGroup,
        groupData,
      )) as {
        data: GroupResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as DeleteGroupResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function getUserGroups(
    userGroupData: GroupRequest,
  ): Promise<GroupResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.getUsersListForGroup,
        userGroupData,
      )) as {
        data: GroupResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as GroupResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function addUserToGroups(
    userGroupData: AddUserToGroupType,
  ): Promise<GroupResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.updateUsersForGroup,
        userGroupData,
      )) as {
        data: GroupResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as GroupResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function getUserCourses(
    userGroupData: GroupRequest,
  ): Promise<GroupResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.getCourseListForGroup,
        userGroupData,
      )) as {
        data: GroupResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as GroupResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function addCourseToGroups(
    courseGroupData: AddCourseToGroupType,
  ): Promise<GroupResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.updateCoursesForGroup,
        courseGroupData,
      )) as {
        data: GroupResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as GroupResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function getPrivilegeGroups(
    privilegesGroupData: GroupRequest,
  ): Promise<GroupResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.getPrivilegesListForGroup,
        privilegesGroupData,
      )) as {
        data: GroupResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as GroupResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function addPrivilegeToGroup(
    privilegesGroupData: AddPrivilegeToGroupType,
  ): Promise<GroupResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.updatePrivilegesForGroup,
        privilegesGroupData,
      )) as {
        data: GroupResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as GroupResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  return {
    getGroups,
    addGroups,
    editGroups,
    deleteGroups,
    getUserGroups,
    addUserToGroups,
    getUserCourses,
    addCourseToGroups,
    getPrivilegeGroups,
    addPrivilegeToGroup,
  };
};

export default useGroups;
