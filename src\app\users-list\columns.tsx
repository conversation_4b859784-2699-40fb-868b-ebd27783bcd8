"use client";

import type { ColumnDef } from "@tanstack/react-table";
// import { ArrowUpDown } from "lucide-react";
// import { Button } from "@/components/ui/button";
import Image from "next/image";
import type {
  UserDetailsTableType,
  // UserDetailsColumnDefinition,
} from "@/types";

export const getColumns = (
  t: (key: string) => string
): ColumnDef<UserDetailsTableType>[] => {

  return [
    {
      accessorKey: "first_name",
      header: t("usersList.columns.firstName"),
    // header: ({ column }: UserDetailsColumnDefinition): React.JSX.Element => {
    //   return (
    //     <Button
    //       className="px-0"
    //       variant="ghost"
    //       onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
    //     >
    //       First name
    //       <ArrowUpDown className="ml-2 h-4 w-4" />
    //     </Button>
    //   );
    // },
      cell: ({
        row,
      }: {
        row: { original: UserDetailsTableType };
      }): React.JSX.Element => {
        const firstName = row.original.first_name ?? " ";
        return <div className="text-align">{firstName}</div>;
      },
    },
    {
      accessorKey: "last_name",
      header: t("usersList.columns.lastName"),
      cell: ({
        row,
      }: {
        row: { original: UserDetailsTableType };
      }): React.JSX.Element => {
        const lastName = row.original.last_name ?? " ";
        return <div className="text-align">{lastName}</div>;
      },
    },
    {
      accessorKey: "email",
      header: t("usersList.columns.email"),
    },
    {
      accessorKey: "avatar_url",
      header: t("usersList.columns.profile"),
      cell: ({ row }) => (
        <div className="h-10 w-10">
          {row.original.avatar_url !== "" && row.original.avatar_url !== null ? (
            <Image
              src={row.original.avatar_url}
              alt=""
              width={45}
              height={45}
              objectFit="cover"
              className="rounded-full"
              style={{
                width: "45px",
                height: "38px",
                border: "2px solid #000",
                borderRadius: "50%",
              }}
            />
          ) : (
            <div className="rounded-full h-25 w-25 bg-gray-300"></div>
          )}
        </div>
      ),
    },

    {
      accessorKey: "phonenumber1",
      header: t("usersList.columns.phone"),
    },
    {
      accessorKey: "roles",
      header: t("usersList.columns.role"),
    },
  ];
};
