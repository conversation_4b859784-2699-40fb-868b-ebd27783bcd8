import { supabase } from "../lib/client";
import { rpc, views } from "../lib/apiConfig";
import type {
  SubscriptionCourse,
  SubscriptionList,
  SubscriptionResource,
  ErrorType,
  SubscriptionPlans,
  SubscriptionRequestType,
  SubscriptionResponseType,
  PendingSubscriptionResponse,
  PendingSubscriptionRequest,
  AddPendingSubscriptions,
  AddPendingSubscriptionsResponse,
  UserPlanList,
  subscriptionListRequest,
  SubscriptionListUpdated,
  DeleteMembershipRequest,
  MembershipResponse,
  ApproveMembershipRequest,
  ApproveMembershipResponse,
  subscriptionDeleteUserRequest,
  DeleteUserResponse,
  SuccessMessage,
  AddUserToSubscriptionRequest,
  AddUserResponse,
  ExtendPlanValidityRequest,
  UpdatePurchaseRequest,
} from "@/types";
import { privilegeData } from "@/lib/constants";

interface useSubscriptionReturn {
  getSubscriptionList: (org_id?: string) => Promise<SubscriptionList[]>;
  getSubscriptionListForAdmin: (
    reqParams: subscriptionListRequest,
  ) => Promise<SubscriptionListUpdated>;
  getListDetailsById: (
    orgId?: string,
    membershipId?: string,
  ) => Promise<SubscriptionPlans[]>;
  addSubscription: (
    formData: SubscriptionRequestType,
  ) => Promise<SubscriptionResponseType>;
  updateSubscription: (
    formData: SubscriptionRequestType,
  ) => Promise<SubscriptionResponseType>;
  getCourseListForPlan: (
    formData: SubscriptionRequestType,
  ) => Promise<SubscriptionCourse>;
  updateCourse: (
    formData: SubscriptionRequestType,
  ) => Promise<SubscriptionCourse>;
  getResoureceList: (
    formData: SubscriptionRequestType,
  ) => Promise<SubscriptionResource>;
  updateResource: (
    formData: SubscriptionRequestType,
  ) => Promise<SubscriptionResponseType>;
  getPendingSubscriptions: (
    params: PendingSubscriptionRequest,
  ) => Promise<PendingSubscriptionResponse>;
  addPendingUsersToPlan: (
    params: AddPendingSubscriptions,
  ) => Promise<AddPendingSubscriptionsResponse>;
  getSubscribedUsers: (
    params: PendingSubscriptionRequest,
  ) => Promise<UserPlanList>;
  deleteSubscription: (
    subscriptionData: DeleteMembershipRequest,
  ) => Promise<MembershipResponse>;
  approveSubscription: (
    subscriptionData: ApproveMembershipRequest,
  ) => Promise<ApproveMembershipResponse>;
  getSubscriptionUserDelete: (
    subscriptionData: subscriptionDeleteUserRequest,
  ) => Promise<DeleteUserResponse>;
  addToSubscription: (
    params: AddUserToSubscriptionRequest,
  ) => Promise<AddUserResponse>;
  extendValidity: (
    params: ExtendPlanValidityRequest,
  ) => Promise<AddUserResponse>;
  updatePurchaseRequest: (
    params: UpdatePurchaseRequest,
  ) => Promise<SuccessMessage>;
}

const useSubscription = (): useSubscriptionReturn => {
  const getSubscriptionListForAdmin = async (
    reqParams: subscriptionListRequest,
  ): Promise<SubscriptionListUpdated> => {
    try {
      const subscriptionView = rpc?.subscriptionList ?? "";

      const formData = reqParams;
      const { data, error } = (await supabase.rpc<string, null>(
        subscriptionView,
        formData,
      )) as {
        data: SubscriptionResponseType;
        error: ErrorType | null;
      };

      if (error) {
        throw new Error(error.details);
      }
      return data as SubscriptionListUpdated;
    } catch (err) {
      console.error("Error:", err);
      throw err;
    }
  };
  async function getSubscriptionUserDelete(
    params?: subscriptionDeleteUserRequest,
  ): Promise<SuccessMessage> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.deleteUserSubscription,
        params,
      )) as { data: SuccessMessage; error: ErrorType | null };
      if (error) {
        throw new Error(error.details);
      }
      return data as SuccessMessage;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  const getSubscriptionList = async (
    orgId?: string,
  ): Promise<SubscriptionList[]> => {
    try {
      const subscriptionView = views?.subscriptionList ?? "";
      const org_id = orgId;
      const exeQuery = supabase
        .from(subscriptionView)
        .select()
        .eq("org_id", org_id);
      const { data, error } = await exeQuery;
      if (error) {
        throw new Error(error.details);
      }
      return data as SubscriptionList[];
    } catch (err) {
      console.error("Error:", err);
      throw err;
    }
  };
  const getListDetailsById = async (
    membershipId?: string,
    orgId?: string,
  ): Promise<SubscriptionPlans[]> => {
    try {
      const subscriptionView = views?.subscriptionList ?? "";
      const org_id = orgId;
      const membership_id = membershipId;

      const exeQuery = supabase
        .from(subscriptionView)
        .select()
        .eq("org_id", org_id)
        .eq("id", membership_id);

      const { data, error } = await exeQuery;

      if (error) {
        throw new Error(error.details);
      }

      return data as SubscriptionPlans[];
    } catch (err) {
      console.error("Error:", err);
      throw err;
    }
  };
  const addSubscription = async (
    formData: SubscriptionRequestType,
  ): Promise<SubscriptionResponseType> => {
    try {
      const { data, error } = (await supabase.rpc<string, null>(
        rpc.addSubscriptionPlan,
        formData,
      )) as {
        data: SubscriptionResponseType;
        error: ErrorType | null;
      };

      if (error) {
        throw new Error(error.details as string);
      }

      return data as SubscriptionResponseType;
    } catch (err) {
      console.error("Error:", err);
      throw err;
    }
  };
  const updateSubscription = async (
    formData: SubscriptionRequestType,
  ): Promise<SubscriptionResponseType> => {
    try {
      const { data, error } = (await supabase.rpc<string, null>(
        rpc.updateSubscriptionPlan,
        formData,
      )) as {
        data: SubscriptionResponseType;
        error: ErrorType | null;
      };

      if (error) {
        throw new Error(error.details);
      }

      return data as SubscriptionResponseType;
    } catch (err) {
      console.error("Error:", err);
      throw err;
    }
  };
  const getCourseListForPlan = async (
    formData: SubscriptionRequestType,
  ): Promise<SubscriptionCourse> => {
    try {
      const { data, error } = (await supabase.rpc<string, null>(
        rpc.getCourseListForPlanMapping,
        formData,
      )) as {
        data: SubscriptionCourse;
        error: ErrorType | null;
      };

      if (error) {
        throw new Error(error.details);
      }

      return data as SubscriptionCourse;
    } catch (err) {
      console.error("Error:", err);
      throw err;
    }
  };
  const updateCourse = async (
    formData: SubscriptionRequestType,
  ): Promise<SubscriptionCourse> => {
    try {
      const { data, error } = (await supabase.rpc<string, null>(
        rpc.updateCoursesForPlan,
        formData,
      )) as {
        data: SubscriptionCourse;
        error: ErrorType | null;
      };

      if (error) {
        throw new Error(error.details);
      }

      return data as SubscriptionCourse;
    } catch (err) {
      console.error("Error:", err);
      throw err;
    }
  };
  const getResoureceList = async (
    formData: SubscriptionRequestType,
  ): Promise<SubscriptionResource> => {
    try {
      const { data, error } = (await supabase.rpc<string, null>(
        rpc.getResourceListForPlanMapping,
        formData,
      )) as {
        data: SubscriptionResource;
        error: ErrorType | null;
      };

      if (error) {
        throw new Error(error.details);
      }

      return data as SubscriptionResource;
    } catch (err) {
      console.error("Error:", err);
      throw err;
    }
  };
  const updateResource = async (
    formData: SubscriptionRequestType,
  ): Promise<SubscriptionCourse> => {
    try {
      const { data, error } = (await supabase.rpc<string, null>(
        rpc.updateResourcesForPlan,
        formData,
      )) as {
        data: SubscriptionCourse;
        error: ErrorType | null;
      };

      if (error) {
        throw new Error(error.details);
      }

      return data as SubscriptionCourse;
    } catch (err) {
      console.error("Error:", err);
      throw err;
    }
  };

  const getPendingSubscriptions = async (
    params?: PendingSubscriptionRequest,
  ): Promise<PendingSubscriptionResponse> => {
    try {
      const { data, error } = (await supabase.rpc<string, null>(
        privilegeData.Subscription_Plans.getPendingUserListForPlan,
        params,
      )) as {
        data: PendingSubscriptionResponse;
        error: ErrorType | null;
      };

      if (error) {
        throw new Error(error.details);
      }

      return data as PendingSubscriptionResponse;
    } catch (err) {
      console.error("Error:", err);
      throw err;
    }
  };

  const addPendingUsersToPlan = async (
    params: AddPendingSubscriptions,
  ): Promise<AddPendingSubscriptionsResponse> => {
    try {
      const { data, error } = (await supabase.rpc<string, null>(
        privilegeData.Subscription_Plans.addPendingUserListToPlan,
        params,
      )) as {
        data: AddPendingSubscriptionsResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as AddPendingSubscriptionsResponse;
    } catch (err) {
      console.error("Error:", err);
      throw err;
    }
  };
  const getSubscribedUsers = async (
    params?: PendingSubscriptionRequest,
  ): Promise<UserPlanList> => {
    try {
      const { data, error } = (await supabase.rpc<string, null>(
        rpc.subscribedUsersList,
        params,
      )) as {
        data: UserPlanList;
        error: ErrorType | null;
      };

      if (error) {
        throw error;
      }

      return data as UserPlanList;
    } catch (err) {
      console.error("Error:", err);
      throw err;
    }
  };
  async function deleteSubscription(
    subscriptionData: DeleteMembershipRequest,
  ): Promise<MembershipResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.deleteSubscriptionPlan,
        subscriptionData,
      )) as {
        data: MembershipResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as MembershipResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function approveSubscription(
    subscriptionData: ApproveMembershipRequest,
  ): Promise<ApproveMembershipResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.approveSubscriptionPlan,
        subscriptionData,
      )) as {
        data: ApproveMembershipResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as ApproveMembershipResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function addToSubscription(
    params: AddUserToSubscriptionRequest,
  ): Promise<AddUserResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.addUsersToSubscription,
        params,
      )) as {
        data: AddUserResponse;
        error: ErrorType | null;
      };

      if (error) {
        throw new Error(error.details);
      }
      return data as AddUserResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function extendValidity(
    params: ExtendPlanValidityRequest,
  ): Promise<AddUserResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.extendPlanValidity,
        params,
      )) as {
        data: AddUserResponse;
        error: ErrorType | null;
      };

      if (error) {
        throw new Error(error.details);
      }
      return data as AddUserResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function updatePurchaseRequest(
    params: UpdatePurchaseRequest,
  ): Promise<SuccessMessage> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.updatePurchaseRequest,
        params,
      )) as {
        data: SuccessMessage;
        error: ErrorType | null;
      };

      if (error) {
        throw new Error(error.details);
      }
      return data as SuccessMessage;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  return {
    updateCourse,
    getSubscriptionList,
    getSubscriptionListForAdmin,
    getListDetailsById,
    addSubscription,
    updateSubscription,
    getCourseListForPlan,
    getResoureceList,
    updateResource,
    getPendingSubscriptions,
    addPendingUsersToPlan,
    getSubscribedUsers,
    deleteSubscription,
    approveSubscription,
    getSubscriptionUserDelete,
    addToSubscription,
    extendValidity,
    updatePurchaseRequest,
  };
};

export default useSubscription;
