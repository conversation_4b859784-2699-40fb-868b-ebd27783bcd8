"use client";
import React, { useState, useEffect } from "react";
import { getColumns } from "./columns";
import { DataTable } from "../../components/ui/data-table/data-table";
import MainLayout from "../layout/mainlayout";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import type { ErrorCatch, InnerItem, UsersDataType } from "@/types";
import useUsers from "@/hooks/useUsers";
import { Combobox } from "@/components/ui/combobox";
import { Modal } from "@/components/ui/modal";
import UserAdd from "@/components/addUsers/addUsers";
import { useToast } from "@/components/ui/use-toast";
import type { ToastType } from "../../types";
import { Spinner } from "@/components/ui/progressiveLoader";
import {
  List,
  PlusIcon,
  // ShieldAlert,
  UserCog,
} from "lucide-react";
import getPrivilegeList from "@/hooks/useCheckPrivilege";
import { ORG_KEY, privilegeData, UserFilter } from "@/lib/constants";
// import DeactivateUser from "./deactivate-user";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumb";
import EnrolledCourses from "./enrolledCourses";
import ChangeUserRole from "./changeUserRole";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useTranslation } from "react-i18next";
import { ERROR_MESSAGES } from "@/lib/messages";

export default function UsersListPage(): React.JSX.Element {
  const { t } = useTranslation();
  const columns = getColumns(t);
  const [roleData, setRoles] = useState<{ value: string; label: string }[]>([]);
  const [users, setUsers] = useState<UsersDataType[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<UsersDataType[]>([]);
  const [passData, setPassData] = React.useState<UsersDataType>();
  const { getRoles, getUsers } = useUsers();
  const [isDialogOpen, setIsDialogOpen] = React.useState(false);
  const [isLoading, setIsLoading] = React.useState<boolean>(true);
  const { toast } = useToast() as ToastType;
  const [disableBtn, setDisableBtn] = useState<boolean>(false);
  // const [activateOpen, setActivateOpen] = React.useState<boolean>(false);
  const [isOpenCourses, setIsOpenCourses] = React.useState<boolean>(false);
  // const [title, setTitle] = useState("");
  const [selectedRole, setSelectedRole] = useState<string>("");
  const [filter, setFilter] = useState<string>("");
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);
  const openDialog = (): void => {
    setIsDialogOpen(true);
  };
  const [isOpenRoleModal, setIsOpenRoleModal] = React.useState<boolean>(false);

  const closeDialog = (value: boolean): void => {
    console.log("value", value);
    setIsDialogOpen(false);
  };
  useEffect(() => {
    setBreadcrumbItems(
      getBreadCrumbItems(t, t("breadcrumb.users"), { "": "" }),
    );
  }, [t]);

  useEffect(() => {
    const fetchRolesAndUsers = async (): Promise<void> => {
      try {
        const org_id = localStorage.getItem(ORG_KEY) as string;
        const roles = await getRoles(org_id);
        if (roles !== null && roles.length > 0) {
          const filteredRoles = roles.map((role) => ({
            value: role.id,
            label: role.display_name ?? role.name,
          }));
          setRoles(filteredRoles);
        } else {
          setRoles([]);
        }
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
      }
    };

    fetchRolesAndUsers().catch((error) => console.log(error));
    usersList(true);
    const canPerformAction = getPrivilegeList(
      "User",
      privilegeData.User.addUser,
    );
    setDisableBtn(canPerformAction);
    setFilter(UserFilter[0].label as string);
  }, []);

  const usersList = (value: boolean): void => {
    console.log("value", value);
    const fetchData = async (): Promise<void> => {
      setIsLoading(true);
      try {
        const org_id = localStorage.getItem("orgId") as string;
        const fetchedUsers = await getUsers(org_id);
        setIsLoading(false);
        if (fetchedUsers.length > 0) {
          setUsers(fetchedUsers);
          setFilteredUsers(fetchedUsers);
        } else {
          setUsers([]);
          setFilteredUsers([]);
        }
        fetchedUsers.map((item) => {
          if (item.status === "Inactive") {
            item.hideViews = true;
          } else {
            item.hideViews = false;
          }
        });
        fetchedUsers.map((item) => {
          if (item.roles.includes("Admin")) {
            item.hideRoleChange = true;
          } else {
            item.hideRoleChange = false;
          }
        });
      } catch (error) {
        setIsLoading(false);
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
      }
    };
    fetchData().catch((error) => console.log(error));
  };

  const handleRoleChange = (selectedOption: string): void => {
    const setRole = roleData.find((role) => role.value === selectedOption);
    if (setRole) {
      const selectedRoleLabel = setRole.label;
      setSelectedRole(selectedRoleLabel);
    }
  };

  const handleFilterChange = (selectedOption: string): void => {
    const setStatus = UserFilter.find(
      (filter) => filter.value === selectedOption,
    );
    if (setStatus) {
      const selectedFilterLabel = setStatus.label;
      setFilter(selectedFilterLabel);
    }
  };

  useEffect(() => {
    let filtered = users;
    if (selectedRole !== "") {
      filtered = filtered.filter((user) => user.roles?.includes(selectedRole));
    }
    if (filter !== "All") {
      filtered = filtered.filter((user) => user.status === filter);
    }
    setFilteredUsers(filtered);
  }, [selectedRole, filter]);

  const handleChangeUserRole = (val: UsersDataType): void => {
    setIsOpenRoleModal(true);
    setPassData(val);
  };

  // const handleDeactivateUser = (val: UsersDataType): void => {
  //   if (val.status === "Active") {
  //     setTitle("Deactivate User");
  //   } else {
  //     setTitle("Activate User");
  //   }
  //   setActivateOpen(true);
  //   setPassData(val);
  // };
  const handleEnrolledCourses = (val: UsersDataType): void => {
    console.log("val", val);
    setIsOpenCourses(true);
    setPassData(val);
  };
  // const onDeativateUser = (): void => {
  //   usersList(true);
  // };
  // const cancelUpdate = (): void => {
  //   setActivateOpen(false);
  // };
  const closeCoruseList = (): void => {
    setIsOpenCourses(false);
  };
  const closeRoleModal = (): void => {
    setIsOpenRoleModal(false);
  };

  return (
    <MainLayout>
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      <div>
        <span>
          <div className="text-2xl font-semibold tracking-tight">
            <h1 className="mb-4">
              {t("usersList.title")}
              {disableBtn && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        onClick={() => openDialog()}
                        className="bg-ring bg-[#fb8500] hover:bg-[#fb5c00] py-2 md:py-3 px-4 md:px-6 whitespace-nowrap ml-2"
                      >
                        <PlusIcon className="h-4 w-4 md:h-5 md:w-5" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{t("usersList.addUser")}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </h1>
          </div>
        </span>
        <div className="border rounded-md p-4 bg-[#fff]">
          <div className="flex items-center justify-between">
            <div className="flex space-x-4 w-full sm:w-1/2 md:w-1/4">
              <div className="flex-1">
                <Label>{t("usersList.selectRole")}</Label>
                <div className="w-full">
                  <Combobox data={roleData} onSelectChange={handleRoleChange} />
                </div>
              </div>
              <div className="flex-1">
                <Label>{t("usersList.status")}</Label>
                <div className="w-full">
                  <Combobox
                    data={UserFilter}
                    onSelectChange={handleFilterChange}
                    defaultLabel={filter}
                  />
                </div>
              </div>
            </div>

            {/* {disableBtn && (
              <div className="flex">
                <Button
                  onClick={() => openDialog()}
                  className="bg-[#fb8500] hover:bg-[#fb5c00] flex items-center"
                >
                  <PlusIcon className="h-5 w-5" /> Add User
                </Button>
              </div>
            )} */}
          </div>

          {isLoading ? (
            <Spinner />
          ) : (
            <div>
              <DataTable
                columns={columns}
                data={filteredUsers} // Display the filtered users
                FilterLabel={t("usersList.filterByFirstName")}
                FilterBy={"first_name"}
                disableActiveUsers={"hideViews"}
                hideAdminUsers={"hideRoleChange"}
                actions={[
                  {
                    title: t("usersList.changeRole"),
                    icon: UserCog,
                    varient: "icon",
                    color: "#9bbb5c",
                    handleClick: (val: unknown) =>
                      handleChangeUserRole(val as UsersDataType),
                  },
                  // {
                  //   title: "Deactivate/Activate",
                  //   icon: ShieldAlert,
                  //   varient: "icon",
                  //   color: "#9bbb5c",
                  //   handleClick: (val: unknown) =>
                  //     handleDeactivateUser(val as UsersDataType),
                  // },
                  {
                    title: t("usersList.enrolledCourses"),
                    icon: List,
                    varient: "icon",
                    color: "#9bbb5c",
                    handleClick: (val: unknown) =>
                      handleEnrolledCourses(val as UsersDataType),
                  },
                ]}
              />
            </div>
          )}
        </div>

        {isDialogOpen && (
          <Modal
            title={t("usersList.addNewUser")}
            header=""
            openDialog={isDialogOpen}
            closeDialog={() => closeDialog(true)}
            type="max-w-xl"
          >
            <UserAdd
              closeDialog={(value: boolean) => closeDialog(value)}
              usersList={(value: boolean) => usersList(value)}
              allUsers={filteredUsers}
            />
          </Modal>
        )}
        {isOpenCourses && (
          <Modal
            title={t("usersList.userEnrolledCourses")}
            header=""
            openDialog={isOpenCourses}
            closeDialog={() => setIsOpenCourses(false)}
            type="max-w-5xl"
          >
            <EnrolledCourses
              onCancel={closeCoruseList}
              userData={passData as UsersDataType}
            />
          </Modal>
        )}
        {/* {activateOpen && (
          <Modal
            title={title}
            header=""
            openDialog={activateOpen}
            closeDialog={() => setActivateOpen(false)}
          >
            <DeactivateUser
              userData={passData as UsersDataType}
              onCancel={cancelUpdate}
              onSave={onDeativateUser}
            />
          </Modal>
        )} */}
        {isOpenRoleModal && (
          <Modal
            title={t("usersList.changeUserRole.title")}
            header=""
            openDialog={isOpenRoleModal}
            closeDialog={() => setIsOpenRoleModal(false)}
          >
            <ChangeUserRole
              userData={passData as UsersDataType}
              onCancel={closeRoleModal}
              usersList={(value: boolean) => usersList(value)}
            />
          </Modal>
        )}
      </div>
    </MainLayout>
  );
}
