import React from "react";
import type {
  DeleteSectionRequest,
  ErrorCatch,
  LogUserActivityRequest,
  ToastType,
} from "@/types";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { ORG_KEY } from "@/lib/constants";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import useCourse from "@/hooks/useCourse";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";
export default function DeleteSection({
  sectionId,
  courseId,
  onCancel,
}: {
  onCancel: () => void;
  sectionId: string;
  courseId: string;
}): React.JSX.Element {
  const { toast } = useToast() as ToastType;
  const { deleteSection } = useCourse();
  const { updateUserActivity } = useLogUserActivity();
  const { t } = useTranslation();
  const handleDeleteClick = (): void => {
    void deleteSections();
  };

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  const deleteSections = async (): Promise<void> => {
    const orgId = localStorage.getItem(ORG_KEY);
    const reqParams: DeleteSectionRequest = {
      course_id: courseId as string,
      org_id: orgId as string,
      section_id: sectionId,
    };
    try {
      const response = await deleteSection(reqParams);
      if (response.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.delete_section_title"),
          description: t("successMessages.delete_section"),
        });
        const params = {
          activity_type: "Course",
          screen_name: "Course",
          action_details: "Course section deleted successfully",
          target_id: courseId as string,
          log_result: "SUCCESS",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
        onCancel();
      } else {
        const params = {
          activity_type: "Course",
          screen_name: "Course",
          action_details: "Failed to delete course section",
          target_id: courseId as string,
          log_result: "ERROR",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
    }
  };
  return (
    <>
      <div className="mb-2 mr-4">
        <p className="ml-0 ">
          {String(t("courses.courseModule.deletePrompt"))}
        </p>
      </div>
      <div className="flex topic-icons pr-4">
        <div className="flex items-center justify-center float-right gap-3">
          <Button type="button" onClick={onCancel} variant="outline">
            {String(t("buttons.cancel"))}
          </Button>

          <Button
            type="submit"
            className="bg-[#33363F]"
            onClick={handleDeleteClick}
          >
            {String(t("buttons.delete"))}
          </Button>
        </div>
      </div>
    </>
  );
}
