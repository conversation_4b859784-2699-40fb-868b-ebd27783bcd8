" use client";

import type {
  // Column,
  ColumnDef,
} from "@tanstack/react-table";
// import { ArrowUpDown } from "lucide-react";
// import { Button } from "@/components/ui/button";
import type {
  GetPrivilegeListResponse,
  // AccessPrivilegeColumnDefinition,
  // AccessPrivilegeRowDefinition,
} from "@/types";

export const getColumns = (
  t: (key: string) => string,
): ColumnDef<GetPrivilegeListResponse>[] => [
  {
    accessorKey: "name",
    header: t("accessPrivileges.name"),
    // header: ({
    //   column,
    // }: {
    //   column: Column<GetPrivilegeListResponse, unknown>;
    // }): React.JSX.Element => {
    //   return (
    //     <Button
    //       className="px-0"
    //       variant="ghost"
    //       onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
    //     >
    //       Name
    //       <ArrowUpDown className="ml-2 h-4 w-4" />
    //     </Button>
    //   );
    // },
    cell: ({ row }) => <div className="text-align">{row.original.name}</div>,
  },
  {
    accessorKey: "privilege_key",
    header: t("accessPrivileges.key"),
    cell: ({ row }) => (
      <div className="text-align">{row.original.privilege_key}</div>
    ),
  },
  {
    accessorKey: "privilege_description",
    header: t("accessPrivileges.description"),
    cell: ({ row }) => (
      <div className="text-align">{row.original.privilege_description}</div>
    ),
  },

  // {
  //   accessorKey: "particulars",
  //   header: "Particulars",
  //   cell: ({ row }) => (
  //     <div className="text-align">{row.original.privilege_description}</div>
  //   ),
  // },
];
