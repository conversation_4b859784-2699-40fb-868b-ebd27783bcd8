"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { ExamDetailsType } from "@/types";

export const getColumns = (
  t: (key: string) => string,
): ColumnDef<ExamDetailsType>[] => [
  {
    accessorKey: "name",
    header: t("exams.questions"),
  },
  {
    accessorKey: "default_mark",
    header: t("exams.defaultMark"),
  },
  {
    accessorKey: "penalty",
    header: t("exams.penalty_"),
  },
];
