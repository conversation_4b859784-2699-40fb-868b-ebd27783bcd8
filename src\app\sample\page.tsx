"use client";

import React from "react";
import Stepper from "@/components/ui/Stepper";
import About from "../about/page";
const Sample: React.FC = () => {
  const stepperData = {
    stepNo: 10,
    StepLabel: "Question",
    stepValue: [
      {
        value: "Multiple Choice",
        label: "Multiple Choice",
        completed: true,
      },
    ],
  };
  return (
    <div>
      <main>
        <Stepper
          stepperData={stepperData}
          component={<About />}
          isVertical={false}
          onStepChange={(step: number) => console.log("step", step)}
          stepLabel={[]}
        />
      </main>
    </div>
  );
};
export default Sample;
