import { supabase } from "@/lib/client";
import { rpc } from "@/lib/apiConfig";
import type {
  APIResponse,
  CustomDashboardData,
  DeleteCustomResource,
  ErrorType,
  UpdateCustomResource,
} from "@/types";

interface UseCustomDashboardReturn {
  getCustomDashboard: (
    params: CustomDashboardData,
  ) => Promise<UpdateCustomResource>;
  updateResources: (params: UpdateCustomResource) => Promise<APIResponse>;
  deleteResources: (params: DeleteCustomResource) => Promise<APIResponse>;
}

const useCustomDashboard = (): UseCustomDashboardReturn => {
  async function getCustomDashboard(
    params: CustomDashboardData,
  ): Promise<UpdateCustomResource> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.getDashboardData,
        params,
      )) as { data: UpdateCustomResource; error: ErrorType | null };
      if (error) {
        throw new Error(error.details);
      }
      return data as UpdateCustomResource;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function updateResources(
    params: UpdateCustomResource,
  ): Promise<APIResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.updateDashboardConfig,
        params,
      )) as { data: APIResponse; error: ErrorType | null };
      if (error) {
        throw new Error(error.details);
      }
      return data as APIResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function deleteResources(
    params: DeleteCustomResource,
  ): Promise<APIResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.removeDashboardResource,
        params,
      )) as { data: APIResponse; error: ErrorType | null };
      if (error) {
        throw new Error(error.details);
      }
      return data as APIResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  return {
    getCustomDashboard,
    updateResources,
    deleteResources,
  };
};

export default useCustomDashboard;
