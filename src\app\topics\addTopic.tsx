"use client";
import React, { useEffect } from "react";
import { Folder } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { Save, XCircle } from "lucide-react";
import useTopic from "@/hooks/useTopics";
import type {
  ToastType,
  updateTopicForm,
  ErrorType,
  ErrorCatch,
  LogUserActivityRequest,
} from "@/types";
import { type TreeDataItem } from "../../components/ui/tree";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { UpdateTopicschema } from "@/schema/schema";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { pageUrl } from "@/lib/constants";
import { Textarea } from "@/components/ui/textarea";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import type { TreeTableItem } from "@/types";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";

export default function AddTopic({
  onSave,
  onCancel,
  newAddedItem,
  isModal,
  passData,
  title,
}: {
  onSave: () => void;
  newAddedItem?: TreeDataItem;
  onCancel: () => void;
  passData?: TreeTableItem;
  isModal?: boolean;
  title?: string;
}): React.JSX.Element {
  const { t } = useTranslation();
  const orgId = localStorage.getItem("orgId") as string;
  const { addCategory, getCategoryHierarchy } = useTopic();
  const handleSaveClick = (): void => {
    void handleToastSave();
  };
  const form = useForm<updateTopicForm>({
    resolver: zodResolver(UpdateTopicschema),
  });
  const handleCancelClick = (): void => {
    handleToastcancel();
    onCancel();
  };
  const { toast } = useToast() as ToastType;
  const router = useRouter();
  const { editCategory } = useTopic();
  const { updateUserActivity } = useLogUserActivity();

  useEffect(() => {
    form.setValue("is_premium", passData?.is_premium as boolean);
    if (title === "Update Category") {
      console.log(newAddedItem);
      form.setValue("name", passData?.label as string);
      form.setValue("description", passData?.description as string);
      form.setValue("is_premium", passData?.is_premium as boolean);
    }
  }, []);

  const handleUpdateClick = async (): Promise<void> => {
    const formData = form.getValues();
    const data = {
      org_id: orgId ?? "",
      category_data: {
        id: passData?.key,
        name: formData.name.trimStart(),
        description: formData.description.trimStart(),
        is_premium: formData.is_premium,
      },
    };
    try {
      const result = await editCategory(data);
      if (result.status === "success") {
        router.push(pageUrl.topics);
        onSave();
        toast({
          variant: SUCCESS_MESSAGES.toast_success_title,
          title: t("successMessages.category_update_title"),
          description: t("successMessages.category_update_msg"),
        });
        const reqParams = {
          activity_type: "Topic",
          screen_name: "Topic",
          action_details: "Topic updated successfully",
          target_id: passData?.key as string,
          log_result: "SUCCESS",
        };
        void updateLogUserActivity(reqParams).catch((error) => {
          console.error(error);
        });
      } else if (result.status === "error") {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: result.status,
        });
        const reqParams = {
          activity_type: "Topic",
          screen_name: "Topic",
          action_details: "Failed to update topic",
          target_id: passData?.key as string,
          log_result: "ERROR",
        };
        void updateLogUserActivity(reqParams).catch((error) => {
          console.error(error);
        });
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err.message,
      });
    }
  };
  const handleToastSave = async (): Promise<void> => {
    const params = {
      org_id: orgId,
      filter_data: 0,
    };

    if (title !== "Update Category") {
      const datas: updateTopicForm = form.getValues();
      const data = {
        org_id: orgId ?? "",
        category_data: {
          name: datas.name.trimStart(),
          description: datas.description.trimStart(),
          is_premium: datas.is_premium,
          parent_id:
            title === "Parent category Information" ? null : passData?.key,
        },
      };
      try {
        const result = await addCategory(data);
        console.log(result);
        router.push(pageUrl.topics);
        onSave();
        if (result.status === "success") {
          toast({
            variant: SUCCESS_MESSAGES.toast_success_title,
            title: t("successMessages.category_add_title"),
            description: t("successMessages.category_add_msg"),
          });
          await getCategoryHierarchy(params);
          const reqParams = {
            activity_type: "Topic",
            screen_name: "Topic",
            action_details: "Topic added successfully",
            target_id: passData?.key as string,
            log_result: "SUCCESS",
          };
          void updateLogUserActivity(reqParams).catch((error) => {
            console.error(error);
          });
        } else if (result.status === "error") {
          toast({
            variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
            description: result.status,
          });
          const reqParams = {
            activity_type: "Topic",
            screen_name: "Topic",
            action_details: "Failed to add topic",
            target_id: passData?.key as string,
            log_result: "ERROR",
          };
          void updateLogUserActivity(reqParams).catch((error) => {
            console.error(error);
          });
        }
      } catch (error) {
        const err = error as ErrorType;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err.message,
        });
      }
    } else {
      void handleUpdateClick();
    }
  };

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  const handleToastcancel = (): void => {
    toast({
      variant: SUCCESS_MESSAGES.toast_success_title,
      title: t("successMessages.category"),
      description: t("successMessages.category_cancelled_msg"),
    });
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    const value = e.target.value;
    const sanitizedValue = value
      .trimStart()
      .replace(/^\p{P}+/u, "")
      .replace(/[^\p{L}\p{M}\p{N}\s&-]/gu, "")
      .replace(/\s{2,}/g, " ")
      .replace(/^[^\p{L}\p{M}\p{N}]|[^\p{L}\p{M}\p{N}\s&-]/gu, "")
      .replace(/\s+$/, (match) =>
        match.length > 1 ? match.slice(0, -1) : match,
      );
    form.setValue("name", sanitizedValue);
  };

  const handleDescriptionChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>,
  ): void => {
    const value = e.target.value;
    const sanitizedValue = value
      .trimStart()
      .replace(/^\p{P}+/u, "")
      .replace(/[^\p{L}\p{M}\p{N}\s\-!@#$%^&*()_+={}[\]:;"'<>,.?/\\|~`]/gu, "")
      .replace(/\s{2,}/g, " ")
      .replace(
        /^[^\p{L}\p{M}\p{N}]|[^\p{L}\p{M}\p{N}\s\-!@#$%^&*()_+={}[\]:;"'<>,.?/\\|~`]/gu,
        "",
      )
      .replace(/\s+$/, (match) =>
        match.length > 1 ? match.slice(0, -1) : match,
      );

    form.setValue("description", sanitizedValue);
  };

  return (
    <Form {...form}>
      <form
        onSubmit={(event) => void form.handleSubmit(handleSaveClick)(event)}
        className="space-y-8"
      >
        <div
          className={
            isModal ?? false
              ? ""
              : "flex items-center bg-gray-200 rounded-md p-2"
          }
        >
          {!(isModal ?? false) && (
            <Folder
              className="h-4 w-4 shrink-0 mr-2 text-accent-foreground/50"
              aria-hidden="true"
            />
          )}

          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                {(isModal ?? false) && (
                  <FormLabel>
                    {String(t("topics.categoryName"))}{" "}
                    <span className="text-red-700">*</span>
                  </FormLabel>
                )}
                <FormControl>
                  <Input
                    placeholder={String(t("topics.name"))}
                    autoComplete="off"
                    {...field}
                    maxLength={30}
                    onChange={handleInputChange}
                  />
                </FormControl>
                <FormDescription></FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                {(isModal ?? false) && (
                  <FormLabel>
                    {String(t("topics.description"))}{" "}
                    <span className="text-red-700">*</span>
                  </FormLabel>
                )}
                <FormControl>
                  <Textarea
                    placeholder={String(t("topics.description"))}
                    autoComplete="off"
                    maxLength={100}
                    {...field}
                    onChange={handleDescriptionChange}
                  />
                </FormControl>
                <FormDescription></FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <div
            className={`flex items-center ${!(isModal ?? false) ? "pl-2" : ""}`}
          >
            <FormField
              name="is_premium"
              control={form.control}
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center">
                    <FormControl>
                      <>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={(value) => {
                            field.onChange(value);
                          }}
                          disabled={
                            passData?.is_premium === true &&
                            title !== "Update Category"
                          }
                        />
                        {/* Added padding to this FormLabel */}
                        <FormLabel className="px-2">
                          {String(t("topics.isPremium"))}
                        </FormLabel>
                      </>
                    </FormControl>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {!(isModal ?? false) ? (
            <div className="flex topic-icons pr-4">
              <div
                onClick={handleSaveClick}
                className={`text-accent-foreground/50 hover:text-accent cursor-pointer ml-4 custom-button
                }`}
              >
                <Save />
              </div>
              <div
                onClick={handleCancelClick}
                className="text-accent-foreground/50 hover:text-accent cursor-pointer ml-4 custom-button"
              >
                <XCircle />
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center float-right space-x-2">
              <Button
                type="button"
                onClick={handleCancelClick}
                className="bg-[#33363F]"
              >
                {String(t("buttons.cancel"))}
              </Button>
              &nbsp;
              <Button type="submit" className="bg-[#9FC089]">
                {String(t("buttons.submit"))}
              </Button>
            </div>
          )}
        </div>
      </form>
    </Form>
  );
}
