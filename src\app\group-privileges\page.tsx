"use client";
import React, { useEffect, useState } from "react";
import "../../styles/main.css";
import MainLayout from "../layout/mainlayout";
import { Label } from "@/components/ui/label";
import { useTranslation } from "react-i18next";
// import { Combobox } from "@/components/ui/combobox";

import type {
  AddPrivilegeToGroupType,
  ErrorCatch,
  InnerItem,
  LogUserActivityRequest,
  // ComboData,
  PrivilegeGroup,
  ToastType,
} from "@/types";
import { DataTable } from "@/components/ui/data-table/data-table";
import { getColumns } from "./column";
import { Button } from "@/components/ui/button";
import useGroups from "@/hooks/useGroups";
import { useToast } from "@/components/ui/use-toast";
import { pageUrl, privilegeData } from "@/lib/constants";
import { useRouter } from "next/navigation";
import { useSearchParams } from "next/navigation";
import ComfirmSubmit from "@/components/ui/confirmationmodal";
import { Modal } from "@/components/ui/modal";
import getPrivilegeList from "@/hooks/useCheckPrivilege";
import { Input } from "@/components/ui/input";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumb";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";

export default function GroupPrivilegesPage(): React.JSX.Element {
  const { t } = useTranslation();
  const columns = getColumns(t);
  const [privilegesData, setPrivilegeData] = useState<PrivilegeGroup[]>([]);
  // const [comboData, setComboData] = useState<ComboData[]>([]);
  const [groupId, setGroupId] = useState<string>();
  const [emptyChoice, setEmptyChoice] = useState<boolean>(false);
  const { getPrivilegeGroups, addPrivilegeToGroup } = useGroups();
  const { toast } = useToast() as ToastType;
  const router = useRouter();
  const searchParams = useSearchParams();
  const defaultGroupId = searchParams.get("group") ?? "";
  const defaultGroupName = searchParams.get("name") ?? "";
  const [disableBtn, setDisableBtn] = useState<boolean>(false);
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);
  const { updateUserActivity } = useLogUserActivity();

  useEffect(() => {
    const fetchPrivilegeData = async (groupId: string): Promise<void> => {
      const orgId = localStorage.getItem("orgId");
      const groupData = {
        org_id: orgId ?? "",
        group_id: groupId,
      };
      try {
        const privileges = await getPrivilegeGroups(groupData);
        if (privileges !== null && privileges !== undefined) {
          const PrivilegeDatas = privileges.privileges_data;
          setPrivilegeData(PrivilegeDatas as PrivilegeGroup[]);
        }
      } catch (error) {
        console.log(t("groups.groupPrivileges.errorFetchingGroups"));
      }
    };

    setBreadcrumbItems(
      getBreadCrumbItems(t, t("breadcrumb.privilegeGroup"), { "": "" }),
    );
    // fetchGroupData().catch((error) => console.log(error));
    fetchPrivilegeData(defaultGroupId as string).catch((error) =>
      console.log(error),
    );
    setGroupId(defaultGroupId);
    const canPerformAction = getPrivilegeList(
      "Group",
      privilegeData.Group.updateGroupPrivilege,
    );
    setDisableBtn(canPerformAction);
  }, [defaultGroupId, t]);

  // const handleGroupChange = (selectedOption: string): void => {
  //   setGroupId(selectedOption);
  //   fetchPrivilegeData(selectedOption as string).catch((error) =>
  //     console.log(error),
  //   );
  // };

  const getAddedItem = (dataIndex: string, isSelect: boolean): void => {
    const index = parseInt(dataIndex);
    if (isSelect === true) {
      privilegesData[index].is_part_of_group = true;
    } else {
      privilegesData[index].is_part_of_group = false;
    }
  };

  const updatePrivilegeData = (): void => {
    const isPartOfGroup = privilegesData?.filter(
      (item) => item.is_part_of_group === true,
    );
    const idList = isPartOfGroup.map((item) => item.id);
    if (idList?.length !== 0) {
      setEmptyChoice(false);
      addPrivilegeToGroups(idList as string[]);
    } else {
      setEmptyChoice(true);
    }
  };

  const onSubmit = (): void => {
    addPrivilegeToGroups([]);
  };

  const closeConfirmation = (): void => {
    setEmptyChoice(false);
  };

  const addPrivilegeToGroups = (idList: string[]): void => {
    const addToGroups = async (): Promise<void> => {
      const orgId = localStorage.getItem("orgId");
      const privilegeGroupData = {
        org_id: orgId ?? "",
        group_id: groupId,
        privilege_ids: idList,
      };
      try {
        const privileges = await addPrivilegeToGroup(
          privilegeGroupData as AddPrivilegeToGroupType,
        );
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.groupsUpdated"),
          description: t("successMessages.successfullyUpdatedGroups"),
        });
        console.log(privileges);
        router.push(pageUrl.teams);
        const params = {
          activity_type: "Group",
          screen_name: "Privilege Group",
          action_details: "Privilege added to group ",
          target_id: groupId as string,
          log_result: "SUCCESS",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
        const params = {
          activity_type: "Group",
          screen_name: "Privilege Group",
          action_details: "Failed to add privilege to group ",
          target_id: groupId as string,
          log_result: "ERROR",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      }
    };
    addToGroups().catch((error) => console.log(error));
  };
  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };
  // const fetchGroupData = async (): Promise<void> => {
  //   try {
  //     const groups = await getGroups();
  //     if (groups !== null && groups !== undefined) {
  //       const comboData: ComboData[] = groups.map((cat) => ({
  //         value: cat.id,
  //         label: cat.name,
  //       }));
  //       // setComboData(comboData);
  //     }
  //   } catch (error) {
  //     console.log("Error fetching groups");
  //   }
  // };

  const addCourseCancel = (): void => {
    router.push(pageUrl.teams);
  };

  return (
    <MainLayout>
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      <h1 className="text-2xl font-semibold tracking-tight pb-4">
        {t("groups.groupPrivileges.title")}
      </h1>
      <div className="border rounded-md p-4 bg-[#fff]">
        <div className="w-full flex flex-wrap justify-between space-x-4">
          <div className="w-full sm:w-1/2 md:w-1/3 lg:w-1/4 xl:w-1/5 2xl:w-1/6 flex">
            <div className="w-full">
              <Label className="block">
                {t("groups.groupPrivileges.selectGroup")}
              </Label>
              <div className="w-full course-width mt-2">
                <Input type="text" value={defaultGroupName} disabled />
                {/* <Combobox
                  data={comboData}
                  onSelectChange={handleGroupChange}
                  defaultLabel={defaultGroupName}
                  isDisabled = {true}
                /> */}
              </div>
            </div>
          </div>
        </div>
        <div>
          {privilegesData?.length > 0 && (
            <DataTable
              columns={columns}
              data={privilegesData as PrivilegeGroup[]}
              isSelectedColumn={"is_part_of_group"}
              FilterLabel={t("groups.groupPrivileges.filterByKey")}
              FilterBy={"privilege_key"}
              onSetCheckedStatus={(index: string, checkedStatus: boolean) => {
                getAddedItem(index as string, checkedStatus as boolean);
              }}
              actions={[]}
            />
          )}
        </div>

        <div className="flex flex-wrap justify-end mt-8">
          <div className="mt-6 flex items-center justify-end gap-x-3">
            <Button
              type="button"
              className="bg-[#33363F]"
              onClick={addCourseCancel}
            >
              {t("buttons.cancel")}
            </Button>
            <Button
              type="submit"
              className="bg-[#9FC089]"
              onClick={updatePrivilegeData}
              disabled={!disableBtn}
            >
              {t("buttons.submit")}
            </Button>
          </div>
        </div>
      </div>
      {emptyChoice && (
        <Modal
          title={""}
          header=""
          openDialog={emptyChoice}
          closeDialog={closeConfirmation}
        >
          <ComfirmSubmit
            onSave={onSubmit}
            onCancel={closeConfirmation}
            isModal={true}
            data={t("groups.groupPrivileges.columns.privilegeName")}
          />
        </Modal>
      )}
    </MainLayout>
  );
}
