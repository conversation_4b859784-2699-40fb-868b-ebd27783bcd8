"use client";
import React, { useState, useEffect } from "react";
import { Combobox } from "@/components/ui/combobox";
import { Input } from "@/components/ui/input";
import { Editor } from "primereact/editor";
import { DateTimePicker } from "@/components/ui/date-time-picker/date-time-picker";
import { useForm } from "react-hook-form";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { zodResolver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import type {
  AddCurrentAffairsSchemaType,
  CurrentAffairsForm,
  AddCurrentAffairs,
  ErrorCatch,
  ToastType,
  UpdateBulletinRequest,
  CurrentAffairsData,
  InnerItem,
  LogUserActivityRequest,
} from "@/types";
import { AddCurrentAffairsSchema } from "@/schema/schema";
import { DEFAULT_FOLDER_ID, months, pageUrl } from "@/lib/constants";
import { parseZonedDateTime } from "@internationalized/date";
import type { DateValue, ZonedDateTime } from "@internationalized/date";
import moment from "moment-timezone";
import useAddCurrentAffairs from "@/hooks/useAddCurrentAffairs";
import useCurrentAffairs from "@/hooks/useCurrentAffairs";
import { useRouter } from "next/navigation";
import { useToast } from "@/components/ui/use-toast";
import NextBreadcrumb from "../breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumb";
import { Spinner } from "../ui/progressiveLoader";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
interface AddBulletinProps {
  mode: string;
}
export default function AddBulletin({
  mode,
}: AddBulletinProps): React.JSX.Element {
  const { t } = useTranslation();
  const form = useForm<AddCurrentAffairsSchemaType>({
    resolver: zodResolver(AddCurrentAffairsSchema),
  });
  const { handleSubmit } = form;
  const { addCurrentAffairs } = useAddCurrentAffairs();
  const { updateBulletin } = useCurrentAffairs();
  const router = useRouter();
  // const searchParams = useSearchParams();
  const bulletinData = localStorage.getItem("currentAffairsData");
  const { toast } = useToast() as ToastType;
  const [publishDate, setPublishDate] = useState<ZonedDateTime | DateValue>();
  const [timezone, setTimezone] = useState("");
  const [content, setContent] = useState("");
  const [monthName, setMonth] = useState("");
  const [isLoading, setIsLoading] = useState<boolean>(true);
  // const [publishVisible, setPublishVisible] = useState<boolean>(false);
  //   const [richTextValues, setRichTextValues] = useState<
  //     richTextType | undefined
  //   >(undefined);
  const [bulletinId, setBulletinId] = useState<string>("");
  const { updateUserActivity } = useLogUserActivity();
  const handleChanage = (dateObject: DateValue): void => {
    const modifiedDateObject = { ...dateObject };
    modifiedDateObject.month = modifiedDateObject.month - 1;
    const setDateTime = parseZonedDateTime(
      moment.tz(modifiedDateObject, timezone).format("YYYY-MM-DDTHH:mm") +
        `[${timezone}]`,
    );
    setPublishDate(setDateTime);
  };
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);

  useEffect(() => {
    setBreadcrumbItems(
      getBreadCrumbItems(t, t("breadcrumb.addCurrentAffairs"), { "": "" }),
    );
  }, [t]);
  useEffect(() => {
    // setPublishVisible(true);
    const currentTimezone = moment.tz.guess();
    setTimezone(currentTimezone);
    if (
      mode === "edit" &&
      (bulletinData != null || bulletinData != undefined)
    ) {
      const parsedData = JSON.parse(bulletinData) as CurrentAffairsData;
      setBulletinId(parsedData.id as string);
      form.setValue("month", parsedData.month as string);
      setMonth(parsedData.month as string);
      form.setValue("title", parsedData?.title);
      form.setValue("content", parsedData?.content);
      setContent(parsedData?.content);
      // setPublishDate(dateTime);
      setIsLoading(false);
    }
    if (mode === "add") {
      const currentDatetime = moment
        .tz(currentTimezone)
        .format("YYYY-MM-DDTHH:mm");
      const dateTime = parseZonedDateTime(
        currentDatetime + `[${currentTimezone}]`,
      );
      form.setValue("publish_date", dateTime);
      setPublishDate(dateTime);
    }
    setIsLoading(false);
  }, [mode, bulletinData]);

  const comboSelectedValue = (res: string): void => {
    form.setValue("month", res);
  };

  const setRichTextValue = (val: string): void => {
    form.setValue("content", val);
    setMonth(val);
  };

  async function onSubmit(data: CurrentAffairsForm): Promise<void> {
    const formData = data;

    const datePublish = data.publish_date;
    const datePublished = new Date(
      datePublish.year,
      datePublish.month - 1,
      datePublish.day,
      datePublish.hour,
      datePublish.minute,
    );
    const momentPublishDate = moment(datePublished);
    const formattedPublishedDate = momentPublishDate.format("YYYY-MM-DD HH:mm");

    const orgId = localStorage.getItem("orgId") as string;
    try {
      let result;
      if (
        mode === "edit" &&
        (bulletinData != null || bulletinData != undefined)
      ) {
        const params: UpdateBulletinRequest = {
          bulletin_data: {
            id: bulletinId,
            course_id: null,
            content: formData.content as string,
            title: formData.title,
            type: "Current_Affairs",
            publish_date: formattedPublishedDate,
            // publish_date: null,
            img_url: "Nothing",
            month: formData.month,
          },
          org_id: orgId,
        };

        result = await updateBulletin(params);
      } else {
        const requestBody: AddCurrentAffairs = {
          course_id: null,
          org_id: orgId,
          bulletin_board: {
            content: formData.content,
            title: formData.title,
            type: "Current_Affairs",
            publish_date: formattedPublishedDate,
            // publish_date: null,
            img_url: "Nothing",
            month: formData.month,
          },
        };
        result = await addCurrentAffairs(requestBody);
      }
      if (result?.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.toast_success_title"),
          description: t("successMessages.currentAffairsAdded"),
        });
        router.push(`${pageUrl.currentAffairs}`);
        const params = {
          activity_type: "Current_Affairs",
          screen_name: "Current Affairs",
          action_details: mode + " Current affairs ",
          target_id: result?.news_id as string,
          log_result: "SUCCESS",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      } else if (result?.status === "error") {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: result.status,
        });
        const params = {
          activity_type: "Current_Affairs",
          screen_name: "Current Affairs",
          action_details: "Failed to " + mode + " Current affairs",
          target_id: DEFAULT_FOLDER_ID,
          log_result: "ERROR",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
      const params = {
        activity_type: "Current_Affairs",
        screen_name: "Current Affairs",
        action_details: "Failed to " + mode + "Current affairs",
        target_id: DEFAULT_FOLDER_ID,
        log_result: "ERROR",
      };
      void updateLogUserActivity(params).catch((error) => {
        console.error(error);
      });
    }
  }
  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };
  return !isLoading ? (
    <div className="w-full">
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      <div className="w-full mb-4">
        {mode === "edit" ? (
          <h1 className="text-2xl font-semibold tracking-tight">
            {t("currentAffairs.updateCurrentAffairs")}
          </h1>
        ) : (
          <h1 className="text-2xl font-semibold tracking-tight">
            {t("currentAffairs.addCurrentAffair")}
          </h1>
        )}
      </div>

      <div className="w-full border rounded-md ps-4 pb-4 pe-4 mb-2 bg-[#fff]">
        <>
          <Form {...form}>
            <form
              onSubmit={(event) => void handleSubmit(onSubmit)(event)}
              className="space-y-8 flex flex-wrap"
            >
              <div className="w-full flex-col flex md:flex-row mt-4">
                <div className="mb-4 md:mr-2 md:w-1/2">
                  <div className="w-full">
                    <FormField
                      name="month"
                      control={form.control}
                      render={() => (
                        <FormItem>
                          <>
                            <FormLabel>
                              {t("currentAffairs.selectMonth")}
                              <span className="text-red-700">*</span>
                            </FormLabel>
                          </>
                          <FormControl>
                            <div className="w-full">
                              <Combobox
                                data={months}
                                onSelectChange={comboSelectedValue}
                                defaultLabel={monthName ?? ""}
                              />
                            </div>
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
                <div className="mb-4 md:ml-2 md:w-1/2">
                  <div className="w-full">
                    <FormField
                      name="title"
                      control={form.control}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            {t("currentAffairs.eventTitle")}
                            <span className="text-red-700">*</span>
                          </FormLabel>
                          <FormControl>
                            <Input
                              type="text"
                              autoComplete="off"
                              {...field}
                              onKeyDown={(e) => {
                                if (
                                  e.target instanceof HTMLInputElement &&
                                  e.key === " " &&
                                  e.target.selectionStart === 0
                                ) {
                                  e.preventDefault();
                                }
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </div>
              <div className="w-full">
                <FormField
                  control={form.control}
                  name="content"
                  render={() => (
                    <FormItem>
                      <FormLabel>
                        {t("currentAffairs.description")}{" "}
                        <span className="text-red-700">*</span>
                      </FormLabel>
                      <FormControl>
                        <Editor
                          value={content}
                          onTextChange={(event) => {
                            const Value = event.htmlValue;
                            setRichTextValue(Value as string);
                          }}
                          style={{ height: "320px" }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              {/* {!publishVisible && ( */}
              <div className="flex flex-col md:flex-row w-full">
                <div className="mb-4 md:mr-2">
                  <FormField
                    control={form.control}
                    name="publish_date"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {" "}
                          {t("currentAffairs.publishedDate")}{" "}
                          <span className="text-red-700">*</span>
                        </FormLabel>
                        <FormControl>
                          <DateTimePicker
                            granularity={"minute"}
                            value={field.value as DateValue}
                            hideTimeZone={true}
                            maxValue={publishDate}
                            onChange={(newDate) => {
                              handleChanage(newDate as DateValue);
                              field.onChange(newDate);
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <></>
              </div>
              {/* )}  */}
              <div className="w-full flex justify-end mt-8 ">
                <div className="w-full flex justify-end gap-3">
                  <Link href={pageUrl.currentAffairs}>
                    <Button className="w-auto bg-[#33363F]">
                      {t("buttons.cancel")}
                    </Button>
                  </Link>
                  <Button className="w-auto bg-[#9FC089]">
                    {mode === "add"
                      ? `${t("buttons.submit")}`
                      : `${t("buttons.update")}`}
                  </Button>
                </div>
              </div>
            </form>
          </Form>
        </>
      </div>
    </div>
  ) : (
    <div className="w-full">
      <Spinner></Spinner>
    </div>
  );
}
