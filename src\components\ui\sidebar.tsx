"use client";
import { getSideMenu } from "@/lib/constants";
import Link from "next/link";
import "@/styles/main.css";
import type { LucideIcon } from "lucide-react";
import Image from "next/image";
import { useEffect, useState } from "react";
import type { RolePrivileges } from "@/types";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { useTranslation } from "react-i18next";

interface menuItemProps {
  MenuIcon: LucideIcon;
  name: string;
  route: string;
  color: string;
}

interface SidebarProps {
  sidebarData: RolePrivileges[];
  onSidebarToggle: (isOpen: boolean) => void;
}

export function Sidebar({
  sidebarData,
  onSidebarToggle,
}: SidebarProps): React.JSX.Element {
  const { i18n, t } = useTranslation();
  // const [sideBarMenu, setSideBarMenu] = useState(getSideMenu(t));
  const [sideBarMenu, setSideBarMenu] = useState(() => getSideMenu(t));
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const pathname = usePathname();
  const isRTL = i18n.language === "ar";

  const MenuItem = ({
    MenuIcon,
    name,
    route,
  }: menuItemProps): React.JSX.Element => {
    const colorClass = "text-black";
    return (
      <div className="relative">
        <Link
          href={route}
          className={cn(
            "flex gap-1 [&>*]:my-auto text-md py-3 border-b-[1px] border-b-white/10 items-center",
            colorClass,
            pathname === route &&
              `before:absolute before:bg-[#2894a0] before:w-[243px] before:h-14 ${
                isRTL ? "before:right-8 before:-top-1" : "before:-left-8 before:-top-1"
              }`,
            isSidebarOpen ? "w-full" : "w-[40px] justify-center",
          )}
        >
          <div className="text-xl text-red-800 flex [&>*]:mx-auto w-[30px] z-10">
            <MenuIcon color={"white"}></MenuIcon>
          </div>
          {/* <div className="text-white z-10">
           */}
          <div className={cn("text-white z-10", !isSidebarOpen && "hidden")}>
            {name}
          </div>
        </Link>
      </div>
    );
  };

  useEffect(() => {
    const updatedMenu = getSideMenu(t).map((item) => ({
      ...item,
      canPerformAction: getPrivilegeList(item.screen, item.action),
    }));
    setSideBarMenu(updatedMenu);
  }, [t, sidebarData]);

  const getPrivilegeList = (screen: string, action: string): boolean => {
    const privilege = sidebarData as RolePrivileges[];
    if (privilege !== null && privilege !== undefined) {
      const privilegeList: RolePrivileges[] = privilege as RolePrivileges[];
      const hasPrivilege = privilegeList?.some(
        (rolePrivilege) =>
          rolePrivilege.screen === screen && rolePrivilege.actions[action],
      );
      return hasPrivilege;
    } else {
      return false;
    }
  };

  const handleSidebarHover = (): void => {
    setIsSidebarOpen(true);
    onSidebarToggle(true);
  };
  const handleSidebarLeave = (): void => {
    setIsSidebarOpen(false);
    onSidebarToggle(false);
  };

  return (
    <div className=" h-full">
      <div
        className={cn(
          "col-span-3 bg-secondary lg:col-span-4 lg:border-r h-full overflow-y-auto custom-scrollbar transition-all duration-300 flex flex-col",
          isSidebarOpen ? "w-56" : "w-16",
          isRTL ? "right-0 border-l" : "left-0 border-r"
        )}
        onMouseEnter={handleSidebarHover}
        onMouseLeave={handleSidebarLeave}
      >
        {/* <div className="col-span-3 bg-secondary lg:col-span-4  lg:border-r h-full w-56 overflow-y-auto custom-scrollbar"> */}

        {/* App Icon Container */}
        {/* <div className="p-2 flex ml-10">
          <Link href="/dashboard">
            <Image
              src="/static/images/smartlearn.png"
              alt="Card Image"
              width={100}
              height={100}
            />
          </Link>
        </div> */}
        <div className="p-2 flex justify-center items-center shrink-0">
          {" "}
          {/* Added justify-center and shrink-0 */}
          <Link href="/dashboard">
            <Image
              src="/static/images/smartlearn.png"
              alt="Card Image"
              width={isSidebarOpen ? 100 : 50} // Adjust width based on state
              height={isSidebarOpen ? 100 : 50} // Adjust height based on state
              className="transition-all duration-300"
            />
          </Link>
        </div>

        {/* Menu Items Container */}
        <div className="space-y-4 py-4 flex-grow">
          {" "}
          {/* Added flex-grow */}
          <div className="px-3 py-2">
            <div className="space-y-1">
              {sideBarMenu.map((item, index) => {
                if (item.canPerformAction === true) {
                  return (
                    <MenuItem
                      MenuIcon={item.icon as LucideIcon}
                      name={item.name}
                      route={item.href}
                      color={item.color}
                      key={index}
                    />
                  );
                }
                return null;
              })}
            </div>
          </div>
        </div>
        {/* <div className="space-y-4 py-4">
          <div className="px-3 py-2">
            <div className="space-y-1">
              {sideBarMenu.map((item, index) => {
                if (item.canPerformAction === true) {
                  return (
                    <MenuItem
                      MenuIcon={item.icon as LucideIcon}
                      name={item.name}
                      route={item.href}
                      color={item.color}
                      key={index}
                    />
                  );
                }
                return null;
              })}
            </div>
          </div>
        </div> */}
      </div>
    </div>
  );
}
