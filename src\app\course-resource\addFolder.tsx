"use client";
import React from "react";
import type {
  AddFolderRequest,
  LogUserActivityRequest,
  ToastType,
  updateFolderForm,
} from "@/types";
import { useToast } from "@/components/ui/use-toast";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { ModalButton } from "@/components/ui/modalButton";
import { Textarea } from "@/components/ui/textarea";
import { UpdateFolderSchema } from "@/schema/schema";
import useCourse from "@/hooks/useCourse";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";

export default function AddFolder({
  onCancel,
  onSave,
  courseId,
  sectionsId,
}: {
  onSave: () => void;
  onCancel: () => void;
  courseId?: string;
  sectionsId?: string;
}): React.JSX.Element {
  const { t } = useTranslation();
  const { toast } = useToast() as ToastType;
  const { addFolder } = useCourse();
  const { updateUserActivity } = useLogUserActivity();
  const form = useForm<updateFolderForm>({
    resolver: zodResolver(UpdateFolderSchema),
  });

  const handleCancelClick = (): void => {
    onCancel();
  };

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  async function handleToastSave(datas: updateFolderForm): Promise<void> {
    const orgId = localStorage.getItem("orgId");
    const folderData = {
      org_id: orgId ?? "",
      folder_data: {
        course_id: courseId,
        section_id: sectionsId,
        folder_name: datas.name,
        description: datas.description,
      },
    };
    try {
      const result = await addFolder(folderData as AddFolderRequest);
      if (result.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_success_title,
          title: t("successMessages.add_folder_title"),
          description: t("successMessages.add_folder_msg"),
        });
        const params = {
          activity_type: "Course",
          screen_name: "Course",
          action_details: "Folder added successfully",
          target_id: courseId as string,
          log_result: "SUCCESS",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
        onSave();
        onCancel();
      } else if (result.status === "error") {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: result.status,
        });
        const params = {
          activity_type: "Course",
          screen_name: "Course",
          action_details: "Failed to add folder",
          target_id: courseId as string,
          log_result: "ERROR",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      }
    } catch (error: unknown) {
      const errMsg: string =
        typeof error === "string"
          ? error
          : error instanceof Error
          ? error.message
          : "Unknown error";
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: errMsg,
      });
    }
  }

  const handleDescriptionChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>,
  ): void => {
    const value = e.target.value;
    const sanitizedValue = value
      .trimStart()
      .replace(/^\p{P}+/u, "")
      .replace(/[^\p{L}\p{M}\p{N}\s\-!@#$%^&*()_+={}[\]:;"'<>,.?/\\|~`]/gu, "")
      .replace(/\s{2,}/g, " ")
      .replace(
        /^[^\p{L}\p{M}\p{N}]|[^\p{L}\p{M}\p{N}\s\-!@#$%^&*()_+={}[\]:;"'<>,.?/\\|~`]/gu,
        "",
      )
      .replace(/\s+$/, (match) =>
        match.length > 1 ? match.slice(0, -1) : match,
      );
    form.setValue("description", sanitizedValue);
  };

  return (
    <Form {...form}>
      <form
        onSubmit={(event) => void form.handleSubmit(handleToastSave)(event)}
        className="space-y-8"
      >
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                {String(t("courses.courseModule.folderName"))}{" "}
                <span className="text-red-700">*</span>
              </FormLabel>
              <FormControl>
                <Input
                  autoComplete="off"
                  placeholder={String(t("courses.courseModule.folderName"))}
                  maxLength={30}
                  {...field}
                />
              </FormControl>
              <FormDescription></FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                {String(t("courses.description"))}{" "}
                <span className="text-red-700">*</span>
              </FormLabel>
              <FormControl>
                <Textarea
                  placeholder={String(t("courses.description"))}
                  autoComplete="off"
                  maxLength={100}
                  {...field}
                  onChange={handleDescriptionChange}
                />
              </FormControl>
              <FormDescription></FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <ModalButton
          closeDialog={handleCancelClick}
          closeLabel={String(t("buttons.cancel"))}
          submitLabel={String(t("buttons.submit"))}
        />
      </form>
    </Form>
  );
}
