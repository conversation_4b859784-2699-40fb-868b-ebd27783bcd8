"use client";
import React, { useState, useEffect, type BaseSyntheticEvent } from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import * as XLSX from "xlsx";
import { Button } from "../ui/button";
import { Spinner } from "@/components/ui/progressiveLoader";
import { DataTable } from "@/components/ui/data-table/data-table";
import { getColumns } from "./columns";
import type {
  ErrorCatch,
  ToastType,
  TopicDataType,
  ExcelImportQuestion,
  ComboData,
  LogUserActivityRequest,
} from "@/types";
import useQuestionBank from "@/hooks/useQuestionBank";
import { useToast } from "@/components/ui/use-toast";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import { Combobox } from "@/components/ui/combobox";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";
// type ExcelData = Record<string, string | number | boolean | null>[];

function QuestionsFromExcel({
  onCancel,
  onSave,
}: {
  onCancel: () => void;
  onSave: (value: boolean) => void;
}): React.JSX.Element {
  const { t } = useTranslation();
  const columns = getColumns(t);
  const customColumnWidths: Record<string, { width: number; align: string }> = {
    Question: { width: 700, align: "left" },
    Quest_Hint: { width: 500, align: "left" },
    Option1: { width: 150, align: "left" },
    Option2: { width: 150, align: "left" },
    Option3: { width: 150, align: "left" },
    Option4: { width: 150, align: "left" },
    Option5: { width: 150, align: "left" },
    Right_Answer: { width: 150, align: "left" },
    Solution1: { width: 150, align: "left" },
    Solution2: { width: 150, align: "left" },
    Solution3: { width: 150, align: "left" },
    Solution4: { width: 150, align: "left" },
    Solution5: { width: 150, align: "left" },
    Best_Solutions1: { width: 150, align: "left" },
    Best_Solutions2: { width: 150, align: "left" },
    Best_Solutions3: { width: 150, align: "left" },
  };

  const [isLoading, setIsLoading] = React.useState<boolean>(false);
  const [questionData, setQuestionData] = React.useState<ExcelImportQuestion[]>(
    [],
  );
  const [comboData, setComboData] = useState<ComboData[]>([]);

  const [selectedCategory, setCategoryId] = React.useState<string | null>();
  const [isEmptyData, setIsEmptyData] = useState(false);

  const { getPublishedQuestionCategory, addExcelQuestions } = useQuestionBank();
  const { updateUserActivity } = useLogUserActivity();
  const { toast } = useToast() as ToastType;

  useEffect(() => {
    const fetchData = async (): Promise<void> => {
      try {
        const category: TopicDataType[] = await getPublishedQuestionCategory();
        const categoryData: ComboData[] = category.map((cat) => ({
          value: cat.value,
          label: cat.label,
        }));

        setComboData(categoryData);
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
      }
    };
    fetchData().catch((error) => console.log(error));
  }, []);

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>): void => {
    const file = e.target.files?.[0];
    if (!file) return;
    setIsLoading(true);
    const reader = new FileReader();
    interface ExcelRow {
      // "Sl. No.": number;
      Question: string;
      Quest_Hint: string;
      Option1: string;
      Option2: string;
      Option3: string;
      Option4: string;
      Option5: string;
      Solution1: string;
      Solution2: string;
      Solution3: string;
      Solution4: string;
      Solution5: string;
      Best_Solutions1: string;
      Best_Solutions2: string;
      Best_Solutions3: string;
      Right_Answer: string[];
    }
    reader.onload = (event) => {
      try {
        const workbook = XLSX.read(event.target?.result, { type: "array" });
        const sheetName = workbook.SheetNames[0];
        const sheet = workbook.Sheets[sheetName];

        // Get headers from first row
        const headers = XLSX.utils.sheet_to_json(sheet, {
          header: 1, // Get rows as arrays, first row will be headers
        })[0] as string[] | undefined;

        const expectedHeaders = [
          "QID",
          "Question",
          "Quest_Hint",
          "Option1",
          "Option2",
          "Option3",
          "Option4",
          "Option5",
          "Right_Answer",
          "Solution1",
          "Solution2",
          "Solution3",
          "Solution4",
          "Solution5",
          "Best_Solutions1",
          "Best_Solutions2",
          "Best_Solutions3",
        ];

        // Basic validation
        const isValidFormat =
          Array.isArray(headers) &&
          expectedHeaders.every((expectedHeader) =>
            headers.some(
              (header) =>
                typeof header === "string" &&
                header.trim().toLowerCase() === expectedHeader.toLowerCase(),
            ),
          );

        if (!isValidFormat) {
          setIsEmptyData(true);
          setQuestionData([]);
          toast({
            variant: ERROR_MESSAGES.toast_variant_destructive,
            title: t("errorMessages.toast_error_title"),
            description: t("errorMessages.excel_format_invalid"),
          });
          return;
        }

        // Parse valid data
        const sheetData = XLSX.utils.sheet_to_json<ExcelRow>(sheet);
        if (sheetData.length === 0) {
          setIsEmptyData(true);
          setQuestionData([]);
          toast({
            variant: ERROR_MESSAGES.toast_variant_destructive,
            title: t("errorMessages.toast_error_title"),
            description: t("errorMessages.empty_excel"),
          });
          return;
        }

        const validData = sheetData.filter((row) => row.Question?.trim());
        if (validData.length === 0) {
          setIsEmptyData(true);
          setQuestionData([]);
          toast({
            variant: ERROR_MESSAGES.toast_variant_destructive,
            title: t("errorMessages.toast_error_title"),
            description: t("errorMessages.no_valid_questions"),
          });
          return;
        }

        setIsEmptyData(false);
        const transformedData: ExcelImportQuestion[] = validData.map((row) => ({
          ques_category_id: "",
          input_data: {
            questions: [
              {
                question_text: row["Question"] ?? "",
                question_hint: row["Quest_Hint"] ?? "",
                options: {
                  option1: row["Option1"] ?? "",
                  option2: row["Option2"] ?? "",
                  option3: row["Option3"] ?? "",
                  option4: row["Option4"] ?? "",
                  option5: row["Option5"] ?? "",
                },
                right_answer: String(row["Right_Answer"] ?? "")
                  .split(/[\n,;]+/)
                  .map((ans) => ans.trim())
                  .filter((ans) => ans !== ""),
                // .map((ans) => `option${ans}`),
                solutions: {
                  solution1: row["Solution1"] ?? "",
                  solution2: row["Solution2"] ?? "",
                  solution3: row["Solution3"] ?? "",
                  solution4: row["Solution4"] ?? "",
                  solution5: row["Solution5"] ?? "",
                },
                best_solutions: {
                  best_solution1: row["Best_Solutions1"] ?? "",
                  best_solution2: row["Best_Solutions2"] ?? "",
                  best_solution3: row["Best_Solutions3"] ?? "",
                },
              },
            ],
          },
          isQuestionAdded: false,
        }));

        setQuestionData(transformedData);
      } catch (error) {
        console.error("Error processing file:", error);
        setIsEmptyData(true);
        setQuestionData([]);
      } finally {
        setIsLoading(false);
      }
    };

    reader.onerror = () => {
      console.error("Error reading file");
      setIsLoading(false);
    };
    // reader.readAsBinaryString(file);
    reader.readAsArrayBuffer(file);
  };

  const getAddedItem = (dataIndex: string, isSelect: boolean): void => {
    const index = parseInt(dataIndex);
    if (isSelect === true) {
      questionData[index].isQuestionAdded = true;
    } else {
      questionData[index].isQuestionAdded = false;
    }
  };

  const submitSelectQuestions = async (
    event: BaseSyntheticEvent,
  ): Promise<void> => {
    console.log(event);

    if (selectedCategory === null || selectedCategory === undefined) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.selectQuestionCategory"),
      });
      return;
    } else if (
      questionData?.length === 0 ||
      questionData?.every((item) => item.isQuestionAdded === false)
    ) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.select_atleast_one_question"),
      });
      return;
    } else {
      const addedQuestions = questionData?.filter(
        (item) => item.isQuestionAdded === true,
      );
      const orgId = localStorage.getItem("orgId");
      let params;
      if (addedQuestions?.length !== 0) {
        params = {
          org_id: orgId ?? "",
          ques_category_id: selectedCategory ?? "",
          input_data: {
            questions: addedQuestions.flatMap(
              (item) => item.input_data.questions,
            ),
          },
        };
      }
      try {
        const response = await addExcelQuestions(params as ExcelImportQuestion);
        if (response.status === "success") {
          toast({
            variant: SUCCESS_MESSAGES.toast_variant_default,
            title: t("errorMessages.toast_success_title"),
            description: t("errorMessages.questions_added"),
          });
          onSave(true);
          const params = {
            activity_type: "Question_Bank",
            screen_name: "Question_Bank",
            action_details: "Successfully import questions from Excel",
            target_id: selectedCategory as string,
            log_result: "SUCCESS",
          };
          void updateLogUserActivity(params).catch((error) => {
            console.error(error);
          });
          onCancel();
        } else if (response.status === "error") {
          toast({
            variant: ERROR_MESSAGES.toast_variant_destructive,
            title: t("errorMessages.toast_success_title"),
            description: response.message,
          });
          const params = {
            activity_type: "Question_Bank",
            screen_name: "Question_Bank",
            action_details: "Failed to import questions from Excel",
            target_id: selectedCategory as string,
            log_result: "ERROR",
          };
          void updateLogUserActivity(params).catch((error) => {
            console.error(error);
          });
        }
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_success_title"),
          description: err?.message,
        });
      }
    }
  };

  const handleComboValueChange = (selectedValue: string): void => {
    setCategoryId(selectedValue);
  };

  return (
    <>
      <div className="flex flex-col md:flex-row justify-between">
        <div className=" w-full border rounded-md p-4">
          <div className="w-full flex space-x-4">
            <div className="min-w-[370px]">
              <Label>
                {t("questionBank.selectFile")}
                <span className="text-red-700">*</span>
              </Label>
              <Input
                type="file"
                autoComplete="off"
                accept=".xlsx, .xls, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
                onChange={handleFileUpload}
              />
            </div>
            <div className="min-w-[370px]">
              <Label>
                {t("questionBank.selectQuestionCategory")}
                <span className="text-red-700">*</span>
              </Label>
              <Combobox
                data={comboData}
                onSelectChange={handleComboValueChange}
                defaultLabel={""}
              />
            </div>
          </div>
          {isLoading ? (
            <Spinner />
          ) : (
            <div>
              {isEmptyData === false && questionData.length > 0 ? (
                <DataTable
                  columns={columns}
                  data={questionData}
                  isSelectedColumn={"isQuestionAdded"}
                  FilterLabel={t("questionBank.filterByQuestion")}
                  FilterBy={"question_text"}
                  actions={[]}
                  onSetCheckedStatus={(
                    index: string,
                    checkedStatus: boolean,
                  ) => {
                    getAddedItem(index as string, checkedStatus as boolean);
                  }}
                  customColumnWidths={customColumnWidths}
                />
              ) : (
                <DataTable
                  columns={columns}
                  data={[]}
                  FilterLabel={t("questionBank.filterByQuestion")}
                  FilterBy={"question_text"}
                  actions={[]}
                />
              )}
            </div>
          )}
        </div>
      </div>
      <div className="flex flex-wrap justify-end mt-6 space-x-2">
        <div>
          <Button className="bg-[#33363F]" onClick={onCancel}>
            {t("buttons.cancel")}
          </Button>
        </div>
        <div>
          <Button
            onClick={(event: BaseSyntheticEvent) => {
              submitSelectQuestions(event).catch((error) => console.log(error));
            }}
            className="bg-[#9FC089]"
            disabled={questionData.length === 0 || selectedCategory === null}
          >
            {t("buttons.submit")}
          </Button>
        </div>
      </div>
    </>
  );
}
export default QuestionsFromExcel;
