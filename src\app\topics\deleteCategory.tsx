import type { BaseSyntheticEvent } from "react";
import React from "react";
import type { <PERSON>rror<PERSON>atch, LogUserActivityRequest, ToastType } from "@/types";
import { Button } from "@/components/ui/button";
import { ORG_KEY } from "@/lib/constants";
import { useToast } from "@/components/ui/use-toast";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import useTopic from "@/hooks/useTopics";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";

export default function DeleteCategory({
  onCancel,
  onSave,
  categoryId,
}: {
  onCancel: () => void;
  onSave: () => void;
  categoryId: string;
}): React.JSX.Element {
  const { toast } = useToast() as ToastType;
  const { t } = useTranslation();
  const { deleteCategory } = useTopic();
  const { updateUserActivity } = useLogUserActivity();

  const handleDeleteClick = async (e: BaseSyntheticEvent): Promise<void> => {
    console.log(e);
    const orgId = localStorage.getItem(ORG_KEY);
    const params = {
      category_id: categoryId,
      org_id: orgId ?? "",
    };
    try {
      const result = await deleteCategory(params);
      if (result.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_success,
          title: t("successMessages.deleteCategoryTitle"),
          description: t("successMessages.deleteCategoryMsg"),
        });

        const reqParams = {
          activity_type: "Topic",
          screen_name: "Topic",
          action_details: "Topic deleted successfully",
          target_id: categoryId,
          log_result: "SUCCESS",
        };
        void updateLogUserActivity(reqParams).catch((error) => {
          console.error(error);
        });
        onSave();
        onCancel();
      } else {
        const reqParams = {
          activity_type: "Topic",
          screen_name: "Topic",
          action_details: "Failed to delete topic",
          target_id: categoryId,
          log_result: "ERROR",
        };
        void updateLogUserActivity(reqParams).catch((error) => {
          console.error(error);
        });
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
    }
  };

  const handleCancel = (): void => {
    onCancel();
  };

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  return (
    <>
      <div className="mb-2 mr-4">
        <p className="ml-0 ">{String(t("topics.deletePrompt"))}</p>
      </div>
      <div className="flex topic-icons pr-4">
        <div className="flex items-center justify-center float-right">
          <Button
            type="button"
            variant="outline"
            className="primary"
            onClick={handleCancel}
          >
            {String(t("buttons.cancel"))}
          </Button>
          &nbsp;
          <Button
            type="submit"
            className="primary"
            onClick={(e: BaseSyntheticEvent) => {
              handleDeleteClick(e).catch((error) => console.log(error));
            }}
          >
            {String(t("buttons.delete"))}
          </Button>
        </div>
      </div>
    </>
  );
}
