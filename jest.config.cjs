// import nextJest from 'next/jest.js'
const nextJest = require('next/jest');
 
const createJestConfig = nextJest({
  dir: './',
})
 
// Add any custom config to be passed to Jest
/** @type {import('jest').Config} */
const config = {
  // Add more setup options before each test is run
  preset: "ts-jest",
  testEnvironment: "jsdom",
  collectCoverage: true,
  coverageReporters: ["lcov", "text"],
  testRegex: '(/__tests__/.*|(\\.|/)(test|spec))\\.tsx$',
}
 
// createJestConfig is exported this way to ensure that next/jest can load the Next.js config which is async
// export default createJestConfig(config)
module.exports = createJestConfig(config)