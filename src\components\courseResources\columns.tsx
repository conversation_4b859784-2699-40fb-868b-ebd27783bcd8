"use client";

import type { ColumnDef } from "@tanstack/react-table";
import React from "react";
import type { CourseResourceList } from "@/types";

export const getColumns = (
  t: (key: string) => string,
): ColumnDef<CourseResourceList>[] => [
  {
    accessorKey: "short_name",
    header: t("resourceLibrary.courseName"),
    cell: ({ row }) => <div className="text-align">{row.original.short_name}</div>,
  },
  // {
  //   accessorKey: "full_name",
  //   header: ({ column }: ColumnDefinition): React.JSX.Element => {
  //     return (
  //       <Button
  //         className="px-0"
  //         variant="ghost"
  //         onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
  //       >
  //         Full name
  //         <ArrowUpDown className="ml-2 h-4 w-4" />
  //       </Button>
  //     );
  //   },
  // },
  {
    accessorKey: "section_name",
    header: t("resourceLibrary.sectionName"),
    cell: ({ row }) => <div className="text-align">{row.original.section_name}</div>,
  },
];
