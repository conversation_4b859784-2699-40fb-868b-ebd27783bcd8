import type {
  SessionStatsDataType,
  SessionStatsGraphType,
  CheckpointProgress,
} from "@/types";
import React, { useEffect, useState } from "react";
import { Doughnut } from "react-chartjs-2";

interface DBUserSessionProps {
  userSessionsData: CheckpointProgress[];
}

export function DBUserSessionStats({
  userSessionsData,
}: DBUserSessionProps): React.JSX.Element {
  const [sessionsData, setSessionsData] = useState<SessionStatsGraphType>({
    labels: [],
    datasets: [
      {
        label: "Resource Progress",
        data: [],
        backgroundColor: [],
        borderWidth: 0,
      },
    ],
  });

  useEffect(() => {
    const chartLabels = [] as string[];
    const chartDataColl = [] as number[];

    userSessionsData?.forEach((item: CheckpointProgress) => {
      chartLabels.push(item.resource_name + "[" + item.course_short_name + "]");
      chartDataColl.push(item.percentage_covered);
    });
    const chartDataSets: SessionStatsDataType[] = [
      {
        label: "Resource Progress",
        data: chartDataColl,
        backgroundColor: [
          "#ff6961",
          "#ffb480",
          "#f8f38d",
          "#42d6a4",
          "#08cad1",
          "#59adf6",
          "#9d94ff",
          "#c780e8",
        ],
        borderWidth: 1,
      },
    ];

    const sessionStats = {
      labels: chartLabels,
      datasets: chartDataSets as SessionStatsDataType[],
    };

    setSessionsData(sessionStats);
  }, []);

  return (
    <div className="p-2">
      {/* <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2"></CardHeader> */}
      <div>
        <h1 className="w-full text-base text-left text-[#00b9c7] capitalize mb-4">
          Course Wise Resource Progress
        </h1>
        <div className="flex justify-center">
          <Doughnut
            data={sessionsData as SessionStatsGraphType}
            height={300}
            options={{
              maintainAspectRatio: false,
              onHover: function (event) {
                const target = event.native?.target;
                if (target instanceof HTMLElement) {
                  target.style.cursor = "pointer";
                }
              },
            }}
          />
        </div>
      </div>
    </div>
  );
}
