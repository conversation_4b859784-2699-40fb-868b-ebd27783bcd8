"use client";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
// import { DataTable } from "@/components/ui/data-table/data-table";
import TreeSelectComponent from "@/components/ui/tree-select/tree-select";
// import { Column } from 'primereact/column';
//import courseDetailsData from "@/app/course-details/courseDetailsData";
import MainLayout from "../layout/mainlayout";
import { Button } from "@/components/ui/button";
import React, { useEffect, useState } from "react";
import { Input } from "@/components/ui/input";
import { ORG_KEY, pageUrl } from "@/lib/constants";
import type {
  CourseDetailsRequest,
  CourseDetailsResultType,
  CourseDetailsValueType,
  EditCourseFormType,
  EditCourseSchemaType,
  EditCourseValueType,
  TopicDataType,
  SectionData,
  richTextType,
  ErrorCatch,
  InnerItem,
  LogUserActivityRequest,
  ToastType,
} from "@/types";
import type { TreeDataItem } from "@/components/ui/tree";
import useCourse from "@/hooks/useCourse";
import { useSearchParams } from "next/navigation";
import { useForm } from "react-hook-form";
import "primereact/resources/themes/lara-light-indigo/theme.css";
import {
  DataTable,
  type DataTableRowEditCompleteEvent,
} from "primereact/datatable";
import { Column, type ColumnEditorOptions } from "primereact/column";
import { InputText } from "primereact/inputtext";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { EditCourseSchema } from "@/schema/schema";
import { Editor } from "primereact/editor";

import { DateTimePicker } from "@/components/ui/date-time-picker/date-time-picker";

import {
  type ZonedDateTime,
  parseZonedDateTime,
} from "@internationalized/date";
import type { DateValue } from "react-aria";

import moment from "moment-timezone";
import "../../styles/main.css";

import useTopics from "@/hooks/useTopics";
import { useToast } from "@/components/ui/use-toast";
import { useRouter } from "next/navigation";
import { Spinner } from "@/components/ui/progressiveLoader";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumb";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { PlusIcon } from "lucide-react";
import { Modal } from "@/components/ui/modal";
import AddSection from "./addSection";
import { useTranslation } from "react-i18next";

interface CourseSection {
  section_id: string;
  course_id: string;
  name: string;
  summary: string;
  section_order: number;
}

interface ModuleData {
  slNo: number;
  id: string;
  summary: string;
  name: string;
}

export default function ViewCourse(): React.JSX.Element {
  const { t } = useTranslation();
  const { toast } = useToast() as ToastType;
  const router = useRouter();
  const { getCategoryHierarchy } = useTopics();
  const { updateUserActivity } = useLogUserActivity();

  const form = useForm<EditCourseSchemaType>({
    resolver: zodResolver(EditCourseSchema),
  });
  const [richTextValues, setRichTextValues] = useState<
    richTextType | undefined
  >(undefined);
  const [courseDetailsData, setCourseDetailsData] =
    React.useState<CourseDetailsValueType | null>();

  const [dateEndTime, setDateEndTime] = useState<ZonedDateTime | DateValue>();
  const { convertDataToTreeNode } = useCourse();
  const [nodeData, setNodeData] = React.useState<TreeDataItem[]>([]);
  const searchParams = useSearchParams();
  const courseId = searchParams.get("courseId");
  const categoryId = searchParams.get("categoryId");
  const { courseDetails, editCourse } = useCourse();
  const [defaultTreeSelect, setDefaultTreeSelect] = useState("");
  const [isLoading, setIsLoading] = React.useState<boolean>(true);
  const [moduleData, setModuleData] = React.useState<ModuleData[]>([]);
  const [sectionData, setSectionData] = React.useState<SectionData[]>([]);
  const Expiry = searchParams?.get("expiry");
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);
  const [isOpenSection, setIsOpenSection] = useState<boolean>(false);
  const is_premium = searchParams.get("is_premium");

  const findKeyByValue = (
    treeData: TreeDataItem[],
    targetValue: unknown,
  ): string | null => {
    for (const node of treeData) {
      if (node.value === targetValue) {
        return String(node.key);
      }

      if (node.children && node.children.length > 0) {
        const keyInChildren = findKeyByValue(node.children, targetValue);
        if (keyInChildren != null) {
          return keyInChildren;
        }
      }
    }
    return null;
  };

  useEffect(() => {
    setBreadcrumbItems(
      getBreadCrumbItems(t, t("breadcrumb.editCourse"), {
        courseId: courseId as string,
        // categoryId: categoryId as string,
        expiry: Expiry as string,
        is_premium: is_premium as string,
      }),
    );
  }, [t]);

  const fetchData = async (): Promise<void> => {
    try {
      const orgId = localStorage.getItem(ORG_KEY);
      const reqParams: CourseDetailsRequest = {
        course_id: courseId as string,
        org_id: orgId ?? "",
      };
      const response = await courseDetails(reqParams);

      setCourseDetailsData(response[0] as CourseDetailsResultType);
      const courseSectionData: CourseSection[] = response[0]?.sections;
      const sectionData = courseSectionData.map((section, index) => ({
        id: section.section_id,
        name: section.name,
        summary: section.summary,
        slNo: index + 1,
      }));
      setModuleData(sectionData);
      form.setValue("courseCategory", response[0].category_id);
      const currentTimezone = moment.tz.guess();
      const parsedDatetime = moment.tz(response[0]?.end_date, currentTimezone);
      const formattedDatetime = parseZonedDateTime(
        parsedDatetime.format("YYYY-MM-DDTHH:mm") + `[${currentTimezone}]`,
      );
      setDateEndTime(formattedDatetime);
      form.setValue("courseEndDate", formattedDatetime);
      form.setValue("courseFullName", response[0]?.full_name as string);
      form.setValue("courseShortName", response[0]?.short_name as string);
      form.setValue("courseDescription", response[0]?.summary);
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  };

  useEffect(() => {
    void fetchData();
    void topicList();
  }, [courseId, isOpenSection]);

  const handleAddSection = (): void => {
    setIsOpenSection(true);
  };

  const closeDialog = (): void => {
    setIsOpenSection(false);
  };

  const topicList = (): void => {
    const fetchData = async (): Promise<void> => {
      const org_id = localStorage.getItem("orgId") as string;
      const params = {
        org_id: org_id,
        filter_data: 1,
      };
      try {
        const topics = await getCategoryHierarchy(params);
        if (topics.length > 0) {
          const treeData: TreeDataItem[] = convertDataToTreeNode(
            topics as TopicDataType[],
          );
          const key = findKeyByValue(treeData ?? [], categoryId);
          setDefaultTreeSelect(key as string);
          setNodeData(treeData);
          setIsLoading(false);
        }
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
        console.error("Error fetching data:");
      }
    };
    fetchData().catch((error) => console.log(error));
  };

  // const handleDetailView = (): void => {
  //   console.log("handle click");
  // };

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  const setRichTextValue = (richTextValue: richTextType | undefined): void => {
    if (richTextValue && richTextValue.htmlValue == null) {
      richTextValue = undefined;
    }
    form.setValue("courseDescription", richTextValue?.htmlValue ?? "");
    setRichTextValues(richTextValue);
  };

  async function onSubmit(data: EditCourseFormType): Promise<void> {
    const org_id = localStorage.getItem("orgId") as string;
    const dateEnd = data.courseEndDate;
    const endDate = new Date(
      dateEnd.year,
      dateEnd.month - 1,
      dateEnd.day,
      dateEnd.hour,
      dateEnd.minute,
    );
    const momentEndDate = moment(endDate);
    const formattedEndDate = momentEndDate.format("YYYY-MM-DD HH:mm");
    const formData = data;

    if (richTextValues?.htmlValue !== undefined) {
      data.courseDescription = richTextValues.htmlValue;
    }

    const transformedData = {
      course_data: {
        id: courseId,
        full_name: formData.courseFullName,
        summary: formData.courseDescription,
        end_date: formattedEndDate,
        category_id: formData.courseCategory ?? "",
        short_name: formData.courseShortName,
      },
      org_id: org_id,
      section_datas: sectionData ?? [],
    };
    try {
      const result = await editCourse(transformedData as EditCourseValueType);

      if (result.status == "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.course_update_title"),
          description: t("successMessages.course_update_msg"),
        });
        const params = {
          activity_type: "Course",
          screen_name: "Course",
          action_details: "Course updated successfully",
          target_id: courseId as string,
          log_result: "SUCCESS",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
        router.push(pageUrl.courseList);
      } else {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: t("errorMessages.something_went_wrong"),
        });
        const params = {
          activity_type: "Course",
          screen_name: "Course",
          action_details: "Failed to update course",
          target_id: courseId as string,
          log_result: "ERROR",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.details,
      });
      console.error("An unexpected error occurred:", error);
    }
  }

  // const setRichTextValue = (val: string): void => {
  //   form.setValue("courseDescription", val);
  // };
  const backToDetails = (): void => {
    router.push(
      `${pageUrl.CourseManagement}?courseId=${courseId}&&expiry=${Expiry}&&is_premium=${is_premium}`,
    );
  };

  const onRowEditComplete = (e: DataTableRowEditCompleteEvent): void => {
    moduleData[e.index] = e.newData as ModuleData;
    const newSectionData = moduleData.map(({ slNo, ...rest }) => {
      console.log(slNo);
      return rest;
    });
    setSectionData(newSectionData);
  };

  const textEditor = (options: ColumnEditorOptions): React.ReactNode => {
    return (
      <InputText
        type="text"
        value={options.value as string}
        onChange={(e: React.ChangeEvent<HTMLInputElement>): void =>
          options.editorCallback!(e.target.value)
        }
      />
    );
  };

  const allowEdit = (rowData: ModuleData): boolean => {
    return rowData.name !== "";
  };

  const handleInputChange =
    (fieldName: "courseFullName" | "courseShortName") =>
    (e: React.ChangeEvent<HTMLInputElement>): void => {
      const value = e.target.value;
      const sanitizedValue = value
        .trimStart()
        .replace(/^\p{P}+/u, "")
        .replace(/[^\p{L}\p{M}\p{N}\s&-]/gu, "")
        .replace(/\s{2,}/g, " ")
        .replace(/^[^\p{L}\p{M}\p{N}]|[^\p{L}\p{M}\p{N}\s&-]/gu, "")
        .replace(/\s+$/, (match) =>
          match.length > 1 ? match.slice(0, -1) : match,
        );
      form.setValue(fieldName, sanitizedValue);
    };

  return (
    <MainLayout>
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      <h1 className="text-2xl font-semibold tracking-tight">
        {t("courses.updateCourse")}
      </h1>
      {isLoading ? (
        <Spinner />
      ) : (
        <div className="border rounded-md p-4 mt-4 bg-[#fff]">
          <Form {...form}>
            <form
              onSubmit={(event) => void form.handleSubmit(onSubmit)(event)}
              className="space-y-8"
            >
              <div className="grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                <div className="sm:col-span-3">
                  <FormField
                    control={form.control}
                    name="courseFullName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {String(t("courses.courseFullName"))}
                        </FormLabel>
                        <FormControl>
                          <Input
                            autoComplete="off"
                            {...field}
                            maxLength={50}
                            defaultValue={courseDetailsData?.full_name ?? ""}
                            name="courseFullName"
                            onChange={handleInputChange("courseFullName")}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="sm:col-span-3">
                  <FormField
                    control={form.control}
                    name="courseShortName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {String(t("courses.courseShortName"))}
                        </FormLabel>
                        <FormControl>
                          <Input
                            autoComplete="off"
                            {...field}
                            maxLength={50}
                            defaultValue={courseDetailsData?.short_name ?? ""}
                            name="courseShortName"
                            onChange={handleInputChange("courseShortName")}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="sm:col-span-3">
                  <FormField
                    control={form.control}
                    name="courseEndDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {String(t("courses.courseEndDate"))}
                        </FormLabel>
                        <FormControl>
                          <DateTimePicker
                            granularity="minute"
                            minValue={dateEndTime}
                            value={field.value as DateValue}
                            hideTimeZone={true}
                            onChange={(newDate) => {
                              field.onChange(newDate);
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="sm:col-span-3">
                  <FormField
                    control={form.control}
                    name="courseCategory"
                    render={() => (
                      <FormItem>
                        <FormLabel>
                          {String(t("courses.courseCategory"))}
                        </FormLabel>
                        <FormControl>
                          <TreeSelectComponent
                            nodeData={nodeData}
                            selectLabel={String(t("courses.courseCategory"))}
                            onNodeSelect={(selectedValue: string | null) => {
                              if (selectedValue !== null) {
                                form.setValue("courseCategory", selectedValue);
                              }
                            }}
                            defaultValueKey={defaultTreeSelect}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
              <div className="col-span-full">
                <FormField
                  control={form.control}
                  name="courseDescription"
                  render={() => (
                    <FormItem>
                      <FormLabel>
                        {String(t("courses.courseDescription"))}
                      </FormLabel>
                      <FormControl>
                        <Editor
                          value={courseDetailsData?.summary}
                          onTextChange={(event) => {
                            const htmlValue = event.htmlValue;
                            const richTextValue = {
                              htmlValue: htmlValue,
                            };
                            setRichTextValue(richTextValue as richTextType);
                          }}
                          style={{ height: "320px" }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="col-span-full">
                <div className="flex justify-end">
                  <Button
                    type="button"
                    className="bg-ring bg-[#fb8500] hover:bg-[#fb5c00] mb-2 md:py-3 md:px-6 whitespace-nowrapS"
                    onClick={handleAddSection}
                  >
                    <PlusIcon className="h-4 w-4 md:h-5 md:w-5" />
                    {t("courses.section.addSection")}
                  </Button>
                </div>

                <DataTable
                  value={moduleData}
                  showGridlines
                  editMode="row"
                  dataKey="id"
                  onRowEditComplete={onRowEditComplete}
                  tableStyle={{ minWidth: "50rem" }}
                  className="custom-data-table"
                  scrollable
                  scrollHeight="300px"
                >
                  <Column
                    field="slNo"
                    header={String(t("courses.courseModule.slno"))}
                    style={{ width: "5%" }}
                  ></Column>
                  <Column
                    field="name"
                    header={String(t("courses.courseModule.name"))}
                    editor={(options) => textEditor(options)}
                    style={{ width: "20%" }}
                  ></Column>
                  <Column
                    rowEditor={allowEdit}
                    headerStyle={{ width: "10%", minWidth: "8rem" }}
                    bodyStyle={{ textAlign: "center" }}
                  ></Column>
                </DataTable>
              </div>
              <div className="flex items-center justify-end gap-x-6">
                <Button
                  type="button"
                  className="bg-[#33363F]"
                  onClick={backToDetails}
                >
                  {String(t("buttons.cancel"))}
                </Button>
                <Button type="submit" className="bg-[#9FC089]">
                  {String(t("buttons.update"))}
                </Button>
              </div>
            </form>
            {isOpenSection && (
              <Modal
                title={t("courses.section.addSection")}
                header=""
                openDialog={isOpenSection}
                closeDialog={closeDialog}
                type="max-w-4xl"
              >
                <AddSection
                  onCancel={closeDialog}
                  courseId={courseId as string}
                  currentSectionsCount={moduleData.length}
                />
              </Modal>
            )}
          </Form>
        </div>
      )}
    </MainLayout>
  );
}
