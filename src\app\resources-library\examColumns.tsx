"use client";
import type { ColumnDef } from "@tanstack/react-table";
import type { ResourceList, ResourcesFormRowDefinition } from "@/types";
import moment from "moment";

export const getExamColumns = (
  t: (key: string) => string,
): ColumnDef<ResourceList>[] => [
  {
    accessorKey: "name",
    header: t("resourceLibrary.quizName"),
    cell: ({ row }: ResourcesFormRowDefinition): React.JSX.Element => (
      <div className="text-left">{row.original.name}</div>
    ),
  },
  // {
  //   header: "Main Topic",
  //   cell: ({ row }: ResourcesFormRowDefinition): React.JSX.Element => (
  //     <div className="text-left">{row.original.main_topic ?? ""}</div>
  //   ),
  // },
  {
    header: t("resourceLibrary.quizType"),
    cell: ({ row }: ResourcesFormRowDefinition): React.JSX.Element => (
      <div className="text-left">{row.original.quiz_type}</div>
    ),
  },
  {
    header: t("resourceLibrary.createdOn"),
    cell: ({ row }: ResourcesFormRowDefinition): React.JSX.Element => {
      const formattedDate = moment
        .utc(row.original.created_at)
        .local()
        .format("DD-MMM-YYYY hh:mm a");

      return <div className="text-align">{formattedDate}</div>;
    },
  },
  {
    header: t("resourceLibrary.duration"),
    cell: ({ row }: ResourcesFormRowDefinition): React.JSX.Element => (
      <div className="text-center">{row.original.duration}</div>
    ),
  },
  {
    header: t("resourceLibrary.noOfQuestions"),
    cell: ({ row }: ResourcesFormRowDefinition): React.JSX.Element => (
      <div className="text-center">{row.original.num_of_questions ?? ""}</div>
    ),
  },
  {
    header: t("resourceLibrary.totalMark"),
    cell: ({ row }: ResourcesFormRowDefinition): React.JSX.Element => (
      <div className="text-center text-blue-700">{row.original.total_mark}</div>
    ),
  },
  {
    header: t("resourceLibrary.passMark"),
    cell: ({ row }: ResourcesFormRowDefinition): React.JSX.Element => (
      <div className="text-center text-blue-700">{row.original.pass_mark}</div>
    ),
  },
  {
    accessorKey: t("resourceLibrary.penalty"),
    header: "",
    cell: ({ row }: ResourcesFormRowDefinition): React.JSX.Element => (
      <div className="text-left text-blue-700">
        {row.original.penalty_available}
      </div>
    ),
  },
];
