import { Button } from "@/components/ui/button";
import { File, Layout, PlaySquareIcon, HelpCircle } from "lucide-react";

export function ResourceIcons(iconType: string): React.JSX.Element {
  const iconComponents: Record<string, React.FC> = {
    File: File,
    Page: Layout,
    Url: PlaySquareIcon,
    Quiz: HelpCircle,
  };

  const IconComponent = iconComponents[iconType];

  if (IconComponent !== null || IconComponent !== undefined) {
    return (
      <>
        <Button variant="outline" size="icon" className="w-6 h-6 rounded-full ">
          <span className="text-xs">
            <IconComponent />
          </span>
        </Button>
      </>
    );
  }

  return <></>;
}
