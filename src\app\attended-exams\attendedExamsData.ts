const AttendedExamsData = [
  {
    user_id: "d1f30244-76f4-4ee0-8a5d-e577f24cd3ac",
    org_id: "19579370-a028-484d-8f35-8af2023068dd",
    email: "ann<PERSON><PERSON><PERSON><PERSON><EMAIL>",
    first_name: "<PERSON>",
    last_name: "<PERSON>",
    course_id: "0843b5da-6b61-4945-8917-83b1ca63b804",
    course_name: "SBI PO Prelims",
    course_start_date: "2023-07-01T21:19:00",
    course_end_date: "2023-07-31T00:00:00",
    name: "SBI Probationary Officers Exam",
    attempt: 10,
    quiz_start_time: "2023-07-05T07:16:12.565287+00:00",
    quiz_end_time: "2023-07-05T07:16:32.950749+00:00",
    duration: 30,
    quiz_attempt_id: "864a2c11-060c-4977-b282-707aedfe5ff0",
    quiz_total_marks: 43,
    quiz_pass_mark: 60,
    quiz_status: "evaluated",
    result_of_quiz: "Failed",
  },
  {
    user_id: "d1f30244-76f4-4ee0-8a5d-e577f24cd3ac",
    org_id: "19579370-a028-484d-8f35-8af2023068dd",
    email: "<EMAIL>",
    first_name: "Ann",
    last_name: "F",
    course_id: "0843b5da-6b61-4945-8917-83b1ca63b804",
    course_name: "SBI PO Prelims",
    course_start_date: "2023-07-01T21:19:00",
    course_end_date: "2023-07-31T00:00:00",
    name: "SBI Probationary Officers Exam",
    attempt: 1,
    quiz_start_time: "2023-07-05T05:22:46.017962+00:00",
    quiz_end_time: "2023-07-05T05:23:58.250343+00:00",
    duration: 30,
    quiz_attempt_id: "38a76247-5b14-4328-80d5-b6055f1e319a",
    quiz_total_marks: 23,
    quiz_pass_mark: 60,
    quiz_status: "evaluated",
    result_of_quiz: "Failed",
  },

  {
    user_id: "933bae34-527d-4da2-8d27-e9307eef6051",
    org_id: "19579370-a028-484d-8f35-8af2023068dd",
    email: "<EMAIL>",
    first_name: "Jijo",
    last_name: "PJ",
    course_id: "daf331c1-e93b-4761-900c-6e56b0fd56e9",
    course_name: "Ionic Framework Program",
    course_start_date: "2023-07-10T00:00:00",
    course_end_date: "2023-07-31T00:00:00",
    name: "Ionic Practise",
    attempt: 8,
    quiz_start_time: "2023-07-19T12:21:07.457584+00:00",
    quiz_end_time: "2023-07-19T12:24:48.102354+00:00",
    duration: 40,
    quiz_attempt_id: "2873d67a-2768-4565-a258-114ce2ded702",
    quiz_total_marks: 80,
    quiz_pass_mark: 70,
    quiz_status: "evaluated",
    result_of_quiz: "Passed",
  },
  {
    user_id: "a46c7ae8-7661-43f1-bcc3-1b00cb145932",
    org_id: "19579370-a028-484d-8f35-8af2023068dd",
    email: "<EMAIL>",
    first_name: "Surya",
    last_name: "Lekshmi Ram",
    course_id: "3e87b65d-b6c7-40da-b",
    course_name: "English Tutorial",
    course_start_date: "2023-08-14T10:06:00",
    course_end_date: "2023-12-28T18:30:00",
    name: "August Exam English",
    attempt: 3,
    quiz_start_time: "2023-09-07T09:47:19.039279+00:00",
    quiz_end_time: "2023-09-07T09:52:03.047166+00:00",
    duration: 10,
    quiz_attempt_id: "20068073-2d2c-4b23-a206-397291817a8c",
    quiz_total_marks: 0,
    quiz_pass_mark: 5,
    quiz_status: "evaluated",
    result_of_quiz: "Failed",
  },
  {
    user_id: "5261023f-467e-4c79-a872-4eb60e53fe9d",
    org_id: "19579370-a028-484d-8f35-8af2023068dd",
    email: "<EMAIL>",
    first_name: "Pooja",
    last_name: "Lakshmi R",
    course_id: "a13634eb-bbd4-4b9a-a",
    course_name: "Javascript",
    course_start_date: "2023-08-08T13:55:00",
    course_end_date: "2023-11-30T13:55:00",
    name: "Javascript MCQ Augus",
    attempt: 3,
    quiz_start_time: "2023-08-18T06:40:12.180601+00:00",
    quiz_end_time: "2023-08-18T06:51:09",
    duration: 20,
    quiz_attempt_id: "3f7d0414-8e70-4f97-838e-3cf18bc26918",
    quiz_total_marks: 0,
    quiz_pass_mark: 10,
    quiz_status: "submitted",
    result_of_quiz: "",
  },
  {
    user_id: "5261023f-467e-4c79-a872-4eb60e53fe9d",
    org_id: "19579370-a028-484d-8f35-8af2023068dd",
    email: "<EMAIL>",
    first_name: "Pooja",
    last_name: "Lakshmi R",
    course_id: "a13634eb-bbd4-4b9a-a",
    course_name: "Javascript",
    course_start_date: "2023-08-08T13:55:00",
    course_end_date: "2023-11-30T13:55:00",
    name: "Javascript Practice ",
    attempt: 1,
    quiz_start_time: "2023-08-18T09:10:37.575602+00:00",
    quiz_end_time: "",
    duration: 15,
    quiz_attempt_id: "def10a60-d513-4a36-989a-dd7bdb36f4a0",
    quiz_total_marks: 0,
    quiz_pass_mark: 3,
    quiz_status: "in_progress",
    result_of_quiz: "",
  },
  {
    user_id: "56aca9bf-ca56-4280-82b0-7423a9f3ea83",
    org_id: "19579370-a028-484d-8f35-8af2023068dd",
    email: "<EMAIL>",
    first_name: "Anu ",
    last_name: "Wilson",
    course_id: "a13634eb-bbd4-4b9a-a5ee-286bdceb8f74",
    course_name: "Javascript",
    course_start_date: "2023-08-08T13:55:00",
    course_end_date: "2023-11-30T13:55:00",
    name: "Javascript Practice ",
    attempt: 1,
    quiz_start_time: "2023-08-18T06:27:32.184592+00:00",
    quiz_end_time: "",
    duration: 15,
    quiz_attempt_id: "8394c897-d227-405a-a461-d1070f4c0b8c",
    quiz_total_marks: 0,
    quiz_pass_mark: 3,
    quiz_status: "in_progress",
    result_of_quiz: "",
  },
];
export default AttendedExamsData;
