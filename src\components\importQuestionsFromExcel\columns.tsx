"use client";

import type { ColumnDef, Row } from "@tanstack/react-table";
// import { ArrowUpDown } from "lucide-react";
// import { Button } from "@/components/ui/button";
import React from "react";
import { type ExcelImportQuestion } from "@/types";
// interface ColumnDefinition {
//   column: Column<Question, unknown>;
// }
interface RowDefinition {
  row: Row<ExcelImportQuestion>;
}

export const getColumns = (
  t: (key: string) => string
): ColumnDef<ExcelImportQuestion>[] => [
  {
    accessorKey: "question_text",
    header: t("questionBank.question"),
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div className="text-left">
        {row.original.input_data?.questions?.map((q, i) => (
          <div key={i}>{q.question_text ?? ""}</div>
        ))}
      </div>
    ),
  },
  {
    accessorKey: "question_hint",
    header: t("questionBank.questionHint"),
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div className="text-left">
        {row.original.input_data?.questions?.map((q, i) => (
          <div key={i}>{q.question_hint ?? ""}</div>
        ))}
      </div>
    ),
  },
  {
    accessorKey: "option1",
    header: t("questionBank.option")+"1",
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div className="text-left">
        {row.original.input_data?.questions?.map((q, i) => (
          <div key={i}>{q.options?.option1 ?? ""}</div>
        ))}
      </div>
    ),
  },
  {
    accessorKey: "option2",
    header: t("questionBank.option")+"2",
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div className="text-left">
        {row.original.input_data?.questions?.map((q, i) => (
          <div key={i}>{q.options?.option2 ?? ""}</div>
        ))}
      </div>
    ),
  },
  {
    accessorKey: "option3",
    header: t("questionBank.option")+"3",
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div className="text-left">
        {row.original.input_data?.questions?.map((q, i) => (
          <div key={i}>{q.options?.option3 ?? ""}</div>
        ))}
      </div>
    ),
  },
  {
    accessorKey: "option4",
    header: t("questionBank.option")+"4",
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div className="text-left">
        {row.original.input_data?.questions?.map((q, i) => (
          <div key={i}>{q.options?.option4 ?? ""}</div>
        ))}
      </div>
    ),
  },
  {
    accessorKey: "option5",
    header: t("questionBank.option")+"5",
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div className="text-left">
        {row.original.input_data?.questions?.map((q, i) => (
          <div key={i}>{q.options?.option5 ?? ""}</div>
        ))}
      </div>
    ),
  },
  {
    accessorKey: "right_answer",
    header: t("questionBank.rightAnswer"),
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div className="text-left">
        {row.original.input_data?.questions?.map((q, i) => (
          <div key={i}>{q.right_answer?.join(", ") ?? ""}</div>
        ))}
      </div>
    ),
  },
  {
    accessorKey: "solution1",
    header: t("questionBank.solution")+"1",
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div className="text-left">
        {row.original.input_data?.questions?.map((q, i) => (
          <div key={i}>{q.solutions.solution1 ?? ""}</div>
        ))}
      </div>
    ),
  },
  {
    accessorKey: "solution2",
    header: t("questionBank.solution")+"2",
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div className="text-left">
        {row.original.input_data?.questions?.map((q, i) => (
          <div key={i}>{q.solutions.solution2 ?? ""}</div>
        ))}
      </div>
    ),
  },
  {
    accessorKey: "solution3",
    header: t("questionBank.solution")+"3",
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div className="text-left">
        {row.original.input_data?.questions?.map((q, i) => (
          <div key={i}>{q.solutions.solution3 ?? ""}</div>
        ))}
      </div>
    ),
  },
  {
    accessorKey: "solution4",
    header: t("questionBank.solution")+"4",
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div className="text-left">
        {row.original.input_data?.questions?.map((q, i) => (
          <div key={i}>{q.solutions.solution4 ?? ""}</div>
        ))}
      </div>
    ),
  },
  {
    accessorKey: "solution5",
    header: t("questionBank.solution")+"5",
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div className="text-left">
        {row.original.input_data?.questions?.map((q, i) => (
          <div key={i}>{q.solutions.solution5 ?? ""}</div>
        ))}
      </div>
    ),
  },
  {
    accessorKey: "best_solution1",
    header: t("questionBank.bestSolution")+"1",
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div className="text-left">
        {row.original.input_data?.questions?.map((q, i) => (
          <div key={i}>{q.best_solutions.best_solution1 ?? ""}</div>
        ))}
      </div>
    ),
  },
  {
    accessorKey: "best_solution2",
    header: t("questionBank.bestSolution")+"2",
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div className="text-left">
        {row.original.input_data?.questions?.map((q, i) => (
          <div key={i}>{q.best_solutions.best_solution2 ?? ""}</div>
        ))}
      </div>
    ),
  },
  {
    accessorKey: "best_solution3",
    header: t("questionBank.bestSolution")+"3",
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div className="text-left">
        {row.original.input_data?.questions?.map((q, i) => (
          <div key={i}>{q.best_solutions.best_solution3 ?? ""}</div>
        ))}
      </div>
    ),
  },
];
