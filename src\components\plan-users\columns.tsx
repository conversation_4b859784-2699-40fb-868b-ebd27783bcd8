"use client";
import React from "react";
import type { ColumnDef, Row } from "@tanstack/react-table";
// import { ArrowUpDown } from "lucide-react";
// import { Button } from "@/components/ui/button";
import type { UserPlanListResult } from "@/types";
import moment from "moment";
import { DATE_FORMAT_DMY_HM_AM_PM } from "@/lib/constants";

interface RowDefinition {
  row: Row<UserPlanListResult>;
}


export const getColumns = (
  t: (key: string) => string,
): ColumnDef<UserPlanListResult>[] => [
  {
    accessorKey: "name",
    header: t("subscriptionPlan.name"),
    // header: ({ column }: ColumnDefinition): React.JSX.Element => {
    //   return (
    //     <Button
    //       className="px-0"
    //       variant="ghost"
    //       onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
    //     >
    //       User
    //       <ArrowUpDown className="ml-2 h-4 w-4" />
    //     </Button>
    //   );
    // },
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div>{row.original.name}</div>
    ),
  },
  {
    header: t("subscriptionPlan.plan"),
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div>{row.original.subscription_plan_name}</div>
    ),
  },

  {
    header: t("subscriptionPlan.purchaseDate"),
    cell: ({ row }: RowDefinition): React.JSX.Element => {
      const formattedDate = moment
        .utc(row.original.user_purchase_date)
        .local()
        .format(DATE_FORMAT_DMY_HM_AM_PM);
      return <div>{formattedDate}</div>;
    },
  },

  {
    header: t("subscriptionPlan.validUpTo"),
    cell: ({ row }: RowDefinition): React.JSX.Element => {
      const formattedToDate = moment
        .utc(row.original.user_subscription_end_date)
        .format(DATE_FORMAT_DMY_HM_AM_PM);

      return <div>{formattedToDate}</div>;
    },
  },
  {
    header: t("subscriptionPlan.paymentMethod"),
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div>{row.original.payment_method}</div>
    ),
  },
  {
    header: t("subscriptionPlan.status"),
    cell: ({ row }: RowDefinition): React.JSX.Element => (
      <div>{row.original.subscription_status}</div>
    ),
  },
];
