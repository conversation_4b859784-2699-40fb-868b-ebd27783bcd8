import { supabase } from "../lib/client";
import { views } from "../lib/apiConfig";
import type { LoginUserData, OrganizaionList } from "@/types";

interface UseOrganizationReturn {
  getOrganizationList: () => Promise<OrganizaionList[]>;
}
const useOrganization = (): UseOrganizationReturn => {
  async function getOrganizationList(): Promise<OrganizaionList[]> {
    const organizationList = views?.organizationList ?? "";
    const userDetails = localStorage.getItem("userDetails");

    try {
      let userId: string | undefined;

      if (userDetails != null && userDetails != undefined) {
        const users = JSON.parse(userDetails) as LoginUserData;
        userId = users?.id;
        console.log(userId);
      }

      if (userId === undefined) {
        throw new Error("User ID is undefined");
      }

      const { data, error } = await supabase
        .from(organizationList)
        .select()
        .eq("user_id", userId);
      if (error) {
        throw new Error(error.message);
      }
      return data as OrganizaionList[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  return { getOrganizationList };
};

export default useOrganization;
