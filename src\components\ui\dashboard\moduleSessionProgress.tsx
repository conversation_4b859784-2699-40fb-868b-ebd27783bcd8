import React from "react";

interface ModuleSessionProgressProps {
  module_name: string;
  course_name: string;
  checkpoints_count: number;
  in_progress_count: number;
  progress_color: string;
  bar_color: string;
}

export function DBModuleSessionProgress({
  module_name,
  course_name,
  checkpoints_count,
  in_progress_count,
  bar_color,
}: ModuleSessionProgressProps): React.JSX.Element {
  console.log(course_name);

  // Calculate the percentage of completion
  const completionPercentage = (in_progress_count / checkpoints_count) * 100;
  const progressClassName = `h-full rounded-full bg-[#fdb666] text-center text-xs text-white`;
  const barClassName = `mb-4 h-2 w-full rounded-full bg-${bar_color}`;

  return (
    <>
      <div className="flex items-center justify-between text-sm text-gray-400 pb-1 ">
        <p className="text leading-[14px]">{module_name}</p>
        <p className="text-[#9fc089] ">
          {in_progress_count}/{checkpoints_count}
        </p>
      </div>
      <div className={barClassName}>
        {/* Apply the width dynamically */}
        <div
          className={`${progressClassName}`}
          style={{ width: `${completionPercentage}%` }}
        />
      </div>
    </>
  );
}
