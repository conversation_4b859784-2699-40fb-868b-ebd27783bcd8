"use client";
import { <PERSON><PERSON><PERSON>, MenubarMenu } from "@/components/ui/menubar";
import { UserNav } from "../user-nav-bar";
import { cn } from "@/lib/utils";
import React, {
  useRef,
  // useState
} from "react";
// import { SidebarCloseIcon, SidebarOpenIcon } from "lucide-react";
import { ORG_NAME } from "@/lib/constants";

import useNoTextSelect from "@/hooks/useNoTextSelect";
import { LanguageSwitcher } from "./language-switcher";
import { useTranslation } from "react-i18next";

interface MenuProps {
  // onMenuToggle: () => void;
  isOpen: boolean;
}

export function Menu({ isOpen }: MenuProps): React.JSX.Element {
  const { i18n } = useTranslation();
  const isRTL = i18n.language === "ar";
  let orgName = "";
  if (typeof window !== "undefined") {
    orgName = localStorage.getItem(ORG_NAME) as string;
  }

  // Function to handle clicks outside the menu-styles div and the button
  // const handleClickOutside = (event: MouseEvent): void => {
  //   if (
  //     menuStylesDivRef.current! &&
  // !menuStylesDivRef.current.contains(event.target) &&
  // buttonRef.current! &&
  // !buttonRef.current.contains(event.target)
  //   ) {
  //     setOpen(false);
  //   }
  // };

  // Attach click event listener to the document when the menu is open
  // useEffect(() => {
  //   if (open) {
  //     document.addEventListener("mousedown", handleClickOutside);
  //   } else {
  //     document.removeEventListener("mousedown", handleClickOutside);
  //   }

  //   return () => {
  //     document.removeEventListener("mousedown", handleClickOutside);
  //   };
  // }, [open]);

  const wrapperRef = useRef(null);

  useNoTextSelect("organizationMenu");

  return (
    // <Menubar
    //   className={`${
    //     open
    //       ? "ml-56 fixed bg-white flex w-[calc(100%-224px)]"
    //       : "ml-0 fixed bg-white flex w-[100%] "
    //   } z-30 justify-between items-center rounded-none border-b-[1px] border-t-0 border-r-0 border-l-0 border-[#e6e9eb] bg-[#fff] px-2 lg:py-4 py-2 h-14 transition-all duration-500 transform`}
    // >
    <Menubar
      className={cn(
        "fixed bg-white flex z-30 justify-between items-center rounded-none border-b-[1px] border-t-0 border-r-0 border-l-0 border-[#e6e9eb] bg-[#fff] px-2 lg:py-4 py-2 h-14 transition-all duration-500 transform",
        isRTL
          ? (isOpen ? "mr-[224px] w-[calc(100%-224px)]" : "mr-[64px] w-[calc(100%-64px)]")
          : (isOpen ? "ml-[224px] w-[calc(100%-224px)]" : "ml-[64px] w-[calc(100%-64px)]")
      )}
    >
      <MenubarMenu>
        <div className="flex lg:justify-start items-center w-[50%]">
          {/* <MenuIcon /> */}
          {/* {!isOpen ? (
            <SidebarOpenIcon
              color="#374151"
              ref={buttonRef}
              className="mx-2 z-50 cursor-pointer"
              // onClick={() => {
              //   onTap(!open);
              //   setOpen(!open);
              // }}
              onClick={onMenuToggle}
            />
          ) : (
            <SidebarCloseIcon
              color="#374151"
              ref={buttonRef}
              className="mx-2 z-50 cursor-pointer"
              // onClick={() => {
              //   onTap(!open);
              //   setOpen(!open);
              // }}
              onClick={onMenuToggle}
            />
          )} */}

          {orgName != null && (
            <div className="relative" ref={wrapperRef} id="organizationMenu">
              <label className="text-2xl font-semibold tracking-tight">
                {" "}
                {orgName}
              </label>
              {/* <div
                className=" hidden text-[#374151] md:flex  text-sm  bg-stone-100 px-2 py-1 w-[200px] justify-between rounded-sm cursor-pointer items-center opacity-50 pointer-events-none cursor-no-drop"
                onClick={() => setShowList(!showList)}
              >
                {orgName}
                {!showList ? (
                  <ChevronDown className="w-6 pl-1 pt-[2px]" />
                ) : (
                  <ChevronUp className="w-6 pl-1 pt-[2px]" />
                )}
              </div> */}
              {/* {showList && (
                <div className="w-[200px] absolute top-[40px] bg-white sh adow">
                  {comboData.map((indv, indx) => (
                    <div
                      key={indx}
                      onClick={() => {
                        comboSelectedValue(indv.value ?? "", indv.label ?? "");
                        setShowList(!showList);
                      }}
                      className="p-2 border-b-[0.5px] border-b-[#c4c4c457] border-solid cursor-pointer text-sm"
                    >
                      {indv.label}
                    </div>
                  ))}
                </div>
              )} */}
            </div>
          )}
        </div>
      </MenubarMenu>

      <MenubarMenu>
        {/* <div className={`w-[94%]   top-[70px] relative md:top-0 md:w-80`}>
          <SearchIcon className=" absolute top-0 bottom-0 h-6 my-auto left-3" />
          <Input type="text" placeholder="Search" className="pl-12 " />
        </div> */}
        <div className="flex items-center gap-4">
          <LanguageSwitcher />
          <UserNav />
        </div>
      </MenubarMenu>
    </Menubar>
  );
}
