import React from "react";
import { Button } from "@/components/ui/button";
import { DataTable } from "@/components/ui/data-table/data-table";
import type { ColumnDef } from "@tanstack/react-table";
import { useTranslation } from "react-i18next";
interface FolderRow {
  value: string;
  label: string;
}

interface FolderManagerProps {
  comboData: FolderRow[];
  editingFolderId: string | null;
  editingFolderName: string;
  onEditClick: (folderId: string, currentName: string) => void;
  onEditCancel: () => void;
  onEditSave: () => void;
  setEditingFolderName: (name: string) => void;
  loading?: boolean;
}

const FolderManager: React.FC<FolderManagerProps> = ({
  comboData,
  editingFolderId,
  editingFolderName,
  onEditClick,
  onEditCancel,
  onEditSave,
  setEditingFolderName,
  loading = false,
}) => {
  const { t } = useTranslation();
  const columns: ColumnDef<FolderRow>[] = [
    {
      accessorKey: "label",
      header: t("resourceLibrary.folderName"),
      cell: ({ row }) => {
        const folder = row.original;
        if (editingFolderId === folder.value) {
          return (
            <input
              className="border p-1 rounded flex-1 focus:outline-none focus:ring-2 focus:ring-orange-400"
              value={editingFolderName}
              onChange={(e) => setEditingFolderName(e.target.value)}
              disabled={loading}
              maxLength={30}
              style={{ minWidth: 0 }}
            />
          );
        }
        return (
          <span
            className="truncate font-medium text-gray-800"
            title={folder.label}
          >
            {folder.label}
          </span>
        );
      },
    },
    {
      id: "actions",
      header: t("resourceLibrary.actions"),
      cell: ({ row }) => {
        const folder = row.original;
        if (editingFolderId === folder.value) {
          return (
            <div className="flex gap-2">
              <Button
                size="sm"
                className="bg-orange-500 hover:bg-orange-600 text-white"
                onClick={() => {
                  console.log("Saving folder:", {
                    value: folder.value,
                    newName: editingFolderName,
                  });
                  onEditSave();
                }}
                disabled={loading || editingFolderName.trim().length === 0}
              >
                {loading ? "Saving..." : "Save"}
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={onEditCancel}
                disabled={loading}
              >
                {t("buttons.cancel")}
              </Button>
            </div>
          );
        }
        return typeof folder.value === "string" &&
          folder.value.trim() !== "" ? (
          <Button
            size="sm"
            variant="outline"
            onClick={() => onEditClick(folder.value, folder.label)}
            className="border-gray-300 hover:border-orange-400"
          >
             {t("buttons.edit")}
          </Button>
        ) : null;
      },
    },
  ];

  return (
    <div className="space-y-2">
      <DataTable
        columns={columns}
        data={comboData}
        FilterLabel={t("resourceLibrary.filterByFolderName")}
        FilterBy="label"
        actions={[]}
      />
    </div>
  );
};

export default FolderManager;
