import { StyleSheet } from "@react-pdf/renderer";

export const pdfStyle = StyleSheet.create({
  page: {
    flexDirection: "row",
    backgroundColor: "#ffffff",
    padding: 20,
  },
  section: {
    flexGrow: 1,
  },
  header: {
    flexDirection: "row",
    marginBottom: 20,
  },
  headerText: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#333333",
  },
  tableContainer: {
    border: 1,
    borderColor: "#bfbfbf",
    borderStyle: "solid",
  },
  tableRow: {
    flexDirection: "row",
  },
  tableHeader: {
    backgroundColor: "#17a2b8",
    color: "#ffffff",
    padding: 8,
    borderRightColor: "#bfbfbf",
    borderRightWidth: 1,
    borderBottomColor: "#bfbfbf",
    borderBottomWidth: 1,
    fontSize: 9,
  },
  tableCell: {
    padding: 8,
    borderRightColor: "#bfbfbf",
    borderRightWidth: 1,
    borderBottomColor: "#bfbfbf",
    borderBottomWidth: 1,
    fontSize: 8,
  },

  // Column Widths
  siNo: { width: "10%" }, // Small column for SI No
  question: { width: "70%" }, // Larger column for Questions
  category: { width: "50%" },
  mark: { width: "10%" }, // Medium column for Default Mark
  penalty: { width: "10%" }, // Medium column for Penalty Score

  emblem: {
    width: 80,
    height: 80,
    marginRight: 20,
  },
});
