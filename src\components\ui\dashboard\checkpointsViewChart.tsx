import React, { useEffect, useState } from "react";
import { Doughn<PERSON> } from "react-chartjs-2";
import Chart, { ArcElement, Legend, Tooltip } from "chart.js/auto";
import type {
  Checkpoint,
  CourseDetailsRequest,
  CourseModule,
  LineChartProps,
  Module,
} from "@/types";
import { Label } from "../label";
import { Combobox } from "../combobox";
import useCourse from "@/hooks/useCourse";
import {} from "../use-toast";
import useSessionViews from "@/hooks/useSessionViews";
import { Spinner } from "../progressiveLoader";
import { ORG_KEY } from "@/lib/constants";
import { useTranslation } from "react-i18next";
Chart.register(ArcElement, Tooltip, Legend);

export type CallbackCheckPointsType = (data: []) => void;

interface CheckpointsViewProps {
  courseIdSelected: string;
  checkpoints: Checkpoint[];
  backgroundColors: string[];
  borderColors: string[];
  handleClick: CallbackCheckPointsType;
  haveSessionData: (data: boolean) => void;
}

export function DBCheckpointsViewGraph({
  courseIdSelected,
  checkpoints,
  backgroundColors,
  borderColors,
  handleClick,
  haveSessionData,
}: CheckpointsViewProps): React.JSX.Element {
  const { t } = useTranslation();
  const { courseDetails } = useCourse();

  const { getSessionViews } = useSessionViews();
  const [courseId, setCourseId] = useState("");
  const [courseModuleId, setCourseModuleId] = useState("");
  const [courseModuleName, setCourseModuleName] = useState("");
  const [checkPointsList, setCheckPoints] = useState<Checkpoint[]>([]);
  const [isLoading, setIsLoading] = React.useState<boolean>(true);
  const [chartViewsData, setChartViewsData] = useState<LineChartProps>({
    labels: [],
    datasets: [
      {
        label: "Sessions",
        data: [],
        borderColor: [],
        backgroundColor: [],
        fill: false,
      },
    ],
  });
  backgroundColors = [
    "#ff6961",
    "#ffb480",
    "#f8f38d",
    "#42d6a4",
    "#08cad1",
    "#59adf6",
    "#9d94ff",
    "#c780e8",
  ];
  const [modulesData, setModulesData] = useState<
    { value: string; label: string }[]
  >([]);

  const chartLabels = [] as string[];
  const chartDataColl = [] as number[];

  checkpoints?.forEach((item) => {
    chartLabels.push(item.checkpoint_name);
    chartDataColl.push(item.sessions.length);
  });

  let chartData: LineChartProps = {
    labels: chartLabels,
    datasets: [
      {
        label: "Sessions",
        data: chartDataColl,
        borderColor: borderColors,
        backgroundColor: backgroundColors,
        fill: true,
      },
    ],
  };

  useEffect(() => {
    setChartViewsData({
      labels: [],
      datasets: [
        {
          label: "Sessions",
          data: [],
          borderColor: [],
          backgroundColor: [],
          fill: false,
        },
      ],
    });
    setCourseId(courseIdSelected);
    setModulesData([]);
    setCourseModuleId("");

    const fetchModulesData = async (): Promise<void> => {
      try {
        if (courseIdSelected !== "") {
          const orgId = localStorage.getItem(ORG_KEY);
          const reqParams: CourseDetailsRequest = {
            course_id: courseIdSelected as string,
            org_id: orgId ?? "",
          };
          const response = await courseDetails(reqParams);
          if (
            response.length > 0 ||
            response.length === undefined ||
            Array.isArray(response)
          ) {
            const modulesList = response.reduce(
              (accumulator: CourseModule[], item) => {
                return accumulator.concat(item.modules);
              },
              [],
            );
            if (modulesList.length > 0) {
              const filteredModulesMap = modulesList.filter(
                (module) => module.module_type === "Url",
              );

              const filteredModules = filteredModulesMap.map((item) => ({
                value: item.course_module_id ?? "",
                label: item.module_name ?? "",
              }));
              if (filteredModules.length > 0) {
                setModulesData(filteredModules);
                setCourseModuleName(filteredModules[0]?.label ?? "");
                setCourseModuleId(filteredModules[0]?.value ?? "");
              } else {
                setModulesData([]);
              }
            }
          }
        }
      } catch (error: unknown) {
        console.error("Error fetching data:");
      }
    };
    fetchModulesData().catch((error) => console.log(error));
  }, [courseIdSelected]);

  const handleOnModuleSelected = (moduleIdSelected: string): void => {
    setCourseModuleId(moduleIdSelected);
    chartData = {
      labels: [],
      datasets: [
        {
          label: "Sessions",
          data: [],
          borderColor: [],
          backgroundColor: [],
          fill: true,
        },
      ],
    };
    setChartViewsData(chartData);
    handleSession(false);
  };

  const handleSession = (data: boolean): void => {
    haveSessionData(data);
  };

  useEffect(() => {
    const chartLabels = [] as string[];
    const chartDataColl = [] as number[];
    setCheckPoints([]);

    const fetchSessionsData = async (): Promise<void> => {
      setIsLoading(false);
      try {
        if (courseId !== "" && courseModuleId !== "") {
          setIsLoading(true);
          const sessionViewsInfo = await getSessionViews(
            courseId,
            courseModuleId,
            null,
          );
          if (sessionViewsInfo.session_data === null) {
            handleSession(false);
          } else {
            handleSession(true);
          }

          const sessionViews = sessionViewsInfo.session_data.reduce(
            (accumulator: Module[], item) => {
              return accumulator.concat(item.modules);
            },
            [],
          );

          const checkPointsViews = sessionViews.reduce(
            (accumulator: Checkpoint[], item) => {
              return accumulator.concat(item.checkpoints);
            },
            [],
          );
          setCheckPoints(checkPointsViews);

          checkPointsViews?.forEach((item) => {
            chartLabels.push(item.checkpoint_name);
            chartDataColl.push(item.sessions.length);
          });

          chartData = {
            labels: chartLabels,
            datasets: [
              {
                label: "Sessions",
                data: chartDataColl,
                borderColor: borderColors,
                backgroundColor: backgroundColors,
                fill: true,
              },
            ],
          };

          setChartViewsData(chartData);
          setIsLoading(false);
        }
      } catch (error: unknown) {
        console.error("Error fetching data : " + error);
        setIsLoading(false);
      }
    };
    fetchSessionsData().catch((error) => console.log(error));
  }, [courseModuleId]);

  return (
    <div>
      <div className=" rounded-lg shadow-lg bg-white relative overflow-hidden">
        {/* Header Section */}
        <div className="p-2 border-b flex justify-between items-center dashboard-session text-black rounded-t-lg">
          <h3 className="text-md font-semibold">{String(t("dashboard.checkpointViews"))}</h3>
        </div>

        {/* Select Video Section */}
        <div className="p-2 space-y-4" style={{ height: "450px" }}>
          <div className="w-full flex text-sm text-muted-foreground">
            <div className="w-full flex flex-col space-y-2">
              <Label>{String(t("dashboard.selectVideo"))}</Label>
              <Combobox
                data={modulesData}
                onSelectChange={handleOnModuleSelected}
                placeHolder="Select Video"
                defaultLabel={courseModuleName}
              />
            </div>
          </div>

          {/* Chart Section */}
          <div className="flex justify-center">
            {isLoading ? (
              <Spinner />
            ) : (
              <Doughnut
                data={chartViewsData}
                height={300}
                options={{
                  maintainAspectRatio: false,
                  onHover: function (event) {
                    const target = event.native?.target;
                    if (target instanceof HTMLElement) {
                      target.style.cursor = "pointer";
                    }
                  },
                  onClick: function (evt, element) {
                    if (element.length > 0) {
                      const ind = element[0]?.index;
                      handleClick(checkPointsList[ind].sessions as []);
                    } else {
                      handleClick([]);
                    }
                  },
                }}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
