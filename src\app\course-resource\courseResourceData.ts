const courseResourceData = [
  {
    name: "Week 3",
    modules: [
      {
        instance: "1009d4e9-b7c0-4036-9c9a-d3c1440dead3",
        progress: 0,
        course_id: "5c246a6b-e48d-4bc7-80ab-61fa73572fe0",
        module_id: "dd33330a-7ab4-452b-b8bd-9e37192f0ffb",
        section_id: "c2e1fa1e-95db-437d-bbf2-a7e5eb136baa",
        time_spent: "00:00:00",
        module_name: "Natural Science Exam -1",
        module_type: "Quiz",
        module_source: "",
        marked_as_done: false,
        attempts_remaining: 250,
      },
      {
        instance: "b7d542fc-ae90-4847-8811-975017e2cc8d",
        progress: 0,
        course_id: "5c246a6b-e48d-4bc7-80ab-61fa73572fe0",
        module_id: "0098bc68-069c-4d19-930c-f4ad03a95bd8",
        section_id: "c2e1fa1e-95db-437d-bbf2-a7e5eb136baa",
        time_spent: "00:00:00",
        module_name: "Natural Science 15 minute video",
        module_type: "Url",
        module_source: "Video",
        marked_as_done: false,
        attempts_remaining: 0,
      },
      {
        instance: "aecb0f28-6c44-4126-b528-aa9ba329169d",
        progress: 0,
        course_id: "5c246a6b-e48d-4bc7-80ab-61fa73572fe0",
        module_id: "0098bc68-069c-4d19-930c-f4ad03a95bd8",
        section_id: "c2e1fa1e-95db-437d-bbf2-a7e5eb136baa",
        time_spent: "00:00:00",
        module_name: "15 Minute Video - Checkpoints - NEW",
        module_type: "Url",
        module_source: "Video",
        marked_as_done: false,
        attempts_remaining: 0,
      },
      {
        instance: "d9f71291-9ce2-44e3-b62c-87defc3e5c03",
        progress: 0,
        course_id: "5c246a6b-e48d-4bc7-80ab-61fa73572fe0",
        module_id: "0098bc68-069c-4d19-930c-f4ad03a95bd8",
        section_id: "c2e1fa1e-95db-437d-bbf2-a7e5eb136baa",
        time_spent: "00:00:00",
        module_name: "Artic Animals - 20 minutes video",
        module_type: "Url",
        module_source: "Video",
        marked_as_done: false,
        attempts_remaining: 0,
      },
      {
        instance: "a0bd3dea-fc1d-412c-b00c-8f4bdabc71f4",
        progress: 0,
        course_id: "5c246a6b-e48d-4bc7-80ab-61fa73572fe0",
        module_id: "0098bc68-069c-4d19-930c-f4ad03a95bd8",
        section_id: "c2e1fa1e-95db-437d-bbf2-a7e5eb136baa",
        time_spent: "00:00:00",
        module_name: "Artic Animals - 20 minutes video - random false",
        module_type: "Url",
        module_source: "Video",
        marked_as_done: false,
        attempts_remaining: 0,
      },
      {
        instance: "8cf54c59-46ca-4b58-946b-21b0d339bbbf",
        progress: 0,
        course_id: "5c246a6b-e48d-4bc7-80ab-61fa73572fe0",
        module_id: "0098bc68-069c-4d19-930c-f4ad03a95bd8",
        section_id: "c2e1fa1e-95db-437d-bbf2-a7e5eb136baa",
        time_spent: "00:00:00",
        module_name: "Artic Animals - For Kids",
        module_type: "Url",
        module_source: "Video",
        marked_as_done: false,
        attempts_remaining: 0,
      },
      {
        instance: "a1d9121b-d771-416f-9b5b-b6ae3ef4e222",
        progress: 0,
        course_id: "5c246a6b-e48d-4bc7-80ab-61fa73572fe0",
        module_id: "0098bc68-069c-4d19-930c-f4ad03a95bd8",
        section_id: "c2e1fa1e-95db-437d-bbf2-a7e5eb136baa",
        time_spent: "00:00:00",
        module_name: "Natural Science Video Nov 6",
        module_type: "Url",
        module_source: "Video",
        marked_as_done: false,
        attempts_remaining: 0,
      },
    ],
    summary: "",
    course_id: "5c246a6b-e48d-4bc7-80ab-61fa73572fe0",
    resources: [null],
    section_id: "c2e1fa1e-95db-437d-bbf2-a7e5eb136baa",
    section_order: 3,
  },
];

export default courseResourceData;
