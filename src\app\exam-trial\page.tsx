"use client";
import React, { useEffect, useState } from "react";
import MainLayout from "../layout/mainlayout";
import ExamTrialQuestions from "../exam-trial-questions/exam-trial";
import { Button } from "@/components/ui/button";
import { useSearchParams } from "next/navigation";
import useExamDetails from "@/hooks/useExamDetails";
import { useToast } from "@/components/ui/use-toast";
import type {
  ErrorCatch,
  ExamDetailsType,
  LoginUserData,
  LogUserActivityRequest,
  startQuizAttemptType,
  ToastType,
} from "@/types";
import HorizontalStepper from "@/components/ui/horizontalStepper";
import { Timer } from "lucide-react";
import { Spinner } from "@/components/ui/progressiveLoader";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";
import { ERROR_MESSAGES } from "@/lib/messages";

const Sample: React.FC = () => {
  const { t } = useTranslation();
  const { toast } = useToast() as ToastType;
  const [examStarted, setExamStarted] = useState<boolean>(false);
  const [stepsIndex, setStepsIndex] = useState<number>(0);
  const [questions, setQuestions] = useState<ExamDetailsType[]>([]);
  const [timerActive, setTimerActive] = useState(false);
  const [isSubmit, setIsSubmit] = useState(false);
  const [duration, setDuration] = useState<number>();
  const [currentDate, setcurrentDate] = useState<Date>();
  const [seconds, setSeconds] = useState<number>(0);
  const [quizAttemptId, setQuizAttemptId] = useState<string>("");
  const [numberOfQuestions, setNumberOfQuestions] = useState<number>(0);
  const [isLoading, setIsLoading] = React.useState<boolean>(true);
  const [examName, setExamName] = useState<string>("");
  const [buttonDisabled, setButtonDisabled] = useState(false);

  const { updateUserActivity } = useLogUserActivity();

  const startTimer = (): void => {
    setTimerActive(true);
  };

  const startExam = (): void => {
    startExamAttempt();
    setExamStarted(true);
  };

  const stepperData = {
    id: stepsIndex,
    stepNo: numberOfQuestions,
    StepLabel: t("exams.question"),
    key: stepsIndex,
    stepValue: [
      {
        value: t("exams.multipleChoice"),
        label: t("exams.multipleChoice"),
        completed: true,
      },
    ],
  };

  const { getQuestions, startQuizAttempt } = useExamDetails();
  const searchParams = useSearchParams();
  const examId = searchParams.get("type") ?? "";
  const showQuestions = (step: number): void => {
    setStepsIndex(step);
  };
  const updateCurrentTime = (): void => {
    setcurrentDate(new Date());
  };

  useEffect(() => {
    setcurrentDate(new Date());
    getExamQuestions();
  }, []); // Empty dependency array ensures this effect runs only once on mount

  useEffect(() => {
    if (examStarted) {
      startTimer();
    }
    let intervalId: NodeJS.Timeout;
    setInterval(() => {
      updateCurrentTime();
    }, 1000);
    if (timerActive && seconds > 0) {
      intervalId = setInterval(() => {
        setSeconds((prevSeconds) => {
          if (prevSeconds === 1) {
            clearInterval(intervalId);
          }
          return prevSeconds - 1;
        });
        updateCurrentTime();
      }, 1000);
    }
    // Cleanup the interval on component unmount or when the timer is stopped
    return () => clearInterval(intervalId);
  }, [timerActive, seconds, examStarted]);

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  const getExamQuestions = (): void => {
    const fetchData = async (): Promise<void> => {
      const quiz_id = examId;
      try {
        const questionsData = await getQuestions(quiz_id);
        setIsLoading(false);
        if (questionsData !== null && questionsData !== undefined) {
          setDuration(questionsData[0]?.duration);
          setExamName(questionsData[0]?.name);
          const extractedData: ExamDetailsType[] =
            questionsData[0].quest_answers.map((question) => ({
              name: question.name,
              default_mark: question.default_mark,
              penalty: question.penalty,
              question_text: question.question_text,
              question_type: question.question_type,
              question_id: question.question_id,
              answers: question.answers,
              ansMarked: false,
            }));
          setSeconds(questionsData[0].duration * 60);
          setQuestions(extractedData);
          setNumberOfQuestions(questionsData[0].quest_answers.length);
        }
      } catch (error: unknown) {
        setIsLoading(false);
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
      }
    };

    fetchData().catch((error) => console.log(error));
  };
  const startExamAttempt = (): void => {
    const ExamData = async (): Promise<void> => {
      const orgId = localStorage.getItem("orgId");
      let user_id = "";
      const userDetails = localStorage.getItem("userDetails");

      if (userDetails !== null && userDetails !== undefined) {
        const users = JSON.parse(userDetails) as LoginUserData;
        user_id = users?.id;
      }

      const passData = {
        org_id: orgId,
        quiz_id: examId,
        user_id: user_id,
        user_start_time: new Date(),
      };

      try {
        const result = await startQuizAttempt(passData as startQuizAttemptType);
        if (result.status === "success") {
          const quizAttemptId = result.quiz_attempt_id;
          setQuizAttemptId(quizAttemptId as string);
          const params = {
            activity_type: "Exam",
            screen_name: "Exam",
            action_details: "Exam started",
            target_id: examId as string,
            log_result: "SUCCESS",
          };
          void updateLogUserActivity(params).catch((error) => {
            console.error(error);
          });
        } else {
          const params = {
            activity_type: "Exam",
            screen_name: "Exam",
            action_details: "Failed to start exam",
            target_id: examId as string,
            log_result: "ERROR",
          };
          void updateLogUserActivity(params).catch((error) => {
            console.error(error);
          });
        }
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
      }
    };
    ExamData().catch((error) => console.log(error));
  };
  const onSecondChange = (): void => {
    setIsSubmit(false);
  };

  function finishExam(): void {
    setIsSubmit(true);
    setButtonDisabled(true);
    setExamStarted(false);
  }

  const formatTimeRemaining = (timeInSeconds: number): string => {
    const minutes = Math.floor(timeInSeconds / 60);
    const remainingSeconds = timeInSeconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  return (
    <MainLayout>
      <div className="w-full">
        <h1 className="text-2xl font-semibold tracking-tight mb-4">
          {t("exams.examTrialRun")}
        </h1>
        {isLoading ? (
          <Spinner />
        ) : (
          <div className="w-full border rounded-md ps-4 pb-4 pe-4 mb-2 bg-[#fff]">
            <div className="flex flex-row justify-between items-center mt-7 ">
              <Button
                className="primary"
                onClick={startExam}
                disabled={examStarted}
              >
                {t("exams.start")}
              </Button>
              <Button
                className="bg-[#9FC089]"
                onClick={finishExam}
                disabled={!examStarted || buttonDisabled}
              >
                {t("exams.submit")}
              </Button>
            </div>
            <div className="text-center">
              <h1 className="text-2xl font-bold">{examName}</h1>
            </div>

            <div className="p-5">
              <div className="flex flex-row justify-between mt-7">
                <label>
                  {t("exams.duration:")} {duration}
                </label>
                <label>
                  <p style={{ display: "flex", alignItems: "center" }}>
                    <span
                      role="img"
                      aria-label="timer"
                      style={{ marginRight: "5px" }}
                    >
                      <Timer />
                    </span>
                    <label className="font-bold">
                      {t("exams.timeRemaining")} {formatTimeRemaining(seconds)}
                    </label>
                  </p>
                </label>
                <label>
                  {t("exams.date")} {currentDate?.toLocaleString()}
                </label>
              </div>
              <main>
                <HorizontalStepper
                  stepLabel={[]}
                  stepperData={stepperData}
                  component={
                    <ExamTrialQuestions
                      index={stepsIndex}
                      data={questions}
                      quizAttemptId={quizAttemptId}
                      examId={examId}
                      seconds={seconds}
                      onSecondChange={onSecondChange}
                      isFinished={isSubmit}
                      isStarted={examStarted}
                    />
                  }
                  onStepChange={(step) => {
                    showQuestions(step);
                  }}
                />
              </main>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  );
};

export default Sample;
