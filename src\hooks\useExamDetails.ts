import { supabase } from "../lib/client";
import type {
  ExamDetails,
  cleanExamResponseType,
  examReviewType,
  startQuizAttemptType,
  startQuizResponseType,
  submitQuizResponseType,
  submitQuizType,
  ErrorType,
} from "@/types";
import { rpc } from "@/lib/apiConfig";

interface useExamDetailsReturn {
  getQuestions: (examId?: string | null) => Promise<ExamDetails[]>;
  getQuestionsOfQuiz: (examId?: string | null) => Promise<ExamDetails[]>;
  startQuizAttempt: (
    examData?: startQuizAttemptType,
  ) => Promise<startQuizResponseType>;
  submitQuiz: (examData?: submitQuizType) => Promise<submitQuizResponseType>;
  evaluateAnswer: (examData?: submitQuizType) => Promise<examReviewType[]>;
  clearExam: (examData?: submitQuizType) => Promise<cleanExamResponseType>;
}

const useExamDetails = (): useExamDetailsReturn => {
  async function getQuestions(examId?: string | null): Promise<ExamDetails[]> {
    const params = {
      quiz_id: examId,
    };
    try {
      const { data, error } = (await supabase.rpc(
        rpc.getQuestionsOfQuiz,
        params,
      )) as {
        data: ExamDetails[];
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as ExamDetails[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function getQuestionsOfQuiz(examId?: string | null): Promise<ExamDetails[]> {
    const params = {
      quiz_id: examId,
    };
    try {
      const { data, error } = (await supabase.rpc(
        rpc.getQuestionsOfQuizFn,
        params,
      )) as {
        data: ExamDetails[];
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as ExamDetails[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function startQuizAttempt(
    examData?: startQuizAttemptType,
  ): Promise<startQuizResponseType> {
    const params = examData;
    try {
      const { data, error } = (await supabase.rpc(
        rpc.startDryRunQuiz,
        params,
      )) as {
        data: startQuizResponseType;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as startQuizResponseType;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function submitQuiz(
    examData?: submitQuizType,
  ): Promise<submitQuizResponseType> {
    const params = examData;
    try {
      const { data, error } = (await supabase.rpc(
        rpc.submitQuizAnswers,
        params,
      )) as {
        data: submitQuizType;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as submitQuizResponseType;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function evaluateAnswer(
    examData?: submitQuizType,
  ): Promise<examReviewType[]> {
    const params = examData;
    try {
      const { data, error } = (await supabase.rpc(
        rpc.getQuizRightAnswers,
        params,
      )) as {
        data: examReviewType[];
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as examReviewType[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function clearExam(
    examData?: submitQuizType,
  ): Promise<cleanExamResponseType> {
    const params = examData;
    try {
      const { data, error } = (await supabase.rpc(
        rpc.cleanupDryRunQuizes,
        params,
      )) as {
        data: cleanExamResponseType;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as cleanExamResponseType;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  return {
    getQuestions,
    getQuestionsOfQuiz,
    startQuizAttempt,
    submitQuiz,
    evaluateAnswer,
    clearExam,
  };
};

export default useExamDetails;
