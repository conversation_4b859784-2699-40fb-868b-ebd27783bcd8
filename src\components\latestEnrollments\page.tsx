"use client";
import React, { useState, useEffect } from "react";
import { getColumns } from "./columns";
import { DataTable } from "@/components/ui/data-table/data-table";
import type { LatestEnrollmentResponse } from "@/types";
import { Spinner } from "@/components/ui/progressiveLoader";
import useDashboardStatsViews from "@/hooks/useDashboardStats";
import { useTranslation } from "react-i18next";

export default function LatestEnrollments(): React.JSX.Element {
  const { t } = useTranslation();
  const columns = getColumns(t);
  const { getLatestEnrollments } = useDashboardStatsViews();
  const [enrollments, setEnrollments] = useState<LatestEnrollmentResponse[]>(
    [],
  );
  const [isLoading, setIsLoading] = React.useState<boolean>(true);

  useEffect(() => {
    const fetchData = async (): Promise<void> => {
      setIsLoading(true);
      try {
        const enrollmentList = await getLatestEnrollments();
        setEnrollments(enrollmentList);
        setIsLoading(false);
      } catch (error) {
        setIsLoading(false);
      }
    };
    fetchData().catch((error) => console.log(error));
  }, []);

  return (
    <div>
      <div
        className="rounded-lg shadow-lg bg-white relative overflow-hidden"
        style={{ height: "450px", overflow: "auto" }}
      >
        <div className="p-2 border-b flex justify-between items-center dashboard-session text-black rounded-t-lg">
          <h3 className="text-md font-semibold">
            {" "}
            {String(t("dashboard.recentEnrollments.title"))}
          </h3>
        </div>
        <div>
          {isLoading ? (
            <Spinner />
          ) : (
            <div className="p-2">
              <div className="overflow-x-auto border rounded-md p-2 ">
                <DataTable
                  columns={columns}
                  data={enrollments}
                  FilterLabel={t("dashboard.recentEnrollments.filterByName")}
                  FilterBy={"first_name"}
                  actions={[]}
                />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
