import { useForm } from "react-hook-form";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { ModalButton } from "@/components/ui/modalButton";
import React, { useEffect } from "react";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import type {
  ErrorCatch,
  LogUserActivityRequest,
  PPTCheckPointForm,
  PPTCheckPointUpdateForm,
  ToastType,
  UpdateCheckPointRequest,
  UpdatePPTCheckPoint,
} from "@/types";
import useCourse from "@/hooks/useCourse";
import { useToast } from "@/components/ui/use-toast";
import { UpdatePPTCheckPointSchema } from "@/schema/schema";
import { zodResolver } from "@hookform/resolvers/zod";
import { But<PERSON> } from "@/components/ui/button";
import { PPTCheckPointDetails } from "./add-ppt-checkpoint-details";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
// zod validation todo
export const AddCheckpointPPTModal: React.FC<PPTCheckPointUpdateForm> = ({
  closeDialog,
  checkPointData,
  checkpointTime,
  onAddedValuesChange,
  allCheckPointData,
  coursePPTModuleId,
  existingCheckPoints,
}): React.JSX.Element => {
  const { t } = useTranslation();
  const { toast } = useToast() as ToastType;
  const [showCheckpoint, setShowcheckpoint] = React.useState(false);
  const [randomEnabled, setRandomenabled] = React.useState(false);
  const [addCheckPoint, setAddCheckpoint] = React.useState(false);
  const [addMilestoneData, setAddMilestoneData] = React.useState(false);
  const [checkPointNumber, setCheckpointNumber] = React.useState(0);
  const [showCheckpointForm, setShowCheckpointForm] = React.useState(true);
  const [addedValues, setAddedValues] = React.useState<PPTCheckPointForm[]>([]);
  const { updateCheckpoint } = useCourse();
  const { updateUserActivity } = useLogUserActivity();

  const form = useForm<UpdatePPTCheckPoint>({
    resolver: zodResolver(UpdatePPTCheckPointSchema),
    defaultValues: {
      checkpointnumber: checkPointData?.num_of_checkpoints,
      randomenabled: checkPointData?.is_random_checkpoint,
      showcheckpoint: checkPointData?.is_checkpoint_enabled,
    },
  });

  useEffect(() => {
    if (
      checkPointData?.length !== null &&
      checkPointData?.is_random_checkpoint !== null
    ) {
      isRandomenabled(checkPointData?.is_random_checkpoint?.toString());
    }
    localStorage.setItem(
      "checkpointNoPPT",
      `${checkPointData?.num_of_checkpoints ?? 0}`,
    );
  }, []);

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  async function onSubmit(formdata: UpdatePPTCheckPoint): Promise<void> {
    setCheckpointNumber(formdata.checkpointnumber);
    console.log(coursePPTModuleId);
    localStorage.setItem(
      "checkpointNoPPT",
      `${formdata.checkpointnumber ?? 0}`,
    );
    const data: UpdateCheckPointRequest = {
      checkpoint_data: {
        is_checkpoint_enabled: true,
        num_of_checkpoints: formdata.checkpointnumber,
        is_random_checkpoint: randomEnabled,
        always_show_checkpoints: showCheckpoint,
      },
      course_module_id: checkPointData?.course_module_id,
      // course_module_id: coursePPTModuleId,
      instance_id: checkPointData?.id,
      org_id: checkPointData?.org_id,
    };

    try {
      const result = await updateCheckpoint(data);

      if (result.status === "success") {
        setShowCheckpointForm(false);
        setAddMilestoneData(true);
        setAddCheckpoint(!addCheckPoint);
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.toast_success_title"),
          description: t("successMessages.checkpoint_updated"),
        });
        const params = {
          activity_type: "Course",
          screen_name: "Course",
          action_details: "Checkpoint added successfully",
          target_id: checkPointData.course_id as string,
          log_result: "SUCCESS",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
        // closeDialog(false);
      } else if (result.status === "error") {
        setAddCheckpoint(!addCheckPoint);
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: result.status,
        });
        const params = {
          activity_type: "Course",
          screen_name: "Course",
          action_details: "Failed to add checkpoint",
          target_id: checkPointData.course_id as string,
          log_result: "ERROR",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      }
    } catch (error) {
      setAddCheckpoint(!addCheckPoint);
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
      console.error("An unexpected error occurred:", error);
    }
  }

  const isRandomenabled = (randomenabledType: string): void => {
    {
      randomenabledType == "true"
        ? setRandomenabled(true)
        : setRandomenabled(false);
    }
  };
  const isShowcheckpoint = (showcheckpointType: string): void => {
    {
      showcheckpointType == "true"
        ? setShowcheckpoint(true)
        : setShowcheckpoint(false);
    }
  };
  const handleAddMilestone = (): void => {
    setShowCheckpointForm(false);
    setAddMilestoneData(!addMilestoneData);
  };
  const handleAddedValuesChange = (values: PPTCheckPointForm[]): void => {
    onAddedValuesChange(values);
    setAddedValues(values);
  };

  useEffect(() => {
    if (allCheckPointData.length > 0) {
      setAddMilestoneData(true);
    }
  }, [allCheckPointData]);

  return (
    <div className="border rounded-md p-4 mt-4">
      {showCheckpointForm &&
        addedValues.length === 0 &&
        allCheckPointData.length < 1 && (
          <>
            <Form {...form}>
              <form
                onSubmit={(event) => void form.handleSubmit(onSubmit)(event)}
                className="space-y-8"
              >
                <div className="w-full flex flex-wrap">
                  <div className="w-full sm:w-1/3 pr-4">
                    <FormField
                      control={form.control}
                      name="checkpointnumber"
                      render={({ field }) => (
                        <FormItem className="space-y-3">
                          <FormLabel>
                            {t("courses.courseModule.numberOfCheckpoints")}
                          </FormLabel>
                          <FormControl>
                            <Input
                              autoComplete="off"
                              type="number"
                              placeholder={t(
                                "courses.courseModule.numberOfCheckpoints",
                              )}
                              min={1}
                              {...field}
                              disabled={checkPointData?.is_checkpoint_enabled}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="w-full sm:w-1/3 pr-4 ps-8">
                    <FormField
                      control={form.control}
                      name="randomenabled"
                      render={({ field }) => (
                        <FormItem className="space-y-3">
                          <FormLabel>
                            {t("courses.courseModule.randomEnabled")}
                          </FormLabel>
                          <FormControl>
                            <RadioGroup
                              onValueChange={(value) => {
                                field.onChange(
                                  value === "true"
                                    ? true || value === "false"
                                    : false,
                                );
                                isRandomenabled(value);
                              }}
                              defaultValue={
                                checkPointData?.is_random_checkpoint
                                  ? checkPointData?.is_random_checkpoint.toString()
                                  : "false"
                              }
                              className="flex flex-row space-x-3"
                            >
                              <FormItem className="flex items-center space-x-3 space-y-0">
                                <FormControl>
                                  <RadioGroupItem
                                    value="true"
                                    disabled={
                                      checkPointData?.is_checkpoint_enabled ===
                                      true
                                    }
                                  />
                                </FormControl>
                                <FormLabel className="font-normal">
                                  Yes
                                </FormLabel>
                              </FormItem>
                              <FormItem className="flex items-center space-x-3 space-y-0">
                                <FormControl>
                                  <RadioGroupItem
                                    value="false"
                                    disabled={
                                      checkPointData?.is_checkpoint_enabled ===
                                      true
                                    }
                                  />
                                </FormControl>
                                <FormLabel className="font-normal">
                                  No
                                </FormLabel>
                              </FormItem>
                            </RadioGroup>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="w-full sm:w-1/3">
                    <FormField
                      control={form.control}
                      name="showcheckpoint"
                      render={({ field }) => (
                        <FormItem className="space-y-3">
                          <FormLabel>
                            {t("courses.courseModule.alwaysShowCheckpoint")}
                          </FormLabel>
                          <FormControl>
                            <RadioGroup
                              onValueChange={(value) => {
                                field.onChange(
                                  value === "true"
                                    ? true || value === "false"
                                    : false,
                                );
                                isShowcheckpoint(value);
                              }}
                              defaultValue={
                                checkPointData?.is_checkpoint_enabled
                                  ? checkPointData?.is_checkpoint_enabled.toString()
                                  : "false"
                              }
                              className="flex flex-row space-x-3"
                            >
                              <FormItem className="flex items-center space-x-3 space-y-0">
                                <FormControl>
                                  <RadioGroupItem
                                    value="true"
                                    disabled={
                                      checkPointData?.is_checkpoint_enabled ===
                                      true
                                    }
                                  />
                                </FormControl>
                                <FormLabel className="font-normal">
                                  Yes
                                </FormLabel>
                              </FormItem>
                              <FormItem className="flex items-center space-x-3 space-y-0">
                                <FormControl>
                                  <RadioGroupItem
                                    value="false"
                                    disabled={
                                      checkPointData?.is_checkpoint_enabled ===
                                      true
                                    }
                                  />
                                </FormControl>
                                <FormLabel className="font-normal">
                                  No
                                </FormLabel>
                              </FormItem>
                            </RadioGroup>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
                {checkPointData?.is_checkpoint_enabled === false && (
                  <ModalButton
                    closeDialog={(value: boolean) => closeDialog(value)}
                    closeLabel={t("buttons.cancel")}
                    submitLabel={t("buttons.submit")}
                  />
                )}
              </form>
            </Form>
            {(checkPointData?.is_checkpoint_enabled === true ||
              addCheckPoint) && (
              <div className="w-full flex ">
                <div className="flex justify-end w-full">
                  {" "}
                  <Button onClick={handleAddMilestone}>
                    {t("buttons.addCheckpoint")}
                  </Button>
                </div>
              </div>
            )}
          </>
        )}

      {addMilestoneData && (
        <PPTCheckPointDetails
          closeMilestoneDialog={closeDialog}
          checkpointNumber={
            checkPointData?.num_of_checkpoints > 0
              ? checkPointData?.num_of_checkpoints
              : checkPointNumber
          }
          randomEnabledStatus={randomEnabled}
          checkpointTime={checkpointTime}
          onAddedValuesChange={handleAddedValuesChange}
          allCheckPointData={allCheckPointData}
          videoLength={""}
          existingCheckPoints={existingCheckPoints}
        />
      )}
    </div>
  );
};
