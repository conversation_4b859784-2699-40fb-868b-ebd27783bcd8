"use client";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useForm } from "react-hook-form";
import React, { useEffect } from "react";

import { Editor } from "primereact/editor";
import type {
  CourseDetailsRequest,
  ErrorCatch,
  PageContentForm,
  PageContentRequest,
  SectionListResponse,
  ToastType,
  richTextType,
} from "@/types";
import useCourse from "@/hooks/useCourse";
import { useSearchParams, useRouter } from "next/navigation";
import { useToast } from "@/components/ui/use-toast";
import { ORG_KEY, pageUrl } from "@/lib/constants";
import { zodResolver } from "@hookform/resolvers/zod";
import { AddPageContentSchema } from "@/schema/schema";
import { Checkbox } from "@/components/ui/checkbox";
import { useTranslation } from "react-i18next";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";

export default function ResourceExamAddPage(): React.JSX.Element {
  const [selCourseId, setCourseId] = React.useState<string>("");
  const form = useForm<PageContentForm>({
    resolver: zodResolver(AddPageContentSchema),
  });
  const { t } = useTranslation();
  const { toast } = useToast() as ToastType;
  const searchParams = useSearchParams();
  const { getCourseList, addPageContent, getSectionList } = useCourse();
  const router = useRouter();
  const [selSectionId, setSectionId] = React.useState<string>("");
  const type = searchParams.get("sectionId");
  const [richTextValues, setRichTextValues] = React.useState<
    richTextType | undefined
  >(undefined);

  async function onSubmit(data: PageContentForm): Promise<void> {
    const org_id = localStorage.getItem("orgId");
    const course_id = localStorage.getItem("courseId");
    if (data?.pageTitle !== null && /^[0-9,-]*$/.test(data?.pageTitle)) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.enterValidDate"),
      });
      return;
    }

    const tagRemovedContent = data?.pageContent?.htmlValue.replace(
      /(<([^>]+)>)/gi,
      "",
    );

    if (
      data?.pageContent.htmlValue !== null &&
      /^[^\w\s]*$/.test(tagRemovedContent)
    ) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.addValidContent"),
      });
      return;
    }

    const PageContent: PageContentRequest = {
      course_id: course_id ?? "",
      section_id: type ?? "",
      org_id: org_id ?? "",
      page_data: {
        name: data?.pageTitle,
        content: data?.pageContent?.htmlValue,
        is_premium: data?.is_premium ?? false,
      },
    };
    try {
      const result = await addPageContent(PageContent);
      const type = searchParams.get("sectionId");

      if (result.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.toast_success_title"),
          description: t("successMessages.addPageContent"),
        });
        router.push(`${pageUrl.resourceListView}?sectionId=${type}`);
      } else if (result.status === "error") {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: result.status,
        });
        console.log("API Error:", result.status);
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.details,
      });
      console.error("An unexpected error occurred:", error);
    }
  }

  const setRichTextValue = (richTextValue: richTextType | undefined): void => {
    if (richTextValue && richTextValue.htmlValue == null) {
      richTextValue = undefined;
    }
    form.setValue("pageContent", richTextValue as { htmlValue: string });

    setRichTextValues(richTextValue);
    console.log("rich value-------", richTextValues);
  };

  useEffect(() => {
    const fetchCourseData = async (): Promise<void> => {
      try {
        const courses = await getCourseList("");
        const course_id = localStorage.getItem("courseId");
        const orgId = localStorage.getItem(ORG_KEY);
        const reqParams: CourseDetailsRequest = {
          course_id: course_id as string,
          org_id: orgId ?? "",
        };
        const section = await getSectionList(reqParams);

        if (courses !== null && courses !== undefined) {
          const CourseDetails = courses.find(
            (course) => course.course_id === course_id,
          );

          if (CourseDetails) {
            const filteredCourses = CourseDetails?.full_name ?? "";
            setCourseId(filteredCourses);
          }
        }
        if (section !== null && section !== undefined) {
          const sectionDet: SectionListResponse[] = section;

          if (sectionDet.length > 0) {
            const sectionDetailResponse: SectionListResponse = sectionDet[0];
            const validSections = sectionDetailResponse.sections ?? [];

            if (validSections.length > 0) {
              const sectionDetails = validSections.find(
                (section) => section.section_id === type,
              );
              if (sectionDetails) {
                const filteredSection = sectionDetails?.name ?? "";
                setSectionId(filteredSection);
              }
            }
          }
        }
      } catch (error) {
        console.log("Error fetching courses");
      }
    };

    fetchCourseData().catch((error) => console.log(error));
  }, [selCourseId]);

  const handleCancel = (): void => {
    const type = searchParams.get("sectionId");
    router.push(`${pageUrl.resourceListView}?sectionId=${type}`);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    const value = e.target.value;
    const sanitizedValue = value
      .trimStart()
      .replace(/^\p{P}+/u, "")
      .replace(/[^\p{L}\p{M}\p{N}\s-]/gu, "")
      .replace(/\s{2,}/g, " ")
      .replace(/^[^\p{L}\p{M}\p{N}]|[^\p{L}\p{M}\p{N}\s-]/gu, "")
      .replace(/\s+$/, (match) =>
        match.length > 1 ? match.slice(0, -1) : match,
      );
    form.setValue("pageTitle", sanitizedValue);
  };

  return (
    <>
      {" "}
      <div className="w-full mb-4 mt-4">
        <h4 className="text-2xl tracking-tight">
          {t("resourceLibrary.add_page")}
        </h4>
      </div>
      <Form {...form}>
        <form
          onSubmit={(event) => void form.handleSubmit(onSubmit)(event)}
          className="space-y-8"
        >
          <div className="mt-10 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
            <div className="sm:col-span-3">
              <label>{t("resourceLibrary.selectCourse")}</label>
              <div className="mt-2">
                <FormField
                  control={form.control}
                  name="course"
                  render={() => (
                    <FormItem>
                      <FormLabel></FormLabel>
                      <FormControl>
                        {/* <Combobox
                        data={courseData}
                        onSelectChange={handleCourseChange}
                      /> */}
                        <Input
                          type="text"
                          autoComplete="off"
                          value={selCourseId}
                          disabled
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
            <div className="sm:col-span-3">
              <label>{t("resourceLibrary.selectSection")}</label>
              <div className="mt-2">
                <FormField
                  control={form.control}
                  name="section"
                  render={() => (
                    <FormItem>
                      <FormLabel></FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          autoComplete="off"
                          value={selSectionId}
                          disabled
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </div>
          <div className="sm:col-span-4">
            <label>{t("resourceLibrary.pageTitle")}</label>
            <div className="mt-2">
              <FormField
                control={form.control}
                name="pageTitle"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel></FormLabel>
                    <FormControl>
                      <Input
                        autoComplete="off"
                        {...field}
                        placeholder={t("resourceLibrary.pageTitle")}
                        onChange={handleInputChange}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* 
        {showMilestone && (
          <Milestone courseModuleId={null} checkpointNumber={0} />
        )} */}

          <div className="sm:col-span-4">
            <label>{t("resourceLibrary.pageContent")}</label>
            <div className="mt-2">
              <FormField
                control={form.control}
                name="pageContent"
                render={() => (
                  <FormItem>
                    <FormLabel></FormLabel>
                    <FormControl>
                      {/* <RichTextEditor
                                  setRichTextValue={setRichTextValue}
                                  initialValue=""
                                /> */}
                      {/* <Editor
                                  value=""
                                  onTextChange={setRichTextValue}
                                  style={{ height: "320px" }}
                                /> */}

                      <Editor
                        value=""
                        onTextChange={(event) => {
                          const htmlValue = event.htmlValue;
                          const richTextValue = {
                            htmlValue: htmlValue,
                          };
                          setRichTextValue(richTextValue as richTextType);
                        }}
                        style={{ height: "320px" }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>
          <div className="sm:col-span-4">
            <FormField
              name="is_premium"
              control={form.control}
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center">
                    <FormControl>
                      <>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={(value) => {
                            field.onChange(value);
                          }}
                        />
                        <FormLabel className="px-4">
                          {t("resourceLibrary.premiumContent")}
                        </FormLabel>
                      </>
                    </FormControl>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <div className="flex justify-end mt-4">
            {/* <div className="px-2">
            <Button variant="secondary">Cancel</Button>
          </div>
          <div className="px-2">
            <Button variant="outline">Reset</Button>
          </div> */}
            <Button
              variant="outline"
              className="w-full sm:w-auto mr-2"
              onClick={handleCancel}
            >
              {t("buttons.cancel")}
            </Button>
            <div className="px-2">
              <Button>{t("buttons.submit")}</Button>
            </div>
          </div>
        </form>
      </Form>
    </>
  );
}
