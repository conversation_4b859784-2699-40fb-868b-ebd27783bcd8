
"use client";

import type { ColumnDef } from "@tanstack/react-table";
import moment from "moment";
import type {
  ResourceList,
  ResourcesFormRowDefinition,
} from "@/types";

export const getColumns = (
  t: (key: string) => string
): ColumnDef<ResourceList>[] => [
  {
    accessorKey: "name",
    header: t("dashboard.youTubeVideos.videoName"),
    cell: ({ row }: ResourcesFormRowDefinition) => (
      <div className="text-align">{row.original.name}</div>
    ),
  },
  {
    header: t("dashboard.youTubeVideos.createdOn"),
    cell: ({ row }: ResourcesFormRowDefinition) => {
      const formattedDate = moment
        .utc(row.original.created_at)
        .local()
        .format("DD-MMM-YYYY hh:mm a");

      return <div className="text-align">{formattedDate}</div>;
    },
  },
];
