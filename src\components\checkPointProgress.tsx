import { PlusCircleIcon, XCircleIcon } from "lucide-react";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
interface Milestone {
  time: number;
  label: string;
}

interface CheckPointProgressProps {
  videoLength: number;
  className: string;
  availableMilestones?: Milestone[];
  onCheckpointAdd?: (time: number) => void;
}

interface ToolTipContent {
  time: number;
  timeLabel: string;
}

const CheckPointProgress: React.FC<CheckPointProgressProps> = ({
  className,
  videoLength,
  availableMilestones = [],
  onCheckpointAdd,
}) => {
  const { t } = useTranslation();
  const [currentTime, setCurrentTime] = useState(0);
  const [toolTip, setToolTip] = useState(false);
  const [toolTipPos, setToolTipPos] = useState("");
  const [toolTipContent, setToolTipContent] = useState<ToolTipContent>({
    time: 0,
    timeLabel: "",
  });
  const [milestones, setMilestones] =
    useState<Milestone[]>(availableMilestones);

  const handleProgressBarClick = (
    e: React.MouseEvent<HTMLDivElement>,
  ): void => {
    const progressBar = e.currentTarget;
    const clickPosition = e.clientX - progressBar.getBoundingClientRect().left;
    const progressBarWidth = progressBar.clientWidth;
    const percentageClicked = (clickPosition / progressBarWidth) * 100;
    const timeClicked = (percentageClicked / 100) * videoLength;

    setCurrentTime(timeClicked);

    // Add the checkpoint to milestones array
    const newMilestone: Milestone = {
      time: timeClicked,
      label: formatTime(timeClicked), // You can customize the label
    };

    console.log("newMilestone", newMilestone, currentTime);

    if (onCheckpointAdd) {
      onCheckpointAdd(timeClicked);
    }

    // Update the milestones array
    // You may want to avoid duplicating checkpoints at the same time
    // Depending on your use case, you might want to handle this differently
    const tempMilestone: Milestone[] = [...milestones];
    tempMilestone.push(newMilestone);
    setMilestones(tempMilestone);
    // Force a re-render by creating a new array
    // This is important for React to detect changes in the milestones array
    // and update the UI accordingly
  };

  const handleProgressBarHover = (
    e: React.MouseEvent<HTMLDivElement>,
  ): void => {
    const progressBar = e.currentTarget;
    const hoverPosition = e.clientX - progressBar.getBoundingClientRect().left;
    const progressBarWidth = progressBar.clientWidth;
    const percentageHovered = (hoverPosition / progressBarWidth) * 100;
    const timeHovered = (percentageHovered / 100) * videoLength;
    setToolTip(true);
    console.log("hoverPosition", hoverPosition);
    setToolTipContent({
      time: timeHovered,
      timeLabel: formatTime(timeHovered),
    });
    setToolTipPos(String(hoverPosition - 50));

    // You can display the timeHovered value in a tooltip or any other UI element.
    console.log("Hovered Time:", timeHovered);
  };

  const formatTime = (time: number): string => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds < 10 ? "0" : ""}${seconds}`;
  };

  return (
    <div
      className={`${className} relative h-2 bg-gray-300 cursor-pointer rounded`}
      onClick={handleProgressBarClick}
      onMouseMove={handleProgressBarHover}
      onMouseOut={() => {
        setToolTip(false);

        setToolTipPos("");
      }}
    >
      {milestones.map((milestone) => (
        <div
          key={milestone.time}
          className="absolute w-16 font-semibold top-3 text-sm flex justify-between bg-green-500 px-1 py-1 rounded-full text-white"
          style={{ left: `${(milestone.time / videoLength) * 100}%` }}
          title={milestone.label}
        >
          {milestone.label}
          <XCircleIcon className="w-5 h-5" />
        </div>
      ))}
      {toolTip && (
        <div
          style={{ left: toolTipPos + "px" }}
          className="flex flex-col items-center absolute bottom-4 bg-slate-300 p-2 rounded"
        >
          <PlusCircleIcon className="text-center" />
          <span className="font-semibold text-sm">
            {t("buttons.addCheckpoint")}
          </span>
          {toolTipContent?.timeLabel}
        </div>
      )}
      {/* <div
        className="absolute h-full bg-blue-500"
        style={{ width: `${(currentTime / videoLength) * 100}%` }}
      ></div> */}
    </div>
  );
};

export default CheckPointProgress;
