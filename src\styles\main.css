.wide-div {
    width: 100%;
    height:100%
  }
  .acc-content{
    padding-left: 1.5rem;
    background: white;
  }
  .topic-icons {
    display: flex;
    align-items: center; 
    justify-content: flex-end; 
    flex: 1; 
  }
 
  
.save-button {
  background-color: black;
  color: white; 
  border: none;
  padding: 8px 16px;
  margin-right: 8px;
  cursor: pointer;
  transition: background-color 0.3s, color 0.3s;
  border-radius: 5px;
}

.save-button:hover {
  background-color: rgb(80, 75, 75);
}


.cancel-button {
  background-color: transparent; 
  color: gray;
  border: 2px solid gray; 
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.3s, color 0.3s, border-color 0.3s;
  border-radius: 5px;
}

.cancel-button:hover {
  background-color: whitesmoke;
  border-color: darkgray; 
}



.cancel-button {
  background-color: white;
  color: gray;
  border: none;
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.3s, color 0.3s;
  border-radius: 5px; 
}

.cancel-button:hover {
  background-color: whitesmoke;
}


.form-container {
  border: 1px solid #ccc;
  padding: 10px;
  border-radius: 5px;
  background-color: #f9f9f9;
  margin-top: 10px;
}


.form-header {
  text-align: center;
  margin-bottom: 10px;
}


.form-field label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}


.form-field input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 3px;
  margin-bottom: 10px;
}


.form-actions {
  text-align: center;
}


.btn-save {
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 3px;
  margin-right: 10px;
  cursor: pointer;
}


.btn-cancel {
  background-color: #ccc;
  color: #333;
  border: none;
  border-radius: 3px;
  cursor: pointer;
}

.topic-icons-inner{
  display: flex;
  align-items: center; 
  justify-content: flex-end; 
  flex: 1; 
  padding-right: 30px !important;
}

.sidebarClass{
  height: calc(100vh - 3.5rem);
}

.select-width{
  width : 650px;
}

.course-width{
  width : 400px;
}

.step-number {
  cursor: pointer;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.step-number.active {
  background-color: #10b981;
  
  color: #fff;
}

.step-number.completed {
  background-color: black;
  color: #fff;
}

.step-number.inactive {
  background-color: #e5e7eb;
  color: #6b7280;
}


.step-content {
  display: none;
}

.step-content.active {
  display: block;
}


.button-disabled {
  background-color: #e5e7eb;
  cursor: not-allowed;
}

.button-active {
  background-color: black;
  color: #fff;
}

.button-active:hover {
  background-color:rgb(148, 143, 143);
  color: #fff;
}
.plusminusBtnClass{
  border: 1px #000;
  width: 25px;
  background: #000;
  color: #fff;
  margin-top: 2px;
}
/* style for editor dropdown */

.ql-snow .ql-picker.ql-expanded .ql-picker-options {
  z-index: 10001 !important;
  background-color: whitesmoke !important; 
  position: absolute !important; 
 }
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #d1d5db #f1f5f9;
}
.p-tree-table-wrapper {
  border: 1px solid #ccc !important;
  border-radius: 4px;
  overflow: auto;
}
/* Custom TreeTable Row */
.p-tree-table-wrapper {
  border: 1px solid #e5e7eb !important;
  overflow: auto;
}
.custom-tree-table .p-treetable-tbody > tr {
  border-bottom: 1px solid #e5e7eb;
  font-size: 14px;
  background-color: transparent;
}
.custom-tree-table .p-treetable-tbody > tr > td {
  padding: 6px;
  word-wrap: break-word;
  font-size: 14px;
  color: hsl(var(--foreground));
}
.custom-tree-table .p-treetable-thead th {
  background-color: hsl(var(--secondary));
  color: white;
  font-size: 14px;
  font-weight: 500;
  padding-top: 10px;
  padding-bottom: 8px;
}
.custom-tree-table .p-paginator {
  background-color: #273B4A;
  color: #fff;
  margin-top: 10px;
  display: flex;
  justify-content: flex-end;
  font-size: 14px;
  padding: 0px;
}


.custom-tree-table .p-sortable-column-icon {
  color: #fff !important;
  display: inline !important;
}

.custom-tree-table .p-inputtext {
  border: none !important;
  padding-top: 10px !important;
  padding-bottom: 7px !important;
}

.custom-data-table .p-datatable-tbody > tr {
  border-bottom: 1px solid #e5e7eb;
  font-size: 14px;
  background-color: transparent;
}
.custom-data-table .p-datatable-tbody > tr > td {
  /* padding: 6px; */
  padding-right: 6px !important;
  padding-top: 6px !important;
  padding-bottom: 6px !important;
  word-wrap: break-word;
  font-size: 14px;
  color: hsl(var(--foreground));
}
.custom-data-table .p-datatable-thead th {
  background-color: hsl(var(--secondary));
  color: white;
  font-size: 14px;
  font-weight: 500;
  padding-top: 10px;
  padding-bottom: 8px;
}

.custom-data-table .p-inputtext {
  width: 100% !important;
  padding-top: 3px !important;
  padding-bottom: 7px !important;
  padding-right: 3px !important;
  padding-left: 5px !important;
}

.custom-data-table .p-paginator {
  background-color: rgb(159, 192, 137);
 
}
.custom-data-table .p-paginator-bottom {
  height: 40px !important;
}
.custom-data-table .p-paginator-element{
  visibility: hidden;
}
.custom-data-table .p-dropdown{
  visibility: hidden;
  height: 30px !important;
}
.non-interactive-element {
  pointer-events: none;
  opacity: 0.5;
}
.non-interactive-approve-element {
  /* pointer-events: none; */
  opacity: 0.5;
}
.btn-bg {
  color: '#9FC089'
}
.days-text {
  color:#E8AE01
}
.svg {
  width: 20px;
  height:20px;
}
.video-counts {
  border-bottom: 1px solid #ccc;
  border-radius: 0;
  padding-bottom: 8px;
  margin-bottom: 8px;

}
.custom-data-table {
  height: 100%;
  overflow-y: auto;
}

.p-checkbox .p-checkbox-box {
  border-color: #888 !important; /* Gray border */
  background-color: #f0f0f0 !important; /* Light gray background */
}

.p-checkbox .p-checkbox-box .p-checkbox-icon {
  color: black !important; /* Black color for the checkmark */
  font-size: 0.875rem; 
}
.custom-placeholder::placeholder {
  color: rgb(105, 101, 101) !important 

}
.dashboard-session {
 background-color:#67d2e5;
 /* background-color: #fc99a7; */
 /* background-color: #fcaf70; */
 color: white !important;
}