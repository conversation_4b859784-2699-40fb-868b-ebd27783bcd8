import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "../card";

import { columns } from "./session_columns";

import type {
  Checkpoint,
  ComboData,
  CourseDetailsRequest,
  CourseModule,
  Module,
  UserSession,
} from "@/types";

import {} from "../use-toast";
import { DataTable } from "../data-table/data-table";
import { Combobox } from "../combobox";
import useSessionViews from "@/hooks/useSessionViews";
import useCourse from "@/hooks/useCourse";
import { Spinner } from "../progressiveLoader";
import { ORG_KEY } from "@/lib/constants";

interface userSessionViewsProps {
  courseData: ComboData[];
  userId: string;
}

export function DBUserSessionDetails({
  courseData,
  userId,
}: userSessionViewsProps): React.JSX.Element {
  const [courseList, setCourseList] = useState<ComboData[]>([]);
  const [selectedCourseId, setSelectedCourseId] = useState("");
  const [selectedUserId, setSelectedUserId] = useState("");
  const [selectedCourseName, setSelectedCourseName] = useState("");
  const { getSessionViews } = useSessionViews();
  const [checkPointsList, setCheckPoints] = useState<UserSession[]>([]);
  const { courseDetails } = useCourse();
  const [courseModuleId, setCourseModuleId] = useState("");
  const [courseModuleName, setCourseModuleName] = useState("");
  const [isLoading, setIsLoading] = React.useState<boolean>(true);
  const [modulesData, setModulesData] = useState<
    { value: string; label: string }[]
  >([]);

  useEffect(() => {
    setCourseList(courseData);
    setSelectedUserId(userId);
  }, [courseData, userId]);

  useEffect(() => {
    const fetchSessionsData = async (): Promise<void> => {
      setIsLoading(true);
      try {
        const userSessions: UserSession[] = [];
        if (selectedCourseId !== "") {
          const sessionViewsInfo = await getSessionViews(
            selectedCourseId,
            courseModuleId,
            selectedUserId,
          );

          if (sessionViewsInfo.session_data !== null) {
            const sessionViews = sessionViewsInfo?.session_data?.reduce(
              (accumulator: Module[], item) => {
                return accumulator.concat(item.modules);
              },
              [],
            );

            const checkPointsViews = sessionViews.reduce(
              (accumulator: Checkpoint[], item) => {
                return accumulator.concat(item.checkpoints);
              },
              [],
            );

            checkPointsViews.map((checkPointSession) => {
              checkPointSession.sessions.map((sessionInfo) => {
                userSessions.push({
                  checkpoint_id: checkPointSession.checkpoint_id,
                  checkpoint_name: checkPointSession.checkpoint_name,
                  sequence: checkPointSession.sequence,
                  start_time: checkPointSession.start_time,
                  user_id: sessionInfo.user_id,
                  lastname: sessionInfo.lastname,
                  quiz_name: sessionInfo.quiz_name,
                  first_name: sessionInfo.first_name,
                  instance_id: checkPointSession.instance_id,
                  session_end_time: sessionInfo.session_end_time,
                  session_start_time: sessionInfo.session_start_time,
                  instance_attempt_id: sessionInfo.instance_attempt_id,
                  session_attempt_number: sessionInfo.session_attempt_number,
                  result: sessionInfo.result,
                });
              });
            });
            setCheckPoints(userSessions);
          } else {
            setCheckPoints(userSessions);
          }
        } else {
          setCheckPoints(userSessions);
        }
        setIsLoading(false);
      } catch (error: unknown) {
        console.error("Error fetching data : " + error);
      }
    };
    fetchSessionsData().catch((error) => console.log(error));
  }, [courseModuleId, selectedUserId]);

  useEffect(() => {
    setModulesData([]);
    setCourseModuleId("");

    const fetchModulesData = async (): Promise<void> => {
      setIsLoading(true);
      try {
        if (selectedCourseId !== "") {
          const orgId = localStorage.getItem(ORG_KEY);
          const reqParams: CourseDetailsRequest = {
            course_id: selectedCourseId as string,
            org_id: orgId ?? "",
          };

          const response = await courseDetails(reqParams);

          const modulesList = response.reduce(
            (accumulator: CourseModule[], item) => {
              return accumulator.concat(item.modules);
            },
            [],
          );

          const filteredModules = modulesList
            .filter((module: CourseModule) => module.module_type === "Url")
            .map((module: CourseModule) => ({
              value: module.course_module_id,
              label: module.module_name,
            }));

          if (filteredModules.length > 0) {
            setModulesData(filteredModules);
            setCourseModuleName(filteredModules[0]?.label);
            setCourseModuleId(filteredModules[0]?.value);
          }
        }
        setIsLoading(false);
      } catch (error: unknown) {
        console.error("Error fetching data:");
        setIsLoading(false);
      }
    };
    fetchModulesData().catch((error) => console.log(error));
  }, [selectedCourseId]);

  const handleCourseData = (data: string): void => {
    setSelectedCourseId(data);

    const courseSelected = courseData.filter((course) =>
      course?.value?.includes(data),
    );
    if (courseSelected !== null) {
      setSelectedCourseName(courseSelected[0].label as string);
    }
  };
  const handleOnModuleSelected = (moduleIdSelected: string): void => {
    setCourseModuleId(moduleIdSelected);
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="w-full text-lg font-medium">
          <div className="flex grid grid-cols-2 lg:grid-cols-2 md:grid-cols-2">
            <div className="w-1/2 flex text-sm text-muted-foreground justify-between mt-4 bg-white">
              <Combobox
                data={courseList}
                onSelectChange={handleCourseData}
                placeHolder="Select Course"
                defaultLabel={selectedCourseName}
              />
            </div>
            <div className="w-full flex text-sm text-muted-foreground flex-wrap  justify-between mt-4 bg-white">
              <Combobox
                data={modulesData}
                onSelectChange={handleOnModuleSelected}
                placeHolder="Select Video"
                defaultLabel={courseModuleName}
              />
            </div>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <Spinner />
        ) : (
          <div className=" overflow-auto">
            <DataTable
              columns={columns}
              data={checkPointsList as UserSession[]}
              FilterLabel={"Filter by exam name"}
              FilterBy={"quiz_name"}
              actions={[]}
              onSelectedDataChange={() => {}}
            />
          </div>
        )}
      </CardContent>
    </Card>
  );
}
