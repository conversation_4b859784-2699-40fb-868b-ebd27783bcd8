"use client";

import type { Column, ColumnDef } from "@tanstack/react-table";
import { ArrowUpDown } from "lucide-react";
import { Button } from "@/components/ui/button";

import type { CourseResource } from "@/types";

const getResourceColumns = (
  t: (key: string) => string,
): ColumnDef<CourseResource>[] => [
  {
    accessorKey: "name",
    header: ({
      column,
    }: {
      column: Column<CourseResource, unknown>;
    }): React.JSX.Element => {
      return (
        <Button
          className="px-0"
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          {t("courses.courseModule.name")}
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
  },
  {
    accessorKey: "url",
    header: ({
      column,
    }: {
      column: Column<CourseResource, unknown>;
    }): React.JSX.Element => {
      return (
        <Button
          className="px-0"
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          {t("courses.courseModule.url")}
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
  },
];

const moduleColumns: ColumnDef<string>[] = [
  {
    accessorKey: "name",
    header: ({
      column,
    }: {
      column: Column<string, unknown>;
    }): React.JSX.Element => {
      return (
        <Button
          type="button"
          className="px-0"
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
  },
];

export { getResourceColumns, moduleColumns };
