"use client";

import { useState } from "react";
import { resourceTypes } from "@/lib/constants";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import MainLayout from "../layout/mainlayout";
import { Label } from "@radix-ui/react-label";
import { CourseResourceAddfile } from "./resource-add-file";
import ResourceExamAddPage from "./resource-add-page";
import ExamAddPage from "@/components/addExam/addExam";

export default function AddResources(): JSX.Element {
  const [selResource, setSelResource] = useState("1");

  const onHandleChange = (selectedValue: string): void => {
    setSelResource(selectedValue);
    console.log(selectedValue);
  };

  return (
    <MainLayout>
      <h1 className="text-2xl font-semibold tracking-tight">Add Resources</h1>

      <div className="border rounded-md p-4 mt-4">
        <div className="w-full sm:w-1/3 pr-4">
          <Label
            htmlFor="firstname"
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            Resource Type
          </Label>
          <Select onValueChange={onHandleChange} defaultValue={selResource}>
            <SelectTrigger>
              <SelectValue placeholder="Select" />
            </SelectTrigger>

            <SelectContent>
              {resourceTypes.map((items, index) => (
                <SelectItem key={index} value={items.value.toString()}>
                  {items.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="text-left border rounded-md p-4 mt-4">
          {(selResource === "1" || selResource === "4") && (
            <CourseResourceAddfile resourceType={selResource} />
          )}
          {selResource === "2" && <ResourceExamAddPage />}
          {selResource === "3" && <ExamAddPage />}
        </div>
      </div>
    </MainLayout>
  );
}
