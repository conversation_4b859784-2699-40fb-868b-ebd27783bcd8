"use client";

import * as React from "react";
import type { ExamInfoType } from "@/types";
// import ExamInfo from "./examInfo";
import { Card, CardContent } from "@/components/ui/card";
import Pagination from "@/components/ui/pagination";
import { useTranslation } from "react-i18next";
export default function ExamInfoModal({
  exam,
}: {
  exam: ExamInfoType[];
}): React.JSX.Element {
  const { t } = useTranslation();
  const [postsToDisplay, setPostsToDisplay] = React.useState<ExamInfoType[]>(
    exam.length > 0 ? exam : [],
  );
  const [startingSerialNumber, setStartingSerialNumber] = React.useState(1);
  const handlePostsToDisplayChange = (newPostsToDisplay: unknown): void => {
    if (Array.isArray(newPostsToDisplay)) {
      setPostsToDisplay(newPostsToDisplay as ExamInfoType[]);
      console.log(newPostsToDisplay);
    }
  };

  const handleCurrentPageChange = (currentPage: number): void => {
    setStartingSerialNumber((currentPage - 1) * 5 + 1);
  };

  return (
    <div className="w-full">
      {postsToDisplay.map((data, index) => (
        <div key={index} className="w-full">
          <Card className="pt-4 mb-2 w-full">
            <CardContent>
              <div className="flex">
                <p className="me-4">{startingSerialNumber + index + "."}</p>
                <p
                  dangerouslySetInnerHTML={{
                    __html: data.question_with_options,
                  }}
                />
              </div>
              {data.response_summary !== "" &&
                data.response_summary !== "[]" && (
                  <span>
                    <p className="ms-8">
                      {data.response_summary} [
                      {data.right_answer == true ? (
                        <span className="text-green-400">
                          &quot;{t("attendedExams.correct")}&quot;
                        </span>
                      ) : (
                        <span className="text-red-300">&quot;{t("attendedExams.wrong")}&quot;</span>
                      )}
                      ]
                    </p>
                  </span>
                )}
            </CardContent>
          </Card>
        </div>
      ))}

      <Pagination
        visiblePages={5}
        data={exam}
        onPostsToDisplayChange={handlePostsToDisplayChange}
        onCurrentPageChange={handleCurrentPageChange}
      />
    </div>
  );
}
