"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type {
  CourseProgressedRowDefinition,
  CourseProgressResult,
} from "@/types";

import Image from "next/image";
export const getColumns = (
  t: (key: string) => string,
): ColumnDef<CourseProgressResult>[] => [
  {
    accessorKey: "user_name",
    header: t("dashboard.name"),
    cell: ({ row }) => <div>{row.original.user_name ?? ""}</div>,
  },
  {
    accessorKey: "avatar_url",
    header: t("dashboard.profile"),
    cell: ({ row }) => (
      <div className="h-10 w-10">
        {row.original.avatar_url !== "" && row.original.avatar_url !== null ? (
          <Image
            src={
              row.original.avatar_url != null &&
              row.original.avatar_url.startsWith("http")
                ? (row.original.avatar_url as string)
                : "/assets/user.png"
            }
            alt=""
            width={45}
            height={45}
            objectFit="cover"
            className="rounded-full"
            style={{
              width: "45px",
              height: "38px",
              border: "2px solid #000",
              borderRadius: "50%",
            }}
          />
        ) : (
          <div className="rounded-full h-25 w-25 bg-gray-300"></div>
        )}
      </div>
    ),
  },
  {
    accessorKey: "email",
    header: t("dashboard.email"),
    cell: ({ row }: CourseProgressedRowDefinition): React.JSX.Element => (
      <div>{row.original.user_email ?? ""}</div>
    ),
  },
  {
    accessorKey: "course_name",
    header: t("dashboard.enrolledCourse"),
    cell: ({ row }: CourseProgressedRowDefinition): React.JSX.Element => (
      <div>{row.original.course_name}</div>
    ),
  },
  // {
  //   accessorKey: "instance_name",
  //   header: "Attended Resource",
  //   cell: ({ row }: CourseProgressedRowDefinition): React.JSX.Element => (
  //     <div>{row.original.resource_name}</div>
  //   ),
  // },
  {
    accessorKey: "instance_progress",
    header: t("dashboard.progress"),
    cell: ({ row }: CourseProgressedRowDefinition): React.JSX.Element => (
      <div>
        {(row.original.total_progress / row.original.resource_count).toFixed(2)}
      </div>
    ),
  },
];
