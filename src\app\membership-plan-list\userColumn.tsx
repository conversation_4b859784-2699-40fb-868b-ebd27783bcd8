"use client";
import type { ColumnDef } from "@tanstack/react-table";
import type { UsersDataType } from "@/types";

export const getColumns = (
  t: (key: string) => string,
): ColumnDef<UsersDataType>[] => [
  {
    id: "select",
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "first_name",
    header: t("subscriptionPlan.firstName"),
  },
  {
    accessorKey: "last_name",
    header: t("subscriptionPlan.lastName"),
  },
  {
    accessorKey: "email",
    header: t("subscriptionPlan.email"),
  },
];
