"use client";
import React, { useState, useEffect } from "react";
// import { columns } from "./columns";
import { getColumns } from "./columns";
import { DataTable } from "../ui/data-table/data-table";
import type {
  ErrorCatch,
  InnerItem,
  ResourceLibrary,
  ResourceList,
} from "@/types";
import { Modal } from "@/components/ui/modal";
import { useToast } from "@/components/ui/use-toast";
import type { ToastType } from "../../types";
import { Spinner } from "@/components/ui/progressiveLoader";
import useResourceLibrary from "@/hooks/useResourceLibrary";
import { Eye } from "lucide-react";
import VideoPlayer from "./video-player";
import NextBreadcrumb from "../breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumb";
import { DEFAULT_FOLDER_ID, extractVideoIdFromSearch } from "@/lib/constants";
import { useTranslation } from "react-i18next";
import { ERROR_MESSAGES } from "@/lib/messages";

export default function YoutubeDatas({
  isDashboard,
}: {
  isDashboard?: boolean;
}): React.JSX.Element {
  const { t } = useTranslation();
  const columns = getColumns(t);
  const [isLoading, setIsLoading] = React.useState<boolean>(true);
  const [url, setUrl] = React.useState<string>("");
  const [viewOpen, setViewOpen] = React.useState(false);
  const { toast } = useToast() as ToastType;
  const { getResourceList } = useResourceLibrary();
  const [resourceLibrary, setResourceLibrary] = useState<ResourceList[]>([]);
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);
  const closeDialog = (): void => {
    setViewOpen(false);
  };

  useEffect(() => {
    setBreadcrumbItems(
      getBreadCrumbItems(t, t("breadcrumb.youtubeVideos"), { "": "" }),
    );

    const fetchData = async (): Promise<void> => {
      try {
        const requestBody = {
          org_id: localStorage.getItem("orgId") ?? "",
          module_id: "0098bc68-069c-4d19-930c-f4ad03a95bd8",
          linked: "0",
          limit_val: 1000,
          offset_val: 0,
          folder_id: DEFAULT_FOLDER_ID as string,
        };
        const resource: ResourceLibrary = await getResourceList(requestBody);
        if (resource.resource_list != null) {
          setIsLoading(false);
          setResourceLibrary(resource.resource_list);
        } else {
          setIsLoading(false);
          setResourceLibrary([]);
        }
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
        console.error("Error fetching data:");
      }
    };
    fetchData().catch((error) => console.log(error));
  }, [t]);

  const dialogOpen = (value: string): void => {
    const videoId = extractVideoIdFromSearch(value);
    if (videoId != null) {
      setUrl(`https://www.youtube.com/watch?v=${videoId}`);
    } else {
      setUrl(value);
    }
    setViewOpen(true);
  };

  return (
    <div>
      {!(isDashboard ?? false) && (
        <NextBreadcrumb
          items={breadcrumbItems}
          separator={<span> | </span>}
          containerClasses="flex py-5"
          listClasses="hover:underline mx-2 font-bold"
          capitalizeLinks
        />
      )}

      <div>
        <div className="text-2xl font-semibold tracking-tight">
          {!(isDashboard ?? false) ? (
            <h1> {String(t("dashboard.youTubeVideos.title"))}</h1>
          ) : (
            <p className="text-lg font-medium"></p>
          )}
        </div>
        <div className="border rounded-md p-4 pt-0 mt- bg-[#fff]">
          {isLoading ? (
            <Spinner />
          ) : (
            <div className="mt-6">
              <DataTable
                columns={columns}
                data={resourceLibrary} // Display the filtered users
                FilterLabel={String(t("dashboard.youTubeVideos.filterByName"))}
                FilterBy={"name"}
                actions={[
                  {
                    title: `${t("dashboard.youTubeVideos.view")}`,
                    icon: Eye,
                    varient: "icon",
                    handleClick: (val: unknown) => {
                      dialogOpen((val as ResourceList).external_url);
                    },
                  },
                ]}
              />
            </div>
          )}
        </div>
      </div>
      {viewOpen && (
        <Modal
          title=""
          header=""
          openDialog={viewOpen}
          closeDialog={closeDialog}
          type="max-w-7xl"
        >
          <VideoPlayer onCancel={closeDialog} url={url} />
        </Modal>
      )}
    </div>
  );
}
