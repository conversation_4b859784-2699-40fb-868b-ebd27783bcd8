import React, { useEffect, useState } from "react";
import type { <PERSON><PERSON>r<PERSON>atch, ForgetPasswordForm, ToastType } from "@/types";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import useAuthorization from "@/hooks/useAuth";
import { useTranslation } from "react-i18next";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";

export default function ForgetPassword({
  onCancel,
  onSave,
  data,
  isModal,
}: {
  onSave: () => void;
  onCancel: () => void;
  data: ForgetPasswordForm;
  isModal?: boolean;
}): React.JSX.Element {
  const { t } = useTranslation();
  const { toast } = useToast() as ToastType;
  const [userEmail, setUserEmail] = useState<string>("");
  const { sendResetLink } = useAuthorization();

  const handleCancelClick = (): void => {
    onCancel();
  };
  const handleSaveClick = (): void => {
    void handleResetMailSend();
    onCancel();
  };
  useEffect(() => {
    setUserEmail(data?.email);
  }, []);

  const handleResetMailSend = async (): Promise<void> => {
    if (userEmail !== "") {
      try {
        const result = sendResetLink(userEmail);
        console.log(result);

        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.resetTitle"),
          description: t("successMessages.resetDescription"),
        });
        onSave();
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.details,
        });
        console.error("An unexpected error occurred:", error);
      }
    }
  };

  return (
    <>
      <div className="mb-2 mr-4">
        <input
          autoComplete="off"
          type="text"
          value={userEmail}
          onChange={(e) => setUserEmail(e.target.value)}
          onClick={(e) => e.stopPropagation()}
          className={`text-sm px-3 py-2 border ${
            isModal ?? false ? "w-full" : "w-40"
          } border-gray-300 rounded-md shadow-sm focus:ring-accent focus:border-accent`}
          placeholder={t("auth.inputMail")}
        />
      </div>
      <div className="flex topic-icons pr-4">
        <div className="flex items-center justify-center float-right">
          <Button
            type="button"
            className="bg-[#33363F]"
            onClick={handleCancelClick}
          >
            {t("buttons.cancel")}
          </Button>
          &nbsp;
          <Button
            type="submit"
            className="bg-[#9FC089]"
            onClick={handleSaveClick}
            disabled={userEmail?.trim() === ""}
          >
            {t("buttons.submit")}
          </Button>
        </div>
      </div>
    </>
  );
}
