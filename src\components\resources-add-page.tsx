"use client";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useForm } from "react-hook-form";
import React, { useEffect, useState } from "react";

import { Editor } from "primereact/editor";
import type {
  ToastType,
  AddResourcePage,
  ErrorCatch,
  ResourceLibraryData,
  EditResourcePage,
  PageResourceForm,
  richTextType,
  ComboData,
  LogUserActivityRequest,
} from "@/types";
import {
  DEFAULT_FOLDER_ID,
  pageUrl,
  ResourceExtensions,
} from "@/lib/constants";
import { zodResolver } from "@hookform/resolvers/zod";
import { AddPageResourceSchema } from "@/schema/schema";
import Link from "next/link";
import { useToast } from "@/components/ui/use-toast";
import useResourceLibrary from "@/hooks/useResourceLibrary";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import { Combobox } from "@/components/ui/combobox";
import { Label } from "@radix-ui/react-label";
import useCourse from "@/hooks/useCourse";
import { FolderPlus } from "lucide-react";
import { Modal } from "./ui/modal";
import AddFolders from "@/app/resources-library/add-folders";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";

export default function ResourcesAddPage({
  onCancel,
  resourceData,
  onSave,
}: {
  onCancel: () => void;
  onSave: () => void;
  resourceData: ResourceLibraryData;
}): React.JSX.Element {
  const { t } = useTranslation();
  const form = useForm<PageResourceForm>({
    resolver: zodResolver(AddPageResourceSchema),
  });
  const { toast } = useToast() as ToastType;
  const { addPageResource, editPageResource } = useResourceLibrary();
  const [comboData, setComboData] = useState<ComboData[]>([]);
  const [isButtonDisable, setButtonDisable] = useState<boolean>(false);
  const [richTextValues, setRichTextValues] = useState<
    richTextType | undefined
  >(undefined);
  const [extensions, setExtensions] = useState<ComboData[]>([]);
  const [selectedExtensions, setSelectedExtensions] = useState<string>("");
  const [openFolder, setOpenFolder] = useState<boolean>(false);
  const [reloadList, setReloadList] = useState<boolean>(false);
  const [folderId, setFolderId] = useState<string>("");
  const { listFolderFromLibrary } = useCourse();
  const { updateUserActivity } = useLogUserActivity();
  const [defaultLabel, setDefaultLabel] = useState<string>("");
  useEffect(() => {
    setExtensions(ResourceExtensions.page);
    setSelectedExtensions(resourceData.extension as string);
    form.setValue("pageTitle", resourceData.name);
    form.setValue("pageContent", resourceData.content as string);
    form.setValue("extension", resourceData.extension as string);
    setFolderId(resourceData.folder_id);
  }, []);

  useEffect(() => {
    const fetchData = async (): Promise<void> => {
      try {
        const orgId = localStorage.getItem("orgId");
        const folderList = await listFolderFromLibrary(orgId as string);
        const comboData: ComboData[] = folderList?.map((cat) => ({
          value: cat.folder_id,
          label: cat.folder_name,
        }));
        const defaultFolder = comboData.find(
          (folder) => folder.value === resourceData.folder_id,
        );
        setDefaultLabel(defaultFolder ? (defaultFolder.label as string) : "");
        setComboData(comboData);
      } catch (error) {
        // const err = error as ErrorCatch;
        // toast({
        //   variant: "destructive",
        //   title: "Error",
        //   description: err?.message,
        // });
        console.error("Error fetching data:");
      }
    };
    fetchData().catch((error) => console.log(error));
  }, [reloadList]);
  const onHandleFolderChange = (selectedValue: string): void => {
    console.log(selectedValue);
    setFolderId(selectedValue);
  };

  const handleImportFolder = (): void => {
    setOpenFolder(!openFolder);
  };
  const setRichTextValue = (richTextValue: richTextType | undefined): void => {
    if (richTextValue && richTextValue.htmlValue == null) {
      richTextValue = undefined;
    }
    form.setValue("pageContent", richTextValue?.htmlValue ?? "");
    setRichTextValues(richTextValue);
  };

  async function onSubmit(data: PageResourceForm): Promise<void> {
    if (
      typeof folderId !== "string" ||
      folderId.trim().length === 0 ||
      folderId === DEFAULT_FOLDER_ID
    ) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.select_folder"),
      });
    } else {
      setButtonDisable(true);
      if (data?.pageTitle !== null && /^[0-9,-]*$/.test(data?.pageTitle)) {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: t("errorMessages.validTitle"),
        });
        return;
      }

      if (richTextValues?.htmlValue !== undefined) {
        data.pageContent = richTextValues.htmlValue;
      }

      const org_id = localStorage.getItem("orgId");
      if (Object.keys(resourceData).length === 0) {
        const pageParams: AddResourcePage = {
          page_data: {
            name: data.pageTitle,
            content: data.pageContent,
            extension: selectedExtensions,
            ...(folderId.length > 0 ? { folder_id: folderId } : {}),
          },
          org_id: org_id ?? "",
        };

        try {
          const result = await addPageResource(pageParams);
          if (result.status === "success") {
            setButtonDisable(true);
            toast({
              variant: SUCCESS_MESSAGES.toast_success_title,
              title: t("successMessages.toast_success_title"),
              description: t("successMessages.addPageMsg"),
            });
            onSave();
            const params = {
              activity_type: "Resource_Library",
              screen_name: "Resource Library",
              action_details: "Failed to add resource to library ",
              target_id: result.instance_id ?? DEFAULT_FOLDER_ID,
              log_result: "SUCCESS",
            };
            void updateLogUserActivity(params).catch((error) => {
              console.error(error);
            });
          } else {
            toast({
              variant: ERROR_MESSAGES.toast_variant_destructive,
              title: t("errorMessages.toast_error_title"),
              description: result.status,
            });
            const params = {
              activity_type: "Resource_Library",
              screen_name: "Resource Library",
              action_details: "Failed to add resource to library ",
              target_id: DEFAULT_FOLDER_ID,
              log_result: "ERROR",
            };
            void updateLogUserActivity(params).catch((error) => {
              console.error(error);
            });
          }
        } catch (error) {
          setButtonDisable(false);
          const err = error as ErrorCatch;
          toast({
            variant: ERROR_MESSAGES.toast_variant_destructive,
            title: t("errorMessages.toast_error_title"),
            description: err?.message,
          });
          const params = {
            activity_type: "Resource_Library",
            screen_name: "Resource Library",
            action_details: "Failed to add resource to library ",
            target_id: DEFAULT_FOLDER_ID,
            log_result: "ERROR",
          };
          void updateLogUserActivity(params).catch((error) => {
            console.error(error);
          });
        }
      } else {
        const pageParams: EditResourcePage = {
          resource_data: {
            name: data.pageTitle,
            content: data.pageContent,
            extension: selectedExtensions,
            ...(folderId.length > 0 ? { folder_id: folderId } : {}),
          },
          org_id: org_id ?? "",
          instance: resourceData.id,
        };

        try {
          const result = await editPageResource(pageParams);
          if (result.status === "success") {
            setButtonDisable(true);
            toast({
              variant: SUCCESS_MESSAGES.toast_success_title,
              title: t("successMessages.toast_success_title"),
              description: t("successMessages.editPageMsg"),
            });
            onSave();
            const params = {
              activity_type: "Resource_Library",
              screen_name: "Resource Library",
              action_details: "Failed to edit resource to library ",
              target_id: resourceData.id as string,
              log_result: "SUCCESS",
            };
            void updateLogUserActivity(params).catch((error) => {
              console.error(error);
            });
          } else {
            toast({
              variant: ERROR_MESSAGES.toast_variant_destructive,
              title: t("errorMessages.toast_error_title"),
              description: result.status,
            });
            const params = {
              activity_type: "Resource_Library",
              screen_name: "Resource Library",
              action_details: "Failed to edit resource to library ",
              target_id: resourceData.id as string,
              log_result: "ERROR",
            };
            void updateLogUserActivity(params).catch((error) => {
              console.error(error);
            });
          }
        } catch (error) {
          setButtonDisable(false);
          const err = error as ErrorCatch;
          console.log("e", err);
          toast({
            variant: ERROR_MESSAGES.toast_variant_destructive,
            title: t("errorMessages.toast_error_title"),
            description: err.message,
          });
          const params = {
            activity_type: "Resource_Library",
            screen_name: "Resource Library",
            action_details: "Failed to edit resource to library ",
            target_id: resourceData.id as string,
            log_result: "ERROR",
          };
          void updateLogUserActivity(params).catch((error) => {
            console.error(error);
          });
        }
      }
    }
  }
  // const setRichTextValue = (val: string): void => {
  //   form.setValue("pageContent", val);
  // };

  const handleCancel = (): void => {
    onCancel();
  };
  const handleExtension = (selectedValue: string): void => {
    setSelectedExtensions(selectedValue);
    form.setValue("extension", selectedValue);
  };

  function handleSaveFolder(): void {
    setOpenFolder(false);
    setReloadList(true);
  }

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  return (
    <div className="border rounded-md p-4">
      <div className="w-full mb-4 mt-4">
        {Object.keys(resourceData).length === 0 ? (
          <h4 className="text-2xl tracking-tight">
            {t("resourceLibrary.addPage")}
          </h4>
        ) : (
          <h4 className="text-2xl tracking-tight">
            {t("resourceLibrary.editPage")}
          </h4>
        )}
      </div>
      <Form {...form}>
        <form
          onSubmit={(event) => void form.handleSubmit(onSubmit)(event)}
          className="space-y-8"
        >
          <div className="sm:col-span-4 flex flex-col sm:flex-row gap-4">
            {/* Page Title Field */}
            <div className="flex-1 gap-2">
              <label>
                {t("resourceLibrary.pageTitle")}
                <span className="text-red-700">*</span>
              </label>
              <div className="mt-2">
                <FormField
                  control={form.control}
                  name="pageTitle"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Input
                          autoComplete="off"
                          {...field}
                          placeholder={t("resourceLibrary.pageTitle")}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Select Folder Field */}
            <div className="flex-1">
              <Label
                htmlFor="firstname"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                {t("resourceLibrary.selectFolder")}{" "}
                <span className="text-red-700 ml-2">*</span>
              </Label>
              <div className="flex items-center gap-2 pt-2">
                <Combobox
                  data={comboData}
                  onSelectChange={onHandleFolderChange}
                  defaultLabel={defaultLabel}
                />
                <button
                  type="button"
                  className="p-2 rounded-full hover:bg-gray-200"
                  title={t("resourceLibrary.addFolder")}
                  onClick={handleImportFolder}
                >
                  <FolderPlus className="w-5 h-5 text-blue-600" />
                </button>
              </div>
            </div>
          </div>

          <div className="sm:col-span-4 w-full sm:w-1/2 pr-4">
            <label>
              {t("resourceLibrary.extension")}
              <span className="text-red-700">*</span>
            </label>
            <FormField
              // control={form.control}
              name="extension"
              render={() => (
                <FormItem>
                  <FormLabel></FormLabel>
                  <FormControl>
                    <Combobox
                      data={extensions}
                      onSelectChange={handleExtension}
                      defaultLabel={selectedExtensions}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="sm:col-span-4">
            <label>
              {t("resourceLibrary.pageContent")}
              <span className="text-red-700">*</span>
            </label>
            <div className="mt-2">
              <FormField
                control={form.control}
                name="pageContent"
                render={() => (
                  <FormItem>
                    <FormLabel></FormLabel>
                    <FormControl>
                      <Editor
                        value={resourceData.content}
                        onTextChange={(event) => {
                          const htmlValue = event.htmlValue;
                          const richTextValue = {
                            htmlValue: htmlValue,
                          };
                          setRichTextValue(richTextValue as richTextType);
                        }}
                        style={{ height: "320px" }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>
          {/* <div className="w-full md:w-1/3">
            <FormItem>
              <FormControl>
                <>
                  <Checkbox id="premium" />
                  <FormLabel className="px-4">Premium content</FormLabel>
                </>
              </FormControl>
              <FormMessage />
            </FormItem>
          </div> */}
          <div className="flex justify-end mt-4 gap-3">
            <Link href={pageUrl.ResourceLibraryLink}>
              <Button
                className="w-full sm:w-auto bg-[#33363F]"
                onClick={handleCancel}
              >
                {t("buttons.close")}
              </Button>
            </Link>
            <Button disabled={isButtonDisable} className="bg-[#9FC089]">
              {t("buttons.submit")}
            </Button>
          </div>
        </form>
      </Form>
      {openFolder && (
        <Modal
          title={t("resourceLibrary.addFolder")}
          header=""
          openDialog={openFolder}
          closeDialog={handleImportFolder}
          type="max-w-3xl"
        >
          <AddFolders onSave={handleSaveFolder} onCancel={handleImportFolder} />
        </Modal>
      )}
    </div>
  );
}
