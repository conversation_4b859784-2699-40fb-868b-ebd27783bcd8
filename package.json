{"name": "smartlearn-administration-dashboard", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:ci": "npm run test -- --testResultsProcessor=\"jest-junit\" --watchAll=false --ci --coverage", "prepare": "husky install", "test:watch": "jest --watchAll", "format": "npx prettier \"src/**/*.{js,jsx,ts,tsx}\" --write", "lint-staged": "npm run lint && npm run format"}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx}": ["npx eslint --quiet --fix", "npx prettier --write"]}, "resolutions": {"wrap-ansi": "7.0.0", "string-width": "4.1.0"}, "dependencies": {"@hookform/resolvers": "^3.3.1", "@internationalized/date": "^3.5.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-context-menu": "^2.1.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.0.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.4", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-toolbar": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@react-pdf-viewer/highlight": "^3.12.0", "@react-pdf/renderer": "^3.3.8", "@supabase/supabase-js": "^2.38.1", "@tanstack/react-table": "^8.10.3", "@types/node": "20.6.2", "@types/react": "18.2.21", "@types/react-dom": "18.2.7", "apexcharts": "^4.4.0", "autoprefixer": "10.4.15", "axios": "^1.6.2", "chart.js": "^4.4.0", "chartjs-plugin-datalabels": "^2.2.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cmdk": "^0.2.0", "date-fns": "^2.30.0", "embla-carousel-react": "^8.6.0", "eslint": "8.49.0", "eslint-config-next": "13.4.19", "html2canvas": "^1.4.1", "i18next": "^25.2.1", "lucide-react": "^0.279.0", "mathjax-react": "^2.0.1", "mathlive": "^0.101.0", "mathml-to-latex": "^1.4.1", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "next": "^13.5.2", "next-i18next": "^15.4.2", "next-images": "^1.8.5", "pdfjs-dist": "3.10.111", "postcss": "8.4.29", "primereact": "^10.6.2", "quill": "^2.0.0", "raw-loader": "^4.0.2", "react": "^18.2.0", "react-apexcharts": "^1.7.0", "react-aria": "^3.28.0", "react-chartjs-2": "^5.2.0", "react-day-picker": "^8.8.2", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-documents": "^1.2.1", "react-dom": "^18.2.0", "react-google-slides": "4.0.0", "react-hook-form": "^7.46.2", "react-i18next": "^15.6.0", "react-lite-youtube-embed": "^2.3.52", "react-map-interaction": "^2.1.0", "react-player": "^2.13.0", "react-stately": "^3.26.0", "react-tweet": "^3.1.1", "react-viewer": "^3.2.2", "recoil": "^0.7.7", "slate": "^0.94.1", "slate-history": "^0.93.0", "slate-hyperscript": "^0.77.0", "slate-react": "^0.99.0", "tailwind-merge": "^1.14.0", "tailwindcss": "3.3.3", "tailwindcss-animate": "^1.0.7", "typescript": "5.2.2", "url": "^0.11.3", "use-resize-observer": "^9.1.0", "xlsx": "^0.18.5", "xlsx-populate": "^1.21.0", "zod": "^3.22.2"}, "devDependencies": {"@commitlint/cli": "^17.7.1", "@commitlint/config-conventional": "^17.7.0", "@next/eslint-plugin-next": "^13.4.19", "@testing-library/jest-dom": "^6.1.3", "@testing-library/react": "^14.0.0", "@types/jest": "^29.5.5", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jest-dom": "^5.1.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-testing-library": "^6.0.1", "husky": "^8.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-junit": "^16.0.0", "ts-jest": "^29.1.1"}}