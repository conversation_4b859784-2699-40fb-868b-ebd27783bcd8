import { supabase } from "@/lib/client";
import { rpc } from "@/lib/apiConfig";
import type {
  GetCoursePurchaseRequest,
  ErrorType,
  CoursePurchase,
} from "@/types";

interface UseCoursePurchaseRequestReturn {
  getCoursePurchaseRequests: (
    params: GetCoursePurchaseRequest,
  ) => Promise<CoursePurchase>;
}

const useCoursePurchaseRequest = (): UseCoursePurchaseRequestReturn => {
  async function getCoursePurchaseRequests(
    params: GetCoursePurchaseRequest,
  ): Promise<CoursePurchase> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.getCoursePurchaseRequest,
        params,
      )) as {
        data: CoursePurchase;
        error: ErrorType | null;
      };

      if (error) {
        throw new Error(error.details);
      }

      return data as CoursePurchase;
    } catch (error) {
      console.error("Error fetching course purchase requests:", error);
      throw error;
    }
  }

  return {
    getCoursePurchaseRequests,
  };
};

export default useCoursePurchaseRequest;
