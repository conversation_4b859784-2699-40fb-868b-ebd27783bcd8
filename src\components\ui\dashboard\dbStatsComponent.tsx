// import { TrendingUp } from "lucide-react";
import React from "react";

interface DBDataStatsProps {
  module: string;
  total: number;
  icon: React.JSX.Element;
  gradientColors: string;
  trend: string;
}

export function DBSataStats({
  module,
  total,
  icon,
  gradientColors,
  // trend,
}: DBDataStatsProps): React.JSX.Element {
  return (
    <div
      className={`group relative w-full p-6 rounded-xl transition-all duration-500 
      bg-gradient-to-r ${gradientColors} hover:shadow-2xl hover:scale-102
      border border-white/10 backdrop-blur-sm overflow-hidden`}
    >
      {/* Animated background patterns */}
      <div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.1)_50%,transparent_75%,transparent_100%)] bg-[length:500px_500px] animate-subtle-move" />
      <div
        className="absolute -right-8 -bottom-8 w-40 h-40 rounded-full 
        bg-white opacity-5 transition-transform duration-500 
        group-hover:scale-150 group-hover:opacity-10"
      />

      <div className="relative flex items-start justify-between z-10">
        <div className="space-y-3">
          <h3 className="text-lg font-medium text-white/90">{module}</h3>
          <div className="space-y-1">
            <p className="text-3xl font-bold text-white">
              {total !== null && total !== undefined
                ? total.toLocaleString()
                : ""}
            </p>
            {/* {trend !== "" && (
              <div className="flex items-center space-x-1 text-white/80 text-sm">
                <TrendingUp className="w-4 h-4" />
                <span>{trend}</span>
              </div>
            )} */}
          </div>
        </div>

        <div
          className="p-4 rounded-xl bg-white/10 
          backdrop-blur-md transition-all duration-500 
          group-hover:scale-110 group-hover:bg-white/20
          group-hover:rotate-3"
        >
          {icon}
        </div>
      </div>

      {/* Bottom decorative bar */}
      <div
        className="absolute bottom-0 left-0 right-0 h-1 bg-white/10
        transform origin-left transition-transform duration-500
        group-hover:scale-x-100 scale-x-0"
      />
    </div>
  );
  {
    /* <div className="w-full">
      <div
        className={`rounded-md bg-white p-4 ${
          module === "Courses"
            ? "border-b-[2px] border-[#006666]"
            : module === "Videos"
              ? "border-b-[2px] border-[#ffac37]"
              : module === "Exams"
                ? "border-b-[2px] border-[#ff826a]"
                : module === "Practice"
                  ? "border-b-[2px] border-[#85909b]"
                  : "" // Default styling
        }`}
      >
        <div className="flex items-center">
          <span
            className={`relative w-10 h-10 p-2 ${
              module === "Courses"
                ? "bg-[#b2d1d1]"
                : module === "Videos"
                  ? "bg-[#ffe6c3]"
                  : module === "Exams"
                    ? "bg-[#ffd2c9] "
                    : module === "Practice"
                      ? "bg-[#f7f8f9]"
                      : "bg-gray-200 text-gray-500 border-b-[1px] border-gray-300" // Default styling
            }`}
          >
            {icon}
          </span>
          <p className="text-md ml-2 text-[#87929d] ">{module}</p>
        </div>
        <div className="flex flex-col justify-start">
          <p className="mt-4 text-left text-4xl text-black text-[30px] font-bold mr-2">
            {total}
            <span className="text-sm"></span>
          </p>          
        </div>
      </div>
    </div> */
  }
}
