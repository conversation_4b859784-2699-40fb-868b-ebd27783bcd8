"use client";

import React, { useState, useEffect } from "react";
import { getColumns } from "./columns";
import { DataTable } from "@/components/ui/data-table/data-table";
import MainLayout from "../layout/mainlayout";
import { Button } from "@/components/ui/button";
import { pageUrl, privilegeData, months } from "@/lib/constants";
import useCurrentAffairs from "@/hooks/useCurrentAffairs";
import type { CurrentAffairsData, InnerItem } from "@/types";
import { Spinner } from "@/components/ui/progressiveLoader";
import { PlusIcon, Edit, Archive, Eye, PencilLineIcon } from "lucide-react";
import getPrivilegeList from "@/hooks/useCheckPrivilege";
import { Modal } from "@/components/ui/modal";
import PublishAffairs from "./publishAffairs";
import UnPublishAffairs from "./unPublishAffairs";
import DeleteBulletinBoard from "./deleteCurrentAffairs";
import { useRouter } from "next/navigation";
import CurrentAffairsContent from "./currentAffairsContent";
import { Combobox } from "@/components/ui/combobox";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumb";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useTranslation } from "react-i18next";

export default function CurrentAffairsList(): React.JSX.Element {
  const { t } = useTranslation();
  const columns = getColumns(t);
  const router = useRouter();
  const { getCurrentAffairsList } = useCurrentAffairs();
  const [currentAffairs, setCurrentAffairs] = useState<CurrentAffairsData[]>(
    [],
  );
  const [initialData, setInitialData] = useState<CurrentAffairsData[]>([]);
  const [isLoading, setIsLoading] = React.useState<boolean>(true);
  const [disableBtn, setDisableBtn] = useState<boolean>(false);
  const [publishAffairs, setPublish] = useState(false);
  const [affairId, setAffairId] = useState("");
  const [deleteModal, setDeleteModal] = useState<boolean>(false);
  const [draftDialogOpen, setDisableApproveDialogOpen] =
    useState<boolean>(false);
  const [viewOpen, setViewOpen] = React.useState<boolean>(false);
  const [content, setContent] = React.useState<string>("");
  const [title, setTitle] = React.useState<string>("");
  const [monthName, setMonth] = useState("");
  const [searchInput, setSearchInput] = React.useState("");
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);

  const customColumnWidths: Record<string, { width: number; align: string }> = {
    title: { width: 700, align: "justify" },
    publish_date: { width: 150, align: "left" },
  };

  useEffect(() => {
    setBreadcrumbItems(getBreadCrumbItems(t,  t("breadcrumb.currentAffairs"), { "": "" }));
    setSearchInput("");
    currentAffairsList(true);
  }, [t]);
  const currentAffairsList = (value: boolean): void => {
    console.log(value);

    const fetchData = async (): Promise<void> => {
      setIsLoading(true);
      try {
        const currentAffairsList = await getCurrentAffairsList();
        setIsLoading(false);
        if (currentAffairsList.length > 0) {
          currentAffairsList.map((item) => {
            if (item.content != null) {
              item.content = item.content.replace(
                /<pre\b[^>]*>(.*?)<\/pre>/s,
                "<p>$1</p>",
              );
            }
          });
          setInitialData(currentAffairsList);
          setCurrentAffairs(currentAffairsList);
        }
      } catch (error) {
        setIsLoading(false);
        console.error("Error fetching data");
      }
    };
    fetchData().catch((error) => console.log(error));

    const canPerformAction = getPrivilegeList(
      "Current_Affairs",
      privilegeData.Current_Affairs.addCurrentAffairs,
    );
    setDisableBtn(canPerformAction);
  };
  currentAffairs.map((item) => {
    if (item.publish_status !== "Published") {
      item.hideIcon = false;
    } else {
      item.hideIcon = true;
    }
  });
  currentAffairs.map((item) => {
    if (item.publish_status === "Draft") {
      item.hideEditDelete = false;
    } else {
      item.hideEditDelete = true;
    }
  });
  currentAffairs.map((item) => {
    if (item.publish_status === "Draft") {
      item.hideEditDelete = false;
    } else {
      item.hideEditDelete = true;
    }
  });
  currentAffairs.map((item) => {
    if (item.publish_status === "Draft") {
      item.hideEdit = false;
    } else {
      item.hideEdit = true;
    }
  });
  const handleClearSearch = (): void => {
    setSearchInput("");
    setCurrentAffairs(initialData);
  };
  const handlePublish = (data: CurrentAffairsData): void => {
    setPublish(true);
    setAffairId(data.id);
  };
  const publishClose = (): void => {
    setPublish(false);
  };
  const isDisabledApprove = (resourceID: string): void => {
    setDisableApproveDialogOpen(true);
    setAffairId(resourceID);
  };
  const closeDraftDialog = (): void => {
    setDisableApproveDialogOpen(false);
  };

  const editCurrentAffairs = (data: CurrentAffairsData): void => {
    const mode = "edit";
    const extractedData = {
      month: data.month,
      title: data.title,
      content: data.content,
      created_at: data.created_at,
      id: data.id,
    };
    localStorage.setItem("currentAffairsData", JSON.stringify(extractedData));
    router.push(`${pageUrl.addCurrentAffairs}?mode=${mode}`);
  };

  const deleteCurrentAffairs = (data: CurrentAffairsData): void => {
    setAffairId(data.id);
    setDeleteModal(!deleteModal);
  };

  const closeDeleteResource = (): void => {
    setDeleteModal(!deleteModal);
    currentAffairsList(true);
  };

  const addCurrentAffairs = (): void => {
    const mode = "add";
    router.push(`${pageUrl.addCurrentAffairs}?mode=${mode}`);
  };

  const handleViewContents = (value: string, title: string): void => {
    setContent(value);
    setTitle(title);
    setViewOpen(true);
  };
  const closeDialog = (): void => {
    setViewOpen(false);
  };

  const comboSelectedValue = (res: string): void => {
    setMonth(res);
    setSearchInput(res);
  };

  const handleSearch = (): void => {
    if (searchInput !== "") {
      const filterData = initialData.filter(
        (item: CurrentAffairsData) => item.month === monthName,
      );
      setCurrentAffairs(filterData);
    } else {
      setCurrentAffairs(initialData);
    }
  };

  return (
    <MainLayout>
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      <div className="w-full">
        <span>
          <h1 className="text-2xl font-semibold tracking-tight">
            {t("currentAffairs.title")}
            {disableBtn && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      onClick={addCurrentAffairs}
                      className="bg-ring bg-[#fb8500] hover:bg-[#fb5c00] py-2 md:py-3 px-4 md:px-6 whitespace-nowrap ml-2"
                    >
                      <PlusIcon className="h-4 w-4 md:h-5 md:w-5" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{t("currentAffairs.addCurrentAffair")}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </h1>
        </span>
        <div className="border rounded-md p-4 mt-4 bg-[#fff]">
          <div className="flex items-center ps-1">
            <div className="w-1/4 flex items-center space-x-4">
              <div className="w-full">
                <label className="ps-2 pb-4">{t("currentAffairs.selectMonth")}</label>
                <Combobox
                  data={months}
                  onSelectChange={comboSelectedValue}
                  defaultLabel={monthName ?? ""}
                />
              </div>
            </div>
            <div className="pl-2">
              <Button
                className="px-2 mt-5 text-sm w-auto justify-center bg-[#9FC089]"
                onClick={handleSearch}
              >
                {t("buttons.search")}
              </Button>
            </div>
            <div className="pl-2">
              <Button
                className="px-2 mt-5 text-sm w-auto justify-center bg-[#33363F]"
                onClick={handleClearSearch}
              >
                {t("buttons.clear")}
              </Button>
            </div>

            {/* <div className="ml-auto mt-5 flex">
              {disableBtn && (
                <Button
                  className="font-bold py-2 px-4 rounded-md bg-[#fb8500] hover:bg-[#fb5c00]"
                  onClick={addCurrentAffairs}
                >
                  <PlusIcon className="h-5 w-5" /> Current Affairs
                </Button>
              )}
            </div> */}
          </div>
          {isLoading ? (
            <Spinner />
          ) : (
            <div className="overflow-x-auto ps-1">
              <DataTable
                columns={columns}
                data={currentAffairs}
                FilterLabel={"Filter by Event Title"}
                FilterBy={"title"}
                disablePublish={"hideIcon"}
                disableEditDelete={"hideEditDelete"}
                hideEdit={"hideEdit"}
                actions={[
                  {
                    title: `${t("currentAffairs.viewContent")}`,
                    icon: Eye,
                    varient: "icon",
                    color: "#9bbb5c",
                    handleClick: (val: unknown) => {
                      handleViewContents(
                        (val as CurrentAffairsData).content,
                        (val as CurrentAffairsData).title,
                      );
                    },
                  },
                  {
                    title: `${t("currentAffairs.edit")}`,
                    icon: Edit,
                    varient: "icon",
                    color: "#fb8500",
                    handleClick: (val: unknown) => {
                      editCurrentAffairs(val as CurrentAffairsData);
                    },
                  },
                  {
                    title: `${t("currentAffairs.delete")}`,
                    icon: Archive,
                    varient: "icon",
                    color: "#ff0000",
                    handleClick: (val: unknown) => {
                      deleteCurrentAffairs(val as CurrentAffairsData);
                    },
                  },
                  {
                    title: `${t("currentAffairs.publish")}`,
                    icon: PencilLineIcon,
                    color: "#fb8500",
                    isEnable: getPrivilegeList(
                      "Current_Affairs",
                      privilegeData.Current_Affairs.publishCurrentAffairs,
                    ),
                    varient: "icon",
                    handleClick: (val: unknown) => {
                      handlePublish(val as CurrentAffairsData);
                    },
                    disabled: (val: unknown) => {
                      isDisabledApprove((val as CurrentAffairsData).id);
                    },
                  },
                ]}
                customColumnWidths={customColumnWidths}
              />
            </div>
          )}
        </div>
      </div>
      {publishAffairs && (
        <Modal
          title={t("currentAffairs.publishCurrentAffairs")}
          header=""
          openDialog={publishAffairs}
          closeDialog={publishClose}
        >
          <PublishAffairs
            onSave={(value: boolean) => currentAffairsList(value)}
            onCancel={publishClose}
            affairId={affairId}
          />
        </Modal>
      )}
      {draftDialogOpen && (
        <Modal
          title={t("currentAffairs.unpublishEvent")}
          header=""
          openDialog={draftDialogOpen}
          closeDialog={closeDraftDialog}
        >
          <UnPublishAffairs
            onSave={(value: boolean) => currentAffairsList(value)}
            onCancel={closeDraftDialog}
            affairId={affairId}
          />
        </Modal>
      )}
      {deleteModal && (
        <Modal
          title={t("currentAffairs.deleteEvent")}
          header=""
          openDialog={deleteModal}
          closeDialog={closeDeleteResource}
        >
          <DeleteBulletinBoard
            onCancel={closeDeleteResource}
            bulletinId={affairId}
          />
        </Modal>
      )}
      {viewOpen && (
        <Modal
          title=""
          header=""
          openDialog={viewOpen}
          closeDialog={closeDialog}
          type="max-w-7xl"
        >
          <CurrentAffairsContent
            onCancel={closeDialog}
            content={content}
            title={title}
          ></CurrentAffairsContent>
        </Modal>
      )}
    </MainLayout>
  );
}
