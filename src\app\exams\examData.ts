const examData = [
  {
    id: "c1d06207-3ae4-4f0b-9210-1649d166742d",
    name: "Exam Check for Type",
    org_id: "19579370-a028-484d-8f35-8af2023068dd",
    duration: 25,
    end_time: "2023-10-10T07:30:00+00:00",
    course_id: "3c1168c1-a39f-4b7c-80ed-c33ce6e2ebe0",
    pass_mark: 35,
    quiz_type: "Practice",
    created_at: "2023-09-20T10:49:23.835271+00:00",
    is_premium: false,
    main_topic: "LDC New",
    start_time: "2023-08-14T18:30:00+00:00",
    total_mark: 50,
    updated_at: "2023-09-20T10:49:23.835271+00:00",
    description: "<p>Exam test for Type</p>",
    allowed_attempts: 15,
    num_of_questions: 5,
    penalty_available: true,
  },
  {
    id: "8084c264-0798-4b85-b68f-a180ef6c5486",
    name: "General Knowledge - GK - New Exam",
    org_id: "19579370-a028-484d-8f35-8af2023068dd",
    duration: 15,
    end_time: "2023-10-09T09:30:00+00:00",
    course_id: "3c1168c1-a39f-4b7c-80ed-c33ce6e2ebe0",
    pass_mark: 20,
    quiz_type: "Main",
    created_at: "2023-08-21T10:24:06.800394+00:00",
    is_premium: false,
    main_topic: "LDC ",
    start_time: "2023-08-15T02:30:00+00:00",
    total_mark: 25,
    updated_at: "2023-08-21T10:24:06.800394+00:00",
    description: "<p>LDC Practise Main</p>",
    allowed_attempts: 5,
    num_of_questions: 5,
    penalty_available: false,
  },
  {
    id: "fffb8155-84a2-4c7c-910a-a6f07c612f3b",
    name: "LDC Revised Exam",
    org_id: "19579370-a028-484d-8f35-8af2023068dd",
    duration: 15,
    end_time: "2023-10-09T07:30:00+00:00",
    course_id: "3c1168c1-a39f-4b7c-80ed-c33ce6e2ebe0",
    pass_mark: 20,
    quiz_type: "Main",
    created_at: "2023-08-21T09:26:25.04071+00:00",
    is_premium: false,
    main_topic: "LDC ",
    start_time: "2023-08-15T02:30:00+00:00",
    total_mark: 25,
    updated_at: "2023-08-21T09:26:25.04071+00:00",
    description: "<p>Test exam for LDC</p>",
    allowed_attempts: 5,
    num_of_questions: 5,
    penalty_available: true,
  },
  {
    id: "a8609252-1ce2-41c1-aac5-19f6b85385e8",
    name: "General GK English",
    org_id: "19579370-a028-484d-8f35-8af2023068dd",
    duration: 15,
    end_time: "2023-10-09T07:30:00+00:00",
    course_id: "3c1168c1-a39f-4b7c-80ed-c33ce6e2ebe0",
    pass_mark: 35,
    quiz_type: "Main",
    created_at: "2023-08-18T04:21:58.567461+00:00",
    is_premium: false,
    main_topic: "LDC ",
    start_time: "2023-08-14T18:30:00+00:00",
    total_mark: 50,
    updated_at: "2023-08-18T04:21:58.567461+00:00",
    description: "<p>GK English exam</p>",
    allowed_attempts: 5,
    num_of_questions: 5,
    penalty_available: false,
  },
  {
    id: "3697a506-d7c9-4a71-99c3-f4d6be19530a",
    name: "LDC Practise Test II",
    org_id: "19579370-a028-484d-8f35-8af2023068dd",
    duration: 25,
    end_time: "2023-10-09T07:30:00+00:00",
    course_id: "3c1168c1-a39f-4b7c-80ed-c33ce6e2ebe0",
    pass_mark: 15,
    quiz_type: "Main",
    created_at: "2023-08-14T04:55:48.100173+00:00",
    is_premium: false,
    main_topic: "LDC ",
    start_time: "2023-08-09T18:30:00+00:00",
    total_mark: 20,
    updated_at: "2023-08-14T04:55:48.100173+00:00",
    description: "<p>Test</p>",
    allowed_attempts: 25,
    num_of_questions: 10,
    penalty_available: true,
  },
  {
    id: "1487c199-b5e0-41f4-8bd8-0ab993a5a277",
    name: "LDC Practice Exam",
    org_id: "19579370-a028-484d-8f35-8af2023068dd",
    duration: 90,
    end_time: "2023-09-15T13:00:00+00:00",
    course_id: "3c1168c1-a39f-4b7c-80ed-c33ce6e2ebe0",
    pass_mark: 80,
    quiz_type: "Main",
    created_at: "2023-08-14T03:59:19.742157+00:00",
    is_premium: false,
    main_topic: "LDC ",
    start_time: "2023-08-13T18:30:00+00:00",
    total_mark: 100,
    updated_at: "2023-08-14T03:59:19.742157+00:00",
    description: "<p>LDC Practise Test</p>",
    allowed_attempts: 25,
    num_of_questions: 50,
    penalty_available: false,
  },
];
export default examData;
