"use client";
import React, { useState, useEffect } from "react";
import {
  PlusIcon,
  Edit,
  LockIcon,
  Trash,
  PencilLine,
  Check,
} from "lucide-react";
import MainLayout from "../layout/mainlayout";
import useTopic from "@/hooks/useTopics";
import { useToast } from "@/components/ui/use-toast";
import { Spinner } from "@/components/ui/progressiveLoader";
import type { TreeTableToggleEvent } from "primereact/treetable";
import { TreeTable } from "primereact/treetable";
import { Column } from "primereact/column";
import type { ErrorCatch, InnerItem, ToastType, TreeTableItem } from "@/types";
import { Button } from "@/components/ui/button";
import { Modal } from "@/components/ui/modal";
import AddTopic from "./addTopic";
import "primereact/resources/themes/lara-light-indigo/theme.css";
import { Input } from "@/components/ui/input";
import getPrivilegeList from "@/hooks/useCheckPrivilege";
import { DOCUMENT_PUBLISHED, privilegeData } from "@/lib/constants";
import DeleteCategory from "./deleteCategory";
import PublishCategory from "./publishCategory";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumb";
import {
  Tooltip,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { TooltipContent } from "@radix-ui/react-tooltip";
import { useTranslation } from "react-i18next";
import { ERROR_MESSAGES } from "@/lib/messages";
interface TreeNode {
  key: string;
  label: string;
  children?: TreeNode[];
  description?: string | undefined;
  is_premium: boolean;
  is_parent: boolean | undefined;
  publish_status?: string;
  data: {
    key: string;
    label: string;
    description: string | undefined;
    children?: TreeNode[];
    is_premium: boolean;
    publish_status?: string;
  };
}

export default function TopicList(): React.JSX.Element {
  const [data, setData] = useState<TreeNode[]>([]);
  const [initialData, setInitialData] = useState<TreeNode[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [expandedKeys, setExpandedKeys] = useState<Record<string, boolean>>({});
  const [addMainCategory, setAddMaincategory] = useState<boolean>(false);
  const [reloadTable, setReloadTable] = useState<boolean>(false);
  const { toast } = useToast() as ToastType;
  const { t } = useTranslation();
  const { getCategoryHierarchy } = useTopic();
  const [passData, setPassData] = useState<TreeTableItem>();
  const [title, setTitle] = useState<string>("");
  const [searchInput, setSearchInput] = React.useState("");
  const [disableBtn, setDisableBtn] = useState<boolean>(false);
  const [disableAddMain, setDisableAddMain] = useState<boolean>(false);
  const [disableAddSub, setDisableAddSub] = useState<boolean>(false);
  const [disableEdit, setDisableEdit] = useState<boolean>(false);
  const [disableDelete, setDisableDelete] = useState<boolean>(false);
  const [disablePublish, setDisablePublish] = useState<boolean>(false);
  const [categoryId, setCategoryId] = useState<string>("");
  const [isOpenDeleteDialog, setIsOpenDeleteDialog] = useState<boolean>(false);
  const [isOpenPulishDialog, setIsOpenPublishDialog] = useState<boolean>(false);
  const [reloadResource, setReloadResource] = useState<boolean>(false);
  const [publishStatus, setPublishStatus] = useState<string>("");
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);

  useEffect(() => {
    setBreadcrumbItems(
      getBreadCrumbItems(t, t("breadcrumb.topics"), { "": "" }),
    );
  }, [t]);

  useEffect(() => {
    const fetchData = async (): Promise<void> => {
      setIsLoading(true);
      const org_id = localStorage.getItem("orgId") as string;
      const params = {
        org_id: org_id,
        filter_data: 0,
      };
      try {
        const topics = await getCategoryHierarchy(params);
        if (topics !== null && topics !== undefined) {
          topics.sort((item, data) => {
            return item.label.localeCompare(data.label);
          });
          topics.forEach((topics) => {
            if (topics.children && Array.isArray(topics.children)) {
              topics.children.sort((item, data) => {
                return item.label.localeCompare(data.label);
              });
            }
          });
          topics.forEach((item) => {
            item.is_parent = true;
            setChildIsParentFalse(item);
          });
          setData(topics.map(transformNode));
          setInitialData(topics.map(transformNode));
          setIsLoading(false);
        } else {
          setIsLoading(false);
        }
      } catch (error) {
        setIsLoading(false);
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
      }
    };
    fetchData().catch((error) => console.log(error));
    const canPerformAction = getPrivilegeList(
      "Topic",
      privilegeData.Topic.addParentCourseCategory,
    );
    const canPerformAdd = getPrivilegeList(
      "Topic",
      privilegeData.Topic.addMainCategory,
    );
    const canPerformAddSub = getPrivilegeList(
      "Topic",
      privilegeData.Topic.addSubCategory,
    );
    const canPerformEdit = getPrivilegeList(
      "Topic",
      privilegeData.Topic.updateCategory,
    );
    const canPerformDelete = getPrivilegeList(
      "Topic",
      privilegeData.Topic.deleteCategory,
    );
    const canPerformPublish = getPrivilegeList(
      "Topic",
      privilegeData.Topic.PublishCategory,
    );
    setDisableBtn(canPerformAction);
    setDisableAddMain(canPerformAdd);
    setDisableAddSub(canPerformAddSub);
    setDisableEdit(canPerformEdit);
    setDisableDelete(canPerformDelete);
    setDisablePublish(canPerformPublish);
  }, [reloadTable, reloadResource]);

  const setChildIsParentFalse = (node: TreeTableItem): void => {
    if (node.children && Array.isArray(node.children)) {
      node.children.forEach((child) => {
        child.is_parent = false;
        setChildIsParentFalse(child);
      });
    }
  };
  const transformNode = (node: TreeTableItem): TreeNode => {
    const transformedNode: TreeNode = {
      key: node.value,
      label: node.label,
      description: node.description,
      is_premium: node.is_premium as boolean,
      is_parent: node.is_parent,
      publish_status: node.publish_status,
      data: {
        key: node.value,
        label: node.label,
        description: node.description,
        is_premium: node.is_premium as boolean,
        publish_status: node.publish_status,
      },
    };

    if (node.children && node.children.length > 0) {
      transformedNode.children = node.children.map(transformNode);
    }
    return transformedNode;
  };

  const onNodeToggle = (event: TreeTableToggleEvent): void => {
    const newExpandedKeys: Record<string, boolean> = {};
    for (const key in event.value) {
      if (event.value[key]) {
        newExpandedKeys[key] = true;
      }
    }
    setExpandedKeys(newExpandedKeys);
  };
  const handleClearSearch = (): void => {
    setSearchInput("");
    setData(initialData ?? []);
  };

  const addSubCategory = (rowData: TreeTableItem): void => {
    setTitle(String(t("topics.addCategory")));
    setPassData(rowData);
    setAddMaincategory(!addMainCategory);
  };
  const editSubCategory = (rowData: TreeTableItem): void => {
    setTitle(String(t("topics.updateCategory")));
    setPassData(rowData);
    setAddMaincategory(!addMainCategory);
  };

  const categoryDelete = (rowData: TreeTableItem): void => {
    setCategoryId(rowData.key as string);
    setIsOpenDeleteDialog(true);
  };
  const categoryPublish = (rowData: TreeTableItem): void => {
    setPublishStatus(rowData.publish_status as string);
    setCategoryId(rowData.key as string);
    setIsOpenPublishDialog(true);
  };
  const closeDeleteDialog = (): void => {
    setIsOpenDeleteDialog(false);
  };
  const closePublishDialog = (): void => {
    setIsOpenPublishDialog(false);
  };
  const handleDeleteReload = (): void => {
    setReloadResource(!reloadResource);
  };
  const handlePublishReload = (): void => {
    setReloadResource(!reloadResource);
  };

  const addMainCategoryModal = (): void => {
    setTitle(String(t("topics.parentCategoryInfo")));
    setAddMaincategory(!addMainCategory);
  };
  const handleSearch = (): void => {
    if (searchInput !== "") {
      const filteredData =
        initialData?.filter((topic) => {
          return searchNode(topic, searchInput.toLowerCase());
        }) ?? [];
      setData(filteredData);
    } else {
      setData(initialData ?? []);
    }
  };

  const searchNode = (node: TreeNode, searchValue: string): boolean => {
    const nodeLabel = node.label.toLowerCase();
    if (nodeLabel.includes(searchValue)) {
      return true;
    }
    if (node.children && node.children.length > 0) {
      for (const childNode of node.children) {
        if (searchNode(childNode, searchValue)) {
          setExpandedKeys((prevExpandedKeys) => ({
            ...prevExpandedKeys,
            [node.key]: true,
          }));
          return true;
        }
      }
    }
    return false;
  };

  const reloadTableData = (): void => {
    setExpandedKeys({});
    setReloadTable(!reloadTable);
    setAddMaincategory(!addMainCategory);
  };

  return (
    <MainLayout>
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      <span>
        <h1 className="text-2xl font-semibold">
          {String(t("topics.title"))}
          {disableBtn && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    onClick={addMainCategoryModal}
                    className="bg-ring bg-[#fb8500] hover:bg-[#fb5c00] py-2 md:py-3 px-4 md:px-6 whitespace-nowrap ml-2"
                  >
                    <PlusIcon className="h-4 w-4 md:h-5 md:w-5" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent className="text-xl">
                  <p>{String(t("topics.addNewTopic"))}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </h1>
      </span>
      <div className="border rounded-md p-4 mt-4 bg-[#fff]">
        <h1 className="text-2xl font-semibold tracking-tight pb-5">
          <div className="flex flex-col md:flex-row items-start md:items-end justify-between">
            <div className="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4 items-start md:items-center w-full">
              <div className="w-full md:w-auto">
                <Input
                  autoComplete="off"
                  type="text"
                  className="min-w-[250px] md:min-w-[430px] font-normal flex justify-between items-center"
                  placeholder={String(t("topics.searchByName"))}
                  value={searchInput}
                  onChange={(e) => setSearchInput(e.target.value.trimStart())}
                />
              </div>
              <Button
                className="px-4 py-2 text-sm w-full md:w-auto mt-4 md:mt-0"
                onClick={handleSearch}
                style={{ backgroundColor: "#9FC089" }}
              >
                {String(t("topics.search"))}
              </Button>
              <Button
                className="px-2 py-2 text-sm w-full md:w-auto justify-end "
                onClick={handleClearSearch}
                style={{ backgroundColor: "#33363F" }}
              >
                {String(t("topics.clear"))}
              </Button>
            </div>
          </div>
        </h1>

        <div>
          {isLoading ? (
            <div className="flex justify-center items-center">
              <Spinner />
            </div>
          ) : (
            <div>
              <TreeTable
                value={data}
                className="custom-tree-table w-full min-w-[50rem]"
                expandedKeys={expandedKeys}
                onToggle={onNodeToggle}
                paginator
                rows={10}
                paginatorTemplate="RowsPerPageDropdown FirstPageLink PrevPageLink CurrentPageReport NextPageLink LastPageLink"
                currentPageReportTemplate="{first} to {last} of {totalRecords}"
                rowsPerPageOptions={[5, 10, 25, 50]}
                tableStyle={{ minWidth: "50rem" }}
              >
                <Column
                  field="label"
                  expander
                  sortable
                  header={String(t("topics.name"))}
                  className="custom-tree-table-column ps-4 sm:w-1/3 md:w-1/4"
                />
                <Column
                  field="description"
                  header={String(t("topics.description"))}
                  className="custom-tree-table-column ps-2 sm:w-1/3 md:w-1/4"
                />
                <Column
                  header={
                    <div className="text-center">{String(t("topics.add"))}</div>
                  }
                  body={(rowData: TreeTableItem) => (
                    <div className="flex items-center justify-center cursor-pointer text-[#1ea185]">
                      {rowData.is_parent === true && disableAddMain && (
                        <PlusIcon onClick={() => addSubCategory(rowData)} />
                      )}
                      {rowData.is_parent === false && disableAddSub && (
                        <PlusIcon onClick={() => addSubCategory(rowData)} />
                      )}
                    </div>
                  )}
                  className="custom-tree-table-column text-center w-10 sm:w-16"
                />
                <Column
                  header={
                    <div className="text-center">
                      {String(t("topics.edit"))}
                    </div>
                  }
                  body={(rowData: TreeTableItem) => (
                    <div className="flex items-center justify-center cursor-pointer text-[#9bbb5c]">
                      {disableEdit && (
                        <Edit
                          onClick={() => editSubCategory(rowData)}
                          opacity={
                            rowData.publish_status !== "Published" ? 1 : 0
                          }
                          pointerEvents={
                            rowData.publish_status !== "Published"
                              ? "auto"
                              : "none"
                          }
                        />
                      )}
                    </div>
                  )}
                  className="custom-tree-table-column text-center w-10 sm:w-16"
                />
                <Column
                  header={
                    <div className="text-center">
                      {String(t("topics.delete"))}
                    </div>
                  }
                  body={(rowData: TreeTableItem) => (
                    <div className="flex items-center justify-center cursor-pointer text-[#f29b26]">
                      {disableDelete && (
                        <Trash
                          onClick={() => categoryDelete(rowData)}
                          opacity={
                            rowData.publish_status !== "Published" ? 1 : 0
                          }
                          pointerEvents={
                            rowData.publish_status !== "Published"
                              ? "auto"
                              : "none"
                          }
                        />
                      )}
                    </div>
                  )}
                  className="custom-tree-table-column text-center w-10 sm:w-16"
                />
                {disablePublish && (
                  <Column
                    header={
                      <div className="text-center">
                        {String(t("topics.status"))}
                      </div>
                    }
                    body={(rowData: TreeTableItem) => (
                      <div className="flex items-center justify-center w-full cursor-pointer text-[#bd392f]">
                        {rowData.publish_status !== DOCUMENT_PUBLISHED ? (
                          <span title="Draft">
                            <PencilLine
                              onClick={() => categoryPublish(rowData)}
                              color="#bd392f"
                              className="w-5 h-5"
                            />
                          </span>
                        ) : (
                          <span title="Published">
                            <Check
                              onClick={() => categoryPublish(rowData)}
                              color="#15cce5"
                              className="w-5 h-5"
                            />
                          </span>
                        )}
                      </div>
                    )}
                    className="custom-tree-table-column text-center w-16 sm:w-24"
                  />
                )}
                <Column
                  field=""
                  header={
                    <div className="text-center">
                      {String(t("topics.premium"))}
                    </div>
                  }
                  body={(rowData: TreeNode) => (
                    <div
                      className="flex items-center justify-center w-full cursor-pointer text-[#445469]"
                      title="Premium Topic"
                    >
                      {rowData.data.is_premium && (
                        <LockIcon className="h-5 w-5 text-gray-500" />
                      )}
                    </div>
                  )}
                  className="custom-tree-table-column text-center w-10 sm:w-20"
                />
              </TreeTable>
            </div>
          )}
        </div>
        {addMainCategory && (
          <Modal
            title={title}
            header=""
            openDialog={addMainCategory}
            closeDialog={addMainCategoryModal}
          >
            <AddTopic
              onSave={reloadTableData}
              onCancel={addMainCategoryModal}
              isModal={true}
              passData={passData}
              title={title}
            />
          </Modal>
        )}

        {isOpenDeleteDialog && (
          <Modal
            title={String(t("topics.deleteCategory"))}
            header=""
            openDialog={isOpenDeleteDialog}
            closeDialog={closeDeleteDialog}
          >
            <DeleteCategory
              onSave={handleDeleteReload}
              onCancel={closeDeleteDialog}
              categoryId={categoryId}
            />
          </Modal>
        )}
        {isOpenPulishDialog && (
          <Modal
            title={String(t("topics.categoryStatus"))}
            header=""
            openDialog={isOpenPulishDialog}
            closeDialog={closePublishDialog}
          >
            <PublishCategory
              onSave={handlePublishReload}
              onCancel={closePublishDialog}
              categoryId={categoryId}
              publishStatus={publishStatus}
            />
          </Modal>
        )}
      </div>
    </MainLayout>
  );
}
