import React from "react";
import { Button } from "@/components/ui/button";
import { useTranslation } from "react-i18next";

export default function ComfirmSubmit({
  data,
  onCancel,
  onSave,
}: {
  onSave: () => void;
  onCancel: () => void;
  data: string;
  isModal?: boolean;
}): React.JSX.Element {
  const { t } = useTranslation();
  const handleDeleteClick = (): void => {
    onSave();
  };

  return (
    <>
      <div className="mb-2 mr-4">
        <p className="ml-0">{t("confirmationModal.submitWithoutData", { data })}</p>
      </div>
      <div className="flex topic-icons pr-4">
        <div className="flex items-center justify-center float-right">
          <Button type="button" className="bg-[#33363F]" onClick={onCancel}>
            {t("buttons.cancel")}
          </Button>
          &nbsp;
          <Button
            type="submit"
            className="bg-[#9FC089]"
            onClick={handleDeleteClick}
          >
            {t("buttons.submit")}
          </Button>
        </div>
      </div>
    </>
  );
}
