"use client";
import MainLayout from "../layout/mainlayout";
import { Combobox } from "@/components/ui/combobox";
import type {
  ComboData,
  ErrorCatch,
  SubscriptionRequestType,
  CourseDatas,
  InnerItem,
  CourseIdList,
  LogUserActivityRequest,
  ToastType,
} from "@/types";
import { Label } from "@radix-ui/react-label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Button } from "@/components/ui/button";
import { DataTable } from "@/components/ui/data-table/data-table";
import { getCourseColumns, getResourceColumns } from "./columns";
import React, { useEffect, useState } from "react";

import { Spinner } from "@/components/ui/progressiveLoader";
import Link from "next/link";
import { ORG_KEY, pageUrl } from "@/lib/constants";
import { useRouter, useSearchParams } from "next/navigation";
import useSubscription from "@/hooks/useSubscription";
import { useToast } from "@/components/ui/use-toast";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import { Modal } from "@/components/ui/modal";
import ComfirmSubmit from "@/components/ui/confirmationmodal";
import NoDataFound from "@/components/ui/noDataFound";
import { Input } from "@/components/ui/input";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumb";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";

export default function CourseResourcePage(): React.JSX.Element {
  const { t } = useTranslation();
  const { toast } = useToast() as ToastType;
  const columns_course = getCourseColumns(t);
  const columns_resource = getResourceColumns(t);
  const [courseData, setCourseData] = useState<
    { value?: string; label?: string }[]
  >([]);
  const [radiobtn, setRadioBtn] = React.useState(false);
  const [moduleData, setModuleData] = React.useState<CourseDatas[]>([]);
  const [resourceData, setResourceData] = React.useState<CourseDatas[]>([]);
  const [isLoading, setIsLoading] = React.useState<boolean>(true);
  const [noResults, setNoResults] = React.useState<boolean>(false);
  const [noModuleResults, setNoModuleResults] = React.useState<boolean>(false);
  const [emptyChoice, setEmptyChoice] = useState<boolean>(false);
  const [courseId, setCourseId] = useState<string>("");
  const [data, setdata] = useState<string>("");
  //const [state, setState] = useState({
  //  module : []
  //})
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);
  const {
    getCourseListForPlan,
    updateCourse,
    getResoureceList,
    updateResource,
  } = useSubscription();

  const router = useRouter();
  const searchParams = useSearchParams();
  const subscription_id = searchParams.get("type") ?? "";
  const defaultName = searchParams.get("name") ?? "";
  const type = searchParams.get("based_on") ?? "";
  const orgId = localStorage.getItem(ORG_KEY) ?? "";
  const expired = searchParams.get("expired");
  const [defaultType, setDefaultType] = useState<string>("course");
  const { updateUserActivity } = useLogUserActivity();
  let title_variable = "";
  if (type === "course_based") {
    title_variable = "Course";
  } else if (type === "resource_based") {
    title_variable = "Resource";
  } else {
    title_variable = "Time";
  }
  const getBasedOn = (basedOn: string): void => {
    switch (basedOn) {
      case "resource_based":
        setDefaultType("resource_based");
        setRadioBtn(true);
        break;
      case "course_based":
        setDefaultType("course_based");
        setRadioBtn(false);
        break;
      case "time_based":
        setDefaultType("time_based");
        setRadioBtn(false);
        break;
    }
  };
  useEffect(() => {
    setBreadcrumbItems(
      getBreadCrumbItems(t, t("breadcrumb.updateSubscriptionPlan"), { "": "" }),
    );
    getBasedOn(type);
    getCourseData(subscription_id);
  }, [t]);

  // function to get course details based on plan
  const getCourseData = (subId: string): void => {
    const passData = {
      plan_id: subId,
      org_id: orgId,
      mapping_type: type,
    };
    const fetchCourseData = async (): Promise<void> => {
      try {
        const result = await getCourseListForPlan(
          passData as SubscriptionRequestType,
        );
        setIsLoading(false);
        if (
          result !== null &&
          result !== undefined &&
          Object.keys(result).length > 0
        ) {
          const uniqueCourses = Array.from(
            new Set(result.courses_data?.map((course) => course.id)),
          ).map((id) => {
            return result.courses_data?.find((course) => course.id === id);
          });
          uniqueCourses
            .filter((item): item is CourseDatas => item !== undefined)
            .map((item: CourseDatas) => {
              if (item.is_expired) {
                item.disableExpired = true;
              }
            });
          setModuleData(uniqueCourses as CourseDatas[]);

          setNoModuleResults(false);
          const filteredCourses = uniqueCourses
            ?.filter((course) => course?.id != null && course.short_name)
            .map((course) => ({
              value: course?.id,
              label: course?.short_name,
            }));
          setCourseData(filteredCourses as ComboData[]);

          if (filteredCourses.length == 0) {
            setNoModuleResults(true);
          } else {
            setNoModuleResults(false);
          }
        } else {
          setCourseData([]);
        }
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
      }
    };
    fetchCourseData().catch((error) => console.log(error));
  };

  const handleCourseChange = (courseId: string): void => {
    void courseDataById(courseId as string);
    setCourseId(courseId);
  };

  // to get resource data based on course selected
  const courseDataById = async (courseId: string): Promise<void> => {
    const passData = {
      plan_id: subscription_id,
      org_id: orgId,
      course_id: courseId,
    };
    setIsLoading(true);
    try {
      const response = await getResoureceList(
        passData as SubscriptionRequestType,
      );
      if (response.resources_data?.length === 0) {
        setNoResults(true);
      } else {
        setNoResults(false);
      }
      setResourceData(
        response.resources_data?.map((resource) => ({
          ...resource,
          full_name: resource.name,
        })) as CourseDatas[],
      );
      setIsLoading(false);
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
      console.error("Error fetching data:", error);
    }
  };

  const getAddedItem = (dataIndex: string, isSelect: boolean): void => {
    const index = parseInt(dataIndex);
    if (radiobtn == false) {
      if (isSelect === true) {
        moduleData[index].is_part_of_plan = true;
      } else {
        moduleData[index].is_part_of_plan = false;
      }
    } else {
      if (isSelect === true) {
        resourceData[index].is_part_of_plan = true;
      } else {
        resourceData[index].is_part_of_plan = false;
      }
    }

    // const selectedRow: ResourceForm = moduleData[index];
    // setAllSelectedRows((prevRows) => {
    //   let updatedRows: ResourceForm[];
    //   if (isSelect) {
    //     updatedRows = [...prevRows, selectedRow];
    //   } else {
    //     updatedRows = prevRows.filter((row) => row !== selectedRow);
    //   }
    //   const uniqueRows = Array.from(
    //     new Set(updatedRows.map((row) => row.course_module_id)),
    //   );
    //   return uniqueRows.map(
    //     (moduleId) =>
    //       updatedRows.find(
    //         (row) => row.course_module_id === moduleId,
    //       ) as ResourceForm,
    //   );
    // });
  };

  // const handlePlanChange = (value: string): void => {
  //   getCourseData(value);
  //   if (defaultType === "resource_based") {
  //     void courseDataById(courseId);
  //   }
  //   setSubscriptionId(value);
  //   const data = subscriptionData.find((item) => item.id === value);
  //   const basedOn = data?.subscription_type;
  //   getBasedOn(basedOn as string);
  //   setResourceData([]);
  // };

  // const handleDelete = (val: ResourceForm): void => {
  //   setAllSelectedRows((prevRows) => prevRows.filter((row) => row !== val));
  //   setModuleData((prevModuleData) =>
  //     prevModuleData.map((item) => {
  //       if (item === val) {
  //         item.is_part_of_plan = false;
  //       }
  //       return item;
  //     }),
  //   );
  // };
  const addSessionCancel = (): void => {
    router.push(pageUrl.membershipPlanList);
  };
  const onSubmitData = (): void => {
    const isPartOfGroup = moduleData?.filter(
      (item) => item.is_part_of_plan === true,
    );
    const idList: CourseIdList[] = isPartOfGroup.map((item) => ({
      course_id: item.id as string,
      is_expired: item.is_expired as boolean,
    }));
    if (idList?.length !== 0) {
      setEmptyChoice(false);
      updateCourseData(idList);
    } else {
      setdata("Course");
      setEmptyChoice(true);
    }
  };
  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  const updateCourseData = (idList: CourseIdList[]): void => {
    const passData = {
      plan_id: subscription_id,
      org_id: orgId,
      courses: idList,
    };
    const updateCourseData = async (): Promise<void> => {
      try {
        await updateCourse(passData as SubscriptionRequestType);
        router.push(pageUrl.membershipPlanList);
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.update_course_to_plan_title"),
          description: t("successMessages.update_course_to_plan_msg"),
        });
        const params = {
          activity_type: "Subscription",
          screen_name: "Link Course to Subscription",
          action_details: "Link course to subscription  ",
          target_id: subscription_id,
          log_result: "SUCCESS",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.update_course_to_plan_title"),
          description: err.message,
        });
        const params = {
          activity_type: "Subscription",
          screen_name: "Link Course to Subscription",
          action_details: "Failed to link course to subscription  ",
          target_id: subscription_id,
          log_result: "ERROR",
        };

        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      }
    };
    updateCourseData().catch((error) => console.log(error));
  };
  const onSubmit = (): void => {
    if (radiobtn === false) {
      updateCourseData([]);
    } else {
      updatePlanData([]);
    }
  };

  const closeConfirmation = (): void => {
    setEmptyChoice(false);
  };
  const onSubmitResource = (): void => {
    const isPartOfGroup = resourceData?.filter(
      (item) => item.is_part_of_plan === true,
    );
    const idList = isPartOfGroup.map((item) => item.course_module_id);
    if (idList?.length !== 0) {
      setEmptyChoice(false);

      updatePlanData(idList as string[]);
    } else {
      setdata("Resource");
      setEmptyChoice(true);
    }
  };
  const updatePlanData = (idList: string[]): void => {
    const passData = {
      plan_id: subscription_id,
      org_id: orgId,
      course_module_ids: idList,
      course_id: courseId,
    };
    const updatePlanData = async (): Promise<void> => {
      try {
        await updateResource(passData as SubscriptionRequestType);
        router.push(pageUrl.membershipPlanList);
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.update_resource_to_plan_title"),
          description: t("successMessages.update_resource_to_plan_msg"),
        });
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.update_resource_to_plan_title"),
          description: err.message,
        });
      }
    };
    updatePlanData().catch((error) => console.log(error));
  };

  return (
    <MainLayout>
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      <h1 className="text-2xl font-semibold tracking-tight pb-5">
        {t("subscriptionPlan.updateToSubscription", { title_variable })}
      </h1>
      <div className="border rounded-md p-4 bg-[#fff]">
        <div className="w-full flex flex-wrap justify-between space-x-4">
          <div className="flex space-x-4">
            <div className="w-full sm:w-1/2">
              <div className="w-full">
                <Label className="block">
                  {t("subscriptionPlan.planSelected")}
                </Label>
                <div
                  className="w-full course-width mt-2"
                  style={{ display: "none" }}
                >
                  {/* <Combobox
                    data={planData}
                    onSelectChange={handlePlanChange}
                    defaultLabel={defaultName}
                  /> */}
                </div>
                <div className="w-full course-width mt-2">
                  <Input type="text" value={defaultName} disabled />
                </div>
              </div>
            </div>

            <div className="pl-5">
              <div className="">
                <Label className="block">
                  {t("subscriptionPlan.basedOn")}:
                </Label>
                <div className="course-width mt-2">
                  <RadioGroup style={{ display: "flex" }} value={defaultType}>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem
                        value="course_based"
                        id="r1"
                        style={{ cursor: "default" }}
                      />
                      <Label htmlFor="r1">
                        {t("subscriptionPlan.courses")}
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem
                        value="resource_based"
                        id="r2"
                        style={{ cursor: "default" }}
                      />
                      <Label htmlFor="r2">
                        {t("subscriptionPlan.resource")}
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem
                        value="time_based"
                        id="r3"
                        style={{ cursor: "default" }}
                      />
                      <Label htmlFor="r3">{t("subscriptionPlan.time")}</Label>
                    </div>
                  </RadioGroup>
                </div>
              </div>
            </div>

            {radiobtn && (
              <div className="w-full sm:w-1/2">
                <div className="w-full">
                  <Label className="block">
                    {t("subscriptionPlan.courseSelected")}
                  </Label>
                  <div className="w-full course-width mt-2">
                    <Combobox
                      data={courseData}
                      onSelectChange={handleCourseChange}
                      // defaultLabel={courseName !== "" ? courseName : ""}
                    />
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {isLoading ? (
          <Spinner />
        ) : moduleData?.length > 0 && !radiobtn ? (
          <DataTable
            columns={columns_course}
            data={moduleData as CourseDatas[]}
            FilterLabel={t("subscriptionPlan.filterByCourseName")}
            FilterBy={"full_name"}
            isSelectedColumn={"is_part_of_plan"}
            actions={[]}
            onSetCheckedStatus={(index: string, checkedStatus: boolean) => {
              getAddedItem(index as string, checkedStatus as boolean);
            }}
            onSelectedDataChange={() => {}}
            disableExpired={"disableExpired"}
          />
        ) : resourceData?.length > 0 && radiobtn ? (
          <DataTable
            columns={columns_resource}
            data={resourceData as CourseDatas[]}
            FilterLabel={t("subscriptionPlan.filterByResourceName")}
            FilterBy={"full_name"}
            isSelectedColumn={"is_part_of_plan"}
            actions={[]}
            onSetCheckedStatus={(index: string, checkedStatus: boolean) => {
              getAddedItem(index as string, checkedStatus as boolean);
            }}
            onSelectedDataChange={() => {}}
          />
        ) : noModuleResults && !radiobtn ? (
          <div className="mt-20">
            <NoDataFound />
          </div>
        ) : noResults && radiobtn ? (
          <div className="mt-20">
            <NoDataFound />
          </div>
        ) : null}

        {/* {allSelectedRows.length > 0 && radiobtn && (
          <div className="mt-4">
            <h2 className="text-lg font-semibold mb-2">Selected Data</h2>
            <DataTable
              columns={columns_resource}
              data={allSelectedRows as CourseDatas[]}
              FilterLabel={"Filter by Course Name"}
              FilterBy={"course_name"}
              actions={[
                {
                  title: "Delete",
                  icon: Trash2,
                  varient: "icon",
                  handleClick: (val: unknown): void =>
                    handleDelete(val as ResourceForm),
                },
              ]}
            />
          </div>
        )} */}

        {radiobtn === false && (
          <div className="flex flex-wrap justify-end mt-8">
            <div className="mt-6 flex items-center justify-end gap-x-3">
              <Link href={pageUrl.membershipPlanList}>
                <Button
                  type="button"
                  className="bg-[#33363F]"
                  onClick={addSessionCancel}
                >
                  {t("buttons.cancel")}
                </Button>
              </Link>
              {moduleData?.length > 0 && expired === "false" && (
                <Button
                  type="button"
                  className="bg-[#9FC089]"
                  onClick={onSubmitData}
                >
                  {t("buttons.submit")}
                </Button>
              )}
            </div>
          </div>
        )}
        {radiobtn === true && (
          <div className="flex flex-wrap justify-end mt-8">
            <div className="mt-6 flex items-center justify-end gap-x-3">
              <Link href={pageUrl.membershipPlanList}>
                <Button
                  type="button"
                  className="bg-[#33363F]"
                  onClick={addSessionCancel}
                >
                  {t("buttons.cancel")}
                </Button>
              </Link>
              {resourceData?.length > 0 && expired === "false" && (
                <Button
                  type="button"
                  className="bg-[#9FC089]"
                  onClick={onSubmitResource}
                >
                  {t("buttons.submit")}
                </Button>
              )}
            </div>
          </div>
        )}
      </div>
      {emptyChoice && (
        <Modal
          title={""}
          header=""
          openDialog={emptyChoice}
          closeDialog={closeConfirmation}
        >
          <ComfirmSubmit
            onSave={onSubmit}
            onCancel={closeConfirmation}
            isModal={true}
            data={data}
          />
        </Modal>
      )}
    </MainLayout>
  );
}
