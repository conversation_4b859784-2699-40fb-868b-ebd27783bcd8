"use client";
import React, { useState, useEffect } from "react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useForm } from "react-hook-form";
import { Input } from "@/components/ui/input";
import { Editor } from "primereact/editor";
import type {
  ErrorCatch,
  InnerItem,
  LogUserActivityRequest,
  SubscriptionListResults,
  SubscriptionPlans,
  ToastType,
} from "@/types";
import { Button } from "@/components/ui/button";
import { DateTimePicker } from "@/components/ui/date-time-picker/date-time-picker";
import { parseZonedDateTime } from "@internationalized/date";
import type { DateValue, ZonedDateTime } from "@internationalized/date";
import moment from "moment-timezone";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  MembershipPlanEditSchema,
  MembershipPlanSchema,
} from "@/schema/schema";
import Link from "next/link";
import {
  ANUAL_PLAN,
  COURSE_BASED,
  CUSTOM_PLAN,
  DATE_FORMAT,
  DEFAULT_CURRENCY,
  DEFAULT_FOLDER_ID,
  FORMATTED_DATE_FORMAT,
  MONTHLY_PLAN,
  ORG_KEY,
  RESOURCE_BASED,
  TIME_BASED,
  pageUrl,
} from "@/lib/constants";
import useSubscription from "@/hooks/useSubscription";
import { useToast } from "@/components/ui/use-toast";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumb";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";
interface subscriptionDataProps {
  subscriptionsData: SubscriptionListResults;
  onCancel: () => void;
  onSave: () => void;
  subscriptionId: string;
}
export const UpdateMembershipPage: React.FC<subscriptionDataProps> = ({
  subscriptionsData,
  onCancel,
  onSave,
  subscriptionId,
}) => {
  const { t } = useTranslation();
  const { toast } = useToast() as ToastType;
  const { addSubscription, updateSubscription } = useSubscription();
  const subscription_id = subscriptionId;
  const [description, setDescription] = useState("");
  const [frequency, setFrequency] = useState("");
  const [basedOn, setBasedOn] = useState("");
  const title =
    subscription_id === ""
      ? t("subscriptionPlan.addSubscriptionPlan")
      : t("subscriptionPlan.editSubscriptionPlan");
  const [startDateTime, setStartDateTime] = useState<DateValue | undefined>();
  const [defaultDate, setDefaultDate] = useState<ZonedDateTime | DateValue>();
  const [endDateTime, setEndDateTime] = useState<ZonedDateTime | DateValue>();
  const form = useForm<SubscriptionPlans>({
    resolver: zodResolver(
      subscription_id === "" ? MembershipPlanSchema : MembershipPlanEditSchema,
    ),
  });
  const [currency, setCurrency] = useState(DEFAULT_CURRENCY);
  const [planStatus, setPlanStatus] = useState<string>("inactive");
  const [breadcrumbAddItems, setBreadcrumbAddItems] = useState<InnerItem[]>([]);
  const { updateUserActivity } = useLogUserActivity();
  useEffect(() => {
    setBreadcrumbAddItems(getBreadCrumbItems(t, title, { "": "" }));
    form.setValue("currency", DEFAULT_CURRENCY);
    if (subscription_id != "") {
      form.setValue("name", subscriptionsData.name);
      form.setValue("description", subscriptionsData.description);
      form.setValue("subscription_type", subscriptionsData.subscription_type);
      form.setValue(
        "subscription_frequency_type",
        subscriptionsData.subscription_frequency_type,
      );
      setPlanStatus(subscriptionsData.subscription_plan_status);
      setDescription(subscriptionsData.description);
      setBasedOn(subscriptionsData.subscription_type);
      setFrequency(subscriptionsData.subscription_frequency_type);
      form.setValue("price", subscriptionsData.price.toString());
      const currentTimezone = moment.tz.guess();
      const parsedDatetime = moment.tz(
        subscriptionsData.valid_from.split("+")[0],
        currentTimezone,
      );
      const formattedDatetime =
        parsedDatetime.format(DATE_FORMAT) + `[${currentTimezone}]`;
      const dateTime = parseZonedDateTime(formattedDatetime);
      form.setValue("valid_from", dateTime);
      setDefaultDate(dateTime);
      setStartDateTime(dateTime);
      const parsedDatetimeValidTo = moment.tz(
        subscriptionsData.valid_to.split("+")[0],
        currentTimezone,
      );
      const formattedEndDatetime =
        parsedDatetimeValidTo.format(DATE_FORMAT) + `[${currentTimezone}]`;
      const endDateTime = parseZonedDateTime(formattedEndDatetime);
      form.setValue("valid_to", endDateTime);
      setEndDateTime(endDateTime);
    }
  }, [t]);

  async function onSubmit(): Promise<void> {
    const formData = form.getValues();
    const dateStart = formData.valid_from;
    const startDate = new Date(
      dateStart.year,
      dateStart.month - 1,
      dateStart.day,
      dateStart.hour,
      dateStart.minute,
    );
    const dateEnd = formData.valid_to;
    const endDate = new Date(
      dateEnd.year,
      dateEnd.month - 1,
      dateEnd.day,
      dateEnd.hour,
      dateEnd.minute,
    );
    const momentStartDate = moment(startDate);

    const momentEndDate = moment(endDate);
    const duration = momentEndDate.diff(momentStartDate, "months");
    let requiredDuration = 0;
    if (formData.subscription_frequency_type === "monthly") {
      requiredDuration = 1;
    } else if (formData.subscription_frequency_type === "annual") {
      requiredDuration = 12;
    }
    if (
      duration < requiredDuration &&
      formData.subscription_frequency_type !== "custom"
    ) {
      let errorMsg;
      if (formData.subscription_frequency_type === "monthly") {
        errorMsg = t("errorMessages.monthly_date_error");
      } else if (formData.subscription_frequency_type === "annual") {
        errorMsg = t("errorMessages.annual_date_error");
      }

      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.invalid_date_msg"),
        description: errorMsg,
      });
      return;
    }
    const formattedValidFrom = momentStartDate.format(FORMATTED_DATE_FORMAT);
    const formattedValidTo = momentEndDate.format(FORMATTED_DATE_FORMAT);

    const org_id = localStorage.getItem(ORG_KEY) ?? "";
    const subscriptionPlanData = {
      name: formData.name.trimStart(),
      description: formData.description,
      type: formData.subscription_type,
      subscription_frequency: formData.subscription_frequency_type,
      valid_from: formattedValidFrom,
      valid_to: formattedValidTo,
      price: formData.price.toString(),
      status: planStatus,
      currency: DEFAULT_CURRENCY,
    };
    const addParams = {
      org_id: org_id,
      subscription_plan_data: subscriptionPlanData,
    };
    const editParams = {
      org_id: org_id,
      plan_id: subscription_id,
      subscription_plan_data: subscriptionPlanData,
    };
    if (subscription_id != "") {
      const updateSubscriptions = async (): Promise<void> => {
        try {
          const subscriptions = await updateSubscription(editParams);
          if (subscriptions?.status === "success") {
            toast({
              variant: SUCCESS_MESSAGES.toast_variant_default,
              title: t("successMessages.update_subscription_title"),
              description: t("successMessages.update_subscription_msg"),
            });
            onCancel();
            onSave();
            const params = {
              activity_type: "Subscription",
              screen_name: "Subscription ",
              action_details: "Subscription edited ",
              target_id: subscriptions.id as string,
              log_result: "SUCCESS",
            };
            void updateLogUserActivity(params).catch((error) => {
              console.error(error);
            });
          }
        } catch (e) {
          const err = e as ErrorCatch;
          let errMsg = "";
          let errTtile = "";
          if (err.message === ERROR_MESSAGES.subscription_update_date) {
            errMsg = err.message.split("Invalid date range.")[1].trim();
            errTtile = ERROR_MESSAGES.invalid_date_msg;
          } else if (err.message === ERROR_MESSAGES.invalid_validity) {
            errMsg = err.message.split("Invalid validity period:")[1].trim();
            errTtile = ERROR_MESSAGES.invalid_date_msg;
          } else {
            errMsg = err.message;
            errTtile = ERROR_MESSAGES.update_subscription_title;
          }
          toast({
            variant: ERROR_MESSAGES.toast_variant_destructive,
            title: errTtile,
            description: errMsg,
          });
          const params = {
            activity_type: "Subscription",
            screen_name: "Subscription ",
            action_details: "Failed to edit subscription ",
            target_id: subscription_id,
            log_result: "ERROR",
          };
          void updateLogUserActivity(params).catch((error) => {
            console.error(error);
          });
        }
      };
      updateSubscriptions().catch((error) => console.log(error));
    } else {
      const addSubscriptions = async (): Promise<void> => {
        try {
          const subscriptions = await addSubscription(addParams);
          if (subscriptions?.status === "success") {
            toast({
              variant: SUCCESS_MESSAGES.toast_variant_default,
              title: t("successMessages.add_subscription_title"),
              description: t("successMessages.add_subscription_msg"),
            });
            onCancel();
            onSave();
            const params = {
              activity_type: "Subscription",
              screen_name: "Subscription ",
              action_details: "Subscription added ",
              target_id: subscriptions.id ?? "",
              log_result: "SUCCESS",
            };
            void updateLogUserActivity(params).catch((error) => {
              console.error(error);
            });
          }
        } catch (e) {
          const err = e as ErrorCatch;
          let errMsg = "";
          let errTtile = "";
          if (err.message === ERROR_MESSAGES.subscription_add_date) {
            errMsg = err.message.split("Invalid validity date:")[1].trim();
            errTtile = ERROR_MESSAGES.invalid_date_msg;
          } else if (err.message === ERROR_MESSAGES.invalid_valid_from) {
            errMsg = ERROR_MESSAGES.invalid_valid_from;
            errTtile = ERROR_MESSAGES.invalid_date_msg;
          } else {
            errMsg = err.message;
            errTtile = ERROR_MESSAGES.update_subscription_title;
          }

          toast({
            variant: ERROR_MESSAGES.toast_variant_destructive,
            title: errTtile,
            description: errMsg,
          });

          const params = {
            activity_type: "Subscription",
            screen_name: "Subscription ",
            action_details: "Failed To Add Subscription ",
            target_id: DEFAULT_FOLDER_ID,
            log_result: "SUCCESS",
          };
          void updateLogUserActivity(params).catch((error) => {
            console.error(error);
          });
        }
      };
      addSubscriptions().catch((error) => console.log(error));
    }
  }
  const setRichTextValue = (val: string): void => {
    form.setValue("description", val);
  };

  const onCancelUpdate = (): void => {
    onCancel();
  };
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    const value = e.target.value;
    const sanitizedValue = value
      .trimStart()
      .replace(/^\p{P}+/u, "")
      .replace(/[^\p{L}\p{M}\p{N}\s-]/gu, "")
      .replace(/\s{2,}/g, " ")
      .replace(/^[^\p{L}\p{M}\p{N}]|[^\p{L}\p{M}\p{N}\s-]/gu, "")
      .replace(/\s+$/, (match) =>
        match.length > 1 ? match.slice(0, -1) : match,
      );
    form.setValue("name", sanitizedValue);
  };
  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };
  return (
    <div className="w-full">
      <NextBreadcrumb
        items={breadcrumbAddItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      <>
        <h1 className="text-2xl font-semibold tracking-tight">{title}</h1>
      </>
      <div className="border rounded-md p-4 mt-4 bg-[#fff]">
        <Form {...form}>
          <form onSubmit={(event) => void form.handleSubmit(onSubmit)(event)}>
            <div>
              <FormField
                name="name"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel autoCorrect="off">
                      {t("subscriptionPlan.membershipName")}{" "}
                      <span className="text-red-700">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        autoComplete="off"
                        type="text"
                        {...field}
                        maxLength={30}
                        onChange={handleInputChange}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="mt-4">
              <FormField
                name="description"
                control={form.control}
                render={() => (
                  <FormItem>
                    <FormLabel>
                      {t("subscriptionPlan.description")}{" "}
                      <span className="text-red-700">*</span>
                    </FormLabel>
                    <FormControl>
                      <Editor
                        value={description}
                        onTextChange={(event) => {
                          const Value = event.textValue;
                          const sanitizedValue = Value.replace(/^\s+/, "");
                          setRichTextValue(sanitizedValue as string);
                        }}
                        style={{ height: "320px" }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4">
              <div className="row-span-2">
                <FormField
                  name="subscription_type"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t("subscriptionPlan.basedOn")}{" "}
                        <span className="text-red-700">*</span>
                      </FormLabel>
                      <FormControl>
                        <Select
                          name="subscription_type"
                          onValueChange={(value) => {
                            setBasedOn(value); // Update the frequency state
                            field.onChange(value); // Update the form control
                          }}
                          value={basedOn}
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Select Based On" />
                          </SelectTrigger>
                          <SelectContent>
                            <>
                              <SelectItem value="course_based">
                                {COURSE_BASED}
                              </SelectItem>
                              <SelectItem value="resource_based">
                                {RESOURCE_BASED}
                              </SelectItem>
                              <SelectItem value="time_based">
                                {TIME_BASED}
                              </SelectItem>
                            </>
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="row-span-2">
                <FormField
                  name="subscription_frequency_type"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t("subscriptionPlan.paymentFrequency")}
                        <span className="text-red-700">*</span>
                      </FormLabel>
                      <Select
                        name="subscription_frequency_type"
                        onValueChange={(value) => {
                          setFrequency(value);
                          field.onChange(value);
                        }}
                        value={frequency}
                      >
                        <FormControl>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Select" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <>
                            <SelectItem value="annual">{ANUAL_PLAN}</SelectItem>
                            <SelectItem value="custom">
                              {CUSTOM_PLAN}
                            </SelectItem>
                            <SelectItem value="monthly">
                              {MONTHLY_PLAN}
                            </SelectItem>
                          </>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="row-span-2 mt-4">
                <FormField
                  name="valid_from"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t("subscriptionPlan.validFrom")}{" "}
                        <span className="text-red-700">*</span>
                      </FormLabel>
                      <FormControl>
                        <DateTimePicker
                          granularity={"minute"}
                          minValue={defaultDate}
                          value={field.value as DateValue}
                          hideTimeZone={true}
                          defaultValue={startDateTime}
                          onChange={(newDate) => {
                            // handleValidFromChange(newDate);
                            field.onChange(newDate);
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="row-span-2 mt-4">
                <FormField
                  name="valid_to"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t("subscriptionPlan.validTo")}{" "}
                        <span className="text-red-700">*</span>
                      </FormLabel>
                      <FormControl>
                        <DateTimePicker
                          granularity="minute"
                          // minValue={endDateTime}
                          value={field.value as DateValue}
                          defaultValue={endDateTime}
                          hideTimeZone={true}
                          onChange={(newDate) => {
                            // handleValidToChange(newDate);
                            field.onChange(newDate);
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="row-span-2">
                <FormField
                  name="currency"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t("subscriptionPlan.currency")}
                        <span className="text-red-700">*</span>
                      </FormLabel>
                      <Select
                        name="currency"
                        onValueChange={(value) => {
                          setCurrency(value); // Update the currency state
                          field.onChange(value); // Update the form control
                        }}
                        value={currency} // Set the value from the state
                      >
                        <FormControl>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Select" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <>
                            <SelectItem value={DEFAULT_CURRENCY}>
                              {DEFAULT_CURRENCY}
                            </SelectItem>
                            {/* Add more currency options if needed */}
                          </>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="row-span-2">
                <FormField
                  name="price"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel autoCorrect="off">
                        {t("subscriptionPlan.amount")}{" "}
                        <span className="text-red-700">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          autoComplete="off"
                          type="number"
                          {...field}
                          onKeyDown={(e) => {
                            if (e.key === " ") {
                              e.preventDefault();
                              return;
                            }
                            if (
                              !/^[0-9.]$/.test(e.key) &&
                              e.key !== "Backspace"
                            ) {
                              e.preventDefault();
                            }
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <div className="w-full flex justify-end mt-4 gap-3">
              <div className="">
                <Link href={pageUrl.membershipPlanList}>
                  <Button
                    className="w-full sm:w-auto bg-[#33363F]"
                    onClick={onCancelUpdate}
                  >
                    {t("buttons.cancel")}
                  </Button>
                </Link>
              </div>
              <div>
                <Button type="submit" className="bg-[#9FC089]">
                  {t("buttons.submit")}
                </Button>
              </div>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
};
