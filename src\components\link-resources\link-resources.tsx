"use client";
import React, { useEffect, useState } from "react";
import { Spinner } from "@/components/ui/progressiveLoader";
import useResourceLibrary from "@/hooks/useResourceLibrary";
import { useToast } from "@/components/ui/use-toast";
import { Button } from "../ui/button";
import type {
  CourseMpaData,
  ErrorCatch,
  LogUserActivityRequest,
  ToastType,
} from "@/types";
import { ArrowUpDown, ChevronLeft, ChevronRight } from "lucide-react";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";

export default function LinkResources({
  onCancel,
  onSave,
  resource,
  moduleType,
}: {
  onCancel: () => void;
  onSave: () => void;
  resource: string;
  moduleType: string;
}): React.JSX.Element {
  const { t } = useTranslation();
  const { getCourseList, mapResourcesToCourse } = useResourceLibrary();
  const { toast } = useToast() as ToastType;

  const [isLoading, setIsLoading] = useState(true);
  const [courseResourceData, setCourseResourceData] = useState<CourseMpaData[]>(
    [],
  );
  const [selected, setSelected] = useState<CourseMpaData[]>([]);
  const [sortKey, setSortKey] = useState<keyof CourseMpaData | null>(null);
  const [sortAsc, setSortAsc] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const itemsPerPage = 10;
  const { updateUserActivity } = useLogUserActivity();
  useEffect(() => {
    const fetchCourseResources = async (): Promise<void> => {
      setIsLoading(true);
      const orgId = localStorage.getItem("orgId");
      const params = {
        org_id: orgId ?? "",
        resource_id: resource,
      };
      try {
        const result = await getCourseList(params);
        setIsLoading(false);
        if (result.status === "success") {
          setCourseResourceData(result.courses_data);
        }
      } catch (error) {
        setIsLoading(false);
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
      }
    };
    void fetchCourseResources();
  }, []);

  const toggleSort = (key: keyof CourseMpaData): void => {
    if (sortKey === key) {
      setSortAsc(!sortAsc);
    } else {
      setSortKey(key);
      setSortAsc(true);
    }
  };

  const filteredData = courseResourceData.filter((item) =>
    item.short_name?.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  const sortedData = [...filteredData].sort((a, b) => {
    if (sortKey === null) return 0;
    const valA = a[sortKey];
    const valB = b[sortKey];
    if ((valA ?? "") < (valB ?? "")) return sortAsc ? -1 : 1;
    if ((valA ?? "") > (valB ?? "")) return sortAsc ? 1 : -1;
    return 0;
  });

  const paginatedData = sortedData.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage,
  );

  const handleSelect = (item: CourseMpaData): void => {
    const isSelected = selected.some(
      (selectedItem) => selectedItem.id === item.id,
    );
    if (isSelected) {
      setSelected(
        selected.filter((selectedItem) => selectedItem.id !== item.id),
      );
    } else {
      setSelected([...selected, item]);
    }
  };

  const handleSelectAll = (checked: boolean): void => {
    if (checked) {
      const allSelected = courseResourceData.map((item) => ({
        ...item,
      }));
      setSelected(allSelected);
    } else {
      setSelected([]);
    }
  };

  const totalPages = Math.ceil(sortedData.length / itemsPerPage);

  const onSubmit = async (): Promise<void> => {
    if (selected.length === 0) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.noCourseSelected"),
      });
      return;
    }

    const org_id = localStorage.getItem("orgId") as string;
    const selectedData = selected.map((item) => ({
      course_id: item.id,
      section_id:
        item.section_id != null && item.section_id.trim() !== ""
          ? item.section_id
          : item.sections[0]?.id,
    }));
    const requestBody = {
      module_type:
        moduleType?.toLowerCase() === "video"
          ? "url"
          : moduleType?.toLowerCase(),
      org_id: org_id.toLowerCase(),
      resource_id: resource.toLowerCase(),
      course_and_section: selectedData,
    };
    try {
      const response = await mapResourcesToCourse(requestBody);
      if (response.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.toast_success_title"),
          description: t("successMessages.link_resource_to_course"),
        });
        onSave();
        const params = {
          activity_type: "Resource_Library",
          screen_name: "Link Resource to Course",
          action_details: "Resource linked to course ",
          target_id: resource as string,
          log_result: "SUCCESS",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      }
    } catch (error) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.link_resource_to_course"),
      });
      console.error("Error fetching data:", error);
      const params = {
        activity_type: "Resource_Library",
        screen_name: "Link Resource to Course",
        action_details: "Failed to link resource to course ",
        target_id: resource as string,
        log_result: "ERROR",
      };
      void updateLogUserActivity(params).catch((error) => {
        console.error(error);
      });
    }
  };

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  return (
    <div className="rounded-lg ">
      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <Spinner />
        </div>
      ) : (
        <div className="mt-4">
          <div className="flex mb-3">
            <input
              type="text"
              placeholder={t("resourceLibrary.searchCourse")}
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                setCurrentPage(1); // Reset to page 1 when searching
              }}
              className="p-2 border border-gray-300 rounded-md w-64"
            />
          </div>
          <div className="overflow-x-auto rounded-lg border border-gray-200">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-[#5BCED6] text-white">
                <tr>
                  <th className="px-4 py-3 text-left" style={{ width: "50px" }}>
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        onChange={(e) => handleSelectAll(e.target.checked)}
                        checked={
                          selected.length === courseResourceData.length &&
                          courseResourceData.length > 0
                        }
                        className="h-4 w-4"
                      />
                    </div>
                  </th>
                  <th
                    className="px-4 py-3 text-left text-xs font-medium text-white tracking-wider cursor-pointer"
                    style={{ width: "200px" }}
                    onClick={() => toggleSort("short_name")}
                  >
                    <div className="flex items-center text-base">
                      {t("resourceLibrary.courseName")}
                      <ArrowUpDown className="ml-2 h-4 w-4" />
                    </div>
                  </th>
                  <th
                    className="px-4 py-3 text-left text-xs font-medium text-white tracking-wider"
                    style={{ width: "150px" }}
                  >
                    <div className="flex items-center text-base">
                      {t("resourceLibrary.selectSection")}
                    </div>
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {paginatedData.length > 0 ? (
                  paginatedData.map((item) => (
                    <tr key={item.id} className="">
                      <td
                        className="px-4 py-3 whitespace-nowrap"
                        style={{ width: "50px" }}
                      >
                        <input
                          type="checkbox"
                          checked={selected.some((s) => s.id === item.id)}
                          onChange={() => handleSelect(item)}
                          className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                      </td>
                      <td
                        className="px-4 py-3 whitespace-nowrap text-sm font-normal text-gray-900"
                        style={{ width: "100px" }}
                      >
                        {item.short_name}
                      </td>
                      <td
                        className="px-4 py-3 whitespace-nowrap text-sm"
                        style={{ width: "150px" }}
                      >
                        <select
                          onChange={(e) => {
                            item.section_id = e.target.value;
                          }}
                          className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm p-2 bg-white border"
                          defaultValue={item.sections[0]?.id}
                        >
                          {item.sections.map((sec) => (
                            <option key={sec.id} value={sec.id}>
                              {sec.name}
                            </option>
                          ))}
                        </select>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td
                      className="px-4 py-4 whitespace-nowrap text-sm text-gray-500 text-center"
                      colSpan={4}
                    >
                      {t("resourceLibrary.noCoursesAvailable")}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>

          <div className="flex items-center justify-center mt-6">
            <div className="flex items-center gap-2">
              <Button
                size="sm"
                variant="outline"
                disabled={currentPage === 1}
                onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                className="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                <ChevronLeft className="h-4 w-4 mr-1" />
              </Button>

              <span className="px-4 py-1 text-sm text-gray-700 bg-gray-100 rounded-md">
                {currentPage} of {totalPages}
              </span>

              <Button
                size="sm"
                variant="outline"
                disabled={currentPage === totalPages || totalPages === 0}
                onClick={() =>
                  setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                }
                className="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                <ChevronRight className="h-4 w-4 ml-1" />
              </Button>
            </div>
          </div>
        </div>
      )}
      <div className="flex justify-end gap-4 mt-8 pt-6">
        <Button className="bg-[#33363F]" onClick={onCancel}>
          {t("buttons.cancel")}
        </Button>
        <Button
          onClick={() => {
            void onSubmit();
          }}
          className="bg-[#9FC089]"
        >
          {t("resourceLibrary.save")}
        </Button>
      </div>
    </div>
  );
}
