"use client";

import type { Column, ColumnDef } from "@tanstack/react-table";
import { ArrowUpDown } from "lucide-react";
import { Button } from "@/components/ui/button";

import type {
  // EnrolledCourseColumnDefinition,
  EnrolledCourseResponse,
  EnrolledCourseRowDefinition,
} from "@/types";
import moment from "moment";
import { DATE_FORMAT_DMY_HM_AM_PM } from "@/lib/constants";
import { useTranslation } from "react-i18next";
// import { Column } from "primereact/column";

interface ColumnDefinition {
  column: Column<EnrolledCourseResponse>;
}

export const useColumns = (): ColumnDef<EnrolledCourseResponse>[] => {
  const { t } = useTranslation();

  return [
    {
      accessorKey: "course_name",
      header: ({ column }: ColumnDefinition): React.JSX.Element => {
        return (
          <Button
            className="px-0"
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            {t("usersList.enrolledCoursesModal.courseName")}
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
    },
    {
      accessorKey: "course_type",
      header: "Course Type",
    },
    {
      accessorKey: "valid_to",
      header: t("usersList.enrolledCoursesModal.validUpTo"),
      cell: ({ row }: EnrolledCourseRowDefinition): React.JSX.Element => {
        const formattedDate = moment
          .utc(row.original.valid_to)
          .format(DATE_FORMAT_DMY_HM_AM_PM);
        return <div>{formattedDate}</div>;
      },
    },
    {
      accessorKey: "status",
      header: t("usersList.enrolledCoursesModal.status"),
    },
  ];
};
