"use client";
import React, { useState, useEffect } from "react";
import { getColumns } from "./columns";
import { DataTable } from "../../components/ui/data-table/data-table";
import MainLayout from "../layout/mainlayout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import AddGroups from "./addGroups";
import {
  Archive,
  Edit,
  PlusIcon,
  ShieldCheck,
  UsersIcon,
  FileText,
} from "lucide-react";
import { Modal } from "@/components/ui/modal";
import DeleteGroups from "./deleteGroups";
import type { GroupForm, InnerItem } from "@/types";
import "../../styles/main.css";
import { pageUrl, privilegeData } from "@/lib/constants";
import { useRouter } from "next/navigation";
import useGroups from "@/hooks/useGroups";
import getPrivilegeList from "@/hooks/useCheckPrivilege";
import { Spinner } from "@/components/ui/progressiveLoader";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumb";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useTranslation } from "react-i18next";

export default function GroupsAddPage(): React.JSX.Element {
  const { t } = useTranslation();
  const columns = getColumns(t);
  const [groupData, setGroupData] = useState<GroupForm[]>([]);
  const [passData, setpassData] = useState<GroupForm>();
  const [title, setTitle] = useState("");
  const [addGroup, setGroup] = useState<boolean>(false);
  const [deleteGroup, setDelete] = useState<boolean>(false);
  const { getGroups } = useGroups();
  const [disableBtn, setDisableBtn] = useState<boolean>(false);
  const [isLoading, setIsLoading] = React.useState<boolean>(true);
  //const [deleteGroupPrivilege, setDeletePrivilege] = useState<boolean>(false);
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);

  useEffect(() => {
    setBreadcrumbItems(getBreadCrumbItems(t, t("breadcrumb.groups"), { "": "" }));
    fetchGroupData().catch((error) => console.log(error));
    const canPerformAction = getPrivilegeList(
      "Group",
      privilegeData.Group.createGroup,
    );
    setDisableBtn(canPerformAction);
    /* const delGroupPrivilege = getPrivilegeList(
      "Group",
      privilegeData.Group.deleteGroup,
    ); */
    //setDisableBtn(canPerformAction);
  }, [t]);

  const fetchGroupData = async (): Promise<void> => {
    setIsLoading(true);
    try {
      const groups = await getGroups();
      if (groups !== null && groups !== undefined) {
        setGroupData(groups);
        setIsLoading(false);
      }
    } catch (error) {
      console.log("Error fetching groups");
      setIsLoading(false);
    }
  };

  const addGroupModal = (): void => {
    setGroup(!addGroup);
    setpassData({ id: "", name: "", description: "" });
    setTitle(t("groups.teams.addGroupModal.title"));
  };
  const closeDelete = (): void => {
    setDelete(false);
  };
  const openEditDialog = (val: GroupForm): void => {
    setTitle(t("groups.teams.addGroupModal.editTitle"));
    setpassData(val);
    setGroup(true);
  };
  const openDeleteDialog = (val: GroupForm): void => {
    setTitle("");
    setpassData(val);
    setDelete(true);
  };
  function onSubmit(): void {
    fetchGroupData().catch((error) => console.log(error));
  }
  const router = useRouter();

  // open Add User to Group Page
  const openAddUserToTeamsPage = (val: GroupForm): void => {
    console.log(val);

    router.push(pageUrl.addUserToTeams);
    router.push(`${pageUrl.addUserToTeams}?group=${val.id}&name=${val.name}`);
  };

  // open Add Course to Group page
  const openAddCourseToTeamsPage = (val: GroupForm): void => {
    console.log(val);

    router.push(`${pageUrl.addCourseToTeams}?group=${val.id}&name=${val.name}`);
  };
  const openAddPrivilegesPage = (val: GroupForm): void => {
    console.log(val);

    router.push(
      `${pageUrl.addGroupPrivileges}?group=${val.id}&name=${val.name}`,
    );
  };

  return (
    <MainLayout>
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      <div>
        <span>
          <h1 className="text-2xl font-semibold">
            {t("groups.teams.title")}
            {disableBtn && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      onClick={addGroupModal}
                      className="bg-ring bg-[#fb8500] hover:bg-[#fb5c00] py-2 md:py-3 px-4 md:px-6 whitespace-nowrap ml-2"
                    >
                      <PlusIcon className="h-4 w-4 md:h-5 md:w-5" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{t("groups.teams.addGroup")}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </h1>
        </span>

        <div className="border rounded-md p-4 mt-4 bg-[#fff]">
          {/* {disableBtn && (
            <Button
              onClick={addGroupModal}
              className="float-right bg-[#fb8500] hover:bg-[#fb5c00] "
              // disabled={!disableBtn}
            >
              <PlusIcon className="h-5 w-5" />
              Add Group
            </Button>
          )} */}
          <div className="w-full flex flex-wrap justify-between space-x-4 ">
            <div className="w-full sm:w-1/2 md:w-1/3 lg:w-1/4 xl:w-1/5 2xl:w-1/6 flex">
              <div className="w-full"></div>
            </div>
          </div>

          {addGroup && (
            <Modal
              title={title}
              header=""
              openDialog={addGroup}
              closeDialog={addGroupModal}
            >
              <AddGroups
                onSave={onSubmit}
                onCancel={addGroupModal}
                isModal={true}
                data={passData as GroupForm}
              />
            </Modal>
          )}
          {deleteGroup && (
            <Modal
              title={t("groups.teams.deleteGroupModal.title")}
              header=""
              openDialog={deleteGroup}
              closeDialog={closeDelete}
            >
              <DeleteGroups
                onSave={onSubmit}
                onCancel={closeDelete}
                isModal={true}
                data={passData as GroupForm}
              />
            </Modal>
          )}

          {isLoading ? (
            <Spinner />
          ) : (
            <div>
              <DataTable
                columns={columns}
                data={groupData as GroupForm[]}
                FilterLabel={t("groups.teams.filterByGroup")}
                FilterBy={"name"}
                actions={[
                  {
                    title: t("groups.teams.users"),
                    icon: UsersIcon,
                    varient: "icon",
                    isEnable: getPrivilegeList(
                      "Group",
                      privilegeData.Group.listGroupUser,
                    ),
                    handleClick: (val: unknown) =>
                      openAddUserToTeamsPage(val as GroupForm),
                  },
                  {
                    title: t("groups.teams.courses"),
                    icon: FileText,
                    varient: "icon",
                    isEnable: getPrivilegeList(
                      "Group",
                      privilegeData.Group?.listGroupCourse,
                    ),
                    handleClick: (val: unknown) =>
                      openAddCourseToTeamsPage(val as GroupForm),
                  },
                  {
                    title: t("groups.teams.privileges"),
                    icon: ShieldCheck,
                    varient: "icon",
                    isEnable: getPrivilegeList(
                      "Group",
                      privilegeData.Group.listGroupPrivilege,
                    ),
                    handleClick: (val: unknown) =>
                      openAddPrivilegesPage(val as GroupForm),
                  },
                  {
                    title: t("groups.teams.editGroup"),
                    icon: Edit,
                    varient: "icon",
                    color: "#fb8500",
                    isEnable: getPrivilegeList(
                      "Group",
                      privilegeData.Group.updateGroup,
                    ),
                    handleClick: (val: unknown) =>
                      openEditDialog(val as GroupForm),
                  },

                  {
                    title: t("groups.teams.deleteGroup"),
                    icon: Archive,
                    varient: "icon",
                    color: "#ff0000",
                    isEnable: getPrivilegeList(
                      "Group",
                      privilegeData.Group.deleteGroup,
                    ),
                    handleClick: (val: unknown) =>
                      openDeleteDialog(val as GroupForm),
                  },
                ]}
              />
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
}
