import React, { useState, useEffect } from "react";
import { getColumns } from "./columns";
import { DataTable } from "../ui/data-table/data-table";
import useUsers from "@/hooks/useUsers";
import type { ToastType, UsersDataType } from "@/types";
import { useToast } from "../ui/use-toast";
import { Spinner } from "@/components/ui/progressiveLoader";
import { useTranslation } from "react-i18next";
import { ERROR_MESSAGES } from "@/lib/messages";

export default function UsersList(): React.JSX.Element {
  const { t } = useTranslation();
  const columns = getColumns(t);
  const [isLoading, setIsLoading] = React.useState<boolean>(true);
  //   const [users, setUsers] = useState<UsersDataType[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<UsersDataType[]>([]);
  const { getUsers } = useUsers();
  const { toast } = useToast() as ToastType;

  useEffect(() => {
    getUsersList().catch((error) => console.log(error));
  }, []);

  const getUsersList = async (): Promise<void> => {
    setIsLoading(true);
    try {
      const org_id = localStorage.getItem("orgId") as string;
      const fetchedUsers = await getUsers(org_id);
      setIsLoading(false);
      if (fetchedUsers.length > 0) {
        //   setUsers(fetchedUsers);
        setFilteredUsers(fetchedUsers);
      } else {
        //   setUsers([]);
        setFilteredUsers([]);
      }
    } catch (error) {
      setIsLoading(false);
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.error_fetching_data"),
      });
    }
  };

  return (
    <div>
      <div
        className="rounded-lg shadow-lg bg-white relative overflow-hidden"
        style={{ height: "450px", overflow: "auto" }}
      >
        <div className="p-2 border-b flex justify-between items-center dashboard-session text-black rounded-t-lg">
          <h3 className="text-md font-semibold">
            {" "}
            {String(t("dashboard.users.title"))}
          </h3>
        </div>
        <div>
          {isLoading ? (
            <Spinner />
          ) : (
            <div className="p-2">
              <div className="overflow-x-auto border rounded-md p-2 ">
                <DataTable
                  columns={columns}
                  data={filteredUsers} // Display the filtered users
                  FilterLabel={String(t("dashboard.users.filterByName"))}
                  FilterBy={"first_name"}
                  actions={[]}
                />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
