import React, { useState, useEffect } from "react";
import type { ErrorCatch, LogUserActivityRequest, ToastType } from "@/types";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { ORG_KEY } from "@/lib/constants";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import useCurrentAffairs from "@/hooks/useCurrentAffairs";
import { DateTimePicker } from "@/components/ui/date-time-picker/date-time-picker";
import { parseZonedDateTime } from "@internationalized/date";
import type { DateValue, ZonedDateTime } from "@internationalized/date";
import moment from "moment-timezone";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";

export default function PublishAffairs({
  onSave,
  onCancel,
  affairId,
}: {
  onSave: (value: boolean) => void;
  onCancel: () => void;
  affairId: string | undefined;
}): React.JSX.Element {
  const { t } = useTranslation();
  const { toast } = useToast() as ToastType;
  const { publishCurrentAffairs } = useCurrentAffairs();
  const [defaultDate, setDefaultDate] = useState<ZonedDateTime | DateValue>();
  const [startDateTime, setStartDateTime] = useState<
    ZonedDateTime | DateValue
  >();
  const { updateUserActivity } = useLogUserActivity();
  useEffect(() => {
    const currentTimezone = moment.tz.guess();
    const originalDatetime = moment
      .tz(currentTimezone)
      .format("YYYY-MM-DDTHH:mm");
    const parsedDatetime = moment.tz(originalDatetime, currentTimezone);

    // Add 1 minute
    const newDatetime = parsedDatetime.add(1, "minute");

    // Format the new datetime as desired
    const formattedDatetime =
      newDatetime.format("YYYY-MM-DDTHH:mm") + `[${currentTimezone}]`;

    const dateTime = parseZonedDateTime(formattedDatetime);
    setDefaultDate(dateTime);
    setStartDateTime(dateTime);
  }, []);

  const handlePublishClick = (): void => {
    void handleToastSave();
    onCancel();
  };

  const handleToastSave = async (): Promise<void> => {
    const dateStart = startDateTime as ZonedDateTime;
    const startDate = new Date(
      dateStart.year,
      dateStart.month - 1,
      dateStart.day,
      dateStart.hour,
      dateStart.minute,
    );
    const momentStartDate = moment(startDate);
    const formattedStartDate = momentStartDate.format("YYYY-MM-DD HH:mm");

    const params = {
      bulletin_id: affairId,
      org_id: localStorage.getItem(ORG_KEY) ?? "",
      status: "Published",
      publish_bulletin_date: formattedStartDate,
    };
    try {
      const result = await publishCurrentAffairs(params);
      if (result.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.eventPublishTitle"),
          description: t("successMessages.eventPublishDescription"),
        });
        onSave(true);
        const params = {
          activity_type: "Current_Affairs",
          screen_name: "Current Affairs",
          action_details: "Current affairs published ",
          target_id: affairId as string,
          log_result: "SUCCESS",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      } else if (result.status === "error") {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: result?.status,
        });
        const params = {
          activity_type: "Current_Affairs",
          screen_name: "Current Affairs",
          action_details: "Failed to publish current affairs ",
          target_id: affairId as string,
          log_result: "ERRROR",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
      console.error("An unexpected error occurred:", error);
      const params = {
        activity_type: "Current_Affairs",
        screen_name: "Current Affairs",
        action_details: "Failed to publish current affairs ",
        target_id: affairId as string,
        log_result: "ERRROR",
      };
      void updateLogUserActivity(params).catch((error) => {
        console.error(error);
      });
    }
  };

  const handleChange = (dateObject: DateValue): void => {
    setStartDateTime(dateObject);
  };

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };
  return (
    <>
      <div className="mb-2 mr-4">
        <p className="ml-0  text-lg">{t("currentAffairs.publishPrompt")}</p>
        <div className="mt-2">
          <label>{t("currentAffairs.selectCourse")}</label>
          <div className="mt-2">
            <DateTimePicker
              granularity="minute"
              minValue={defaultDate}
              hideTimeZone={true}
              value={startDateTime}
              defaultValue={startDateTime}
              onChange={(newDate) => {
                handleChange(newDate as DateValue);
              }}
            />
          </div>
        </div>
      </div>
      <div className="flex topic-icons pr-4">
        <div className="flex items-center justify-center float-right gap-3">
          <Button type="button" onClick={onCancel} className="bg-[#33363F]">
            {t("buttons.cancel")}
          </Button>

          <Button
            type="submit"
            onClick={handlePublishClick}
            className="bg-[#9FC089]"
          >
            {t("buttons.publish")}
          </Button>
        </div>
      </div>
    </>
  );
}
