"use client";
import React, { useState } from "react";
import MainLayout from "../layout/mainlayout";
import { Input } from "@/components/ui/input";
import { Label } from "@radix-ui/react-label";
import * as XLSX from "xlsx";
import { useTranslation } from "react-i18next";

interface Record {
  // Define named properties instead of an index signature
  columnName: string;
  columnValue: string | number;
}

export default function ExamListPage(): React.JSX.Element {
  const { t } = useTranslation();
  const [columns, setColumns] = useState<string[]>([]);
  const [records, setRecords] = useState<Record[]>([]);

  const readExcel = (file: File): void => {
    const fileReader = new FileReader();
    fileReader.readAsArrayBuffer(file);
    fileReader.onload = (e) => {
      const bufferArray = e.target?.result;
      const wb = XLSX.read(bufferArray, {
        type: "buffer",
      });
      const wsname = wb.SheetNames[0];
      const ws = wb.Sheets[wsname];
      const data: Record[] = XLSX.utils.sheet_to_json<Record>(ws);
      console.log(data);
      setColumns(Object.keys(data[0]));
      setRecords(data);
    };
  };

  return (
    <MainLayout>
      <>
        <h1 className="text-2xl font-semibold tracking-tight">Excel Export</h1>

        <div className="mt-8">
          <Label>{t("questionBank.selectFile")}</Label>
          <Input
            type="file"
            accept=".xls,.xlsx"
            onChange={(e) => {
              const file = e.target.files?.[0];
              readExcel(file as File);
            }}
          />
        </div>
        <div className="mt-8 rounded-lg overflow-hidden border border-gray-400">
          <table className="table w-full">
            <thead>
              <tr>
                {columns.map((c: string, i: number) => (
                  <th key={i} className="border border-gray-400 p-2">
                    {c}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {records.map((record: Record, i: number) => (
                <tr key={i} className="border border-gray-400">
                  {columns.map((column: string, j: number) => (
                    <td key={j} className="border border-gray-400 p-2">
                      {record[column as keyof Record]}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </>
    </MainLayout>
  );
}
