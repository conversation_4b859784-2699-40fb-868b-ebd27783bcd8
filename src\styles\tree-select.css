.treeSelect{
    height: 37px;
    border-color: none;
    
}

.p-treeselect .p-treeselect-label {
    padding: 0.50rem 0.50rem;
}

.p-treeselect-trigger-icon{
    width: 8px;
    height: 8px;
}

.p-treeselect:not(.p-disabled).p-focus{
    outline: none;
    outline-offset: none;
    box-shadow: none;
    border-color: none;
}

.p-inputtext{
    outline: none;
    outline-offset: none;
    box-shadow: none;
    border: 1px solid #ddd !important;
    padding: 2px 10px !important;
}

.p-treenode-children{
    margin-left: 12px;
}

.p-treenode-content{
    height: 40px;
}

.p-treenode-label{
    font-size: 0.875rem;
    line-height: 1.25rem;
}

.p-treenode-selectable{
    color: #000;
}

.p-placeholder{
    color: #000;
    font-weight: 400;
}
.p-treeselect-panel{
    pointer-events: all;
    width: 100px !important;
}
.p-tree .p-tree-container .p-treenode {
    word-break: break-all;
    height: auto;
}