"use client";
import React, { type BaseSyntheticEvent } from "react";
import { But<PERSON> } from "@/components/ui/button";
import useResourceLibrary from "@/hooks/useResourceLibrary";
import { useToast } from "@/components/ui/use-toast";
import type { ErrorCatch, LogUserActivityRequest, ToastType } from "@/types";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";

export default function ApproveResource({
  onCancel,
  onSave,
  resourceId,
  resourceType,
  publishedStatus,
}: {
  onCancel: () => void;
  onSave: () => void;
  resourceId: string;
  resourceType: string;
  publishedStatus: string;
}): React.JSX.Element {
  const { t } = useTranslation();
  const { approveResources } = useResourceLibrary();
  const { toast } = useToast() as ToastType;
  const { updateUserActivity } = useLogUserActivity();
  const handleDeleteClick = async (e: BaseSyntheticEvent): Promise<void> => {
    console.log(e);
    const orgId = localStorage.getItem("orgId");
    const params = {
      org_id: orgId ?? "",
      module_id: resourceType,
      instance_id: resourceId,
      status: publishedStatus === "Published" ? "Draft" : "Published",
    };

    try {
      const result = await approveResources(params);
      if (result.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_success_title,
          title: t("successMessages.approvResourceTitle"),
          description: t("successMessages.approvResourceMsg"),
        });
        onCancel();
        onSave();
        const params = {
          activity_type: "Resource_Library",
          screen_name: "Update Resource Status",
          action_details: "Resource status updated ",
          target_id: resourceId as string,
          log_result: "SUCCESS",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
      const params = {
        activity_type: "Resource_Library",
        screen_name: "Update Resource Status",
        action_details: "Failed to update resource status ",
        target_id: resourceId as string,
        log_result: "ERROR",
      };
      void updateLogUserActivity(params).catch((error) => {
        console.error(error);
      });
    }
  };
  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };
  return (
    <>
      <div className="mb-2 mr-4">
        {publishedStatus === "Published" ? (
          <p className="ml-0 ">{t("resourceLibrary.draftPrompt")}</p>
        ) : (
          <p className="ml-0 ">{t("resourceLibrary.publishPrompt")}</p>
        )}
      </div>
      <div className="flex topic-icons pr-4">
        <div className="flex items-center justify-center float-right  gap-3">
          <Button type="button" className="bg-[#33363F]" onClick={onCancel}>
            {t("buttons.cancel")}
          </Button>

          <Button
            type="submit"
            className="bg-[#9FC089]"
            onClick={(e: BaseSyntheticEvent) => {
              handleDeleteClick(e).catch((error) => console.log(error));
            }}
          >
            {t("buttons.submit")}
          </Button>
        </div>
      </div>
    </>
  );
}
