import React, { useEffect, useState } from "react";
import type {
  EnrolledCourseResponse,
  ErrorCatch,
  ToastType,
  UsersDataType,
} from "@/types";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { ERROR_MESSAGES } from "@/lib/messages";
import useUsers from "@/hooks/useUsers";
import { useColumns } from "./courseColumn";
import { DataTable } from "@/components/ui/data-table/data-table";
import { useTranslation } from "react-i18next";

export default function EnrolledCourses({
  userData,
  onCancel,
}: {
  onCancel: () => void;
  userData: UsersDataType;
  isModal?: boolean;
}): React.JSX.Element {
  const { t } = useTranslation();
  const columns = useColumns();
  const { toast } = useToast() as ToastType;
  const { getEnrolledCourses } = useUsers();
  const [courseList, setCourseList] = useState<EnrolledCourseResponse[]>([]);
  useEffect(() => {
    void getCoursesList();
  }, []);

  const getCoursesList = async (): Promise<void> => {
    const params = {
      user_id: userData.id,
      org_id: userData.org_id,
    };
    try {
      const result = await getEnrolledCourses(params);
      setCourseList(result);
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
      console.error("An unexpected error occurred:", error);
    }
  };

  return (
    <>
      <DataTable
        columns={columns}
        data={courseList as EnrolledCourseResponse[]}
        FilterLabel={t("usersList.enrolledCoursesModal.courseName")}
        FilterBy={"course_name"}
        actions={[]}
      />
      <div className="flex topic-icons pr-4">
        <div className="flex items-center justify-center float-right gap-3">
          <Button type="button" className="bg-[#33363F]" onClick={onCancel}>
            {t("usersList.enrolledCoursesModal.close")}
          </Button>
        </div>
      </div>
    </>
  );
}
