"use client";
import React, { useState, useEffect } from "react";
import MainLayout from "../layout/mainlayout";
import { DataTable } from "../../components/ui/data-table/data-table";
import { getColumns } from "./columns";
import { pageUrl, privilegeData } from "@/lib/constants";
import { Button } from "@/components/ui/button";
import { PlusIcon } from "@radix-ui/react-icons";
import {
  Archive,
  Edit,
  Link2,
  MoveUpRight,
  PencilLineIcon,
  PlusCircleIcon,
  ShieldPlusIcon,
  FileText,
} from "lucide-react";
import { User } from "lucide-react";
import type {
  ErrorCatch,
  InnerItem,
  SubscriptionListResults,
  subscriptionListRequest,
} from "@/types";
import { useRouter } from "next/navigation";
import getPrivilegeList from "@/hooks/useCheckPrivilege";
import useSubscription from "@/hooks/useSubscription";
import { ORG_KEY } from "@/lib/constants";
import { useToast } from "@/components/ui/use-toast";
import type { ToastType } from "@/types";
import { Spinner } from "@/components/ui/progressiveLoader";
import { MembershipUserListPage } from "../../components/plan-users/plan-users";
import { Modal } from "@/components/ui/modal";
import ApproveSubscription from "../approve-subscription/approveSubscription";
import EditSubscription from "@/components/ui/subscription-modal";
import { UpdateMembershipPage } from "./updatePlanList";
import DeleteSubscription from "./deleteSubscription";
import { ERROR_MESSAGES } from "@/lib/messages";
import PublishSubscription from "./publishSubscription";
import AddUsers from "./addUsers";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumb";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import ExtendSubscriptionModal from "./extendPlanValidity";
import { useTranslation } from "react-i18next";
import CoursePurchaseRequests from "@/components/course-purchase-requests/course-purchase-requests";

export default function MembershipPlanListPage(): React.JSX.Element {
  const { t } = useTranslation();
  const columns = getColumns(t);
  const router = useRouter();
  const { getSubscriptionListForAdmin } = useSubscription();
  const { toast } = useToast() as ToastType;
  const [subscriptionData, setSubscriptionData] = useState<
    SubscriptionListResults[]
  >([]);
  const [isLoading, setIsLoading] = React.useState<boolean>(true);
  const [showUser, setShowUser] = React.useState<boolean>(false);
  const [planId, setPlanId] = React.useState<string>("");
  const [planName, setPlanName] = React.useState<string>("");
  const [isDialogOpen, setIsDialogOpen] = React.useState(false);
  const [isAssigned, setIsAssigned] = React.useState(false);
  const [isSuperUser, setIsSuperUser] = React.useState(false);
  const [isUpdate, setIsUpdate] = React.useState(false);
  const [reloadData, setReloadData] = React.useState(false);
  const [passData, setPassData] = React.useState<SubscriptionListResults>();
  const [deleteSubscription, setDelete] = useState<boolean>(false);
  const [approveSubscription, setPublish] = useState<boolean>(false);
  //const [deleteSubscriptionPrivilege, setDeletePrivilege] = useState<boolean>(false);
  const [openAddUser, setOpenAddUser] = useState<boolean>(false);
  const [title, setTitle] = useState("");
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);
  const [openDateExtendModal, setOpenDateExtendModal] =
    useState<boolean>(false);
  const [isReviewRequestModalOpen, setIsReviewRequestModalOpen] =
    useState<boolean>(false);

  const disableBtn = getPrivilegeList(
    "Subscription_Plans",
    privilegeData.Subscription_Plans.addNewSubscription,
  );
  const disableApproval = getPrivilegeList(
    "Subscription_Plans",
    privilegeData.Subscription_Plans.userSubscriptionPendingList,
  );

  useEffect(() => {
    // setBreadcrumbAddItems(getBreadCrumbItems("Add Subscription Plan", {"":""}))
    setBreadcrumbItems(
      getBreadCrumbItems(t, t("breadcrumb.subscriptionPlans"), { "": "" }),
    );
  }, [t]);

  useEffect(() => {
    /* const disableDeleteBtn = getPrivilegeList(
      "Subscription_Plans",
      privilegeData.Subscription_Plans.deleteSubcriptionPlan,
    ); */
    //setDeletePrivilege(disableDeleteBtn);
  }, []);

  useEffect(() => {
    void fetchSubscriptionList();
  }, [reloadData]);

  const customColumnWidths: Record<string, { width: number; align: string }> = {
    name: { width: 300, align: "justify" },
    description: { width: 850, align: "justify" },
    subscription_type: { width: 200, align: "justify" },
    price: { width: 100, align: "center" },
    currency: { width: 100, align: "justify" },
    valid_from: { width: 250, align: "justify" },
    valid_to: { width: 250, align: "justify" },
    subscription_plan_status: { width: 100, align: "justify" },
    subscription_expiry: { width: 200, align: "justify" },
  };

  const fetchSubscriptionList = async (): Promise<void> => {
    const org_id = localStorage.getItem(ORG_KEY) ?? "";

    const reqParams = {
      org_id: org_id,
      limit_param: null, //to do
      offset_param: 0, //to do
    };
    try {
      const subscriptions = await getSubscriptionListForAdmin(
        reqParams as subscriptionListRequest,
      );
      console.log("sub", subscriptions);
      setIsLoading(false);
      if (subscriptions.result != null) {
        setSubscriptionData(subscriptions.result);
        setIsSuperUser(subscriptions.is_super_user);
      }
      subscriptions.result.map((item) => {
        if (
          item.subscription_status === "Published" ||
          item.is_expired === true
        ) {
          item.hideViews = true;
        } else {
          item.hideViews = false;
        }
      });
      subscriptions.result.map((item) => {
        if (item.subscription_status === "Draft") {
          item.hideEditDelete = false;
        } else {
          item.hideEditDelete = true;
        }
      });
      subscriptions.result.map((item) => {
        if (item.subscription_status === "Draft" || item.is_expired === true) {
          item.hideEdit = false;
        } else {
          item.hideEdit = true;
        }
      });
      subscriptions.result.map((item) => {
        if (item.is_expired === true) {
          item.hideEdit = true;
          item.hideEditDelete = true;
          item.hideStatus = true;
        }
        if (item.is_subscription_course_assigned === true) {
          item.isCourseAssigned = true;
        }
      });
      subscriptions.result.map((item) => {
        if (item.subscription_status === "Published") {
          item.hideIcon = true;
          item.hideExtendExpiry = false;
        } else {
          item.hideIcon = false;
          item.hideExtendExpiry = true;
        }
      });
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.details,
      });
    }
  };

  const courseResourceView = (data: SubscriptionListResults): void => {
    const planId = data.id;
    const name = data.name;
    const type = data.subscription_type;
    router.push(
      `${pageUrl.courseResourceLink}?type=${planId}&name=${name}&based_on=${type}&expired=${data.is_expired}`,
    );
  };

  const usersView = (data: SubscriptionListResults): void => {
    setShowUser(true);
    setPlanId(data.id as string);
    setPlanName(data.name);
  };

  const updateSubscription = (data: SubscriptionListResults): void => {
    setPlanId(data.id as string);
    if (data.is_subscription_assigned === true) {
      setIsAssigned(true);
      setPassData(data);
    } else {
      setIsAssigned(false);
      setIsUpdate(true);
      setPassData(data);
    }
  };

  const delsubscription = (data: SubscriptionListResults): void => {
    setTitle("");
    setPassData(data);
    setDelete(true);
  };
  const pubsubscription = (data: SubscriptionListResults): void => {
    if (data.subscription_status === "Draft") {
      setTitle(`${t("subscriptionPlan.publishSubscription")}`);
    } else {
      setTitle(`${t("subscriptionPlan.draftSubscription")}`);
    }
    setPassData(data);
    setPublish(true);
  };
  const addUserToSubscription = (data: SubscriptionListResults): void => {
    setOpenAddUser(true);
    setPlanId(data.id as string);
  };
  const extendPlanValidity = (data: SubscriptionListResults): void => {
    setOpenDateExtendModal(true);
    setPassData(data);
  };
  const closeUserDialog = (): void => {
    setOpenAddUser(false);
    setOpenDateExtendModal(false);
  };
  const addSubscription = (): void => {
    setPlanId("");
    setIsUpdate(true);
  };
  const handleModal = (): void => {
    setIsDialogOpen(true);
  };

  const closeDialog = (): void => {
    setIsDialogOpen(false);
    setIsAssigned(false);
    setShowUser(false);
    setOpenDateExtendModal(false);
  };
  const saveUpdateDate = (): void => {
    setReloadData(!reloadData);
    setOpenDateExtendModal(false);
  };
  const closeDelete = (): void => {
    setIsDialogOpen(false);
    setDelete(false);
    setIsAssigned(false);
  };
  const closePublish = (): void => {
    setIsDialogOpen(false);
    void fetchSubscriptionList();
    setPublish(false);
  };

  const reloadPlanList = (): void => {
    setIsAssigned(false);
    void fetchSubscriptionList();
  };

  const cancelUpdate = (): void => {
    setIsUpdate(!isUpdate);
  };
  const onUpdateSubscription = (): void => {
    setReloadData(!reloadData);
  };
  function onSubmit(): void {
    fetchSubscriptionList().catch((error) => console.log(error));
  }
  function onPublish(): void {
    fetchSubscriptionList().catch((error) => console.log(error));
  }

  return (
    <MainLayout>
      {!isUpdate && (
        <div>
          <NextBreadcrumb
            items={breadcrumbItems}
            separator={<span> | </span>}
            containerClasses="flex py-5"
            listClasses="hover:underline mx-2 font-bold"
            capitalizeLinks
          />
          <span>
            <h1 className="text-2xl font-semibold ">
              {t("subscriptionPlan.title")}
              {disableBtn && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        onClick={addSubscription}
                        className="bg-ring bg-[#fb8500] hover:bg-[#fb5c00] py-2 md:py-3 px-4 md:px-6 whitespace-nowrap ml-2"
                      >
                        <PlusIcon className="h-4 w-4 md:h-5 md:w-5" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p> {t("subscriptionPlan.addSubscription")}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
              {disableApproval && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        onClick={handleModal}
                        className="bg-ring bg-[#fb8500] hover:bg-[#fb5c00] py-2 md:py-3 px-4 md:px-6 whitespace-nowrap ml-2"
                      >
                        <ShieldPlusIcon className="h-4 w-4 md:h-5 md:w-5" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{t("subscriptionPlan.approveSubscriptionRequest")}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      onClick={() => setIsReviewRequestModalOpen(true)}
                      className="bg-ring bg-[#fb8500] hover:bg-[#fb5c00] py-2 md:py-3 px-4 md:px-6 whitespace-nowrap ml-2"
                    >
                      <FileText className="h-4 w-4 md:h-5 md:w-5" />
                      Review Course Purchase Request
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Review Course Purchase Requests</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </h1>
          </span>
          <div className="w-full">
            <div className="flex items-center justify-between">
              <h1 className="text-2xl font-semibold tracking-tight">
                {/* Subscription Plans */}
              </h1>
            </div>
            {isLoading ? (
              <Spinner />
            ) : (
              <div className="border rounded-md p-4 mt-4 bg-[#fff]">
                {/* <div className="flex justify-end">
                  {disableBtn && (
                    <Button
                      className="w-full md:w-auto px-2 bg-[#fb8500] hover:bg-[#fb5c00] "
                      onClick={addSubscription}
                    >
                      <PlusIcon className="h-5 w-5" />
                      Add New Subscription
                    </Button>
                  )}
                  {disableApproval && (
                    <Button
                      className="w-full md:w-auto px-2 bg-[#fb8500] hover:bg-[#fb5c00] ms-5 ps-5 pe-5"
                      onClick={handleModal}
                    >
                      Approve Subscription
                    </Button>
                  )}
                </div> */}
                <DataTable
                  columns={columns}
                  data={subscriptionData}
                  FilterLabel={t("subscriptionPlan.filterByPlan")}
                  FilterBy={"name"}
                  disablePublish={"hideViews"}
                  disableEditDelete={"hideEditDelete"}
                  hideEdit={"hideEdit"}
                  hideExtendExpiry={"hideExtendExpiry"}
                  disableIcon={"hideIcon"}
                  disableStatus={"hideStatus"}
                  isCourseAssigned={"isCourseAssigned"}
                  actions={[
                    {
                      title: t("subscriptionPlan.viewLinkCourseResources"),
                      icon: Link2,
                      varient: "icon",
                      isEnable: isSuperUser,
                      handleClick: (val: unknown) =>
                        courseResourceView(val as SubscriptionListResults),
                    },
                    {
                      title: t("subscriptionPlan.user"),
                      icon: User,
                      varient: "icon",
                      isEnable: isSuperUser,
                      handleClick: (val: unknown) =>
                        usersView(val as SubscriptionListResults),
                    },
                    {
                      title: t("subscriptionPlan.edit"),
                      icon: Edit,
                      color: "#fb8500",
                      varient: "icon",
                      isEnable: isSuperUser,
                      handleClick: (val: unknown) => {
                        updateSubscription(val as SubscriptionListResults);

                        // if (
                        //   (val as SubscriptionListResults)
                        //     .is_subscription_assigned
                        // ) {
                        //   toast({
                        //     variant: ERROR_MESSAGES.toast_variant_destructive,
                        //     title: ERROR_MESSAGES.toast_error_title,
                        //     description: ERROR_MESSAGES.edit_not_allowed,
                        //   });
                        // } else {
                        //   updateSubscription(val as SubscriptionListResults);
                        // }
                      },
                    },
                    {
                      title: t("subscriptionPlan.delete"),
                      icon: Archive,
                      varient: "icon",
                      color: "#ff0000",
                      isEnable: getPrivilegeList(
                        "Subscription_Plans",
                        privilegeData.Subscription_Plans.deleteSubcriptionPlan,
                      ),

                      handleClick: (val: unknown) => {
                        if (
                          (val as SubscriptionListResults)
                            .is_subscription_assigned
                        ) {
                          toast({
                            variant: ERROR_MESSAGES.toast_variant_destructive,
                            title: t("errorMessages.toast_error_title"),
                            description: t("errorMessages.delete_not_allowed"),
                          });
                        } else {
                          delsubscription(val as SubscriptionListResults);
                        }
                      },
                    },

                    {
                      title: t("subscriptionPlan.extendValidity"),
                      icon: MoveUpRight,
                      varient: "icon",
                      // to do privilage
                      color: "#6C63FF",
                      handleClick: (val: unknown) => {
                        extendPlanValidity(val as SubscriptionListResults);
                      },
                    },
                    {
                      title: t("subscriptionPlan.publishedStatus"),
                      icon: PencilLineIcon,
                      varient: "icon",
                      isEnable: getPrivilegeList(
                        "Subscription_Plans",
                        privilegeData.Subscription_Plans.publishSubscription,
                      ),
                      color: "#445469",
                      handleClick: (val: unknown) => {
                        pubsubscription(val as SubscriptionListResults);
                      },
                    },
                    {
                      title: t("subscriptionPlan.addUser"),
                      icon: PlusCircleIcon,
                      varient: "icon",
                      // to do privilage
                      color: "#9FC089",
                      handleClick: (val: unknown) => {
                        addUserToSubscription(val as SubscriptionListResults);
                      },
                    },
                  ]}
                  customColumnWidths={customColumnWidths}
                />
              </div>
            )}
          </div>

          {isAssigned && (
            <Modal
              title={t("subscriptionPlan.editSubscriptionPlan")}
              header=""
              openDialog={isAssigned}
              closeDialog={closeDialog}
              type="max-w-7xl"
            >
              <EditSubscription
                onCancel={closeDelete}
                isModal={true}
                data={passData as SubscriptionListResults}
                onSave={reloadPlanList}
              />
            </Modal>
          )}
          {deleteSubscription && (
            <Modal
              title={t("subscriptionPlan.deleteSubscriptionPlan")}
              header=""
              openDialog={deleteSubscription}
              closeDialog={closeDelete}
            >
              <DeleteSubscription
                onSave={onSubmit}
                onCancel={closeDelete}
                isModal={true}
                data={passData as SubscriptionListResults}
              />
            </Modal>
          )}
          {approveSubscription && (
            <Modal
              title={title}
              header=""
              openDialog={approveSubscription}
              closeDialog={closePublish}
            >
              <PublishSubscription
                onSave={onPublish}
                onCancel={closePublish}
                isModal={true}
                data={passData as SubscriptionListResults}
              />
            </Modal>
          )}
          {showUser && (
            <Modal
              title={t("subscriptionPlan.userMembership")}
              header=""
              openDialog={showUser}
              closeDialog={closeDialog}
              type="max-w-5xl"
            >
              <MembershipUserListPage
                onCancel={closeDialog}
                isModal={true}
                data={planId as string}
                plan={planName}
              />
            </Modal>
          )}
          {isDialogOpen && (
            <Modal
              title={t("subscriptionPlan.approveSubscription")}
              header=""
              openDialog={isDialogOpen}
              closeDialog={closeDialog}
              type="max-w-7xl"
            >
              <ApproveSubscription onCancel={closeDialog} />
            </Modal>
          )}
          {openAddUser && (
            <Modal
              title=""
              header=""
              openDialog={openAddUser}
              closeDialog={closeUserDialog}
              type="max-w-7xl"
            >
              <AddUsers data={planId as string} onCancel={closeUserDialog} />
            </Modal>
          )}
          {openDateExtendModal && (
            <Modal
              title={t("subscriptionPlan.updatePlanExpiry")}
              header=""
              openDialog={openDateExtendModal}
              closeDialog={closeUserDialog}
              type="max-w-md"
            >
              <ExtendSubscriptionModal
                data={passData as SubscriptionListResults}
                onCancel={closeUserDialog}
                onSave={saveUpdateDate}
              />
            </Modal>
          )}
        </div>
      )}

      {isUpdate && (
        <UpdateMembershipPage
          subscriptionsData={passData as SubscriptionListResults}
          onCancel={cancelUpdate}
          onSave={onUpdateSubscription}
          subscriptionId={planId}
        />
      )}

      {/* Review Request Modal */}
      {isReviewRequestModalOpen && (
        <Modal
          title="Course Purchase Requests"
          header=""
          openDialog={isReviewRequestModalOpen}
          closeDialog={() => setIsReviewRequestModalOpen(false)}
          type="max-w-6xl"
        >
          <CoursePurchaseRequests
            onCancel={() => setIsReviewRequestModalOpen(false)}
          />
        </Modal>
      )}
    </MainLayout>
  );
}
