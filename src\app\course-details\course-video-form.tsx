import { useForm } from "react-hook-form";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import type {
  AddCourseVideoForm,
  CourseDetailsRequest,
  ErrorCatch,
  ToastType,
} from "@/types";
import { AddCourseVideoschema } from "@/schema/schema";
import { ModalButton } from "@/components/ui/modalButton";
import useCourse from "@/hooks/useCourse";
import { useToast } from "@/components/ui/use-toast";
import { ORG_KEY } from "@/lib/constants";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import { useTranslation } from "react-i18next";

export function CourseVideoForm(props: {
  closeDialog: () => void;
}): React.JSX.Element {
  const { t } = useTranslation();
  const { addCourseVideo, courseDetails } = useCourse();
  const { toast } = useToast() as ToastType;
  const form = useForm<AddCourseVideoForm>({
    resolver: zodResolver(AddCourseVideoschema),
  });

  async function onSubmit(data: AddCourseVideoForm): Promise<void> {
    console.log(data);
    const org_id = localStorage.getItem("orgId");
    const course_id = localStorage.getItem("courseId");
    const insertData = {
      course_id: course_id ?? "",
      org_id: org_id ?? "",
      resource_data: {
        resource_name: data.name,
        resource_type: "Video",
        resource_url: data.video_url,
      },
    };
    try {
      const result = await addCourseVideo(insertData);

      if (result.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.toast_success_title"),
          description: t("successMessages.addedFile"),
        });
        const orgId = localStorage.getItem(ORG_KEY);
        const reqParams: CourseDetailsRequest = {
          course_id: course_id as string,
          org_id: orgId ?? "",
        };
        await courseDetails(reqParams);
        props.closeDialog();
      } else if (result.status === "error") {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: result.status,
        });
        console.log("API Error:", result.status);
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
      console.error("An unexpected error occurred:", error);
    }
  }

  return (
    <Form {...form}>
      <form
        onSubmit={(event) => void form.handleSubmit(onSubmit)(event)}
        className="space-y-8"
      >
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t(("courses.courseModule.name"))}</FormLabel>
              <FormControl>
                <Input autoComplete="off" placeholder={t(("courses.courseModule.name"))} {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="video_url"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t(("courses.courseModule.pasteLink"))}</FormLabel>
              <FormControl>
                <Input
                  autoComplete="off"
                  placeholder={t(("courses.courseModule.pasteUrl"))}
                  {...field}
                />
              </FormControl>
              <FormDescription></FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <ModalButton
          closeDialog={() => props.closeDialog()}
          closeLabel={t("buttons.close")}
          submitLabel={t("buttons.confirm")}
        />
      </form>
    </Form>
  );
}
