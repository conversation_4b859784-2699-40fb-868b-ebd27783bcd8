import React, { type ReactNode, useState, useEffect } from "react";
import "./../../styles/main.css";

interface StepperProps {
  stepperData: {
    stepNo: number;
    StepLabel: string;
    stepValue: {
      value: string;
      label: string;
    }[];
  };
  stepLabel: string[];
  isVertical: boolean;
  component: ReactNode;
  onStepChange: (step: number) => void; // Define a prop for the callback function
  stepIcons?: React.JSX.Element[];
  stepInstance?: string[];
  onStepInstance?: (stepType: string, stepInstance: string) => void;
  stepType?: string[];
}

const Stepper: React.FC<StepperProps> = ({
  stepperData,
  isVertical,
  component,
  onStepChange,
  stepLabel,
  stepIcons,
  stepInstance,
  onStepInstance,
  stepType,
}) => {
  const { stepNo } = stepperData;

  const [currentStep, setCurrentStep] = useState(0);

  useEffect(() => {
    handleStepClick(0);
  }, []);

  const handleStepClick = (stepIndex: number): void => {
    if (stepType && stepInstance) {
      onStepInstance?.(stepType?.[stepIndex], stepInstance?.[stepIndex]);
    }

    setCurrentStep(stepIndex);
    onStepChange(stepIndex); // Call the callback function
  };

  const handlePrevClick = (): void => {
    setCurrentStep(Math.max(currentStep - 1, 0));
    handleStepClick(Math.max(currentStep - 1, 0));
  };

  const handleNextClick = (): void => {
    setCurrentStep(Math.min(currentStep + 1, stepNo - 1));
    handleStepClick(Math.min(currentStep + 1, stepNo - 1));
  };

  return (
    <div className="w-full max-w-md  p-4 space-y-4">
      {isVertical ? (
        <div className="flex flex-col space-y-4">
          {Array.from({ length: stepNo }, (_, index) => (
            <>
              <div key={index} className="flex">
                <div
                  key={index}
                  onClick={() => handleStepClick(index)}
                  className={`step-number ${
                    currentStep === index
                      ? "active"
                      : index < currentStep
                        ? "completed"
                        : "inactive"
                  }`}
                >
                  {index + 1}
                </div>
                <div
                  onClick={() => handleStepClick(index)}
                  className="flex items-center ml-4 cursor-pointer"
                >
                  <div className="mr-2">{stepIcons?.[index]}</div>
                  <div>{stepLabel[index]}</div>
                </div>
              </div>

              <div className={`${currentStep === index ? "show" : "hidden"}`}>
                {component}
              </div>
            </>
          ))}
        </div>
      ) : (
        <div className="flex space-x-4">
          {Array.from({ length: stepNo }, (_, index) => (
            <>
              <div key={index} className="flex">
                <div
                  key={index}
                  onClick={() => handleStepClick(index)}
                  className={`step-number ${
                    currentStep === index
                      ? "active"
                      : index < currentStep
                        ? "completed"
                        : "inactive"
                  }`}
                >
                  {index + 1}
                </div>
                <div
                  onClick={() => handleStepClick(index)}
                  className="flex items-center ml-4 cursor-pointer"
                >
                  <div className="mr-2">{stepIcons?.[index]}</div>
                  <div>{stepLabel[index]}</div>
                </div>
              </div>

              <div className={`${currentStep === index ? "show" : "hidden"}`}>
                {component}
              </div>
            </>
          ))}
        </div>
      )}

      <div className="flex justify-between">
        <button
          onClick={handlePrevClick}
          disabled={currentStep === 0}
          className={`px-4 py-2 ${
            currentStep === 0
              ? "button-disabled"
              : "button-active hover:button-active"
          } rounded`}
        >
          Prev
        </button>
        <button
          onClick={handleNextClick}
          disabled={currentStep === stepNo - 1}
          className={`px-4 py-2 ${
            currentStep === stepNo - 1
              ? "button-disabled"
              : "button-active hover:button-active"
          } rounded`}
        >
          Next
        </button>
      </div>
    </div>
  );
};

export default Stepper;
