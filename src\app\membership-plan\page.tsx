"use client";
import React, { useState, useEffect } from "react";
import MainLayout from "../layout/mainlayout";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useForm } from "react-hook-form";
import { Input } from "@/components/ui/input";
import { Editor } from "primereact/editor";
import type { ErrorCatch, SubscriptionPlans, ToastType } from "@/types";
import { Button } from "@/components/ui/button";
import { DateTimePicker } from "@/components/ui/date-time-picker/date-time-picker";
import { parseZonedDateTime } from "@internationalized/date";
import type { DateValue, ZonedDateTime } from "@internationalized/date";
import moment from "moment-timezone";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  MembershipPlanEditSchema,
  MembershipPlanSchema,
} from "@/schema/schema";
import Link from "next/link";
import {
  ANUAL_PLAN,
  COURSE_BASED,
  CUSTOM_PLAN,
  DATE_FORMAT,
  DEFAULT_CURRENCY,
  FORMATTED_DATE_FORMAT,
  MONTHLY_PLAN,
  ORG_KEY,
  RESOURCE_BASED,
  /* TIME_BASED, */
  pageUrl,
} from "@/lib/constants";
import { useRouter, useSearchParams } from "next/navigation";
import useSubscription from "@/hooks/useSubscription";
import { useToast } from "@/components/ui/use-toast";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import { useTranslation } from "react-i18next";

export default function MembershipPlanPage(): React.JSX.Element {
  const { t } = useTranslation();
  const { toast } = useToast() as ToastType;
  const { getListDetailsById, addSubscription, updateSubscription } =
    useSubscription();
  const searchParams = useSearchParams();
  const subscription_id = searchParams.get("type") ?? "";
  const [description, setDescription] = useState("");
  const [frequency, setFrequency] = useState("");
  const [basedOn, setBasedOn] = useState("");
  const title =
    subscription_id === "" ? "Add Subscription Plan" : "Edit Subscription Plan";
  const [startDateTime, setStartDateTime] = useState<DateValue | undefined>();
  const [defaultDate, setDefaultDate] = useState<ZonedDateTime | DateValue>();
  const [endDateTime, setEndDateTime] = useState<ZonedDateTime | DateValue>();
  // const [selectedStartDate, setSelectedStartDate] = useState<
  //   ZonedDateTime | DateValue
  // >();
  // const [minEndDate, setMinEndDate] = React.useState<DateValue | undefined>();

  const form = useForm<SubscriptionPlans>({
    resolver: zodResolver(
      subscription_id === "" ? MembershipPlanSchema : MembershipPlanEditSchema,
    ),
  });
  const [currency, setCurrency] = useState(DEFAULT_CURRENCY);
  const router = useRouter();

  useEffect(() => {
    form.setValue("currency", DEFAULT_CURRENCY);
    if (subscription_id != "") {
      const fetchSubscriptionList = async (): Promise<void> => {
        try {
          const org_id = localStorage.getItem(ORG_KEY) ?? "";
          const subscriptions = await getListDetailsById(
            subscription_id,
            org_id,
          );
          const subscriptionData = subscriptions[0];
          console.log("subscriptionData", subscriptionData);

          form.setValue("name", subscriptionData.name);
          form.setValue("description", subscriptionData.description);
          form.setValue(
            "subscription_type",
            subscriptionData.subscription_type,
          );
          form.setValue(
            "subscription_frequency_type",
            subscriptionData.subscription_frequency_type,
          );
          form.setValue("price", subscriptionData.price);
          setDescription(subscriptionData.description);
          setBasedOn(subscriptionData.subscription_type);
          setFrequency(subscriptionData.subscription_frequency_type);
          form.setValue("price", subscriptionData.price.toString());
          const currentTimezone = moment.tz.guess();
          const parsedDatetime = moment.tz(
            subscriptionData.valid_from,
            currentTimezone,
          );
          const formattedDatetime =
            parsedDatetime.format(DATE_FORMAT) + `[${currentTimezone}]`;
          const dateTime = parseZonedDateTime(formattedDatetime);
          form.setValue("valid_from", dateTime);
          setDefaultDate(dateTime);
          setStartDateTime(dateTime);
          const parsedDatetimeValidTo = moment.tz(
            subscriptionData.valid_to,
            currentTimezone,
          );
          const formattedEndDatetime =
            parsedDatetimeValidTo.format(DATE_FORMAT) + `[${currentTimezone}]`;
          const endDateTime = parseZonedDateTime(formattedEndDatetime);
          form.setValue("valid_to", endDateTime);
          console.log("e", endDateTime);
          setEndDateTime(endDateTime);
        } catch (e) {
          console.error(e);
          e;
        }
      };
      fetchSubscriptionList().catch((error) => console.log(error));
    }
  }, []);

  async function onSubmit(): Promise<void> {
    const formData = form.getValues();
    const dateStart = formData.valid_from;
    const startDate = new Date(
      dateStart.year,
      dateStart.month - 1,
      dateStart.day,
      dateStart.hour,
      dateStart.minute,
    );
    const dateEnd = formData.valid_to;
    const endDate = new Date(
      dateEnd.year,
      dateEnd.month - 1,
      dateEnd.day,
      dateEnd.hour,
      dateEnd.minute,
    );
    const momentStartDate = moment(startDate);

    const momentEndDate = moment(endDate);
    const duration = momentEndDate.diff(momentStartDate, "months");
    let requiredDuration = 0;
    if (formData.subscription_frequency_type === "monthly") {
      requiredDuration = 1;
    } else if (formData.subscription_frequency_type === "annual") {
      requiredDuration = 12;
    }
    if (
      duration < requiredDuration &&
      formData.subscription_frequency_type !== "custom"
    ) {
      let errorMsg;
      if (formData.subscription_frequency_type === "monthly") {
        errorMsg = t("errorMessages.monthly_date_error");
      } else if (formData.subscription_frequency_type === "annual") {
        errorMsg = t("errorMessages.annual_date_error");
      }

      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.invalid_date_msg"),
        description: errorMsg,
      });
      return;
    }
    const formattedValidFrom = momentStartDate.format(FORMATTED_DATE_FORMAT);
    const formattedValidTo = momentEndDate.format(FORMATTED_DATE_FORMAT);

    const org_id = localStorage.getItem(ORG_KEY) ?? "";
    const subscriptionPlanData = {
      name: formData.name.trimStart(),
      description: formData.description,
      type: formData.subscription_type,
      subscription_frequency: formData.subscription_frequency_type,
      valid_from: formattedValidFrom,
      valid_to: formattedValidTo,
      price: formData.price.toString(),
      status: "active",
      currency: DEFAULT_CURRENCY,
    };
    const addParams = {
      org_id: org_id,
      subscription_plan_data: subscriptionPlanData,
    };
    const editParams = {
      org_id: org_id,
      plan_id: subscription_id,
      subscription_plan_data: subscriptionPlanData,
    };
    if (subscription_id != "") {
      const updateSubscriptions = async (): Promise<void> => {
        try {
          const subscriptions = await updateSubscription(editParams);
          if (subscriptions?.status === "success") {
            toast({
              variant: SUCCESS_MESSAGES.toast_variant_default,
              title: t("successMessages.update_subscription_title"),
              description: t("successMessages.update_subscription_msg"),
            });
            router.push(pageUrl.membershipPlanList);
          }
        } catch (e) {
          const err = e as ErrorCatch;
          let errMsg = "";
          let errTtile = "";
          if (err.message === ERROR_MESSAGES.subscription_update_date) {
            errMsg = err.message.split("Invalid date range.")[1].trim();
            errTtile = ERROR_MESSAGES.invalid_date_msg;
          } else if (err.message === ERROR_MESSAGES.invalid_validity) {
            errMsg = err.message.split("Invalid validity period:")[1].trim();
            errTtile = ERROR_MESSAGES.invalid_date_msg;
          } else {
            errMsg = err.message;
            errTtile = ERROR_MESSAGES.update_subscription_title;
          }
          toast({
            variant: ERROR_MESSAGES.toast_variant_destructive,
            title: errTtile,
            description: errMsg,
          });
        }
      };
      updateSubscriptions().catch((error) => console.log(error));
    } else {
      const addSubscriptions = async (): Promise<void> => {
        try {
          const subscriptions = await addSubscription(addParams);
          if (subscriptions?.status === "success") {
            toast({
              variant: SUCCESS_MESSAGES.toast_variant_default,
              title: t("successMessages.add_subscription_title"),
              description: t("successMessages.add_subscription_msg"),
            });
            router.push(pageUrl.membershipPlanList);
          }
        } catch (e) {
          const err = e as ErrorCatch;
          let errMsg = "";
          let errTtile = "";
          if (err.message === ERROR_MESSAGES.subscription_add_date) {
            errMsg = err.message.split("Invalid validity date:")[1].trim();
            errTtile = ERROR_MESSAGES.invalid_date_msg;
          } else if (err.message === ERROR_MESSAGES.invalid_valid_from) {
            errMsg = ERROR_MESSAGES.invalid_valid_from;
            errTtile = ERROR_MESSAGES.invalid_date_msg;
          } else {
            errMsg = err.message;
            errTtile = ERROR_MESSAGES.update_subscription_title;
          }

          toast({
            variant: ERROR_MESSAGES.toast_variant_destructive,
            title: errTtile,
            description: errMsg,
          });
        }
      };
      addSubscriptions().catch((error) => console.log(error));
    }
  }
  const setRichTextValue = (val: string): void => {
    form.setValue("description", val);
  };

  // const handleValidFromChange = (dateObject: DateValue): void => {
  //   const nextDateObject = { ...dateObject };
  //   console.log("nextDateObject");

  //   // setSelectedStartDate(dateObject);
  //   const subscriptionFrequencyType = frequency;
  //   if (subscription_id === "") {
  //     if (subscriptionFrequencyType === "monthly") {
  //       nextDateObject.month += 1;
  //       setEndDateTime(nextDateObject as DateValue);
  //     } else if (subscriptionFrequencyType === "annual") {
  //       nextDateObject.year += 1;
  //       setEndDateTime(nextDateObject as DateValue);
  //     }
  //   }
  // };

  return (
    <MainLayout>
      <div className="w-full">
        <>
          <h1 className="text-2xl font-semibold tracking-tight">{title}</h1>
        </>
        <div className="border rounded-md p-4 mt-4">
          <Form {...form}>
            <form onSubmit={(event) => void form.handleSubmit(onSubmit)(event)}>
              <div>
                <FormField
                  name="name"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel autoCorrect="off">
                        {t("subscriptionPlan.membershipName")}{" "}
                        <span className="text-red-700">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          autoComplete="off"
                          type="text"
                          {...field}
                          maxLength={30}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="mt-4">
                <FormField
                  name="description"
                  control={form.control}
                  render={() => (
                    <FormItem>
                      <FormLabel>
                        {t("subscriptionPlan.description")}{" "}
                        <span className="text-red-700">*</span>
                      </FormLabel>
                      <FormControl>
                        <Editor
                          value={description}
                          onTextChange={(event) => {
                            const Value = event.textValue;
                            setRichTextValue(Value as string);
                          }}
                          style={{ height: "320px" }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4">
                <div className="row-span-2">
                  <FormField
                    name="subscription_type"
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t("subscriptionPlan.basedOn")}{" "}
                          <span className="text-red-700">*</span>
                        </FormLabel>
                        <FormControl>
                          <Select
                            name="subscription_type"
                            onValueChange={(value) => {
                              setBasedOn(value); // Update the frequency state
                              field.onChange(value); // Update the form control
                            }}
                            value={basedOn}
                          >
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select Based On" />
                            </SelectTrigger>
                            <SelectContent>
                              <>
                                <SelectItem value="course_based">
                                  {COURSE_BASED}
                                </SelectItem>
                                <SelectItem value="resource_based">
                                  {RESOURCE_BASED}
                                </SelectItem>
                                {/* <SelectItem value="time_based">
                                  {TIME_BASED}
                                </SelectItem> */}
                              </>
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="row-span-2">
                  <FormField
                    name="subscription_frequency_type"
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t("subscriptionPlan.paymentFrequency")}
                          <span className="text-red-700">*</span>
                        </FormLabel>
                        <Select
                          name="subscription_frequency_type"
                          onValueChange={(value) => {
                            setFrequency(value);
                            field.onChange(value);
                          }}
                          value={frequency}
                        >
                          <FormControl>
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <>
                              <SelectItem value="annual">
                                {ANUAL_PLAN}
                              </SelectItem>
                              <SelectItem value="custom">
                                {CUSTOM_PLAN}
                              </SelectItem>
                              <SelectItem value="monthly">
                                {MONTHLY_PLAN}
                              </SelectItem>
                            </>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="row-span-2 mt-4">
                  <FormField
                    name="valid_from"
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t("subscriptionPlan.validFrom")}
                          <span className="text-red-700">*</span>
                        </FormLabel>
                        <FormControl>
                          <DateTimePicker
                            granularity={"minute"}
                            minValue={defaultDate}
                            value={field.value as DateValue}
                            defaultValue={startDateTime}
                            onChange={(newDate) => {
                              // handleValidFromChange(newDate);
                              field.onChange(newDate);
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="row-span-2 mt-4">
                  <FormField
                    name="valid_to"
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t("subscriptionPlan.validTo")}{" "}
                          <span className="text-red-700">*</span>
                        </FormLabel>
                        <FormControl>
                          <DateTimePicker
                            granularity="minute"
                            // minValue={endDateTime}
                            value={field.value as DateValue}
                            defaultValue={endDateTime}
                            onChange={(newDate) => {
                              // handleValidToChange(newDate);
                              field.onChange(newDate);
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="row-span-2">
                  <FormField
                    name="currency"
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t("subscriptionPlan.currency")}
                          <span className="text-red-700">*</span>
                        </FormLabel>
                        <Select
                          name="currency"
                          onValueChange={(value) => {
                            setCurrency(value); // Update the currency state
                            field.onChange(value); // Update the form control
                          }}
                          value={currency} // Set the value from the state
                        >
                          <FormControl>
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <>
                              <SelectItem value={DEFAULT_CURRENCY}>
                                {DEFAULT_CURRENCY}
                              </SelectItem>
                              {/* Add more currency options if needed */}
                            </>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="row-span-2">
                  <FormField
                    name="price"
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel autoCorrect="off">
                          {t("subscriptionPlan.amount")}{" "}
                          <span className="text-red-700">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            autoComplete="off"
                            type="number"
                            {...field}
                            onKeyDown={(e) => {
                              if (e.key === " ") {
                                e.preventDefault();
                                return;
                              }
                              if (
                                !/^[0-9.]$/.test(e.key) &&
                                e.key !== "Backspace"
                              ) {
                                e.preventDefault();
                              }
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <div className="w-full flex justify-end mt-4">
                <div className="px-4">
                  <Link href={pageUrl.membershipPlanList}>
                    <Button
                      className="w-full sm:w-auto bg-[#33363F]"
                      type="button"
                    >
                      {t("buttons.cancel")}
                    </Button>
                  </Link>
                </div>
                <div>
                  <Button type="submit" className="bg-[#9FC089]">
                    {t("buttons.submit")}
                  </Button>
                </div>
              </div>
            </form>
          </Form>
        </div>
      </div>
    </MainLayout>
  );
}
