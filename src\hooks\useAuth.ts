import { views } from "@/lib/apiConfig";
import { supabase } from "../lib/client";
import type {
  LoginFormType,
  ResetPasswordReturn,
  SignOutResponse,
  ToastType,
  profileImageResponse,
} from "@/types";
import { useToast } from "@/components/ui/use-toast";
import { ERROR_MESSAGES } from "@/lib/messages";
import { useTranslation } from "react-i18next";
interface LoginReturn {
  data: {};
  error: string;
  session: {};
}

interface UseAuthReturn {
  signIn: (formdata: LoginFormType) => Promise<LoginReturn[]>;
  signOut: () => Promise<SignOutResponse>;
  getUserInfo: () => Promise<string>;
  sendResetLink: (userEmail: string) => Promise<string>;
  ResetPassword: (
    password: string,
    accessToken: string,
  ) => Promise<
    | {
        data: string;
        session: string;
        error: string;
      }
    | undefined
  >;
  getProfileImage: (id: string) => Promise<profileImageResponse[]>;
}

const useAuthorization = (): UseAuthReturn => {
  const { t } = useTranslation();
  const { toast } = useToast() as ToastType;
  async function signIn(formdata: LoginFormType): Promise<LoginReturn[]> {
    try {
      const {
        error,
        data: { user, session },
      } = await supabase.auth.signInWithPassword({
        email: formdata.email,
        password: formdata.password,
      });

      if (!error) {
        if (user && session) {
          return [{ data: user, session: session, error: "" }];
        } else {
          return [{ data: "", session: "", error: "User is null." }];
        }
      } else {
        return [{ data: "", session: "", error: error.message }];
      }
    } catch (error) {
      console.log("Error logging in:", error);
      return [{ data: "", session: "", error: "An error occurred." }];
    }
  }
  async function signOut(): Promise<SignOutResponse> {
    try {
      const { error } = await supabase.auth.signOut();

      if (error) {
        console.error("Error signing out:", error);
        return {
          status: false,
          message: "Error signing out: " + error.message,
        };
      } else {
        return { status: true, message: "Signed out successfully" };
      }
    } catch (error) {
      console.error("Error signing out:", error);
      return { status: false, message: "Error signing out: " + error };
    }
  }

  async function getUserInfo(): Promise<string> {
    try {
      const { data, error } = await supabase.auth.getUser();
      if (error) {
        if (error.status === 401) {
          localStorage.removeItem("access_token");

          toast({
            variant: ERROR_MESSAGES.toast_variant_destructive,
            title: t("errorMessages.toast_error_title"),
            description: t("errorMessages.sessionExpired"),
          });
        } else {
          console.error("Error getting user information:", error.message);
          return "Error getting user information.";
        }
      }
      const user = data?.user;
      if (user) {
        const userName: string = user.id;
        return userName;
      } else {
        return "";
      }
    } catch (error) {
      // Handle unexpected errors
      console.error("Unexpected error:", error);
      return "Unexpected error getting user information.";
    }
  }

  async function sendResetLink(userEmail: string): Promise<string> {
    const { error } = await supabase.auth.resetPasswordForEmail(userEmail, {
      redirectTo: "https://competitor-admin-portal.vercel.app/reset-password",
    });
    if (error) {
      console.error("Error sending reset password link", error);
      return "Error sending reset password link" + error.message;
    } else {
      return "Reset Password link sent successfully";
    }
  }

  async function ResetPassword(
    password: string,
    accessToken: string,
  ): Promise<
    | {
        data: string;
        session: string;
        error: string;
      }
    | undefined
  > {
    try {
      const response = await fetch(
        process.env.NEXT_PUBLIC_SUPABASE_URL + "/auth/v1/user",
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            apikey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY as string,
            Authorization: "Bearer " + accessToken,
          },
          body: JSON.stringify({
            password: password,
          }),
        },
      );
      const pwdStatus = (await response.json()) as ResetPasswordReturn;

      if (pwdStatus?.id != null) {
        return { data: pwdStatus.id, session: "", error: "" };
      } else if (pwdStatus?.code != null) {
        return {
          data: "",
          session: "",
          error: pwdStatus?.msg ?? "Unknown error",
        };
      } else {
        return {
          data: "",
          session: "",
          error: "Unexpected response structure.",
        };
      }
    } catch (error) {
      console.log("Error logging in:", error);
      return { data: "", session: "", error: "An error occurred." };
    }
  }

  async function getProfileImage(
    userId: string,
  ): Promise<profileImageResponse[]> {
    try {
      const profileImages = views?.profileImage ?? "";
      const exeQuery = supabase
        .from(profileImages)
        .select("avatar_url")
        .eq("id", userId);

      const { data, error } = await exeQuery;
      if (error) {
        throw new Error(error.message);
      }

      return data as profileImageResponse[];
      // }
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  return {
    signIn,
    signOut,
    getUserInfo,
    sendResetLink,
    ResetPassword,
    getProfileImage,
  };
};

export default useAuthorization;
