"use client";
import React, { useState, useEffect, type KeyboardEvent } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  // useFieldArray,
  useForm,
} from "react-hook-form";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { AddquestionFormSchema } from "@/schema/schema";
import type {
  AddQuestionSchemaType,
  AnswerType,
  ComboData,
  ErrorCatch,
  ErrorType,
  InnerItem,
  LogUserActivityRequest,
  QuestionBankData,
  ToastType,
  TopicDataType,
  richTextType,
} from "@/types";
import { Input } from "@/components/ui/input";
import { Combobox } from "@/components/ui/combobox";
import { Card, CardContent } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
// import { X } from "lucide-react";
import { questionType, contentType, AppConfig } from "../../lib/constants";
import useQuestionBank from "@/hooks/useQuestionBank";
import { useRouter } from "next/navigation";
import { useToast } from "@/components/ui/use-toast";
import { Editor } from "primereact/editor";
import { useSearchParams } from "next/navigation";
import { pageUrl } from "@/lib/constants";
import Link from "next/link";
// import { PlusCircle } from "lucide-react";
import { Modal } from "../ui/modal";
import { AddNewQuesionCategoryForm } from "@/app/questionbank/addNewQuestionCategory";
import useExamDetails from "@/hooks/useExamDetails";
import { Spinner } from "@/components/ui/progressiveLoader";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
// import MathFormulaEditor from "../formulaEditor";
// import { MathComponent } from "mathjax-react";
import { MathMLToLaTeX } from "mathml-to-latex";
import dynamic from "next/dynamic";
import NextBreadcrumb from "../breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumb";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";

function AddQuestions(): React.JSX.Element {
  const MathFormulaEditor = dynamic(() => import("../formulaEditor"), {
    ssr: false,
  });
  const MathComponent = dynamic(
    () => import("mathjax-react").then((mod) => mod.MathComponent),
    {
      ssr: false,
    },
  );
  const { toast } = useToast() as ToastType;
  const { t } = useTranslation();
  const searchParams = useSearchParams();
  const examId = searchParams.get("type") ?? null;
  const noOfChoices = AppConfig.noOfChoices; //searchParams.get("noOfChoices") ?? 0;
  // const [choiceNumber, setChoiceNumber] = useState<number>(0);
  const [comboData, setComboData] = useState<ComboData[]>([]);
  const [categoryId, setCategoryId] = React.useState("");
  const { getQuestions } = useExamDetails();
  const {
    addQuestions,
    editQuestions,
    getQuestionBankList,
    getPublishedQuestionCategory,
  } = useQuestionBank();
  const [categoryAddStatus, setCategoryAddStatus] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [richTextValues, setRichTextValues] = React.useState<
    richTextType | undefined
  >(undefined);
  const [questionBankList, setQuestionBankList] = React.useState<
    QuestionBankData | undefined
  >();
  const [isDisabled, setIsDisabled] = useState(false);
  const [isOpen, setIsOpen] = React.useState<boolean>(false);
  const [formulaValue, setFormulaValue] = useState<string>("");
  const [defaultEquation, setDefaultEquation] = useState<string>("");
  // const [initialComponentsAdded, setInitialComponentsAdded] =
  //   useState<boolean>(false);
  // const [expandMoreValue, setExpandMoreValue] = useState('');
  // const handleExpandMore = (value : string): void => {
  //   setExpandMoreValue(value);
  // };
  // console.log(expandMoreValue);
  const form = useForm<AddQuestionSchemaType>({
    resolver: zodResolver(AddquestionFormSchema),
  });
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);
  const { register } = form;
  // const { fields, append, remove } = useFieldArray({
  //   control,
  //   name: "answers",
  // });
  const router = useRouter();
  const questId = searchParams.get("questionId") ?? null;
  const title = questId != null ? "Edit Question" : "Add Question";
  const { updateUserActivity } = useLogUserActivity();

  useEffect(() => {
    setBreadcrumbItems(
      getBreadCrumbItems(t, t("breadcrumb.addQuestion"), { "": "" }),
    );
  }, [t]);

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  async function onSubmit(): Promise<void> {
    const formData = form.getValues();
    const org_id = localStorage.getItem("orgId");
    if (questId == null) {
      const exam_id = examId;
      if (richTextValues?.htmlValue !== undefined) {
        formData.question = richTextValues?.htmlValue;
      }
      const getIsAnswer = (formData.answers ?? []).every(
        (answer) => !answer.isAnswer,
      );

      if (getIsAnswer) {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: t("errorMessages.select_atleast_one_ans"),
        });
        return;
      } else {
        const transformedData = {
          answer_datas: (formData.answers ?? []).map((answer, index) => ({
            slot: index + 1,
            answer: answer.text,
            fraction: answer.fraction,
            answer_type: answer.type ?? "PLAIN_TEXT",
          })),
          org_id: org_id ?? "",
          ques_category_id: categoryId,
          question_data: {
            name: formData.question, // actual value formData.question
            question_text: formData.question, // actual value formData.question
            default_mark: formData.markassigned ?? 0,
            penalty: formData.penality ?? 0,
            question_type: formData.contenttype ?? "PLAIN_TEXT",
          },
          quiz_id: exam_id ?? null,
        };
        try {
          const questionList = await getQuestionBankList(categoryId);
          const questionExists = questionList.some(
            (question) => question.question_text === formData.question,
          );
          if (questionExists) {
            toast({
              variant: ERROR_MESSAGES.toast_variant_destructive,
              title: t("errorMessages.toast_error_title"),
              description: t("errorMessages.question_already_exists"),
            });
            return;
          }
        } catch (error) {
          const err = error as ErrorCatch;
          toast({
            variant: ERROR_MESSAGES.toast_variant_destructive,
            title: t("errorMessages.toast_error_title"),
            description: err?.message,
          });
          console.error("Error fetching question list:", error);
          return;
        }

        try {
          const result = await addQuestions(transformedData);

          if (result.status === "success") {
            toast({
              variant: SUCCESS_MESSAGES.toast_variant_default,
              title: t("successMessages.toast_success_title"),
              description: t("successMessages.add_question"),
            });
            const params = {
              activity_type: "Question_Bank",
              screen_name: "Question_Bank",
              action_details: "Question added successfully",
              target_id: result.question_id as string,
              log_result: "SUCCESS",
            };
            void updateLogUserActivity(params).catch((error) => {
              console.error(error);
            });
            if (exam_id !== null) {
              router.push(`${pageUrl.examDetails}?type=${exam_id}`);
            } else {
              router.push(`${pageUrl.questionBank}`);
            }
          } else {
            const params = {
              activity_type: "Question_Bank",
              screen_name: "Question_Bank",
              action_details: "Failed to add question",
              target_id: result.question_id as string,
              log_result: "ERROR",
            };
            void updateLogUserActivity(params).catch((error) => {
              console.error(error);
            });
          }
        } catch (error) {
          const err = error as ErrorCatch;
          toast({
            variant: ERROR_MESSAGES.toast_variant_destructive,
            title: t("errorMessages.toast_error_title"),
            description: err?.message,
          });
          console.error("An unexpected error occurred:", error);
        }
      }
    } else {
      const formData = form.getValues();
      const exam_id = examId;
      if (richTextValues?.htmlValue !== undefined) {
        formData.question = richTextValues?.htmlValue;
      }
      const getIsAnswer = (formData.answers ?? []).every(
        (answer) => !answer.isAnswer,
      );
      if (getIsAnswer) {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: t("errorMessages.select_atleast_one_ans"),
        });
        return;
      } else {
        const transformedData = {
          answer_data: (formData.answers ?? []).map((answer) => ({
            slot: answer.slot,
            answer: answer.text,
            fraction: answer.fraction,
            answer_type: answer.type ?? "PLAIN_TEXT",
            answer_id: answer.id,
          })),
          org_id: org_id ?? "",
          question_id: questId,
          question_data: {
            name: formData.question,
            question_text: formData.question,
            default_mark: formData.markassigned ?? 0,
            penalty: formData.penality ?? 0,
            question_type: formData.contenttype ?? "PLAIN_TEXT",
            question_category_id: categoryId,
          },
          // quiz_id: exam_id ?? null,
        };

        try {
          const result = await editQuestions(transformedData);

          if (result.status === "success") {
            toast({
              variant: SUCCESS_MESSAGES.toast_variant_default,
              title: t("successMessages.toast_success_title"),
              description: t("successMessages.edit_question"),
            });
            const params = {
              activity_type: "Question_Bank",
              screen_name: "Question_Bank",
              action_details: "Question updated successfully",
              target_id: questId as string,
              log_result: "SUCCESS",
            };
            void updateLogUserActivity(params).catch((error) => {
              console.error(error);
            });
            if (exam_id !== null) {
              router.push(`${pageUrl.examDetails}?type=${exam_id}`);
            } else {
              router.push(`${pageUrl.questionBank}`);
            }
          } else if (result.status === "error") {
            toast({
              variant: ERROR_MESSAGES.toast_variant_destructive,
              title: t("errorMessages.toast_error_title"),
              description: result.status,
            });
            const params = {
              activity_type: "Question_Bank",
              screen_name: "Question_Bank",
              action_details: "Failed to update question",
              target_id: questId as string,
              log_result: "ERROR",
            };
            void updateLogUserActivity(params).catch((error) => {
              console.error(error);
            });
          }
        } catch (error) {
          const err = error as ErrorCatch;
          toast({
            variant: ERROR_MESSAGES.toast_variant_destructive,
            title: t("errorMessages.toast_error_title"),
            description: err?.message,
          });
          console.error("An unexpected error occurred:", error);
        }
      }
    }
  }

  // const handleAddChoices = (): void => {
  //   if (!initialComponentsAdded) {
  //     append({ text: "", type: "PLAIN_TEXT", isAnswer: false, fraction: 0 });
  //     append({ text: "", type: "PLAIN_TEXT", isAnswer: false, fraction: 0 });
  //     setInitialComponentsAdded(true);
  //   } else {
  //     append({ text: "", type: "PLAIN_TEXT", isAnswer: false, fraction: 0 });
  //   }

  //   const choiceCount = choiceNumber + 1;

  //   setChoiceNumber(choiceCount);
  // };

  // const handleRemoveChoice = (index: number): void => {
  //   if (fields.length > 2) {
  //     remove(index);
  //     setChoiceNumber(choiceNumber - 1);
  //   }
  // };

  useEffect(() => {
    questionCategoryList();
    markAssigned();
  }, [categoryAddStatus]);

  useEffect(() => {
    if (questId != null) {
      const fetchData = async (): Promise<void> => {
        try {
          const qcatId = categoryId;
          const data = await getQuestionBankList(
            qcatId !== "" ? qcatId : undefined,
          );
          setIsLoading(false);
          const filteredData = data.filter(
            (item) => item.question_id === questId,
          );
          if (data?.length > 0) {
            filteredData.map((item) => {
              if (item.question_text != null) {
                item.question_text = item.question_text.replace(
                  /<pre\b[^>]*>(.*?)<\/pre>/s,
                  "<p>$1</p>",
                );
              }
            });
            setQuestionBankList(filteredData[0] as QuestionBankData);
            setDefaultEquation(
              MathMLToLaTeX.convert(filteredData[0].question_text as string),
            );
            if (
              MathMLToLaTeX.convert(filteredData[0].question_text as string)
                .length > 0
            ) {
              setFormulaValue(filteredData[0].question_text as string);
            }

            setCategoryId(filteredData[0].question_category_id as string);
            form.setValue("markassigned", filteredData[0].default_mark);
            form.setValue("penality", filteredData[0].penalty);
            form.setValue("question", filteredData[0].question_text as string);
            form.setValue("category", filteredData[0].question_category_id);
            const answerArray: AnswerType[] = [];
            filteredData[0]?.answers?.sort((a, b) => a.slot - b.slot);
            filteredData[0]?.answers?.map((item) => {
              answerArray.push({
                type: item.answer_type,
                isAnswer: item.fraction > 0 ? true : false,
                fraction: item.fraction,
                text: item.answer,
                id: item.id,
                slot: item.slot,
              });
            });
            form.setValue("answers", answerArray);
          }
        } catch (error) {
          const err = error as ErrorType;
          toast({
            variant: ERROR_MESSAGES.toast_variant_destructive,
            title: t("errorMessages.toast_error_title"),
            description: err?.details,
          });
        }
      };

      fetchData().catch((error) => console.log(error));
    } else {
      setIsLoading(false);
    }
  }, []);
  function saveModal(mathmlInput: string, latexValue: string): void {
    console.log(mathmlInput);
    setDefaultEquation(latexValue);
    // MathML content
    const mathMLContent = `<math xmlns="http://www.w3.org/1998/Math/MathML">${mathmlInput}</math>`;
    setFormulaValue(mathMLContent);
    form.setValue("question", mathMLContent);
  }

  function cancelModal(): void {
    setIsOpen(false);
  }

  const handleAddCategory = (): void => {
    setCategoryAddStatus(!categoryAddStatus);
  };
  const questionCategoryList = (): void => {
    const fetchData = async (): Promise<void> => {
      try {
        const category: TopicDataType[] = await getPublishedQuestionCategory();
        const comboData: ComboData[] = category.map((cat) => ({
          value: cat.value,
          label: cat.label,
        }));

        setComboData(comboData);
      } catch (error) {
        console.error("Error fetching data:");
      }
    };
    fetchData().catch((error) => console.log(error));
  };
  const markAssigned = (): void => {
    const fetchData = async (): Promise<void> => {
      try {
        const questionsData = await getQuestions(examId);
        questionsData[0].is_equal_weightage
          ? form.setValue("markassigned", questionsData[0].eq_weightage_marks)
          : "";
        questionsData[0].is_equal_weightage ? setIsDisabled(true) : "";
      } catch (error) {
        console.error("Error fetching data:");
      }
    };
    fetchData().catch((error) => console.log(error));
  };
  const handleComboValueChange = (selectedValue: string): void => {
    setCategoryId(selectedValue);
    form.setValue("category", selectedValue);
  };

  const updateFractions = (): void => {
    const formData = form.getValues();
    const updatedAnswers = formData.answers?.map((answer) => {
      if (answer.isAnswer) {
        const totalIsAnswerTrue = formData.answers?.filter(
          (ans) => ans.isAnswer,
        ).length;
        const fraction =
          totalIsAnswerTrue !== undefined && totalIsAnswerTrue > 0
            ? 1 / totalIsAnswerTrue
            : 0;
        return {
          ...answer,
          fraction,
        };
      } else {
        return {
          ...answer,
          fraction: 0,
        };
      }
    });
    // setChoiceNumber;
    form.setValue("answers", updatedAnswers);
  };
  const setRichTextValue = (richTextValue: richTextType | undefined): void => {
    if (richTextValue && richTextValue.htmlValue == null) {
      richTextValue = undefined;
    }
    form.setValue("question", richTextValue?.htmlValue ?? "");

    setRichTextValues(richTextValue);
  };

  const handleKeyDown = (event: KeyboardEvent<HTMLInputElement>): void => {
    const forbiddenKeys = ["e", "E", "-", "+"];
    if (forbiddenKeys.includes(event.key)) {
      event.preventDefault();
    }
  };

  const handlePenaltyKeyPress = (
    event: KeyboardEvent<HTMLInputElement>,
  ): void => {
    //const forbiddenKeys = ["e", "E", "-", "+"];
    //if (forbiddenKeys.includes(event.key)) {
    event.preventDefault();
    //}
  };
  const openModal = (): void => {
    setIsOpen(true);
  };

  return (
    <div>
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      <div>
        <h1 className="text-2xl font-semibold tracking-tight">{title}</h1>
      </div>
      {isLoading ? (
        <Spinner />
      ) : (
        <div className="border rounded-md p-4 mt-4 bg-[#fff]">
          <Form {...form}>
            <form
              onSubmit={(event) => void form.handleSubmit(onSubmit)(event)}
              className="space-y-4"
            >
              <div className="w-full flex flex-wrap">
                <div className="w-full sm:w-1/3 pr-4">
                  <FormField
                    control={form.control}
                    name="category"
                    render={() => (
                      <FormItem>
                        <FormLabel>
                          {String(t("questionBank.selectQuestionCategory"))}{" "}
                          <span className="text-red-700">*</span>
                        </FormLabel>
                        <FormControl>
                          <div className="overflow-x-auto">
                            <Combobox
                              data={comboData}
                              onSelectChange={handleComboValueChange}
                              defaultLabel={
                                questionBankList?.question_category_name
                              }
                            />
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                {/* <div
                  className="mt-8 mr-4 text-[#fb8500]"
                  title="Add Question Category"
                >
                  <PlusCircle onClick={handleAddCategory} />
                </div> */}
                <div className="w-full sm:w-1/3 pr-4">
                  <FormField
                    control={form.control}
                    name="questiontype"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {String(t("questionBank.typeOfQuestions"))}{" "}
                          <span className="text-red-700">*</span>
                        </FormLabel>
                        <FormControl>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={questionType[0].label}
                            disabled={true}
                          >
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select" />
                            </SelectTrigger>
                            <SelectContent>
                              {questionType.map((type) => (
                                <SelectItem key={type.value} value={type.value}>
                                  {type.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="w-full sm:w-1/3 pr-4">
                  <FormField
                    control={form.control}
                    name="contenttype"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {String(t("questionBank.type"))}{" "}
                          <span className="text-red-700">*</span>
                        </FormLabel>
                        <FormControl>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={
                              questionBankList?.question_type ?? "PLAIN_TEXT"
                            }
                          >
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select" />
                            </SelectTrigger>
                            <SelectContent>
                              {contentType.map((type) => (
                                <SelectItem key={type.value} value={type.value}>
                                  {type.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
              <Button
                type="button"
                className="mb-2 float-right bg-[#fb8500]"
                onClick={openModal}
              >
                {formulaValue !== undefined && formulaValue !== ""
                  ? String(t("buttons.editEquations"))
                  : String(t("buttons.addEquation"))}
              </Button>
              <div className="w-full flex">
                <div className="w-full pr-4">
                  <FormField
                    name="question"
                    render={() => (
                      <FormItem>
                        <FormLabel>
                          {defaultEquation.length > 0 ? (
                            <>
                              {String(t("questionBank.question"))}{" "}
                              <span className="text-red-700">*</span>
                            </>
                          ) : (
                            <>
                              {String(t("buttons.enterTheQuestion"))}
                              <span className="text-red-700">*</span>
                            </>
                          )}
                        </FormLabel>
                        {formulaValue === "" && (
                          <Editor
                            value={questionBankList?.question_text}
                            maxLength={400}
                            onTextChange={(event) => {
                              const htmlValue = event.htmlValue;
                              const richTextValue = {
                                htmlValue: htmlValue,
                              };
                              setRichTextValue(richTextValue as richTextType);
                            }}
                            style={{ height: "200px" }}
                          />
                        )}
                        {formulaValue !== "" && (
                          <div
                            style={{
                              border: "1px solid #000",
                              padding: "10px",
                              borderRadius: "8px",
                              marginTop: "20px",
                              width: "100%", // Adjust the width as needed
                              height: "200px", // Set the height to half of the original value if the original was 100px
                            }}
                          >
                            <MathComponent
                              mathml={formulaValue}
                              display={true}
                            />
                          </div>
                        )}
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <div className="w-full flex">
                <div className="w-full sm:w-1/3 pr-4">
                  <FormField
                    name="markassigned"
                    render={(field) => (
                      <FormItem>
                        <FormLabel>
                          {String(t("questionBank.markAssigned"))}{" "}
                          <span className="text-red-700">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            id="markassigned"
                            type="number"
                            disabled={isDisabled}
                            min={0}
                            onKeyDown={handleKeyDown}
                            placeholder={String(t("questionBank.markAssigned"))}
                            autoComplete="off"
                            {...register("markassigned")}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="w-full sm:w-1/4 pr-4">
                  <FormField
                    name="penality"
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {String(t("questionBank.penaltyApplicable"))}
                        </FormLabel>
                        <FormControl>
                          <Input
                            id="penality"
                            type="number"
                            min={0.0}
                            max={1.0}
                            step=".05"
                            onKeyDown={handlePenaltyKeyPress}
                            autoComplete="off"
                            placeholder={String(
                              t("questionBank.penaltyApplicable"),
                            )}
                            defaultValue={0.0}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* <div className="w-full sm:w-1/3 pr-4">
              <Button type="button" onClick={handleAddChoices}>
                Add Choice
              </Button>
            </div> */}

              {noOfChoices !== 0 && (
                <div className="border  p-4 mt-4 bg-[#fff]">
                  <div className="w-full flex ml-2 mt-2 mb-2 flex-wrap ">
                    {Array.from({ length: noOfChoices }, (_, index) => (
                      <div key={index} className="w-full sm:w-1/2 pr-4 mb-4">
                        <Card className="mt-2 rounded-none shadow-none">
                          {/* <div className="flex justify-end p-2">
                          <div onClick={() => handleRemoveChoice(index)}>
                            <X />
                          </div>
                        </div> */}
                          <CardContent className="flex flex-row bg-[#FFF] pt-4 ">
                            <div className="w-1/2 pr-4 rounded-none">
                              <FormField
                                name={`answers.${index}.text`}
                                render={() => (
                                  <FormItem>
                                    <FormLabel className="mr-2">
                                      {String(t("questionBank.choice"))}{" "}
                                      {index + 1}{" "}
                                      <span className="text-red-700">*</span>
                                    </FormLabel>
                                    <FormControl>
                                      <Input
                                        autoComplete="off"
                                        {...register(`answers.${index}.text`)}
                                        defaultValue={
                                          questionBankList?.answers?.[index]
                                            .answer
                                        }
                                        onChange={(e) => {
                                          const value = e.target.value;
                                          const sanitizedValue = value
                                            .trimStart()
                                            .replace(/\s{2,}/g, " ")
                                            .replace(/\s+$/, (match) =>
                                              match.length > 1
                                                ? match.slice(0, -1)
                                                : match,
                                            );
                                          e.target.value = sanitizedValue;
                                        }}
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>

                            <div className="w-1/2 pr-4 flex-grow">
                              <FormField
                                control={form.control}
                                name={`answers.${index}.type`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel className="mr-2">
                                      {String(t("questionBank.type"))}{" "}
                                      <span className="text-red-700">*</span>
                                    </FormLabel>
                                    <FormControl>
                                      <Select
                                        onValueChange={field.onChange}
                                        defaultValue={
                                          (field.value as string) ??
                                          "PLAIN_TEXT"
                                        }
                                      >
                                        <SelectTrigger className="w-full">
                                          <SelectValue placeholder="Select" />
                                        </SelectTrigger>
                                        <SelectContent>
                                          {contentType.map((type) => (
                                            <SelectItem
                                              key={type.value}
                                              value={type.value}
                                            >
                                              {type.label}
                                            </SelectItem>
                                          ))}
                                        </SelectContent>
                                      </Select>
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>
                          </CardContent>
                          <CardContent className="flex flex-row bg-[#FFF]">
                            <div className="w-1/2 pr-4 flex-grow">
                              <FormField
                                control={form.control}
                                name={`answers.${index}.isAnswer`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormControl>
                                      <Checkbox
                                        checked={field.value}
                                        onCheckedChange={(value) => {
                                          field.onChange(value);
                                          updateFractions();
                                        }}
                                      />
                                    </FormControl>
                                    <FormLabel className="ml-2">
                                      {String(t("questionBank.markAsAnswer"))}
                                    </FormLabel>
                                  </FormItem>
                                )}
                              />
                            </div>
                            <div className="w-1/2 pr-4  flex-grow">
                              <FormField
                                control={form.control}
                                name={`answers.${index}.fraction`}
                                render={() => (
                                  <FormItem>
                                    <FormLabel>
                                      {String(t("questionBank.fraction"))}
                                    </FormLabel>
                                    <FormControl>
                                      <Input
                                        autoComplete="off"
                                        {...register(
                                          `answers.${index}.fraction`,
                                        )}
                                        defaultValue={0}
                                        disabled={true}
                                      />
                                    </FormControl>
                                  </FormItem>
                                )}
                              />
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                    ))}
                  </div>
                  <div className="flex items-center justify-end gap-3">
                    <Link
                      href={
                        examId != null ? pageUrl.exams : pageUrl.questionBank
                      }
                    >
                      <Button type="button" className="bg-[#33363F]">
                        {String(t("buttons.cancel"))}
                      </Button>
                    </Link>
                    <Button className="bg-[#9FC089]" type="submit">
                      {String(t("buttons.submit"))}
                    </Button>
                  </div>
                </div>
              )}
            </form>
          </Form>
          {categoryAddStatus && (
            <Modal
              title={String(t("questionBank.addQuestionCategory"))}
              header=""
              openDialog={categoryAddStatus}
              closeDialog={handleAddCategory}
            >
              <AddNewQuesionCategoryForm
                closeDialog={() => handleAddCategory()}
                isDialogOpen={() => categoryAddStatus}
                categoryList={() => {}}
                data={{ value: "", label: "", description: "" }}
              />
            </Modal>
          )}
          {isOpen && (
            <Modal
              title={String(t("questionBank.addEquation"))}
              header=""
              openDialog={isOpen}
              closeDialog={openModal}
              type="max-w-5xl"
            >
              <MathFormulaEditor
                onSave={saveModal}
                onCancel={cancelModal}
                isModal={true}
                data={defaultEquation}
              />
            </Modal>
          )}
        </div>
      )}
    </div>
  );
}

export default AddQuestions;
