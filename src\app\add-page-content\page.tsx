"use client";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import MainLayout from "../layout/mainlayout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { type FieldValues, useForm } from "react-hook-form";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import React from "react";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// ZOD and generic validation TO DO

interface FormData {
  course: string;
  page: string;
  pageTitle: string;
  type: string;
  pageContent: string;
}
export default function AddPageContent(): React.JSX.Element {
  const form = useForm<FormData>();

  function onSubmit(data: FieldValues): void {
    console.log(data);
  }

  return (
    <MainLayout>
      <h1 className="text-2xl font-semibold tracking-tight">Page Content</h1>
      <div className="border rounded-md p-4 mt-4">
        <Form {...form}>
          <form
            onSubmit={() => form.handleSubmit(onSubmit)}
            className="space-y-8"
          >
            <div className="mt-10 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
              <div className="sm:col-span-3">
                <label>Select Course</label>
                <div className="mt-2">
                  <FormField
                    control={form.control}
                    name="course"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel></FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="Select Course"
                            autoComplete="off"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
              <div className="sm:col-span-3">
                <label>Select Page</label>
                <div className="mt-2">
                  <FormField
                    control={form.control}
                    name="page"
                    render={() => (
                      <FormItem>
                        <FormLabel></FormLabel>
                        <FormControl>
                          <Select>
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select Page" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectGroup>
                                <SelectItem value="1">page1</SelectItem>
                                <SelectItem value="2">page2</SelectItem>
                              </SelectGroup>
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </div>
            <div className="sm:col-span-4">
              <label>Page Title</label>
              <div className="mt-2">
                <FormField
                  control={form.control}
                  name="pageTitle"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel></FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="Page Title"
                          autoComplete="off"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <div className="sm:col-span-4">
              <label>Milestone Present?</label>
              <div className="mt-2">
                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem className="space-y-3">
                      <FormLabel></FormLabel>
                      <FormControl>
                        <RadioGroup
                          onValueChange={(value) => {
                            field.onChange(value);
                          }}
                          defaultValue={field.value}
                          className="flex flex-row space-x-3"
                        >
                          <FormItem className="flex items-center space-x-3 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="true" />
                            </FormControl>
                            <FormLabel className="font-normal">Yes</FormLabel>
                          </FormItem>
                          <FormItem className="flex items-center space-x-3 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="false" />
                            </FormControl>
                            <FormLabel className="font-normal">No</FormLabel>
                          </FormItem>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* {showMilestone && (
              <Milestone courseModuleId={null} checkpointNumber={0} />
            )} */}

            <div className="sm:col-span-4">
              <label>Page Content</label>
              <div className="mt-2">
                <FormField
                  control={form.control}
                  name="pageContent"
                  render={() => (
                    <FormItem>
                      <FormLabel></FormLabel>
                      <FormControl></FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
            <div className="flex justify-end mt-4">
              <div className="px-2">
                <Button variant="secondary">Cancel</Button>
              </div>
              <div className="px-2">
                <Button variant="outline">Reset</Button>
              </div>
              <div className="px-2">
                <Button>Submit</Button>
              </div>
            </div>
          </form>
        </Form>
      </div>
    </MainLayout>
  );
}
