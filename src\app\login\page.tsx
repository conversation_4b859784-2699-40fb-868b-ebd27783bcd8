"use client";

import React, { useEffect, useState } from "react";
import "../../styles/auth.css";
import AuthLayout from "../layout/authlayout";

import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

import { LoginFormSchema } from "@/schema/schema";
import type {
  ErrorCatch,
  LoginFormType,
  ToastType,
  LoginUserData,
  SessionData,
  ForgetPasswordForm,
  ComboData,
  LogUserActivityRequest,
} from "@/types";
import { Checkbox } from "@/components/ui/checkbox";
// import Link from "next/link";
import useAuthorization from "@/hooks/useAuth";
import useOrganization from "@/hooks/useOrganization";
import { useRouter } from "next/navigation";
import { useToast } from "@/components/ui/use-toast";
import { Modal } from "@/components/ui/modal";
import ForgetPassword from "./forgetPassword";
import { DEFAULT_FOLDER_ID, ORG_KEY, ORG_NAME } from "@/lib/constants";
import { SelectOrganization } from "./selectOrganization";
import useResourceLibrary from "@/hooks/useResourceLibrary";
import useLogUserActivity from "@/hooks/useLogUserActivity";

export default function LoginAccount(): React.JSX.Element {
  const [resetPasswordFlag, setResetPasswordFlag] = useState<boolean>(false);
  const [passData, setpassData] = useState<ForgetPasswordForm>();
  const [title, setTitle] = useState("");
  const [isOrganizationOpen, setisOrganizationOpen] = useState<boolean>(false);
  const router = useRouter();
  const form = useForm<LoginFormType>({
    resolver: zodResolver(LoginFormSchema),
  });
  const { toast } = useToast() as ToastType;
  const { signIn, getProfileImage } = useAuthorization();
  const { getOrganizationList } = useOrganization();
  const [comboData, setComboData] = useState<ComboData[]>([]);
  const { getModuleList } = useResourceLibrary();
  const { updateUserActivity } = useLogUserActivity();

  useEffect(() => {
    localStorage.removeItem("orgId");
    localStorage.removeItem("orgName");
    localStorage.removeItem("userDetails");
    localStorage.removeItem("role_privileges");
    localStorage.removeItem("orgName");
    localStorage.removeItem("orgId");
    localStorage.removeItem("courseId");
    localStorage.removeItem("profile_image");
    localStorage.removeItem("ExamDetails");
    localStorage.removeItem("access_token");
    localStorage.removeItem("checkpointNo");
    localStorage.removeItem("checkpointNoPPT");
  }, []);

  const addReSetMailModal = (): void => {
    setResetPasswordFlag(!resetPasswordFlag);
    setpassData({ email: "" });
    setTitle("Enter your email address");
  };
  const organizationDialog = (): void => {
    setisOrganizationOpen(!isOrganizationOpen);
  };

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  async function onSubmit(data: LoginFormType): Promise<void> {
    try {
      const result = await signIn(data);
      let userId = "";
      if (result.length > 0 && result[0].error !== "") {
        toast({
          variant: "destructive",
          title: "Error",
          description: result[0].error,
        });
        const params = {
          activity_type: "Authentication",
          screen_name: "Login",
          action_details: result[0].error as string,
          target_id: DEFAULT_FOLDER_ID,
          log_result: "ERROR",
          org_id: DEFAULT_FOLDER_ID,
          user_id: DEFAULT_FOLDER_ID,
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      } else if (result.length > 0 && "id" in result[0].data) {
        const userData = result[0].data as LoginUserData;
        const apiData = result[0].session as SessionData;
        const access_token = apiData.access_token;
        localStorage.setItem("access_token", access_token);
        userId = userData.id;
        const session_data = {
          id: userData.id,
          last_sign_in_at: userData.last_sign_in_at,
          user_metadata: userData.user_metadata,
          email: userData?.email,
        };
        toast({
          variant: "success",
          title: "Login Success",
          description: "Successfully logged in",
        });

        localStorage.setItem("userDetails", JSON.stringify(session_data));
        const orgIdSelected = localStorage.getItem(ORG_KEY);
        void getUserImage(userData.id);
        getModuleLists();

        if (
          orgIdSelected === null ||
          orgIdSelected === undefined ||
          orgIdSelected === ""
        ) {
          const orgList = await getOrganizationList();

          if (orgList.length > 1) {
            organizationDialog();
            const comboData: ComboData[] = orgList.map((org) => ({
              value: org.org_id,
              label: org.org_name,
            }));
            setComboData(comboData);
          } else {
            const orgId = orgList[0].org_id as string;
            const orgName = orgList[0].org_name as string;
            localStorage.setItem(ORG_KEY, orgId);
            localStorage.setItem(ORG_NAME, orgName);
            router.push("/dashboard");
          }
          const params = {
            activity_type: "Authentication",
            screen_name: "Login",
            action_details: "User Succesfully Logged In",
            target_id: userId,
            log_result: "SUCCESS",
          };
          void updateLogUserActivity(params).catch((error) => {
            console.error(error);
          });
          //  router.push("/organization");
        } else {
          router.push("/dashboard");
        }
      } else {
        const params = {
          activity_type: "Authentication",
          screen_name: "Login",
          action_details: "User Failed to Logged in",
          target_id: DEFAULT_FOLDER_ID,
          log_result: "ERROR",
          org_id: DEFAULT_FOLDER_ID,
          user_id: DEFAULT_FOLDER_ID,
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: "destructive",
        title: "Error",
        description: err?.message,
      });
    }
  }
  async function getUserImage(data: string): Promise<void> {
    try {
      const image = await getProfileImage(data);
      localStorage.setItem("profile_image", JSON.stringify(image));
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: "destructive",
        title: "Error",
        description: err?.message,
      });
    }
  }
  const getModuleLists = (): void => {
    const fetchData = async (): Promise<void> => {
      try {
        const modules = await getModuleList();
        localStorage.setItem("moduleList", JSON.stringify(modules));
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: "destructive",
          title: "Error",
          description: err?.message,
        });
        console.error("Error fetching data:");
      }
    };
    fetchData().catch((error) => console.log(error));
  };
  return (
    <AuthLayout>
      <div className="lg:p-8">
        <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px] border border-gray-300  bg-[white]  p-6">
          <div className="flex flex-col space-y-2 text-center">
            <h1 className="text-2xl font-semibold tracking-tight">Sign in</h1>
            <p className="text-sm text-muted-foreground">
              Enter your email and password to login
            </p>
          </div>

          {resetPasswordFlag && (
            <Modal
              title={title}
              header=""
              openDialog={resetPasswordFlag}
              closeDialog={addReSetMailModal}
            >
              <ForgetPassword
                onSave={addReSetMailModal}
                onCancel={addReSetMailModal}
                isModal={true}
                data={passData as ForgetPasswordForm}
              />
            </Modal>
          )}

          <Form {...form}>
            <form
              onSubmit={(event) => void form.handleSubmit(onSubmit)(event)}
              className="grid gap-4"
            >
              <FormField
                name="email"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input
                        id="email"
                        type="email"
                        className="w-full h-9 rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
                        placeholder="<EMAIL>"
                        autoCapitalize="none"
                        autoComplete="email"
                        autoCorrect="off"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                name="password"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Password</FormLabel>
                    <FormControl>
                      <Input
                        id="password"
                        type="password"
                        className="w-full h-9 rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
                        placeholder="Password"
                        autoCapitalize="none"
                        autoComplete="password"
                        autoCorrect="off"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="flex items-center space-x-2">
                <Checkbox id="terms" />
                <label
                  htmlFor="terms"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Remember me
                </label>
              </div>
              <div className="flex justify-center items-center">
                <Button
                  type="submit"
                  className="w-1/4 bg-[#9FC089] text-primary-foreground rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 p-2"
                >
                  Submit
                </Button>
              </div>
            </form>
            <div className="px-8 text-center text-sm text-muted-foreground">
              {/* <p>
                Forgot password?
                <Link
                  href="#"
                  onClick={addReSetMailModal}
                  className="ml-1 underline underline-offset-4 hover:text-primary"
                >
                  Reset
                </Link>
              </p> */}
              {/* as suggested by Rasakka, we are hding the Signup link for now. */}
              {/*<p>
                {"Don't have an account?"}
                <Link
                  href="/signup"
                  className="ml-1 underline underline-offset-4 hover:text-primary"
                >
                  Sign up
                </Link>
                </p>*/}
            </div>
          </Form>
        </div>
        {isOrganizationOpen && (
          <Modal
            title="Select Organization"
            header=""
            openDialog={isOrganizationOpen}
            closeDialog={organizationDialog}
          >
            <SelectOrganization
              orgList={comboData as ComboData[]}
              closeDialog={organizationDialog}
            />
          </Modal>
        )}
      </div>
    </AuthLayout>
  );
}
