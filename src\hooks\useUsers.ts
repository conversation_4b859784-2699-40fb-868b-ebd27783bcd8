import { supabase } from "../lib/client";
import { views, rpc, functions } from "../lib/apiConfig";
import type {
  RoleType,
  UsersDataType,
  UserUpdate,
  UserUpdateResult,
  ErrorType,
  AddUserForm,
  AddUserResponse,
  UserDeactivate,
  UserDeactivateResult,
  fetchTokenRequest,
  EnrolledCourseResponse,
  ReportersResponse,
  InviteUserRequest,
  InviteUserResponse,
} from "@/types";
import { ACCESS_TOKEN } from "@/lib/constants";

interface UseUserseReturn {
  getRoles: (orgid: string) => Promise<RoleType[]>;
  getUsers: (org_id: string) => Promise<UsersDataType[]>;
  signUpUser: (params: AddUserForm) => Promise<AddUserResponse>;
  updateUser: (params: UserUpdate) => Promise<UserUpdateResult>;
  deactivateUsers: (params: UserDeactivate) => Promise<UserDeactivateResult>;
  getEnrolledCourses: (
    params: fetchTokenRequest,
  ) => Promise<EnrolledCourseResponse[]>;
  getReportingToResource: (
    params: fetchTokenRequest,
  ) => Promise<ReportersResponse[]>;
  inviteNewUser: (params: InviteUserRequest) => Promise<InviteUserResponse>;
}

const useUsers = (): UseUserseReturn => {
  async function getRoles(orgId: string): Promise<RoleType[]> {
    try {
      const requestBody = {
        org_id: orgId,
      };
      const { data, error } = await supabase.rpc<string, null>(
        rpc.getRolesOfOrg,
        requestBody,
      );
      if (error) {
        throw new Error(error.details);
      }
      return data as RoleType[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function getUsers(org_id: string): Promise<UsersDataType[]> {
    try {
      const usersView = views?.users ?? "";
      const exeQuery = supabase
        .from(usersView)
        .select()
        .eq("org_id", org_id)
        .order("first_name", { ascending: true });
      const { data, error } = await exeQuery;
      if (error) {
        throw new Error(error.details);
      }
      return data as UsersDataType[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function signUpUser(params: AddUserForm): Promise<AddUserResponse> {
    try {
      const response = await fetch(rpc.signUp, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          apikey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY as string,
          Authorization: "Bearer" + localStorage.getItem(ACCESS_TOKEN),
        },
        body: JSON.stringify({
          email: params.email,
          password: params.password,
          data: {
            first_name: params.first_name,
            last_name: params.last_name,
            phonenumber1: params.phonenumber1,
            reporting_to: params.reporting_to,
          },
        }),
      });
      if (response.ok) {
        const data = (await response.json()) as AddUserResponse;
        return data;
      } else {
        if (response.status == 500) {
          throw new Error("User domain not supported! Please contact admin.");
        } else {
          throw new Error("User already exists");
        }
      }
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function updateUser(params: UserUpdate): Promise<UserUpdateResult> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.addOrgClaimRoleToUsers,
        params,
      )) as {
        data: UserUpdateResult;
        error: ErrorType | null;
      };

      if (error) {
        throw new Error(error.details);
      }
      return data as UserUpdateResult;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function deactivateUsers(
    params: UserDeactivate,
  ): Promise<UserDeactivateResult> {
    try {
      const { data, error } = (await supabase.rpc(rpc.blockUser, params)) as {
        data: UserDeactivateResult;
        error: ErrorType | null;
      };

      if (error) {
        throw new Error(error.details);
      }
      return data as UserDeactivateResult;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function getEnrolledCourses(
    params: fetchTokenRequest,
  ): Promise<EnrolledCourseResponse[]> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.getEnrolledCourses,
        params,
      )) as {
        data: EnrolledCourseResponse[];
        error: ErrorType | null;
      };

      if (error) {
        throw new Error(error.details);
      }
      return data as EnrolledCourseResponse[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function getReportingToResource(
    params: fetchTokenRequest,
  ): Promise<ReportersResponse[]> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.getReportingToResource,
        params,
      )) as {
        data: ReportersResponse[];
        error: ErrorType | null;
      };

      if (error) {
        throw new Error(error.details);
      }
      return data as ReportersResponse[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function inviteNewUser(
    params: InviteUserRequest,
  ): Promise<InviteUserResponse> {
    try {
      console.log("params.email", params.recipient);
      const response = await fetch(functions.inviteUserToWebapp, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          apikey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY as string,
          Authorization: "Bearer " + localStorage.getItem(ACCESS_TOKEN),
        },
        body: JSON.stringify({
          params,
        }),
      });
      if (response.ok) {
        const data = (await response.json()) as InviteUserResponse;
        return data;
      } else {
        throw new Error("Failed to send notification");
      }
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  return {
    getRoles,
    getUsers,
    signUpUser,
    updateUser,
    deactivateUsers,
    getEnrolledCourses,
    getReportingToResource,
    inviteNewUser,
  };
};

export default useUsers;
