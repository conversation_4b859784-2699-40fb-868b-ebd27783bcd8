"use client";
import React, { useState, useEffect, type FC } from "react";
import { getColumns } from "./columns";
import { DataTable } from "@/components/ui/data-table/data-table";
import { Label } from "@/components/ui/label";
import { But<PERSON> } from "@/components/ui/button";
// import Link from "next/link";
import type {
  QuestionBankData,
  ComboData,
  TopicDataType,
  Question,
  ImportQuestionProps,
  ErrorCatch,
  ToastType,
} from "../../types";
import { Combobox } from "@/components/ui/combobox";
import useQuestionBank from "@/hooks/useQuestionBank";
import useImportQuestions from "@/hooks/useImportQuestions";
import type { ImportQuestionRequest } from "@/types";
import type { BaseSyntheticEvent } from "react";
import { useRouter } from "next/navigation";
import { pageUrl } from "@/lib/constants";
import { useToast } from "@/components/ui/use-toast";
import { Spinner } from "@/components/ui/progressiveLoader";
import { useTranslation } from "react-i18next";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";

const ImportQuestions: FC<ImportQuestionProps> = ({
  closeDialog,
  questionType,
  examDetailsList,
  numberOfQuestions,
  rowCount,
  questions,
  isExamData,
}): React.JSX.Element => {
  const { t } = useTranslation();
  const columns = getColumns(t);
  const { toast } = useToast() as ToastType;
  const router = useRouter();
  const { postImportQuestions, addImportQuestionsFromBank } =
    useImportQuestions();
  const [categoryId, setCategoryId] = React.useState("");
  const [categoryData, setCategoryData] = useState<ComboData[]>([]);
  const [questionBankList, setQuestionBankList] = React.useState<
    QuestionBankData[] | undefined
  >([]);
  const [selectedData, setSelectedData] = useState<Question[]>([]);
  const [isButtonDisabled, setIsButtonDisabled] = useState(true);
  const [isLoading, setIsLoading] = React.useState<boolean>(true);
  const { getPublishedQuestions, getPublishedQuestionCategory } =
    useQuestionBank();
  // const [idsOfQuestions, setIdsOfQuestions] = useState<(string | undefined)[]>(
  //   [],
  // );
  const customColumnWidths: Record<string, { width: number; align: string }> = {
    name: { width: 600, align: "justify" },
  };
  useEffect(() => {
    const fetchCategoryData = async (): Promise<void> => {
      setIsLoading(true);
      try {
        const category: TopicDataType[] = await getPublishedQuestionCategory();
        setIsLoading(false);
        if (category !== null && category.length > 0) {
          const comboData: ComboData[] = category.map((cat) => ({
            value: cat.value,
            label: cat.label,
          }));
          setCategoryData(comboData);
        } else {
          setCategoryData([]);
        }
      } catch (error) {
        setIsLoading(false);
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
      }
    };
    fetchCategoryData().catch((error) => console.log(error));
  }, []);

  const handleComboValueChange = (selectedValue: string): void => {
    setCategoryId(selectedValue);
  };

  useEffect(() => {
    const fetchQuestionData = async (): Promise<void> => {
      setIsLoading(true);
      try {
        const qcatId = categoryId;
        const data = await getPublishedQuestions(
          qcatId !== "" ? qcatId : undefined,
        );
        setIsLoading(false);
        if (data.length > 0) {
          console.log("Question data", data);
          const extractedQuestionIds = data.map(
            (question) => question.question_id,
          );
          console.log("extractedQuestionIds", extractedQuestionIds);
          const questionsNotInExtractedIds = (
            extractedQuestionIds ?? []
          )?.filter((question) => questions && !questions?.includes(question));
          console.log(questionsNotInExtractedIds);

          const extracted_questions: QuestionBankData[] = data.filter((item) =>
            questionsNotInExtractedIds.includes(item.question_id),
          );

          extracted_questions.forEach((question) => {
            console.log("Question ID:", question.question_id);
            console.log("Question Text:", question.name);
            console.log("Answers:");
            question.answers?.forEach((answer) => {
              console.log("-", answer.answer);
            });
            console.log();
          });

          // setIdsOfQuestions(extractedQuestionIds);
          setQuestionBankList(data);
        } else {
          setQuestionBankList([]);
        }
      } catch (error) {
        setIsLoading(false);
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
      }
    };
    if (categoryData.length > 0) {
      fetchQuestionData().catch((error) => console.log(error));
    }
  }, [categoryId]);

  const handleSelectedData = (selectedData: Question[]): void => {
    console.log("selected data", selectedData);
    // const selectedQuestionId = selectedData.map(item => item.question_id);
    const selectedQuestions = selectedData.length;
    const remainingQuestions = numberOfQuestions - rowCount;
    console.log(selectedQuestions);
    console.log(remainingQuestions);
    if (selectedQuestions > remainingQuestions) {
      setIsButtonDisabled(true);
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.selectMaxQuestions", {
          count: remainingQuestions,
        }),
      });
    } else {
      setIsButtonDisabled(false);
    }
    setSelectedData(selectedData);
  };

  const handleImportQuestions = async (
    e: BaseSyntheticEvent,
  ): Promise<void> => {
    console.log(e);
    const questionIds = selectedData.map((id) => id.question_id);
    console.log("selected question ids", questionIds);
    console.log("isExamData", isExamData);

    const orgId = localStorage.getItem("orgId");
    if (categoryId === "" || questionIds.length === 0) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.selectQuestionsToImport"),
      });
    } else {
      const questionData = {
        org_id: orgId ?? "",
        question_ids: questionIds,
        quiz_id: questionType,
      };
      try {
        const result =
          isExamData === true
            ? await addImportQuestionsFromBank(
                questionData as ImportQuestionRequest,
              )
            : await postImportQuestions(questionData as ImportQuestionRequest);
        if (result.status === "success") {
          let user_msg = "";
          if (result.num_questions_inserted > 0) {
            user_msg = t("successMessages.questions_imported", {
              count: result.num_questions_inserted,
            });
            toast({
              variant: SUCCESS_MESSAGES.toast_variant_default,
              title: t("successMessages.toast_success_title"),
              description: user_msg,
            });
          } else {
            user_msg = t("errorMessages.noQuestionsImported");
            toast({
              variant: ERROR_MESSAGES.toast_variant_destructive,
              title: t("errorMessages.toast_error_title"),
              description: user_msg,
            });
          }

          closeDialog(true);
          examDetailsList(true);
          router.push(`${pageUrl.examDetails}?type=${questionType}`);
        } else if (result.status === "error") {
          toast({
            variant: ERROR_MESSAGES.toast_variant_destructive,
            title: t("errorMessages.toast_error_title"),
            description: result.status,
          });
          console.log("API Error:", result.status);
        }
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.details,
        });
        console.error("An unexpected error occurred:", error);
      }
    }
  };

  const handleCancel = (): void => {
    closeDialog(true);
  };

  console.log("Already exists", questions);

  return (
    <div className="border rounded-md p-4 ">
      <div className="w-full flex justify-between space-x-4">
        <div className="w-full flex items-center">
          <>
            <Label>{t("exams.selectQuestionCategory")}</Label>
          </>
          <div className="w-1/3 ml-2">
            <Combobox
              data={categoryData}
              onSelectChange={handleComboValueChange}
            />
          </div>
        </div>
      </div>
      {isLoading ? (
        <Spinner />
      ) : (
        <div>
          {questionBankList && questionBankList?.length > 0 && (
            <DataTable
              columns={columns}
              data={questionBankList as Question[]}
              FilterLabel={t("exams.filterByQuestions")}
              FilterBy={"name"}
              actions={[]}
              onSelectedDataChange={(value: unknown) =>
                handleSelectedData(value as Question[])
              }
              customColumnWidths={customColumnWidths}
            />
          )}
        </div>
      )}
      {!isButtonDisabled && (
        <div className="flex flex-wrap justify-end mt-6 space-x-2">
          <div>
            <Button className="bg-[#33363F]" onClick={handleCancel}>
              {t("buttons.cancel")}
            </Button>
          </div>
          <div>
            <Button
              onClick={(e: BaseSyntheticEvent) => {
                handleImportQuestions(e).catch((error) => console.log(error));
              }}
              className="bg-[#9FC089]"
            >
              {t("exams.import")}
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ImportQuestions;
