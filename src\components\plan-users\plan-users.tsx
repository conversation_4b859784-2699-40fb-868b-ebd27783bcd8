"use client";
import React, { useEffect, useState } from "react";
import { DataTable } from "../ui/data-table/data-table";
import { getColumns } from "./columns";
import { ORG_KEY, privilegeData } from "@/lib/constants";
import useSubscription from "@/hooks/useSubscription";
import type { ErrorCatch, ToastType, UserPlanListResult } from "@/types";
import { ERROR_MESSAGES } from "@/lib/messages";
import { Spinner } from "../ui/progressiveLoader";
import { Input } from "../ui/input";
import { Archive } from "lucide-react";
import getPrivilegeList from "@/hooks/useCheckPrivilege";
import { Modal } from "../ui/modal";
import { useToast } from "../ui/use-toast";
import DeleteSubscriptionUser from "./remove-suscription-user";
import { useTranslation } from "react-i18next";

function MembershipUserListPage({
  data,
  plan,
}: {
  onCancel: () => void;
  data: string;
  plan: string;
  isModal?: boolean;
}): React.JSX.Element {
  const { t } = useTranslation();
  const columns = getColumns(t);
  const planId = data;
  const PlanName = plan;
  const { getSubscribedUsers } = useSubscription();
  const [subscriptionUsers, setSubscriptionUsers] = useState<
    UserPlanListResult[]
  >([]);
  const [deleteUser, setDelete] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const { toast } = useToast() as ToastType;
  const [passData, setPassData] = React.useState<UserPlanListResult>();

  useEffect(() => {
    void fetchUserList();
  }, []);
  const fetchUserList = async (): Promise<void> => {
    try {
      const orgId = localStorage.getItem(ORG_KEY) ?? "";
      const params = {
        org_id: orgId,
        plan_id: planId,
      };
      const users = await getSubscribedUsers(params);
      setSubscriptionUsers(users.result);
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
    }
  };
  const openDeleteDialog = (val: UserPlanListResult): void => {
    setPassData(val);
    setDelete(true);
  };
  const closeDelete = (): void => {
    setDelete(false);
  };
  function deleteSubscriptionUser(): void {
    fetchUserList().catch((error) => console.log(error));
  }

  return (
    <>
      {isLoading ? (
        <Spinner />
      ) : (
        <div className="w-full">
          <div className="w-full course-width mt-2">
            <Input type="text" value={PlanName} disabled />
          </div>
          <div className="border rounded-md p-4 mt-4">
            <DataTable
              columns={columns}
              data={subscriptionUsers}
              FilterLabel={t("subscriptionPlan.filterByUser")}
              FilterBy={"name"}
              actions={[
                {
                  title: t("subscriptionPlan.delete"),
                  icon: Archive,
                  varient: "icon",
                  isEnable: getPrivilegeList(
                    "Subscription_Plans",
                    privilegeData.Subscription_Plans.deleteUserFromPlan,
                  ),
                  handleClick: (val: unknown) =>
                    openDeleteDialog(val as UserPlanListResult),
                },
              ]}
            />
          </div>
        </div>
      )}
      {deleteUser && (
        <Modal
          title={t("subscriptionPlan.deleteUser")}
          header=""
          openDialog={deleteUser}
          closeDialog={closeDelete}
        >
          <DeleteSubscriptionUser
            onSave={deleteSubscriptionUser}
            onCancel={closeDelete}
            isModal={true}
            data={passData as UserPlanListResult}
            planId={planId}
          />
        </Modal>
      )}
    </>
  );
}

export { MembershipUserListPage };
