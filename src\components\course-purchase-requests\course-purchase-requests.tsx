"use client";
import React, { useState, useEffect, useRef } from "react";
import { DataTable } from "@/components/ui/data-table/data-table";
import { Spinner } from "@/components/ui/progressiveLoader";
import { useToast } from "@/components/ui/use-toast";
import useCoursePurchaseRequest from "@/hooks/useCoursePurchaseRequest";
import useSubscription from "@/hooks/useSubscription";
import type {
  GetCoursePurchaseRequest,
  ErrorCatch,
  ToastType,
  SubscriptionListResults,
  subscriptionListRequest,
  SubscriptionRequestType,
  AddPendingSubscriptions,
  UpdatePurchaseRequest,
  CoursePurchase,
  CoursePurchaseRequestResponse,
} from "@/types";
import { ORG_KEY } from "@/lib/constants";
import { useTranslation } from "react-i18next";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";

import { But<PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import moment from "moment";
import type { ColumnDef } from "@tanstack/react-table";

interface CoursePurchaseRequestsProps {
  onCancel: () => void;
}

export default function CoursePurchaseRequests({
  onCancel,
}: CoursePurchaseRequestsProps): React.JSX.Element {
  const { t } = useTranslation();
  const { toast } = useToast() as ToastType;
  const { getCoursePurchaseRequests } = useCoursePurchaseRequest();
  const {
    getSubscriptionListForAdmin,
    addPendingUsersToPlan,
    updateCourse,
    updatePurchaseRequest,
  } = useSubscription();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [purchaseRequests, setPurchaseRequests] =
    useState<CoursePurchaseRequestResponse[]>();
  const [subscriptionPlans, setSubscriptionPlans] = useState<
    SubscriptionListResults[]
  >([]);
  const [selectedRowCount, setSelectedRowCount] = useState<number>(0);
  const [selectedData, setSelectedData] = useState<
    CoursePurchaseRequestResponse[]
  >([]);

  // Use a ref to maintain a stable data reference
  const stableDataRef = useRef<CoursePurchaseRequestResponse[]>([]);
  const lastDataLengthRef = useRef<number>(0);

  // Only update the stable reference when we have new data (different length or IDs)
  // This prevents the DataTable from re-rendering and losing selection when plans change
  const currentDataIds = purchaseRequests?.map((req) => req.id).join(",") ?? "";
  const lastDataIdsRef = useRef<string>("");

  if (
    purchaseRequests &&
    (purchaseRequests.length !== lastDataLengthRef.current ||
      currentDataIds !== lastDataIdsRef.current)
  ) {
    stableDataRef.current = [...purchaseRequests];
    lastDataLengthRef.current = purchaseRequests.length;
    lastDataIdsRef.current = currentDataIds;
  }

  // Update the stable reference with current plan selections without changing the reference
  if (
    purchaseRequests &&
    stableDataRef.current.length === purchaseRequests.length
  ) {
    stableDataRef.current.forEach((item, index) => {
      const currentRequest = purchaseRequests[index];
      if (currentRequest != null && item.id === currentRequest.id) {
        item.selected_plan_id = currentRequest.selected_plan_id;
      }
    });
  }

  const memoizedData = stableDataRef.current;
  const columns: ColumnDef<CoursePurchaseRequestResponse>[] = [
    {
      id: "select",
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "course_name",
      header: "Course Name",
      cell: ({ row }) => (
        <div className="font-medium">{row.original.course_name}</div>
      ),
    },
    {
      accessorKey: "user_name",
      header: "User Name",
      cell: ({ row }) => <div>{row.original.user_name}</div>,
    },

    {
      accessorKey: "request_date",
      header: "Request Date",
      cell: ({ row }) => {
        const formatted = moment(row.original.requested_on);
        return (
          <div>
            {formatted.isValid() ? formatted.format("DD MMM YYYY") : "-"}
          </div>
        );
      },
    },

    {
      id: "plan_selection",
      header: "Assign Plan *",
      cell: ({ row }) => (
        <Select
          value={row.original.selected_plan_id ?? ""}
          onValueChange={(value) => {
            setPurchaseRequests((prev) =>
              prev?.map((req) =>
                req.id === row.original.id
                  ? { ...req, selected_plan_id: value }
                  : req,
              ),
            );
          }}
          required
        >
          <SelectTrigger>
            <SelectValue placeholder="Select a plan *" />
          </SelectTrigger>
          <SelectContent>
            {subscriptionPlans.map((plan) => (
              <SelectItem key={plan.id} value={plan.id}>
                {plan.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      ),
      enableSorting: false,
    },
  ];

  useEffect(() => {
    void fetchCoursePurchaseRequests();
    void fetchSubscriptionPlans();
  }, []);

  const fetchCoursePurchaseRequests = async (): Promise<void> => {
    try {
      setIsLoading(true);
      const orgId = localStorage.getItem(ORG_KEY);
      const params: GetCoursePurchaseRequest = { org_id: orgId ?? "" };
      const response: CoursePurchase = await getCoursePurchaseRequests(params);
      console.log(response);
      if (response.status === "success") {
        setPurchaseRequests(response.requests);
      }
    } catch (error) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: "Failed to fetch course purchase requests",
      });
      setPurchaseRequests([]);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchSubscriptionPlans = async (): Promise<void> => {
    try {
      const orgId = localStorage.getItem(ORG_KEY);
      const reqParams: subscriptionListRequest = {
        org_id: orgId ?? "",
        limit_param: null,
        offset_param: 0,
      };
      const response = await getSubscriptionListForAdmin(reqParams);
      if (response.status === "success") {
        const publishedPlans = response.result.filter(
          (plan) => plan.subscription_status === "Published",
        );
        setSubscriptionPlans(publishedPlans);
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: "Error",
        description: err?.message ?? "Failed to fetch subscription plans",
      });
    }
  };

  const handleSaveAssignments = async (): Promise<void> => {
    try {
      setIsSaving(true);
      const orgId = localStorage.getItem(ORG_KEY);
      // Use the selected data from DataTable
      const selectedRequests = selectedData;
      if (selectedRequests?.length === 0) {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: "Error",
          description: ERROR_MESSAGES.select_request,
        });
        return;
      }
      // Check if all selected requests have plans assigned
      const requestsWithoutPlans = selectedRequests?.filter(
        (req) =>
          req.selected_plan_id.length === 0 ||
          req.selected_plan_id.trim().length === 0,
      );
      if ((requestsWithoutPlans?.length ?? 0) > 0) {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: "Please assign plans to all selected requests",
        });
        return;
      }
      const requestsWithPlans = selectedRequests;
      // Group requests by plan_id and course_id
      const planCourseGroups: Record<
        string,
        { plan_id: string; course_id: string; user_ids: string[] }
      > = (requestsWithPlans ?? []).reduce(
        (groups, req) => {
          const key = `${req.selected_plan_id}_${req.course_id}`;

          groups[key] = {
            plan_id: req.selected_plan_id!,
            course_id: req.course_id,
            user_ids: [],
          };

          groups[key].user_ids.push(req.requested_by);
          return groups;
        },
        {} as Record<
          string,
          { plan_id: string; course_id: string; user_ids: string[] }
        >,
      );

      for (const group of Object.values(planCourseGroups)) {
        const formData = {
          org_id: orgId ?? "",
          plan_id: group.plan_id,
          courses: [
            {
              course_id: group.course_id,
              is_expired: false,
            },
          ],
        };
        await updateCourse(formData as SubscriptionRequestType);
        const addUserData: AddPendingSubscriptions = {
          org_id: orgId ?? "",
          plan_id: group.plan_id,
          user_ids: group.user_ids,
        };
        await addPendingUsersToPlan(addUserData);
        // Update the purchase request status
        const updateRequestData: UpdatePurchaseRequest = {
          org_id: orgId ?? "",
          course_id: group.course_id,
          request_status: "approved",
          status_notes: null,
          requested_by: group.user_ids[0],
        };
        await updatePurchaseRequest(updateRequestData);
      }

      toast({
        variant: "default",
        title: "Success",
        description: SUCCESS_MESSAGES.course_request,
      });

      // Clear selections and refresh the data

      setSelectedRowCount(0);
      setSelectedData([]);
      await fetchCoursePurchaseRequests();
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: "Error",
        description: err?.message ?? ERROR_MESSAGES.course_request,
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      {isLoading ? (
        <div className="flex justify-center py-8">
          <Spinner />
        </div>
      ) : (
        <div className="space-y-4">
          {purchaseRequests?.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <div className="text-lg font-medium">
                No course purchase requests found
              </div>
            </div>
          ) : (
            <>
              <DataTable
                key="course-purchase-requests-table"
                columns={columns}
                data={memoizedData}
                FilterLabel="Filter by course name"
                FilterBy="course_name"
                onSetRowSelection={setSelectedRowCount}
                onSelectedDataChange={setSelectedData}
                actions={[]}
              />
              <div className="flex gap-2 justify-end">
                <Button
                  onClick={onCancel}
                  variant="outline"
                  disabled={isSaving}
                >
                  Cancel
                </Button>
                <Button
                  onClick={() => void handleSaveAssignments()}
                  disabled={
                    isSaving ||
                    selectedRowCount === 0 ||
                    // eslint-disable-next-line @typescript-eslint/strict-boolean-expressions
                    selectedData.some(
                      (req) =>
                        req.selected_plan_id === undefined ||
                        req.selected_plan_id === null ||
                        req.selected_plan_id.trim() === "",
                    )
                  }
                  variant="default"
                  title={
                    selectedRowCount === 0
                      ? "Please select at least one request"
                      : // eslint-disable-next-line @typescript-eslint/strict-boolean-expressions
                        selectedData.some(
                            (req) =>
                              req.selected_plan_id === undefined ||
                              req.selected_plan_id === null ||
                              req.selected_plan_id.trim() === "",
                          )
                        ? "Please assign plans to all selected requests"
                        : "Save assignments"
                  }
                >
                  {"Save"}
                </Button>
              </div>
            </>
          )}
        </div>
      )}
    </div>
  );
}
