"use client";
import React from "react";
import MainLayout from "../layout/mainlayout";
import AddBulletin from "@/components/addCurrentAffairs/addCurrentAffairs";
import { useSearchParams } from "next/navigation";
export default function AddCurrentAffairs(): React.JSX.Element {
  const searchParams = useSearchParams();
  const mode = searchParams.get("mode") as string;

  return (
    <MainLayout>
      <AddBulletin mode={mode} />
    </MainLayout>
  );
}
