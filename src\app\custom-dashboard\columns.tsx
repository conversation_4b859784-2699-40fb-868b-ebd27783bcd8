"use client";
import type { ColumnDef } from "@tanstack/react-table";
import type { CustomResource, CustomResourceRowDefinition } from "@/types";

export const columns = (
  selectedFilter: string,
): ColumnDef<CustomResource>[] => {
  const baseColumns: ColumnDef<CustomResource>[] = [
    {
      accessorKey: "resource_name",
      header: "Resource Name",
      cell: ({ row }: CustomResourceRowDefinition): React.JSX.Element => (
        <div className="text-align">{row.original.resource_name}</div>
      ),
    },
  ];

  // Only add section_name column for course_and_section filter
  if (selectedFilter === "course_and_section") {
    baseColumns.push({
      accessorKey: "section_name",
      header: "Section Name",
      cell: ({ row }: CustomResourceRowDefinition): React.JSX.Element => (
        <div className="text-align">{row.original.section_name}</div>
      ),
    });
  }

  return baseColumns;
};
