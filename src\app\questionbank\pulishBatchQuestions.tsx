import React, { useEffect, useState } from "react";
import type {
  BatchQuestionData,
  ComboData,
  ErrorCatch,
  LogUserActivityRequest,
  QuestionBankData,
  ToastType,
  TopicDataType,
} from "@/types";
import { Button } from "@/components/ui/button";
import useQuestionBank from "@/hooks/useQuestionBank";
import { useToast } from "@/components/ui/use-toast";
import { useRouter } from "next/navigation";
import { ORG_KEY } from "@/lib/constants";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import { DataTable } from "@/components/ui/data-table/data-table";
import { getBatchColumns } from "./batchcolumns";
import { Label } from "@/components/ui/label";
import { Combobox } from "@/components/ui/combobox";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";

export default function PublishBatchQuestions({
  onSave,
  onCancel,
}: {
  onSave: (value: boolean) => void;
  onCancel: () => void;
  unPublishedQuestions: BatchQuestionData[];
}): React.JSX.Element {
  const router = useRouter();
  const { t } = useTranslation();
  const batchcolumns = getBatchColumns(t);
  const { toast } = useToast() as ToastType;
  const [SelectedQuestionsBatch, setSelectedQuestionsBatch] = React.useState<
    (string | undefined)[]
  >([]);
  const {
    publishQuestions,
    getUnPublishedQuestions,
    getPublishedQuestionCategory,
  } = useQuestionBank();
  const { updateUserActivity } = useLogUserActivity();
  const [comboData, setComboData] = useState<ComboData[]>([]);
  //const [categoryAddStatus, setCategoryAddStatus] = useState<boolean>(false);
  const [categoryId, setCategoryId] = React.useState("");
  const [categoryQuestions, setCategoryQuestions] = useState<
    BatchQuestionData[]
  >([]);
  const [filteredCategoryQuestions, setFilteredCategoryQuestions] = useState<
    BatchQuestionData[]
  >([]);

  useEffect(() => {
    const getQuestionsData = async (): Promise<void> => {
      try {
        const category: QuestionBankData[] = await getUnPublishedQuestions();
        const comboData: BatchQuestionData[] = category?.map((cat) => ({
          question_id: cat.question_id,
          question_text: cat.question_text,
          question_category_id: cat.question_category_id,
          question_category_name: cat.question_category_name,
        }));

        setCategoryQuestions(comboData);
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
        console.error("Error fetching data:");
      }
    };
    getQuestionsData().catch((error) => console.log(error));
  }, []);

  const handlePublishClick = (): void => {
    void handleToastSave();
  };
  const customColumnWidths: Record<string, { width: number; align: string }> = {
    question_text: { width: 1100, align: "justify" },
  };
  const handleSelectedData = (selData: BatchQuestionData[]): void => {
    const selectedquestions = selData.map((item) => item.question_id);
    setSelectedQuestionsBatch(selectedquestions);
  };

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  const questionCategoryList = (): void => {
    const fetchData = async (): Promise<void> => {
      try {
        const category: TopicDataType[] = await getPublishedQuestionCategory();
        const comboData: ComboData[] = category?.map((cat) => ({
          value: cat.value,
          label: cat.label,
        }));

        setComboData(comboData);
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
        console.error("Error fetching data:");
      }
    };
    fetchData().catch((error) => console.log(error));
  };

  useEffect(() => {
    questionCategoryList();
  }, []);

  const handleComboValueChange = (selectedValue: string): void => {
    setCategoryId(selectedValue);

    const filteredQData: BatchQuestionData[] = categoryQuestions
      ?.filter((item) => item.question_category_id == selectedValue) // Filter the items based on the condition
      .map((item) => ({
        question_id: item.question_id,
        question_text: item.question_text,
      }));
    if (filteredQData.length > 0) {
      setFilteredCategoryQuestions(filteredQData);
    } else {
      setFilteredCategoryQuestions([]);
    }
  };

  const handleToastSave = async (): Promise<void> => {
    if (SelectedQuestionsBatch.length > 0) {
      const params = {
        question_ids: SelectedQuestionsBatch,
        org_id: localStorage.getItem(ORG_KEY) ?? "",
        status: "Published",
      };
      try {
        const result = await publishQuestions(params);
        if (result.status === "success") {
          toast({
            variant: SUCCESS_MESSAGES.toast_variant_default,
            title: t("successMessages.questionPublished"),
            description: t("successMessages.questionPublishDesc"),
          });
          onSave(true);
          const params = {
            activity_type: "Question_Bank",
            screen_name: "Question_Bank",
            action_details: "Successfully published question in batch",
            target_id: categoryId as string,
            log_result: "SUCCESS",
          };
          void updateLogUserActivity(params).catch((error) => {
            console.error(error);
          });
          onCancel();
          router.push("/questionbank");
        } else if (result.status === "error") {
          toast({
            variant: ERROR_MESSAGES.toast_variant_destructive,
            title: t("errorMessages.toast_error_title"),
            description: result?.status,
          });
          const params = {
            activity_type: "Question_Bank",
            screen_name: "Question_Bank",
            action_details: "Failed to publish question in batch",
            target_id: categoryId as string,
            log_result: "ERROR",
          };
          void updateLogUserActivity(params).catch((error) => {
            console.error(error);
          });
        }
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: ERROR_MESSAGES.toast_error_title,
          description: err?.message,
        });
        console.error("An unexpected error occurred:", error);
      }
    } else {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.selectQuestion"),
      });
      console.error("Error fetching data:");
    }
  };
  return (
    <>
      <div className="border rounded-md p-4 ">
        <div>
          <div className="text-left ">
            <Label>{t("questionBank.selectQuestionCategory")}</Label>{" "}
            <div className="w-1/2 flex">
              <Combobox
                data={comboData}
                onSelectChange={handleComboValueChange}
              />
            </div>
          </div>
        </div>
        <div>
          <DataTable
            columns={batchcolumns}
            data={filteredCategoryQuestions as BatchQuestionData[]}
            FilterLabel={t("questionBank.filterByQuestion")}
            FilterBy={"question_text"}
            actions={[]}
            onSelectedDataChange={(value: unknown) =>
              handleSelectedData(value as BatchQuestionData[])
            }
            customColumnWidths={customColumnWidths}
          />
        </div>
        <div className="flex topic-icons  pt-6">
          <div className="flex items-center justify-center float-right gap-3">
            <Button type="button" className="bg-[#33363F]" onClick={onCancel}>
              {t("buttons.cancel")}
            </Button>

            <Button
              type="submit"
              className="bg-[#9FC089]"
              onClick={handlePublishClick}
            >
              {t("buttons.publish")}
            </Button>
          </div>
        </div>
      </div>
    </>
  );
}
