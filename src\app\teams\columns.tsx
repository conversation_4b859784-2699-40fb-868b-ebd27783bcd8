"use client";

import type { ColumnDef } from "@tanstack/react-table";
// import { ArrowUpDown } from "lucide-react";
// import { Button } from "@/components/ui/button";
import type {
  GroupForm,
  // GroupFormDefinition,
  GroupFormRowDefinition,
} from "@/types";

export const getColumns = (
  t: (key: string) => string
): ColumnDef<GroupForm>[] => {

  return [
    {
      accessorKey: "name",
      header: t("groups.teams.columns.groupName"),
    // header: ({ column }: GroupFormDefinition): React.JSX.Element => {
    //   return (
    //     <Button
    //       className="px-0"
    //       variant="ghost"
    //       onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
    //     >
    //       Group
    //       <ArrowUpDown className="ml-2 h-4 w-4" />
    //     </Button>
    //   );
    // },
      cell: ({ row }: GroupFormRowDefinition): React.JSX.Element => (
        <div className="text-align word-break: break-all">
          {row.original.name}
        </div>
      ),
    },
    {
      accessorKey: "description",
      header: t("groups.teams.columns.description"),
      cell: ({ row }: GroupFormRowDefinition): React.JSX.Element => (
        <div className="text-align word-break: break-all">
          {row.original.description}
        </div>
      ),
    },
  ];
};
