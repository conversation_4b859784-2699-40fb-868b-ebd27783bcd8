"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import * as React from "react";
import { DateTimePicker } from "@/components/ui/date-time-picker/date-time-picker";
import { FormSchema } from "@/schema/schema";
import type { FormSchemaType } from "@/types";
import type { DateValue } from "@internationalized/date";
import type { DatePickerStateOptions } from "react-stately";
export default function About(): React.JSX.Element {
  const form = useForm<FormSchemaType>({
    resolver: zodResolver(FormSchema),
  });

  function onSubmit(data: FormSchemaType): void {
    const dateSelected = data.dateOnly;
    // const dateTest = dateSelected ? new Date(dateSelected.year,dateSelected.month-1,dateSelected.day,dateSelected.hour,dateSelected.minute):"";
    console.log(dateSelected);
  }

  return (
    <Form {...form}>
      <form onSubmit={() => form.handleSubmit(onSubmit)} className="space-y-8">
        <FormField
          control={form.control}
          name="dateOnly"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Date</FormLabel>
              <FormControl>
                <DateTimePicker
                  granularity={"minute"}
                  {...(field as unknown as DatePickerStateOptions<DateValue>)}
                />
              </FormControl>
              <FormDescription>
                This is your public display name.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit">Submit</Button>
      </form>
    </Form>
  );
}
