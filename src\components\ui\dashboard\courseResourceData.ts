const courseResourceData = [
  {
    section_id: "4cafdcf5-dd70-4aa7-ac33-8da6336dc9a7",
    course_id: "47fb669a-c8e8-428f-8f1f-28cc9ab69f42",
    name: "Topic 1",
    summary: "",
    section_order: 1,
    resources: [null],
    modules: [
      {
        course_id: "47fb669a-c8e8-428f-8f1f-28cc9ab69f42",
        module_id: "0098bc68-069c-4d19-930c-f4ad03a95bd8",
        instance: "d34d44c6-97b4-4171-8da1-5eeb782b74c0",
        section_id: "4cafdcf5-dd70-4aa7-ac33-8da6336dc9a7",
        attempts_remaining: 0,
        marked_as_done: false,
        progress: 0,
        time_spent: "00:00:00",
        module_type: "Url",
        module_name: "Fractions",
        module_source: "Video",
      },
      {
        course_id: "47fb669a-c8e8-428f-8f1f-28cc9ab69f42",
        module_id: "a726d173-4b3f-410d-b024-c0df9a94bf4c",
        instance: "64a397d6-2cbe-4833-8ddc-dd102fbdb9b1",
        section_id: "4cafdcf5-dd70-4aa7-ac33-8da6336dc9a7",
        attempts_remaining: 0,
        marked_as_done: false,
        progress: 0,
        time_spent: "00:00:00",
        module_type: "File",
        module_name: "Number system",
        module_source: "File",
      },
      {
        course_id: "47fb669a-c8e8-428f-8f1f-28cc9ab69f42",
        module_id: "65331446-ca27-48bf-acc4-27d09ffa9bed",
        instance: "fb918957-7800-4765-b97d-1e4c816c79ef",
        section_id: "4cafdcf5-dd70-4aa7-ac33-8da6336dc9a7",
        attempts_remaining: 0,
        marked_as_done: false,
        progress: 0,
        time_spent: "00:00:00",
        module_type: "Page",
        module_name: "Number System",
        module_source: "",
      },
      {
        course_id: "47fb669a-c8e8-428f-8f1f-28cc9ab69f42",
        module_id: "a726d173-4b3f-410d-b024-c0df9a94bf4c",
        instance: "cc00418d-84e8-4fd3-a282-cda0c39aa188",
        section_id: "4cafdcf5-dd70-4aa7-ac33-8da6336dc9a7",
        attempts_remaining: 0,
        marked_as_done: false,
        progress: 0,
        time_spent: "00:00:00",
        module_type: "File",
        module_name: "Number system",
        module_source: "File",
      },
      {
        course_id: "47fb669a-c8e8-428f-8f1f-28cc9ab69f42",
        module_id: "a726d173-4b3f-410d-b024-c0df9a94bf4c",
        instance: "8d1ac9ec-9c14-4d49-b75f-aaecea46dc00",
        section_id: "4cafdcf5-dd70-4aa7-ac33-8da6336dc9a7",
        attempts_remaining: 0,
        marked_as_done: false,
        progress: 0,
        time_spent: "00:00:00",
        module_type: "File",
        module_name: "number system -3",
        module_source: "File",
      },
      {
        course_id: "47fb669a-c8e8-428f-8f1f-28cc9ab69f42",
        module_id: "0098bc68-069c-4d19-930c-f4ad03a95bd8",
        instance: "52f675e0-4298-4f00-b701-58fed6109d91",
        section_id: "4cafdcf5-dd70-4aa7-ac33-8da6336dc9a7",
        attempts_remaining: 0,
        marked_as_done: false,
        progress: 0,
        time_spent: "00:00:00",
        module_type: "Url",
        module_name: "Fractions",
        module_source: "Video",
      },
      {
        course_id: "47fb669a-c8e8-428f-8f1f-28cc9ab69f42",
        module_id: "0098bc68-069c-4d19-930c-f4ad03a95bd8",
        instance: "b29a0c1e-ca00-4310-90ba-aa73b4c4fda7",
        section_id: "4cafdcf5-dd70-4aa7-ac33-8da6336dc9a7",
        attempts_remaining: 0,
        marked_as_done: false,
        progress: 0,
        time_spent: "00:00:00",
        module_type: "Url",
        module_name: "Speed",
        module_source: "Video",
      },
      {
        course_id: "47fb669a-c8e8-428f-8f1f-28cc9ab69f42",
        module_id: "dd33330a-7ab4-452b-b8bd-9e37192f0ffb",
        instance: "6406c351-59a0-41e4-ab59-e165c259764e",
        section_id: "4cafdcf5-dd70-4aa7-ac33-8da6336dc9a7",
        attempts_remaining: 0,
        marked_as_done: false,
        progress: 0,
        time_spent: "00:00:00",
        module_type: "Quiz",
        module_name: "Maths Exam",
        module_source: "",
      },
      {
        course_id: "47fb669a-c8e8-428f-8f1f-28cc9ab69f42",
        module_id: "dd33330a-7ab4-452b-b8bd-9e37192f0ffb",
        instance: "45b35079-e63c-4bab-b2c6-2890b21944d3",
        section_id: "4cafdcf5-dd70-4aa7-ac33-8da6336dc9a7",
        attempts_remaining: 9,
        marked_as_done: false,
        progress: 0,
        time_spent: "00:00:00",
        module_type: "Quiz",
        module_name: "Surface Areas and Volumes",
        module_source: "",
      },
      {
        course_id: "47fb669a-c8e8-428f-8f1f-28cc9ab69f42",
        module_id: "dd33330a-7ab4-452b-b8bd-9e37192f0ffb",
        instance: "8c5735c6-6b48-4fc9-96f9-e4aac3cf3c26",
        section_id: "4cafdcf5-dd70-4aa7-ac33-8da6336dc9a7",
        attempts_remaining: 0,
        marked_as_done: true,
        progress: 100,
        time_spent: "06:40:00",
        module_type: "Quiz",
        module_name: "Basic Accounting",
        module_source: "",
      },
      {
        course_id: "47fb669a-c8e8-428f-8f1f-28cc9ab69f42",
        module_id: "0098bc68-069c-4d19-930c-f4ad03a95bd8",
        instance: "4f695f03-08c6-46e4-8e4c-2c63e0610603",
        section_id: "4cafdcf5-dd70-4aa7-ac33-8da6336dc9a7",
        attempts_remaining: 0,
        marked_as_done: false,
        progress: 72.**************,
        time_spent: "45:00:17",
        module_type: "Url",
        module_name: "Trigonometry",
        module_source: "Video",
      },
      {
        course_id: "47fb669a-c8e8-428f-8f1f-28cc9ab69f42",
        module_id: "a726d173-4b3f-410d-b024-c0df9a94bf4c",
        instance: "e3f98456-0dbb-494d-8add-5121291b321e",
        section_id: "4cafdcf5-dd70-4aa7-ac33-8da6336dc9a7",
        attempts_remaining: 0,
        marked_as_done: false,
        progress: 0,
        time_spent: "00:00:00",
        module_type: "File",
        module_name: "Maths Course",
        module_source: "File",
      },
      {
        course_id: "47fb669a-c8e8-428f-8f1f-28cc9ab69f42",
        module_id: "dd33330a-7ab4-452b-b8bd-9e37192f0ffb",
        instance: "7a49ae6e-739b-4bd7-9a50-16821181a40c",
        section_id: "4cafdcf5-dd70-4aa7-ac33-8da6336dc9a7",
        attempts_remaining: 4,
        marked_as_done: false,
        progress: 0,
        time_spent: "00:00:00",
        module_type: "Quiz",
        module_name: "Computer Knowledge",
        module_source: "",
      },
      {
        course_id: "47fb669a-c8e8-428f-8f1f-28cc9ab69f42",
        module_id: "dd33330a-7ab4-452b-b8bd-9e37192f0ffb",
        instance: "5189259c-ecbe-4854-ac07-3ce4a7bf39c7",
        section_id: "4cafdcf5-dd70-4aa7-ac33-8da6336dc9a7",
        attempts_remaining: 3,
        marked_as_done: false,
        progress: 0,
        time_spent: "00:00:00",
        module_type: "Quiz",
        module_name: "Preliminary Test",
        module_source: "",
      },
      {
        course_id: "47fb669a-c8e8-428f-8f1f-28cc9ab69f42",
        module_id: "65331446-ca27-48bf-acc4-27d09ffa9bed",
        instance: "934c1d9c-09e7-48c0-8e2d-676bf5547e3b",
        section_id: "4cafdcf5-dd70-4aa7-ac33-8da6336dc9a7",
        attempts_remaining: 0,
        marked_as_done: false,
        progress: 0,
        time_spent: "00:00:00",
        module_type: "Page",
        module_name: "Class 12 Maths Notes",
        module_source: "",
      },
      {
        course_id: "47fb669a-c8e8-428f-8f1f-28cc9ab69f42",
        module_id: "0098bc68-069c-4d19-930c-f4ad03a95bd8",
        instance: "c626fe6f-7d3a-4d3a-a90d-7bbb9f16ab6e",
        section_id: "4cafdcf5-dd70-4aa7-ac33-8da6336dc9a7",
        attempts_remaining: 0,
        marked_as_done: false,
        progress: 0,
        time_spent: "00:00:00",
        module_type: "Url",
        module_name: "Fractions",
        module_source: "Video",
      },
    ],
  },
];

export default courseResourceData;
