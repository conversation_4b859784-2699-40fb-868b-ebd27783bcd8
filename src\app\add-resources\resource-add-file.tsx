"use client";
import { useForm } from "react-hook-form";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

import React from "react";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
// import { Milestone } from "./milestone";
import { Button } from "@/components/ui/button";
import type {
  AddResources,
  ErrorCatch,
  ResourceFileRequest,
  ResourceVideoRequest,
  ToastType,
} from "@/types";
import { AddResourceVideoschema, AddResourceschema } from "@/schema/schema";
import { zodResolver } from "@hookform/resolvers/zod";
import useCourse from "@/hooks/useCourse";
import { useToast } from "@/components/ui/use-toast";
// import { Modal } from "@/components/ui/modal";
import { useSearchParams, useRouter } from "next/navigation";
import { pageUrl, privilegeData } from "@/lib/constants";
import getPrivilegeList from "@/hooks/useCheckPrivilege";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import { useTranslation } from "react-i18next";
// zod validation todo
interface ResourceAddProps {
  resourceType: string;
}

export const CourseResourceAddfile: React.FC<ResourceAddProps> = ({
  resourceType,
}) => {
  const { t } = useTranslation();
  const checkPointBtn = getPrivilegeList(
    "Checkpoint",
    privilegeData.Checkpoint.createCheckpoint,
  );
  const [showMilestone, setShowMilestone] = React.useState(false);
  const [randomEnabled, setRandomenabled] = React.useState(false);
  const [showCheckpoint, setShowcheckpoint] = React.useState(false);
  // const [checkpointNumber, setCheckPointNumber] = React.useState(0);
  // const [checkpointVideoLength, setCheckPointVideoLength] =
  //   React.useState("00:00:00");
  // const [courseModuleId, setCourseModId] = React.useState<string | null>("");
  // const [checkPointStatus, setCheckPointStatus] =
  //   React.useState<boolean>(false);
  const { toast } = useToast() as ToastType;
  const searchParams = useSearchParams();
  const { resourceInsert } = useCourse();

  const form = useForm<AddResources>({
    resolver: zodResolver(
      resourceType === "1" ? AddResourceschema : AddResourceVideoschema,
    ),
  });
  const router = useRouter();
  async function onSubmit(): Promise<void> {
    const formData = form.getValues();
    // setCheckPointNumber(formData.checkpointnumber ?? 0);
    if (formData.name !== null && /^[0-9,-]*$/.test(formData.name)) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.valid_name"),
      });
      return;
    }
    if (
      formData.description !== null &&
      /^[0-9\s\W_]*$/.test(formData.description)
    ) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.valid_description"),
      });
      return;
    }

    const org_id = localStorage.getItem("orgId");
    const course_id = localStorage.getItem("courseId");
    const type = searchParams.get("sectionId");
    const videoLength = `${
      formData.videolength?.HH !== undefined && formData.videolength.HH !== ""
        ? formData.videolength.HH.length === 1
          ? `0${formData.videolength.HH}`
          : formData.videolength.HH
        : "00"
    }:${
      formData.videolength?.MM !== undefined && formData.videolength.MM !== ""
        ? formData.videolength.MM.length === 1
          ? `0${formData.videolength.MM}`
          : formData.videolength.MM
        : "00"
    }:${
      formData.videolength?.SS !== undefined && formData.videolength.SS !== ""
        ? formData.videolength.SS.length === 1
          ? `0${formData.videolength.SS}`
          : formData.videolength.SS
        : "00"
    }`;
    //console.log(type);

    // setCheckPointVideoLength(videoLength);
    if (formData.type === "true" && formData.checkpointnumber == null) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.enter_checkpoint_no"),
      });
      return; // Stop the function execution
    }

    const apiFileData: ResourceFileRequest = {
      org_id: org_id ?? "",
      course_id: course_id ?? "",
      section_id: type ?? "",
      file_data: {
        url: formData.url,
        name: formData.name,
        description: formData.description,
        is_premium: formData.is_premium ?? false,
        module_source: resourceType === "1" ? "File" : "Video",
      },
    };

    const apiVideoData: ResourceVideoRequest = {
      org_id: org_id ?? "",
      course_id: course_id ?? "",
      section_id: type ?? "",
      url_data: {
        external_url: formData.url,
        name: formData.name,
        length: videoLength,
        description: formData.description,
        module_source: "Video",
        is_checkpoint_enabled: formData.type,
        num_of_checkpoints: formData.checkpointnumber ?? 0,
        is_random_checkpoint: randomEnabled ?? false,
        always_show_checkpoints: showCheckpoint ?? false,
        is_premium: formData.is_premium ?? false,
      },
    };
    localStorage.setItem("checkpointNo", `${formData.checkpointnumber ?? 0}`);
    const apiData = resourceType === "1" ? apiFileData : apiVideoData;
    try {
      const result = await resourceInsert(apiData, resourceType);

      if (result.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.toast_success_title"),
          description: t("successMessages.added_file"),
        });
        router.push(`${pageUrl.resourceListView}?sectionId=${type}`);
        // setCourseModId(result.course_module_id);
        // if (showMilestone == true && resourceType === "4") {
        //   setCheckPointStatus(!checkPointStatus);
        // } else {
        //   router.push(`${pageUrl.resourceListView}?sectionId=${type}`);
        // }

        // router.push("/questionbank");
      } else if (result.status === "error") {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: result.status,
        });
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.details,
      });
      console.error("An unexpected error occurred:", error);
    }
  }

  const isMilestoneNeeded = (mileStoneType: string): void => {
    {
      mileStoneType == "true"
        ? setShowMilestone(true)
        : setShowMilestone(false);
    }
  };
  const isRandomenabled = (randomenabledType: string): void => {
    {
      randomenabledType == "true"
        ? setRandomenabled(true)
        : setRandomenabled(false);
    }
  };
  const isShowcheckpoint = (showcheckpointType: string): void => {
    {
      showcheckpointType == "true"
        ? setShowcheckpoint(true)
        : setShowcheckpoint(false);
    }
  };
  const handleCancel = (): void => {
    const type = searchParams.get("sectionId");
    router.push(`${pageUrl.resourceListView}?sectionId=${type}`);
  };
  // const handleCheckPointCancel = (value: boolean): void => {
  //   setCheckPointStatus(value);
  //   const type = searchParams.get("sectionId");
  //   router.push(`${pageUrl.resourceListView}?sectionId=${type}`);
  // };
  const handleNameInputChange = (
    e: React.ChangeEvent<HTMLInputElement>,
  ): void => {
    const value = e.target.value;
    const sanitizedValue = value
      .trimStart()
      .replace(/\s{2,}/g, " ")
      .replace(/\s+$/, (match) =>
        match.length > 1 ? match.slice(0, -1) : match,
      );
  
    form.setValue("name", sanitizedValue);
  };
  

  const handleDescriptionChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>,
  ): void => {
    const value = e.target.value;
    const sanitizedValue = value
      .trimStart()
      .replace(/^\p{P}+/u, "")
      .replace(/[^\p{L}\p{M}\p{N}\s\-!@#$%^&*()_+={}[\]:;"'<>,.?/\\|~`]/gu, "")
      .replace(/\s{2,}/g, " ")
      .replace(
        /^[^\p{L}\p{M}\p{N}]|[^\p{L}\p{M}\p{N}\s\-!@#$%^&*()_+={}[\]:;"'<>,.?/\\|~`]/gu,
        "",
      )
      .replace(/\s+$/, (match) =>
        match.length > 1 ? match.slice(0, -1) : match,
      );
    form.setValue("description", sanitizedValue);
  };

  return (
    <>
      <div className="w-full mb-4 mt-4">
        <h4 className="text-2xl tracking-tight">
          {t("resourceLibrary.addModule")} -{" "}
          {resourceType === "1"
            ? t("resourceLibrary.file")
            : t("resourceLibrary.file")}
        </h4>
      </div>
      <Form {...form}>
        <form
          onSubmit={(event) => void form.handleSubmit(onSubmit)(event)}
          className="space-y-8"
        >
          <>
            <FormField
              control={form.control}
              name="url"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("resourceLibrary.url")}</FormLabel>
                  <FormControl>
                    <Input
                      autoComplete="off"
                      placeholder={t("resourceLibrary.pasteUrl")}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("resourceLibrary.name")}</FormLabel>
                  <FormControl>
                    <Input
                      autoComplete="off"
                      {...field}
                      placeholder={t("resourceLibrary.name")}
                      onChange={handleNameInputChange}
                      maxLength={50}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {resourceType === "4" && (
              <div className="w-full sm:w-1/3">
                <FormField
                  control={form.control}
                  name="videolength"
                  render={() => (
                    <FormControl>
                      <FormItem>
                        <FormLabel>
                          {t("resourceLibrary.videoLength")}
                        </FormLabel>
                        <FormControl>
                          <div className="flex space-x-2">
                            <FormField
                              control={form.control}
                              name="videolength.HH"
                              render={() => (
                                <FormItem>
                                  <FormControl>
                                    <Input
                                      autoComplete="off"
                                      type="number"
                                      placeholder={t("resourceLibrary.hours")}
                                      {...form.register("videolength.HH")}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <span>:</span>
                            <FormField
                              control={form.control}
                              name="videolength.MM"
                              render={() => (
                                <FormItem>
                                  <FormControl>
                                    <Input
                                      autoComplete="off"
                                      type="number"
                                      placeholder={t("resourceLibrary.minutes")}
                                      {...form.register("videolength.MM")}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <span>:</span>
                            <FormField
                              control={form.control}
                              name="videolength.SS"
                              render={() => (
                                <FormItem>
                                  <FormControl>
                                    <Input
                                      autoComplete="off"
                                      type="number"
                                      placeholder={t("resourceLibrary.seconds")}
                                      {...form.register("videolength.SS")}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                        </FormControl>
                      </FormItem>
                    </FormControl>
                  )}
                />
              </div>
            )}

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("resourceLibrary.description")}</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder={t("resourceLibrary.description")}
                      autoComplete="off"
                      maxLength={100}
                      {...field}
                      onChange={handleDescriptionChange}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex items-center">
              <FormField
                name="is_premium"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <div className="flex items-center  mb-6">
                      <FormControl>
                        <>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={(value) => {
                              field.onChange(value);
                            }}
                          />
                          <FormLabel className="px-4">
                            {t("resourceLibrary.premiumContent")}
                          </FormLabel>
                        </>
                      </FormControl>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
              {resourceType === "4" && checkPointBtn && (
                <>
                  <div className="flex space-x-20  ms-8">
                    <FormField
                      control={form.control}
                      name="type"
                      render={({ field }) => (
                        <FormItem className="space-y-3">
                          <FormLabel>
                            {t("resourceLibrary.milestonePresent")}
                          </FormLabel>
                          <FormControl>
                            <RadioGroup
                              onValueChange={(value) => {
                                field.onChange(value);
                                isMilestoneNeeded(value);
                              }}
                              defaultValue="false"
                              className="flex flex-row space-x-3"
                            >
                              <FormItem className="flex items-center space-x-3 space-y-0">
                                <FormControl>
                                  <RadioGroupItem value="true" />
                                </FormControl>
                                <FormLabel className="font-normal">
                                  Yes
                                </FormLabel>
                              </FormItem>
                              <FormItem className="flex items-center space-x-3 space-y-0">
                                <FormControl>
                                  <RadioGroupItem value="false" />
                                </FormControl>
                                <FormLabel className="font-normal">
                                  No
                                </FormLabel>
                              </FormItem>
                            </RadioGroup>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {showMilestone && (
                    <div className=" grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6 mt-4">
                      <div className="sm:col-span-4 ms-8">
                        <FormField
                          control={form.control}
                          name="checkpointnumber"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                {t("resourceLibrary.numberOfCheckpoints")}
                              </FormLabel>
                              <FormControl>
                                <Input
                                  autoComplete="off"
                                  type="number"
                                  placeholder={t(
                                    "resourceLibrary.numberOfCheckpoints",
                                  )}
                                  min={1}
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      <FormField
                        control={form.control}
                        name="randomenabled"
                        render={({ field }) => (
                          <FormItem className="space-y-3">
                            <FormLabel>
                              {t("resourceLibrary.randomEnabled")}
                            </FormLabel>
                            <FormControl>
                              <RadioGroup
                                onValueChange={(value) => {
                                  field.onChange(
                                    value === "true"
                                      ? true || value === "false"
                                      : false,
                                  );
                                  isRandomenabled(value);
                                }}
                                defaultValue="false"
                                className="flex flex-row space-x-3"
                              >
                                <FormItem className="flex items-center space-x-3 space-y-0">
                                  <FormControl>
                                    <RadioGroupItem value="true" />
                                  </FormControl>
                                  <FormLabel className="font-normal">
                                    Yes
                                  </FormLabel>
                                </FormItem>
                                <FormItem className="flex items-center space-x-3 space-y-0">
                                  <FormControl>
                                    <RadioGroupItem value="false" />
                                  </FormControl>
                                  <FormLabel className="font-normal">
                                    No
                                  </FormLabel>
                                </FormItem>
                              </RadioGroup>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="showcheckpoint"
                        render={({ field }) => (
                          <FormItem className="space-y-3">
                            <FormLabel>
                              {t("resourceLibrary.alwaysShowCheckpoint")}
                            </FormLabel>
                            <FormControl>
                              <RadioGroup
                                onValueChange={(value) => {
                                  field.onChange(
                                    value === "true"
                                      ? true || value === "false"
                                      : false,
                                  );
                                  isShowcheckpoint(value);
                                }}
                                defaultValue="false"
                                className="flex flex-row space-x-3"
                              >
                                <FormItem className="flex items-center space-x-3 space-y-0">
                                  <FormControl>
                                    <RadioGroupItem value="true" />
                                  </FormControl>
                                  <FormLabel className="font-normal">
                                    Yes
                                  </FormLabel>
                                </FormItem>
                                <FormItem className="flex items-center space-x-3 space-y-0">
                                  <FormControl>
                                    <RadioGroupItem value="false" />
                                  </FormControl>
                                  <FormLabel className="font-normal">
                                    No
                                  </FormLabel>
                                </FormItem>
                              </RadioGroup>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  )}
                </>
              )}
            </div>
          </>

          <div className="w-full flex justify-end mt-4">
            <div className="px-4">
              <Button
                variant="outline"
                className="w-full sm:w-auto mr-2"
                onClick={handleCancel}
              >
                {t("buttons.cancel")}
              </Button>
              <Button type="submit">{t("buttons.submit")}</Button>
            </div>
          </div>
        </form>
      </Form>
      {/* {checkPointStatus && (
        <Modal
          title="Checkpoint details"
          header=""
          openDialog={checkPointStatus}
          closeDialog={(value: boolean) =>
            handleCheckPointCancel(value as boolean)
          }
          type="max-w-5xl"
        >
          <Milestone
            closeMilestoneDialog={(value: boolean) =>
              handleCheckPointCancel(value as boolean)
            }
            courseModuleId={courseModuleId}
            checkpointNumber={checkpointNumber}
            checkpointVideoLength={checkpointVideoLength}
            randomEnabledStatus={randomEnabled}
          />
        </Modal>
      )} */}
    </>
  );
};
