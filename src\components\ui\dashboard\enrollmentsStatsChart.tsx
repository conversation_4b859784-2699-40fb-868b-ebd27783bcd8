import React from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader } from "../card";

import { Doughnut } from "react-chartjs-2";
import Chart, { ArcElement, Legend, Tooltip } from "chart.js/auto";

Chart.register(ArcElement, Tooltip, Legend);

interface EnrollmentsProps {
  labels: string[];
  chartLabel: string;
  data: number[];
  backgroundColors: string[];
  borderColors: string[];
}

export function DBEnrolmentsStats({
  labels,
  chartLabel,
  data,
  backgroundColors,
  borderColors,
}: EnrollmentsProps): React.JSX.Element {
  const chartData = {
    labels: labels,
    datasets: [
      {
        label: chartLabel,
        data: data,
        backgroundColor: backgroundColors,
        borderColor: borderColors,
        borderWidth: 1,
      },
    ],
  };
  return (
    <Card>
      <CardHeader></CardHeader>
      <CardContent>
        <Doughnut
          data={chartData}
          height="450px"
          width="450px"
          options={{ maintainAspectRatio: false }}
        />
      </CardContent>
    </Card>
  );
}
