import type { ReactNode } from "react";
import React from "react";
import Link from "next/link";

interface BreadcrumbItem {
  name: string;
  path: string;
}

interface TBreadCrumbProps {
  items: BreadcrumbItem[];
  separator: ReactNode;
  containerClasses?: string;
  listClasses?: string;
  capitalizeLinks?: boolean;
}

const NextBreadcrumb: React.FC<TBreadCrumbProps> = ({
  items,
  separator,
  containerClasses,
  listClasses,
  capitalizeLinks,
}) => {
  return (
    <div className={`flex ${containerClasses} pt-1`}>
      {" "}
      {/* Reduced padding on top */}
      <ul className="flex items-center space-x-2 breadcrumb-list text-sm">
        {items.map((item, index) => (
          <React.Fragment key={index}>
            <li className={`text-gray-500 ${listClasses}`}>
              {index === items.length - 1 ? (
                <span
                  className={`text-[#00afbb] font-semibold ${
                    (capitalizeLinks ?? false) ? "uppercase" : ""
                  }`}
                >
                  {(capitalizeLinks ?? false)
                    ? item.name.toUpperCase()
                    : item.name}
                </span>
              ) : (
                <Link
                  href={item.path}
                  className={`text-gray-600 hover:text-blue-500 transition-colors ${
                    (capitalizeLinks ?? false) ? "uppercase" : ""
                  }`}
                >
                  {(capitalizeLinks ?? false)
                    ? item.name.toUpperCase()
                    : item.name}
                </Link>
              )}
            </li>
            {index !== items.length - 1 && (
              <span className="mx-1">{separator}</span>
            )}
          </React.Fragment>
        ))}
      </ul>
    </div>
  );
};

export default NextBreadcrumb;
