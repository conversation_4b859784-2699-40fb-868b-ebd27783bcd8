"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import React, { type BaseSyntheticEvent, useEffect, useState } from "react";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { DataTable } from "../../components/ui/data-table/data-table";
import { Input } from "../../components/ui/input";
import { milestoneType } from "@/lib/constants";
import { useForm } from "react-hook-form";
import { Combobox } from "../../components/ui/combobox";
import useCourse from "@/hooks/useCourse";
import type {
  CheckPointData,
  CheckPointForm,
  CheckPointList,
  CheckPointRequest,
  ComboData,
  ErrorCatch,
  ToastType,
} from "@/types";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { getColumns } from "./columns";
import { CheckPointFormShema } from "@/schema/schema";
import { zodResolver } from "@hookform/resolvers/zod";
import { Trash2 } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import { useTranslation } from "react-i18next";
interface MilestoneProps {
  closeMilestoneDialog: (value: boolean) => void;
  courseModuleId: string | null;
  checkpointNumber: number;
  randomEnabledStatus: boolean | false;
  checkpointVideoLength?: string;
}

export const Milestone: React.FC<MilestoneProps> = ({
  courseModuleId,
  checkpointNumber,
  randomEnabledStatus,
  checkpointVideoLength,
}): React.JSX.Element => {
  const { t } = useTranslation();
  const columns = getColumns(t);
  const { getCheckPointQuizList, addCheckPoint } = useCourse();
  const [comboData, setComboData] = useState<ComboData[]>([]);
  const [selResourceId, setSelResourceId] = useState("");
  const [selResourceName, setSelResourceName] = useState("");
  const [addedValues, setAddedValues] = useState<CheckPointForm[]>([]);
  const [sequenceNo, setSequenceNo] = useState<ComboData[]>([]);
  // const [isMandatoryType, setIsMandatoryType] = useState<boolean>(false);
  const { toast } = useToast() as ToastType;
  const form = useForm<CheckPointForm>({
    resolver: zodResolver(CheckPointFormShema),
    defaultValues: {
      isMandatory: "false",
    },
  });
  // const searchParams = useSearchParams();
  // const router = useRouter();
  // const type = searchParams.get("sectionId");

  async function onSubmit(): Promise<void> {
    form.setValue("checkpoint_resname", selResourceName);
    if (addedValues.length < checkpointNumber) {
      const formData = form.getValues();

      let isUniqueName = true;
      if (randomEnabledStatus === false) {
        isUniqueName = addedValues.every(
          (item) => item.checkpoint_name !== formData.checkpoint_name,
        );
      } else {
        isUniqueName = true;
      }

      let isUniqueSequence = true;
      //To Do : need to check validation of checkpoint

      if (randomEnabledStatus === false) {
        isUniqueSequence = addedValues.every(
          (item) => item.sequence_number !== formData.sequence_number,
          //   &&
          //   JSON.stringify({
          //     HH: item.checkpoint_startTime.HH ?? "00",
          //     MM: item.checkpoint_startTime.MM ?? "00",
          //     SS: item.checkpoint_startTime.SS ?? "00",
          // }) !==
          //     JSON.stringify(formData.checkpoint_startTime),
        );
      } else {
        isUniqueSequence = true;
      }

      let isUniqueStartTime = true;
      if (randomEnabledStatus === false) {
        isUniqueStartTime = addedValues.every((item) => {
          const {
            HH: itemHH,
            MM: itemMM,
            SS: itemSS,
          } = item.checkpoint_startTime;
          const { HH, MM, SS } = formData.checkpoint_startTime;
          return itemHH !== HH || itemMM !== MM || itemSS !== SS;
        });
      } else {
        isUniqueStartTime = true;
      }

      let isUniqueResourceId = true;
      if (randomEnabledStatus === false || randomEnabledStatus === true) {
        isUniqueResourceId = addedValues.every((item) => {
          return item.checkpoint_resid !== formData.checkpoint_resid;
        });
      } else {
        isUniqueResourceId = true;
      }

      const hasMandatoryTrue = addedValues.some(
        (item) => item.isMandatory === "true",
      );

      if (hasMandatoryTrue) {
        formData.isMandatory = "false";
      }
      const HH =
        formData.checkpoint_startTime.HH !== ""
          ? formData.checkpoint_startTime.HH
          : "00";
      const MM =
        formData.checkpoint_startTime.MM !== ""
          ? formData.checkpoint_startTime.MM
          : "00";
      const SS =
        formData.checkpoint_startTime.SS !== ""
          ? formData.checkpoint_startTime.SS
          : "00";

      // Combine the values to create the desired format
      const formattedStartTime = `${String(HH).padStart(2, "0")}:${String(
        MM,
      ).padStart(2, "0")}:${String(SS).padStart(2, "0")}`;

      if (checkpointVideoLength != null) {
        const isStartTimeValid = formattedStartTime <= checkpointVideoLength;
        if (isStartTimeValid) {
          if (
            isUniqueName &&
            isUniqueSequence &&
            isUniqueStartTime &&
            isUniqueResourceId
          ) {
            if (formData.checkpoint_resid === "") {
              form.setValue("checkpoint_resid", selResourceId);
            }
            setAddedValues((prevValues) => [...prevValues, formData]);
            form.setValue("checkpoint_startTime", {
              HH: "00",
              MM: "00",
              SS: "00",
            });
            form.setValue("sequence_number", "");
            form.setValue("checkpoint_name", "");
          } else {
            toast({
              variant: ERROR_MESSAGES.toast_variant_destructive,
              title: t("errorMessages.toast_error_title"),
              description: t("errorMessages.unique_checkpoint"),
            });
          }
        } else {
          toast({
            variant: ERROR_MESSAGES.toast_variant_destructive,
            title: t("errorMessages.toast_error_title"),
            description: t("errorMessages.check_point_length"),
          });
          return;
        }
      }
    } else {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.check_checkpoint_no"),
      });
    }
    form.setValue("checkpoint_startTime.HH", "");
    form.setValue("checkpoint_startTime.MM", "");
    form.setValue("checkpoint_startTime.SS", "");
  }

  const handleComboValueChange = (selectedValue: string): void => {
    setSelResourceId(selectedValue);
    const selectedLabel = comboData.find((item) => item.value === selectedValue)
      ?.label as string;
    setSelResourceName(selectedLabel);
    form.setValue("checkpoint_resname", selResourceName);
    form.setValue("checkpoint_resid", selectedValue);
    form.setValue("checkpoint_type", "Exam");
  };

  const CheckPointQuizList = (): void => {
    const fetchData = async (): Promise<void> => {
      try {
        const cpList = await getCheckPointQuizList();

        const comboData: ComboData[] = cpList.map((item) => ({
          value: item.id,
          label: item.name,
        }));

        setComboData(comboData);
        // console.log(category);
      } catch (error) {
        console.error("Error fetching data:");
      }
    };
    fetchData().catch((error) => console.log(error));
  };
  const generateSequenceNumber = (): void => {
    const maxSequenceNo = checkpointNumber;
    const generatedSequenceNo = [];
    for (let i = 1; i <= maxSequenceNo; i++) {
      const sequenceNoData: ComboData = {
        value: i.toString(),
        label: i.toString(),
      };
      generatedSequenceNo.push(sequenceNoData);
      setSequenceNo(generatedSequenceNo);
    }
  };

  useEffect(() => {
    CheckPointQuizList();
    generateSequenceNumber();
  }, []);

  const handleDelete = (data: CheckPointForm[]): void => {
    const updatedValues = addedValues.filter(
      (item) => JSON.stringify(item) !== JSON.stringify(data),
    );
    setAddedValues(updatedValues);
  };
  const checkPointUpdate = async (e: BaseSyntheticEvent): Promise<void> => {
    console.log(e);
    const checkpointData = (
      addedValues: CheckPointForm[],
    ): CheckPointData[] => {
      return addedValues.map((item) => ({
        sequence: parseInt(item.sequence_number),
        is_mandatory: item?.isMandatory === "false" ? false : true,
        name: item.checkpoint_name,
        start_time: `${
          item.checkpoint_startTime?.HH !== undefined &&
          item.checkpoint_startTime.HH !== ""
            ? item.checkpoint_startTime.HH.length === 1
              ? `0${item.checkpoint_startTime.HH}`
              : item.checkpoint_startTime.HH
            : "00"
        }:${
          item.checkpoint_startTime?.MM !== undefined &&
          item.checkpoint_startTime.MM !== ""
            ? item.checkpoint_startTime.MM.length === 1
              ? `0${item.checkpoint_startTime.MM}`
              : item.checkpoint_startTime.MM
            : "00"
        }:${
          item.checkpoint_startTime?.SS !== undefined &&
          item.checkpoint_startTime.SS !== ""
            ? item.checkpoint_startTime.SS.length === 1
              ? `0${item.checkpoint_startTime.SS}`
              : item.checkpoint_startTime.SS
            : "00"
        }`,
        module_type_id: "dd33330a-7ab4-452b-b8bd-9e37192f0ffb",
        instance_id: item.checkpoint_resid,
        checkpoint_type: item.checkpoint_type,
      }));
    };

    const checkpoint_data = checkpointData(addedValues);

    const org_id = localStorage.getItem("orgId");
    const data: CheckPointRequest = {
      checkpoint_data: checkpoint_data,
      course_module_id: courseModuleId ?? "",
      org_id: org_id ?? "",
    };

    try {
      const result = await addCheckPoint([data]);

      if (result.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.toast_success_title"),
          description: t("successMessages.checkpoint_details"),
        });
        //To Do: need to update it to router push
        window.location.reload();
        //closeMilestoneDialog(false);
      } else if (result.status === "error") {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: result.status,
        });
      }
    } catch (error) {
      const err = error as ErrorCatch;

      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
      console.error("An unexpected error occurred:", error);
    }
  };

  const isMandatoryenabled = (isMandatoryType: string): void => {
    form.setValue("isMandatory", isMandatoryType);
  };

  return (
    <div className="border rounded-md p-4 mt-4">
      <Form {...form}>
        <form
          onSubmit={(event) => void form.handleSubmit(onSubmit)(event)}
          className="space-y-8"
        >
          <div className="w-full flex flex-wrap">
            <div className="w-full sm:w-1/3 pr-4">
              <FormField
                control={form.control}
                name="sequence_number"
                render={() => (
                  <FormItem>
                    {" "}
                    <FormLabel>
                      {t("courses.courseModule.sequenceNumber")}
                    </FormLabel>
                    <FormControl>
                      {/* <Input {...field} /> */}
                      <Combobox data={sequenceNo} onSelectChange={() => {}} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="w-full sm:w-1/3 pr-4">
              <FormField
                control={form.control}
                name="checkpoint_name"
                render={({ field }) => (
                  <FormItem>
                    {" "}
                    <FormLabel>
                      {t("courses.courseModule.checkpointName")}
                    </FormLabel>
                    <FormControl>
                      <Input {...field} autoComplete="off" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="w-full sm:w-1/3">
              <FormField
                control={form.control}
                name="checkpoint_startTime"
                render={() => (
                  <FormControl>
                    <FormItem>
                      <FormLabel>
                        {t("courses.courseModule.checkpointStartTime")}
                      </FormLabel>
                      <FormControl>
                        <div className="flex space-x-2">
                          <FormField
                            control={form.control}
                            name="checkpoint_startTime.HH"
                            render={() => (
                              <FormItem>
                                <FormControl>
                                  <Input
                                    autoComplete="off"
                                    type="number"
                                    placeholder="Hours"
                                    {...form.register(
                                      "checkpoint_startTime.HH",
                                    )}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <span>:</span>
                          <FormField
                            control={form.control}
                            name="checkpoint_startTime.MM"
                            render={() => (
                              <FormItem>
                                <FormControl>
                                  <Input
                                    autoComplete="off"
                                    type="number"
                                    placeholder="Minutes"
                                    {...form.register(
                                      "checkpoint_startTime.MM",
                                    )}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <span>:</span>
                          <FormField
                            control={form.control}
                            name="checkpoint_startTime.SS"
                            render={() => (
                              <FormItem>
                                <FormControl>
                                  <Input
                                    autoComplete="off"
                                    type="number"
                                    placeholder="Seconds"
                                    {...form.register(
                                      "checkpoint_startTime.SS",
                                    )}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </FormControl>
                    </FormItem>
                  </FormControl>
                )}
              />
            </div>
          </div>
          <div className="w-full flex flex-wrap">
            <div className="w-full sm:w-1/3 pr-4">
              <FormField
                control={form.control}
                name="checkpoint_type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {t("courses.courseModule.checkpointType")}
                    </FormLabel>

                    <Select
                      name="courseVisibility"
                      onValueChange={field.onChange}
                      defaultValue={"exam"}
                      disabled
                    >
                      {" "}
                      <FormControl>
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Select" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {milestoneType.map((item, index) => (
                          <SelectItem key={index} value={item.value.toString()}>
                            {item.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>

                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="w-full sm:w-1/3 pr-4">
              <FormField
                control={form.control}
                name="checkpoint_resid"
                render={() => (
                  <FormItem>
                    <FormLabel>
                      {t("courses.courseModule.selectCheckpointResourceId")}
                    </FormLabel>
                    <FormControl>
                      <div className="overflow-x-auto">
                        <Combobox
                          data={comboData}
                          onSelectChange={handleComboValueChange}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="w-full sm:w-1/3 pr-4">
              <FormField
                control={form.control}
                name="isMandatory"
                render={() => (
                  <FormItem className="space-y-3">
                    <FormLabel>
                      {t("courses.courseModule.isMandatory")}
                    </FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={(value) => {
                          // field.onChange(
                          //   value === "true"
                          //     ? true || value === "false"
                          //     : false,
                          // );
                          form.setValue("isMandatory", value);
                          isMandatoryenabled(value);
                        }}
                        defaultValue="false"
                        className="flex flex-row space-x-3"
                      >
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="true" />
                          </FormControl>
                          <FormLabel className="font-normal">Yes</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="false" />
                          </FormControl>
                          <FormLabel className="font-normal">No</FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="w-full flex ">
              <div className="flex justify-end w-full">
                {" "}
                <Button type="submit">{t("buttons.addCheckpoint")}</Button>
              </div>
            </div>
          </div>
        </form>
      </Form>

      <div className="border rounded-md p-4 mt-4">
        <DataTable
          columns={columns}
          data={addedValues as CheckPointList[]}
          FilterLabel={t("courses.courseModule.filterByCheckpointName")}
          FilterBy={"checkpoint_name"}
          actions={[
            {
              title: t("buttons.delete"),
              icon: Trash2,
              varient: "icon",
              handleClick: (val): void => handleDelete(val as CheckPointForm[]),
            },
          ]}
        />
      </div>
      {addedValues.length > 0 && (
        <div className="float-right mt-4">
          <Button
            type="submit"
            onClick={(e: BaseSyntheticEvent) => {
              checkPointUpdate(e).catch((error) => console.log(error));
            }}
          >
            {t("buttons.submit")}
          </Button>
        </div>
      )}
    </div>
  );
};
