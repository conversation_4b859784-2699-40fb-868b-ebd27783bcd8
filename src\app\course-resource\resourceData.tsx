"use client";
import React, { useEffect, type JSX } from "react";
import type { ResourceDataType } from "@/types";

export const ResourceData: React.FC<ResourceDataType> = ({
  data,
  onSteptypeChange,
}): JSX.Element => {
  const handleSteptypeClick = (): void => {
    onSteptypeChange(data.StepType);
  };

  useEffect(() => {
    void handleSteptypeClick();
  }, [onSteptypeChange]);
  return (
    <>
      <div className="ml-6 relative border p-4 rounded-lg transition-all duration-300 hover:border-gray-400 hover:shadow-md bg-gray-200 ">
        <div className="flex justify-between">
          <div className="ml-3 text-lg font-semibold">{data.StepLabel}</div>
          <div className="ml-3 text-lg font-semibold mt-1">
            {/* <EyeIcon onClick={handleSteptypeClick} /> */}
          </div>
        </div>
      </div>
    </>
  );
};
