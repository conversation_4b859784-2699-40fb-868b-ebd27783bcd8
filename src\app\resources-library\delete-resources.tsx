import React, { type BaseSyntheticEvent } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import useResourceLibrary from "@/hooks/useResourceLibrary";
import { useToast } from "@/components/ui/use-toast";
import type { ErrorCatch, LogUserActivityRequest, ToastType } from "@/types";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import { ORG_KEY } from "@/lib/constants";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";

export default function DeleteResources({
  onCancel,
  resourceId,
  resourceType,
}: {
  onCancel: () => void;
  resourceId: string;
  resourceType: string;
}): React.JSX.Element {
  const { t } = useTranslation();
  const { deleteResources } = useResourceLibrary();
  const { toast } = useToast() as ToastType;
  const { updateUserActivity } = useLogUserActivity();
  const handleDeleteClick = async (e: BaseSyntheticEvent): Promise<void> => {
    console.log(e);

    const orgId = localStorage.getItem(ORG_KEY);
    const params = {
      org_id: orgId ?? "",
      module_id: resourceType,
      instance_id: resourceId,
    };

    try {
      const result = await deleteResources(params);
      if (result.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_success,
          title: t("successMessages.deleteResourceTitle"),
          description: t("successMessages.deleteResourceMsg"),
        });
        onCancel();
        const params = {
          activity_type: "Resource_Library",
          screen_name: "Delete Resource",
          action_details: "Resource deleted ",
          target_id: resourceId as string,
          log_result: "SUCCESS",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
      const params = {
        activity_type: "Resource_Library",
        screen_name: "Delete Resource",
        action_details: "Failed to delete resource ",
        target_id: resourceId as string,
        log_result: "ERROR",
      };
      void updateLogUserActivity(params).catch((error) => {
        console.error(error);
      });
    }
  };
  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };
  return (
    <>
      <div className="mb-2 mr-4">
        <p className="ml-0 ">{t("resourceLibrary.deletePrompt")}</p>
      </div>
      <div className="flex topic-icons pr-4">
        <div className="flex items-center justify-center float-right">
          <Button type="button" className="bg-[#33363F]" onClick={onCancel}>
            {t("buttons.cancel")}
          </Button>
          &nbsp;
          <Button
            type="submit"
            className="bg-[#9FC089]"
            onClick={(e: BaseSyntheticEvent) => {
              handleDeleteClick(e).catch((error) => console.log(error));
            }}
          >
            {t("buttons.delete")}
          </Button>
        </div>
      </div>
    </>
  );
}
