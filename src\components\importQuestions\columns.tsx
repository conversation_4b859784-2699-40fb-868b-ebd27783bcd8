"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { Question } from "@/types";

export const getColumns = (
  t: (key: string) => string,
): ColumnDef<Question>[] => [
  {
    id: "select",
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "name",
    header:t("exams.questions"),
  },
  {
    accessorKey: "default_mark",
    header:t("exams.defaultMark"),
  },
  {
    accessorKey: "penalty",
    header:t("exams.penaltyScore"),
  },
];
