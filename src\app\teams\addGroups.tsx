import React, { useEffect } from "react";
import type {
  <PERSON><PERSON>r<PERSON>atch,
  GroupForm,
  LogUserActivityRequest,
  ToastType,
  updateGroupForm,
} from "@/types";

import { useToast } from "@/components/ui/use-toast";
import useGroups from "@/hooks/useGroups";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { UpdateGroupschema } from "@/schema/schema";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { ModalButton } from "@/components/ui/modalButton";
import { Textarea } from "@/components/ui/textarea";
import { DEFAULT_FOLDER_ID } from "@/lib/constants";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
export default function AddGroups({
  onCancel,
  onSave,
  data,
}: {
  onSave: () => void;
  onCancel: () => void;
  data: GroupForm;
  isModal?: boolean;
}): React.JSX.Element {
  const { t } = useTranslation();
  const { toast } = useToast() as ToastType;

  const form = useForm<updateGroupForm>({
    resolver: zodResolver(UpdateGroupschema),
  });
  const handleCancelClick = (): void => {
    onCancel();
  };
  const { addGroups } = useGroups();
  const { editGroups } = useGroups();
  const { updateUserActivity } = useLogUserActivity();
  useEffect(() => {
    if (data.id !== "") {
      form.setValue("name", data.name);
      form.setValue("description", data.description as string);
    }
  }, []);

  async function handleToastSave(datas: updateGroupForm): Promise<void> {
    const orgId = localStorage.getItem("orgId");
    const groupAddData = {
      org_id: orgId ?? "",
      group_data: {
        group_name: datas.name,
        description: datas.description,
      },
    };
    if (data.id === "") {
      try {
        const result = await addGroups(groupAddData);
        if (result.status === "success") {
          toast({
            variant: SUCCESS_MESSAGES.toast_variant_default,
            title: t("successMessages.groupTitle"),
            description: t("successMessages.addGroup"),
          });

          onSave();
          onCancel();
          const params = {
            activity_type: "Group",
            screen_name: "Add Group",
            action_details: "Group created ",
            target_id: result.group_id ?? DEFAULT_FOLDER_ID,
            log_result: "SUCCESS",
          };
          void updateLogUserActivity(params).catch((error) => {
            console.error(error);
          });
        } else if (result.status === "error") {
          toast({
            variant: ERROR_MESSAGES.toast_variant_destructive,
            title: t("errorMessages.toast_error_title"),
            description: result.status,
          });
          const params = {
            activity_type: "Group",
            screen_name: "Add Group",
            action_details: "Failed to create group ",
            target_id: result.group_id ?? DEFAULT_FOLDER_ID,
            log_result: "ERROR",
          };
          void updateLogUserActivity(params).catch((error) => {
            console.error(error);
          });
        }
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
        const params = {
          activity_type: "Group",
          screen_name: "Add Group",
          action_details: "Failed to create group ",
          target_id: DEFAULT_FOLDER_ID,
          log_result: "ERROR",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      }
    } else {
      const groupEditData = {
        org_id: orgId ?? "",
        group_data: {
          group_id: data?.id,
          group_name: datas.name,
          description: datas.description,
        },
      };
      try {
        const result = await editGroups(groupEditData);
        if (result.status === "success") {
          toast({
            variant: SUCCESS_MESSAGES.toast_variant_default,
            title: t("successMessages.groupsUpdated"),
            description: t("successMessages.successfullyUpdatedGroups"),
          });
          onSave();
          onCancel();
          const params = {
            activity_type: "Group",
            screen_name: "Edit Group",
            action_details: "Group edited ",
            target_id: data?.id as string,
            log_result: "SUCCESS",
          };
          void updateLogUserActivity(params).catch((error) => {
            console.error(error);
          });
        } else if (result.status === "error") {
          toast({
            variant: ERROR_MESSAGES.toast_variant_destructive,
            title: t("errorMessages.toast_error_title"),
            description: result.status,
          });
          const params = {
            activity_type: "Group",
            screen_name: "Edit Group",
            action_details: "Failed to edit group ",
            target_id: data?.id as string,
            log_result: "ERROR",
          };
          void updateLogUserActivity(params).catch((error) => {
            console.error(error);
          });
        }
      } catch (error) {
        const errMsg: string =
          typeof error === "string"
            ? error
            : error instanceof Error
            ? error.message
            : "Unknown error";

        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: errMsg,
        });
        const params = {
          activity_type: "Group",
          screen_name: "Edit Group",
          action_details: "Failed to edit group ",
          target_id: data?.id as string,
          log_result: "ERROR",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      }
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    const value = e.target.value;
    const sanitizedValue = value
      .trimStart()
      .replace(/[^a-zA-Z0-9\s&-]/g, "")
      .replace(/\s{2,}/g, " ")
      .replace(/^[^a-zA-Z]|[^a-zA-Z0-9\s&-]/g, "")
      .replace(/\s+$/, (match) =>
        match.length > 1 ? match.slice(0, -1) : match,
      );
    form.setValue("name", sanitizedValue);
  };

  const handleDescriptionChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>,
  ): void => {
    const value = e.target.value;
    const sanitizedValue = value
      .trimStart()
      .replace(/^\p{P}+/u, "")
      .replace(/[^\p{L}\p{M}\p{N}\s\-!@#$%^&*()_+={}[\]:;"'<>,.?/\\|~`]/gu, "")
      .replace(/\s{2,}/g, " ")
      .replace(
        /^[^\p{L}\p{M}\p{N}]|[^\p{L}\p{M}\p{N}\s\-!@#$%^&*()_+={}[\]:;"'<>,.?/\\|~`]/gu,
        "",
      )
      .replace(/\s+$/, (match) =>
        match.length > 1 ? match.slice(0, -1) : match,
      );
    form.setValue("description", sanitizedValue);
  };

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };
  return (
    <Form {...form}>
      <form
        onSubmit={(event) => void form.handleSubmit(handleToastSave)(event)}
        className="space-y-4"
      >
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                {t("groups.teams.addGroupModal.groupName")}{" "}
                <span className="text-red-700">*</span>
              </FormLabel>
              <FormControl>
                <Input
                  autoComplete="off"
                  placeholder={t("groups.teams.addGroupModal.groupName")}
                  maxLength={30}
                  {...field}
                  onChange={handleInputChange}
                />
              </FormControl>
              <FormDescription></FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                {t("groups.teams.addGroupModal.description")}{" "}
                <span className="text-red-700">*</span>
              </FormLabel>
              <FormControl>
                {/* <textarea
                  className="w-full md:w-full whitespace-normal bg-white border border-gray-300 rounded-md shadow-sm py-2 px-3 text-sm text-gray-700 placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary"
                  placeholder="Description"
                  {...field}
                  title={field.value}
                  maxLength={100}
                /> */}
                <Textarea
                  placeholder={t("groups.teams.addGroupModal.description")}
                  autoComplete="off"
                  maxLength={100}
                  {...field}
                  onChange={handleDescriptionChange}
                />
              </FormControl>
              <FormDescription></FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <ModalButton
          closeDialog={handleCancelClick}
          closeLabel={t("groups.teams.addGroupModal.cancel")}
          submitLabel={t("groups.teams.addGroupModal.save")}
        />
      </form>
    </Form>
  );
}
