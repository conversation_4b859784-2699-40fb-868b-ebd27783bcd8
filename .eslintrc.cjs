module.exports = {
  extends: [
    "next/core-web-vitals",
    "eslint:recommended",
    // "plugin:react/recommended",
    // "plugin:react-hooks/recommended",
    "prettier",
  ],
  env: {
    browser: true,
    amd: true,
    node: true,
  },

  overrides: [
    {
      files: ["*.ts", "*.tsx"],
      parser: "@typescript-eslint/parser",
      parserOptions: {
        parser: "@typescript-eslint/parser",
        ecmaVersion: "latest",
        project: ["tsconfig.json"],
      },
      extends: ["plugin:@typescript-eslint/recommended", "prettier"],
      plugins: ["@typescript-eslint", "prettier"],
      env: {
        node: true,
        browser: true,
        es6: true,
      },
      rules: {
        // Typescript-eslint
        "@typescript-eslint/no-unused-vars": "error",
        "@typescript-eslint/no-explicit-any": "error",
        "@typescript-eslint/adjacent-overload-signatures": "error",
        "@typescript-eslint/array-type": "error",
        "@typescript-eslint/await-thenable": "error",
        "@typescript-eslint/ban-ts-comment": [
          "error",
          {
            "ts-expect-error": "allow-with-description",
            "ts-ignore": true,
            "ts-nocheck": true,
            "ts-check": false,
            minimumDescriptionLength: 5,
          },
        ],
        "@typescript-eslint/ban-tslint-comment": "error",
        "@typescript-eslint/ban-types": [
          "error",
          {
            types: {
              Object: "Use {} instead.",
              String: { message: 'Use "string" instead.', fixWith: "string" },
              Number: { message: 'Use "number" instead.', fixWith: "number" },
              Boolean: {
                message: 'Use "boolean" instead.',
                fixWith: "boolean",
              },
            },
            extendDefaults: false,
          },
        ],
        "@typescript-eslint/consistent-indexed-object-style": [
          "error",
          "record",
        ],
        "@typescript-eslint/consistent-type-assertions": [
          "error",
          {
            assertionStyle: "as",
            objectLiteralTypeAssertions: "allow-as-parameter",
          },
        ],
        "@typescript-eslint/consistent-type-definitions": [
          "error",
          "interface",
        ],
        "@typescript-eslint/consistent-type-imports": [
          "error",
          { prefer: "type-imports" },
        ],
        "@typescript-eslint/explicit-function-return-type": [
          "error",
          {
            allowExpressions: true,
            allowTypedFunctionExpressions: true,
          },
        ],
        "@typescript-eslint/explicit-member-accessibility": [
          "error",
          {
            accessibility: "explicit",
            overrides: {
              accessors: "explicit",
              constructors: "no-public",
              methods: "explicit",
              properties: "off",
              parameterProperties: "explicit",
            },
          },
        ],
        "@typescript-eslint/member-delimiter-style": [
          "error",
          {
            multiline: {
              delimiter: "semi",
              requireLast: true,
            },
            singleline: {
              delimiter: "semi",
              requireLast: false,
            },
          },
        ],
        "@typescript-eslint/member-ordering": [
          "error",
          {
            default: [
              "public-static-field",
              "protected-static-field",
              "private-static-field",
              "public-instance-field",
              "protected-instance-field",
              "private-instance-field",
              "public-constructor",
              "protected-constructor",
              "private-constructor",
              "public-instance-method",
              "protected-instance-method",
              "private-instance-method",
              "public-static-method",
              "protected-static-method",
              "private-static-method",
            ],
          },
        ],
        "@typescript-eslint/method-signature-style": ["error", "property"],
        "@typescript-eslint/no-inferrable-types": "error",
        "@typescript-eslint/explicit-module-boundary-types": "error",
        "@typescript-eslint/no-floating-promises": "error",
        "@typescript-eslint/no-misused-promises": "error",
        "@typescript-eslint/no-unsafe-assignment": "error",
        "@typescript-eslint/no-unsafe-call": "error",
        "@typescript-eslint/no-unsafe-member-access": "error",
        "@typescript-eslint/no-unsafe-return": "error",
        "@typescript-eslint/prefer-nullish-coalescing": "error",
        "@typescript-eslint/prefer-optional-chain": "error",
        "@typescript-eslint/restrict-template-expressions": [
          "error",
          { allowBoolean: true },
        ],
        "@typescript-eslint/strict-boolean-expressions": [
          "error",
          { allowString: false, allowNumber: false },
        ],
        "@typescript-eslint/switch-exhaustiveness-check": "error",
      },
    },
  ],
  plugins: ["react"],
};
